/* margin: 10px 5px 15px 20px; 
top margin is 10px. right margin is 5px. 
bottom margin is 15px. left margin is 20px. */

table {
    width: 100%;
    margin: auto;
    padding: 0px 0px 0px 0px;
}

table,
th,
td {
    border: 1px solid rgb(205, 210, 212);
    border-collapse: collapse;
}

tr {
    page-break-inside: avoid;
    height: 28px;
    vertical-align: middle;
}

th {
    background-color: #D9D9D9;
    border: 1px solid rgb(186, 191, 192);
    /* vertical-align: middle; */
    text-align: center;
    margin: 0px 0px 0px 0px;
    padding: 0px 5px 0px 5px;
    font-size: 11px;
}

td {
    font-family: 'BanglaFontSejuti';
    /* vertical-align: middle; */
    margin: 0px 0px 0px 0px;
    padding: 0px 5px 0px 5px;
    font-size: 10px;
}

td:first-child,
th:first-child,
td:nth-child(n+4) {  
    text-align: center;
}


td:last-child,
tr:not(:first-child):last-child th:first-child,
tr:not(:first-child):last-child th:nth-last-child(-n+2) {
    text-align: right;
}

tr:nth-child(even) {
    background-color: #F0FFF0;
}

#heading {
    text-align: center;
    color: #7a7b7d;
    font-size: 20px;
    margin: 0px 0px 10px 0px;
}

.report_date {
    text-align: right;
    font-size: 14px;
    margin: 0px 0px 0px 0px;
}

.card {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
    margin: auto;
    margin-bottom: 10px;
    transition: 0.3s;
    width: 90%;
}

.card:hover {
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2);
}

.container {
    padding: 5px 5px;
}

.r_corners {
    border-radius: 15%;
    /* border: 1px solid #8a8b87; */
    /* background: url(paper.gif); */
    /* background-position: left top;
  background-repeat: repeat; */
    /* padding: 20px; 
  width: 200px;
  height: 150px;   */
}

.r_corners_5px {
    border-radius: 5px;
    align-items: middle;
    background-color: #FAEBD7;
    margin: 5px 0px 5px 0px;
    padding: 5px;
    /* border: 1px solid #8a8b87; */
    /* background:rgb(180, 201, 201); */

}

.flex_content_between {
    display: flex;
    justify-content: space-between;
    /* align-content: center; */
    align-items: center;
    margin: 0px 0px 0px 0px;
    padding: 0px 0px 0px 0px;
}

.avoid_break {
    page-break-inside: avoid;
}

.pagebreak_after {
    page-break-after: always;
}

.pagebreak_after:not(:last-child) {
    page-break-after: always;
}

.center {
    text-align: center;
}
/* .pagebreak_after {
    page-break-after: always;

}
.pagebreak_after:last-of-type {
    page-break-after: auto;
} */

