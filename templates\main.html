{% load static %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% load mahimsoft_tags %}
<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <title>{% block title %}{{company_name}}{%endblock %}</title>
  <meta name="author" content="Mahim Soft">
  <link rel="canonical" href="{{request.path}}" />
  {% comment %} <link rel="home" href="{% url 'Accounts:index' %}" /> {% endcomment %}
  <meta name="description" content="{% block description %}{% endblock %}">
  <meta name="keywords" content="{% block keywords %}{% endblock %}">

  <link rel="shortcut icon" type="image/x-icon" href="{% get_static_prefix %}images/{{company_logo_fevicon}}">
  <link rel="apple-touch-icon" type="image/jpg" href="{% get_static_prefix %}images/{{company_logo_fevicon}}">
  <style>
    p.lefttext {
      text-orientation: mixed;
      writing-mode: vertical-rl;
      clip-path: polygon(0 0, 100% 10%, 100% 90%, 0 100%);
      background-color: #ED9121;
      padding: 3rem 0rem 3rem 0rem;
      text-align: center;
      margin-left: 0%;
      margin-top: 110px;
      position: absolute;
    }
  </style>
  <!-- ================================
    Start CSS
    ================================= -->
  <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">
  <!-- <link rel="stylesheet" href="https://unpkg.com/swiper@7.0.5/swiper-bundle.min.css"> -->
  <link href="{% static 'css/swiper-bundle.min.css' %}" rel="stylesheet">
  <link href="{% static 'css/glightbox.min.css' %}" rel="stylesheet">
  <link href="{% static 'css/mahimsoft.css' %}" rel="stylesheet">
  {% comment %}
  <link href="{% static 'css/style.css' %}" rel="stylesheet"> {% endcomment %}
  {% comment %} <link href="{% static 'css/aos.css' %}" rel="stylesheet"> {% endcomment %}
    <link href="{% static 'form_css.css' %}" rel="stylesheet">
  {% comment %}
  <link href="{% static 'css/nav_style.css' %}" rel="stylesheet"> {% endcomment %}
  <link href="{% static 'bootstrap-icons/font/bootstrap-icons.css' %}" rel="stylesheet">
  {% block extend_header %}{% endblock extend_header %}
  {% block style %}{% endblock style %}
  <!-- ================================
    End CSS
    ================================= -->

</head>

<body>

  {% include 'navbar_new.html' %}
  {% comment %} {% include 'navbar.html' %} {% endcomment %}
  {% include 'messages.html' %}

  {% if work_phase %}
  <p class="lefttext float-start position-sticky">
    <span id="current_workphase"></span>
  </p>
  {% endif %}
  <div class="container">
    {% block content %}
    {% endblock content %}
  </div>
  <br>
  
  <div class="sticky-bottom">
    {% include 'footer.html' %}
  </div>
  {% block extend_footer %}{%endblock%}
  <!-- ================================
    Start Scripts
    ================================= -->
  <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script> -->
  <script src="{% static 'js/jquery-3.7.1.min.js' %}"></script>
  <!-- {% comment %} <script src="{% static 'js/glightbox.min.js' %}"></script> {% endcomment %} -->
  {% comment %} <!-- <script src="{% static 'js/swiper.min.js' %}"></script> --> {% endcomment %}
  <script src="{% static 'js/bootstrap.min.js' %}"></script>
  <script src="{% static 'js/script.js' %}"></script>
  {% comment %} <!-- <script src="{% static 'js/aos.js' %}"></script> --> {% endcomment %}

  <script>
  {% if work_phase %}
    $.ajax({
      url: '/get_current_workphase/',
      dataType: "json",
      success: function (data) {
        $("#current_workphase").text("Current Work Phase: " + data["current_workphase"]);
      }
    });
    {% endif %}
  </script>
  <!-- {% comment %} <script src="{% static 'js/nav_main.js' %}"></script> {% endcomment %} -->
  {% block script %} {% endblock script %}
  <!-- ================================
    End Scripts
    ================================= -->
</body>

</html>