<!DOCTYPE html>
<html lang="en">
<head>
  <title>Bootstrap Example</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<style>
  .custom-tabs {
    background-color: #f8f9fa;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.custom-tabs .nav-link {
    color: #495057;
    border: none;
    padding: 15px 25px;
    transition: all 0.3s ease;
}

.custom-tabs .nav-link.active {
    color: #fff;
    background-color: #007bff;
    box-shadow: 0 4px 6px rgba(0, 123, 255, 0.3);
}

.custom-tabs .tab-content {
    padding: 30px;
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

</head>
<body>

<div class="container mt-5">
    <h2 class="text-center mb-4">Beautiful Custom Tabs</h2>
    <div class="custom-tabs">
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home" type="button" role="tab" aria-controls="home" aria-selected="true">Home</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab" aria-controls="profile" aria-selected="false">Profile</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab" aria-controls="contact" aria-selected="false">Contact</button>
            </li>
        </ul>
        <div class="tab-content" id="myTabContent">
            <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
                <h3 class="mb-3">Welcome Home</h3>
                <p>This is the home tab content. It's beautifully styled and animated for a great user experience.</p>
            </div>
            <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                <h3 class="mb-3">User Profile</h3>
                <p>Here you can find information about your profile and account settings.</p>
            </div>
            <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">
                <h3 class="mb-3">Contact Us</h3>
                <p>Get in touch with us for any inquiries or support you may need.</p>
            </div>
        </div>
    </div>
</div>

</body>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const tabLinks = document.querySelectorAll('.nav-link');
    const tabContents = document.querySelectorAll('.tab-pane');

    tabLinks.forEach(link => {
        link.addEventListener('click', function () {
            const targetId = this.getAttribute('data-bs-target').substring(1);
            const targetContent = document.getElementById(targetId);

            tabContents.forEach(content => {
                content.classList.remove('fade-in');
            });

            setTimeout(() => {
                targetContent.classList.add('fade-in');
            }, 50);
        });
    });
});
</script>
</html>
