<!DOCTYPE html><html lang="en"><head><script async="" src="https://www.googletagmanager.com/gtag/js?id=UA-140352188-1"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'UA-140352188-1');</script><meta charSet="utf-8"/><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/><meta name="description" content="The CDN for swiper"/><meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1"/><meta name="timestamp" content="2023-10-13T16:57:44.424Z"/><link rel="shortcut icon" href="/favicon.ico"/><title>UNPKG - swiper</title><script>window.Promise || document.write('\x3Cscript src="/es6-promise@4.2.5/dist/es6-promise.min.js">\x3C/script>\x3Cscript>ES6Promise.polyfill()\x3C/script>')</script><script>window.fetch || document.write('\x3Cscript src="/whatwg-fetch@3.0.0/dist/fetch.umd.js">\x3C/script>')</script><script>window.__DATA__ = {"packageName":"swiper","packageVersion":"7.0.5","availableVersions":["0.9.0-beta.12","0.9.0-beta.14","0.9.0-beta.15","0.9.0-beta.16","2.7.0","2.7.5","2.7.6","3.0.0","3.0.1","3.0.2","3.0.3","3.0.4","3.0.5","3.0.6","3.0.7","3.0.8","3.1.0","3.1.2","3.1.5","3.1.7","3.2.0","3.2.5","3.2.6","3.2.7","3.3.0","3.3.1","3.4.0","3.4.1","3.4.2","4.0.0-beta.1","4.0.0-beta.2","4.0.0-beta.3","4.0.0-beta.4","4.0.0","4.0.1","4.0.2","4.0.3","4.0.5","4.0.6","4.0.7","4.1.0","4.1.5","4.1.6","4.2.0","4.2.2","4.2.5","4.2.6","4.3.0","4.3.2","4.3.3","4.3.5","4.4.0","4.4.1","4.4.2","4.4.5","4.4.6","4.5.0","4.5.1","5.0.0","5.0.1","5.0.2","5.0.3","5.0.4","5.1.0","5.2.0","5.2.1","5.3.0","5.3.1","5.3.5","5.3.6","5.3.7","5.3.8","5.4.0","5.4.1","5.4.2","5.4.3","5.4.4","5.4.5","6.0.0-alpha.1","6.0.0-alpha.2","6.0.0-alpha.3","6.0.0-alpha.4","6.0.0-alpha.5","6.0.0-alpha.6","6.0.0-alpha.7","6.0.0-alpha.8","6.0.0-alpha.9","6.0.0-alpha.10","6.0.0-alpha.11","6.0.0-alpha.12","6.0.0-alpha.15","6.0.0-alpha.16","6.0.0-alpha.17","6.0.0-alpha.18","6.0.0","6.0.1","6.0.2","6.0.3","6.0.4","6.1.0","6.1.1","6.1.2","6.1.3","6.2.0","6.3.0","6.3.1","6.3.2","6.3.3","6.3.4","6.3.5","6.4.0","6.4.1","6.4.2","6.4.3","6.4.4","6.4.5","6.4.6","6.4.7","6.4.8","6.4.9","6.4.10","6.4.11","6.4.12","6.4.14","6.4.15","6.5.0","6.5.1","6.5.2","6.5.3","6.5.4","6.5.5-beta.1","6.5.5","6.5.6","6.5.7","6.5.8","6.5.9","6.6.0","6.6.1","6.6.2","6.7.0","6.7.1","6.7.5","6.8.0-beta.1","6.8.0","6.8.1","6.8.2","6.8.3","6.8.4","7.0.0-alpha.1","7.0.0-alpha.2","7.0.0-alpha.3","7.0.0-alpha.4","7.0.0-alpha.5","7.0.0-alpha.6","7.0.0-alpha.7","7.0.0-alpha.8","7.0.0-alpha.9","7.0.0-alpha.10","7.0.0-alpha.11","7.0.0-alpha.12","7.0.0-alpha.14","7.0.0-alpha.15","7.0.0-alpha.16","7.0.0-alpha.17","7.0.0-alpha.18","7.0.0-alpha.19","7.0.0-alpha.20","7.0.0-alpha.21","7.0.0-alpha.22","7.0.0-alpha.23","7.0.0-alpha.24","7.0.0-alpha.25","7.0.0-alpha.26","7.0.0-alpha.27","7.0.0-alpha.28","7.0.0-alpha.29","7.0.0-alpha.30","7.0.0-alpha.31","7.0.0-alpha.32","7.0.0-alpha.33","7.0.0-alpha.34","7.0.0-alpha.35","7.0.0-alpha.36","7.0.0-alpha.37","7.0.0-alpha.38","7.0.0-alpha.39","7.0.0-alpha.40","7.0.0","7.0.1","7.0.2","7.0.3","7.0.4","7.0.5","7.0.6","7.0.7","7.0.8","7.0.9","7.1.0","7.2.0","7.3.0","7.3.1","7.3.2","7.3.3","7.3.4","7.4.0","7.4.1","8.0.0","8.0.1","8.0.2","8.0.3","8.0.4","8.0.5","8.0.6","8.0.7","8.1.0","8.1.1","8.1.2","8.1.3","8.1.4","8.1.5","8.1.6","8.2.0","8.2.1","8.2.2","8.2.3","8.2.4","8.2.5","8.2.6","8.3.0","8.3.1","8.3.2","8.4.0","8.4.1","8.4.2","8.4.3","8.4.4","8.4.5","8.4.6","8.4.7","9.0.0-beta.1","9.0.0-beta.2","9.0.0-beta.3","9.0.0-beta.4","9.0.0-beta.5","9.0.0-beta.6","9.0.0-beta.7","9.0.0-beta.8","9.0.0-beta.9","9.0.0-beta.10","9.0.0-beta.11","9.0.0-beta.16","9.0.0-beta.17","9.0.0-beta.18","9.0.0-beta.19","9.0.0-beta.20","9.0.0-beta.21","9.0.0-beta.22","9.0.0-beta.23","9.0.0-beta.24","9.0.0-beta.25","9.0.0-beta.26","9.0.0-beta.28","9.0.0-beta.29","9.0.0-beta.30","9.0.0-beta.31","9.0.0-beta.32","9.0.0-beta.33","9.0.0-beta.34","9.0.0-beta.35","9.0.0-beta.36","9.0.0-beta.38","9.0.0-beta.40","9.0.0-beta.41","9.0.0-beta.42","9.0.0","9.0.1","9.0.2","9.0.3","9.0.4","9.0.5","9.1.0","9.1.1","9.2.0","9.2.1","9.2.2","9.2.3","9.2.4","9.3.0-beta.1","9.3.0","9.3.1","9.3.2","9.4.0","9.4.1","10.0.0-beta.1","10.0.0-beta.2","10.0.0-beta.3","10.0.0-beta.4","10.0.0-beta.5","10.0.0-beta.6","10.0.0","10.0.1","10.0.2","10.0.3","10.0.4","10.1.0","10.2.0","10.3.0","10.3.1","11.0.0-beta.1"],"filename":"/swiper-bundle.min.css","target":{"path":"/swiper-bundle.min.css","type":"file","details":{"contentType":"text/css","integrity":"sha384-iEvLLFNZq7eolXl+SvclG3QZT7qgghPSSOst5kBshnWNPayaZPfDYsE8JZHGW8lx","language":"CSS","size":15563,"uri":null,"highlights":["<span class=\"code-comment\">/**\n</span>","<span class=\"code-comment\"> * Swiper 7.0.5\n</span>","<span class=\"code-comment\"> * Most modern mobile touch slider and framework with hardware accelerated transitions\n</span>","<span class=\"code-comment\"> * https://swiperjs.com\n</span>","<span class=\"code-comment\"> *\n</span>","<span class=\"code-comment\"> * Copyright 2014-2021 Vladimir Kharlampidi\n</span>","<span class=\"code-comment\"> *\n</span>","<span class=\"code-comment\"> * Released under the MIT License\n</span>","<span class=\"code-comment\"> *\n</span>","<span class=\"code-comment\"> * Released on: September 9, 2021\n</span>","<span class=\"code-comment\"> */</span>\n","\n","@<span class=\"code-keyword\">font-face</span>{<span class=\"code-attribute\">font-family</span>:swiper-icons;<span class=\"code-attribute\">src</span>:<span class=\"code-built_in\">url</span>(<span class=\"code-string\">'data:application/font-woff;charset=utf-8;base64, 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'</span>);<span class=\"code-attribute\">font-weight</span>:<span class=\"code-number\">400</span>;<span class=\"code-attribute\">font-style</span>:normal}<span class=\"hljs-selector-pseudo\">:root</span>{<span class=\"code-attribute\">--swiper-theme-color</span>:<span class=\"code-number\">#007aff</span>}<span class=\"hljs-selector-class\">.swiper</span>{<span class=\"code-attribute\">margin-left</span>:auto;<span class=\"code-attribute\">margin-right</span>:auto;<span class=\"code-attribute\">position</span>:relative;<span class=\"code-attribute\">overflow</span>:hidden;<span class=\"code-attribute\">list-style</span>:none;<span class=\"code-attribute\">padding</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">z-index</span>:<span class=\"code-number\">1</span>}<span class=\"hljs-selector-class\">.swiper-vertical</span>&gt;<span class=\"hljs-selector-class\">.swiper-wrapper</span>{<span class=\"code-attribute\">flex-direction</span>:column}<span class=\"hljs-selector-class\">.swiper-wrapper</span>{<span class=\"code-attribute\">position</span>:relative;<span class=\"code-attribute\">width</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">height</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">z-index</span>:<span class=\"code-number\">1</span>;<span class=\"code-attribute\">display</span>:flex;<span class=\"code-attribute\">transition-property</span>:transform;<span class=\"code-attribute\">box-sizing</span>:content-box}<span class=\"hljs-selector-class\">.swiper-android</span> <span class=\"hljs-selector-class\">.swiper-slide</span>,<span class=\"hljs-selector-class\">.swiper-wrapper</span>{<span class=\"code-attribute\">transform</span>:<span class=\"code-built_in\">translate3d</span>(0px,0,0)}<span class=\"hljs-selector-class\">.swiper-pointer-events</span>{<span class=\"code-attribute\">touch-action</span>:pan-y}<span class=\"hljs-selector-class\">.swiper-pointer-events</span><span class=\"hljs-selector-class\">.swiper-vertical</span>{<span class=\"code-attribute\">touch-action</span>:pan-x}<span class=\"hljs-selector-class\">.swiper-slide</span>{<span class=\"code-attribute\">flex-shrink</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">width</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">height</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">position</span>:relative;<span class=\"code-attribute\">transition-property</span>:transform}<span class=\"hljs-selector-class\">.swiper-slide-invisible-blank</span>{<span class=\"code-attribute\">visibility</span>:hidden}<span class=\"hljs-selector-class\">.swiper-autoheight</span>,<span class=\"hljs-selector-class\">.swiper-autoheight</span> <span class=\"hljs-selector-class\">.swiper-slide</span>{<span class=\"code-attribute\">height</span>:auto}<span class=\"hljs-selector-class\">.swiper-autoheight</span> <span class=\"hljs-selector-class\">.swiper-wrapper</span>{<span class=\"code-attribute\">align-items</span>:flex-start;<span class=\"code-attribute\">transition-property</span>:transform,height}<span class=\"hljs-selector-class\">.swiper-3d</span>,<span class=\"hljs-selector-class\">.swiper-3d</span><span class=\"hljs-selector-class\">.swiper-css-mode</span> <span class=\"hljs-selector-class\">.swiper-wrapper</span>{<span class=\"code-attribute\">perspective</span>:<span class=\"code-number\">1200px</span>}<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-cube-shadow</span>,<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-slide</span>,<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow</span>,<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-bottom</span>,<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-left</span>,<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-right</span>,<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-top</span>,<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-wrapper</span>{<span class=\"code-attribute\">transform-style</span>:preserve-<span class=\"code-number\">3</span>d}<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow</span>,<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-bottom</span>,<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-left</span>,<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-right</span>,<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-top</span>{<span class=\"code-attribute\">position</span>:absolute;<span class=\"code-attribute\">left</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">top</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">width</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">height</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">pointer-events</span>:none;<span class=\"code-attribute\">z-index</span>:<span class=\"code-number\">10</span>}<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow</span>{<span class=\"code-attribute\">background</span>:<span class=\"code-built_in\">rgba</span>(0,0,0,.15)}<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-left</span>{<span class=\"code-attribute\">background-image</span>:<span class=\"code-built_in\">linear-gradient</span>(to left,rgba(0,0,0,.5),<span class=\"code-built_in\">rgba</span>(0,0,0,0))}<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-right</span>{<span class=\"code-attribute\">background-image</span>:<span class=\"code-built_in\">linear-gradient</span>(to right,rgba(0,0,0,.5),<span class=\"code-built_in\">rgba</span>(0,0,0,0))}<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-top</span>{<span class=\"code-attribute\">background-image</span>:<span class=\"code-built_in\">linear-gradient</span>(to top,rgba(0,0,0,.5),<span class=\"code-built_in\">rgba</span>(0,0,0,0))}<span class=\"hljs-selector-class\">.swiper-3d</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-bottom</span>{<span class=\"code-attribute\">background-image</span>:<span class=\"code-built_in\">linear-gradient</span>(to bottom,rgba(0,0,0,.5),<span class=\"code-built_in\">rgba</span>(0,0,0,0))}<span class=\"hljs-selector-class\">.swiper-css-mode</span>&gt;<span class=\"hljs-selector-class\">.swiper-wrapper</span>{<span class=\"code-attribute\">overflow</span>:auto;<span class=\"code-attribute\">scrollbar-width</span>:none;<span class=\"code-attribute\">-ms-overflow-style</span>:none}<span class=\"hljs-selector-class\">.swiper-css-mode</span>&gt;<span class=\"hljs-selector-class\">.swiper-wrapper</span><span class=\"hljs-selector-pseudo\">::-webkit-scrollbar</span>{<span class=\"code-attribute\">display</span>:none}<span class=\"hljs-selector-class\">.swiper-css-mode</span>&gt;<span class=\"hljs-selector-class\">.swiper-wrapper</span>&gt;<span class=\"hljs-selector-class\">.swiper-slide</span>{<span class=\"code-attribute\">scroll-snap-align</span>:start start}<span class=\"hljs-selector-class\">.swiper-horizontal</span><span class=\"hljs-selector-class\">.swiper-css-mode</span>&gt;<span class=\"hljs-selector-class\">.swiper-wrapper</span>{<span class=\"code-attribute\">scroll-snap-type</span>:x mandatory}<span class=\"hljs-selector-class\">.swiper-vertical</span><span class=\"hljs-selector-class\">.swiper-css-mode</span>&gt;<span class=\"hljs-selector-class\">.swiper-wrapper</span>{<span class=\"code-attribute\">scroll-snap-type</span>:y mandatory}<span class=\"hljs-selector-class\">.swiper-centered</span>&gt;<span class=\"hljs-selector-class\">.swiper-wrapper</span><span class=\"hljs-selector-pseudo\">::before</span>{<span class=\"code-attribute\">content</span>:<span class=\"code-string\">''</span>;<span class=\"code-attribute\">flex-shrink</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">order</span>:<span class=\"code-number\">9999</span>}<span class=\"hljs-selector-class\">.swiper-centered</span><span class=\"hljs-selector-class\">.swiper-horizontal</span>&gt;<span class=\"hljs-selector-class\">.swiper-wrapper</span>&gt;<span class=\"hljs-selector-class\">.swiper-slide</span><span class=\"hljs-selector-pseudo\">:first-child</span>{<span class=\"code-attribute\">margin-inline-start</span>:<span class=\"code-built_in\">var</span>(--swiper-centered-offset-before)}<span class=\"hljs-selector-class\">.swiper-centered</span><span class=\"hljs-selector-class\">.swiper-horizontal</span>&gt;<span class=\"hljs-selector-class\">.swiper-wrapper</span><span class=\"hljs-selector-pseudo\">::before</span>{<span class=\"code-attribute\">height</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">min-height</span>:<span class=\"code-number\">1px</span>;<span class=\"code-attribute\">width</span>:<span class=\"code-built_in\">var</span>(--swiper-centered-offset-after)}<span class=\"hljs-selector-class\">.swiper-centered</span><span class=\"hljs-selector-class\">.swiper-vertical</span>&gt;<span class=\"hljs-selector-class\">.swiper-wrapper</span>&gt;<span class=\"hljs-selector-class\">.swiper-slide</span><span class=\"hljs-selector-pseudo\">:first-child</span>{<span class=\"code-attribute\">margin-block-start</span>:<span class=\"code-built_in\">var</span>(--swiper-centered-offset-before)}<span class=\"hljs-selector-class\">.swiper-centered</span><span class=\"hljs-selector-class\">.swiper-vertical</span>&gt;<span class=\"hljs-selector-class\">.swiper-wrapper</span><span class=\"hljs-selector-pseudo\">::before</span>{<span class=\"code-attribute\">width</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">min-width</span>:<span class=\"code-number\">1px</span>;<span class=\"code-attribute\">height</span>:<span class=\"code-built_in\">var</span>(--swiper-centered-offset-after)}<span class=\"hljs-selector-class\">.swiper-centered</span>&gt;<span class=\"hljs-selector-class\">.swiper-wrapper</span>&gt;<span class=\"hljs-selector-class\">.swiper-slide</span>{<span class=\"code-attribute\">scroll-snap-align</span>:center center}<span class=\"hljs-selector-class\">.swiper-virtual</span><span class=\"hljs-selector-class\">.swiper-css-mode</span> <span class=\"hljs-selector-class\">.swiper-wrapper</span><span class=\"hljs-selector-pseudo\">::after</span>{<span class=\"code-attribute\">content</span>:<span class=\"code-string\">''</span>;<span class=\"code-attribute\">position</span>:absolute;<span class=\"code-attribute\">left</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">top</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">pointer-events</span>:none}<span class=\"hljs-selector-class\">.swiper-virtual</span><span class=\"hljs-selector-class\">.swiper-css-mode</span><span class=\"hljs-selector-class\">.swiper-horizontal</span> <span class=\"hljs-selector-class\">.swiper-wrapper</span><span class=\"hljs-selector-pseudo\">::after</span>{<span class=\"code-attribute\">height</span>:<span class=\"code-number\">1px</span>;<span class=\"code-attribute\">width</span>:<span class=\"code-built_in\">var</span>(--swiper-virtual-size)}<span class=\"hljs-selector-class\">.swiper-virtual</span><span class=\"hljs-selector-class\">.swiper-css-mode</span><span class=\"hljs-selector-class\">.swiper-vertical</span> <span class=\"hljs-selector-class\">.swiper-wrapper</span><span class=\"hljs-selector-pseudo\">::after</span>{<span class=\"code-attribute\">width</span>:<span class=\"code-number\">1px</span>;<span class=\"code-attribute\">height</span>:<span class=\"code-built_in\">var</span>(--swiper-virtual-size)}<span class=\"hljs-selector-pseudo\">:root</span>{<span class=\"code-attribute\">--swiper-navigation-size</span>:<span class=\"code-number\">44px</span>}<span class=\"hljs-selector-class\">.swiper-button-next</span>,<span class=\"hljs-selector-class\">.swiper-button-prev</span>{<span class=\"code-attribute\">position</span>:absolute;<span class=\"code-attribute\">top</span>:<span class=\"code-number\">50%</span>;<span class=\"code-attribute\">width</span>:<span class=\"code-built_in\">calc</span>(var(--swiper-navigation-size)/ <span class=\"code-number\">44</span> * <span class=\"code-number\">27</span>);<span class=\"code-attribute\">height</span>:<span class=\"code-built_in\">var</span>(--swiper-navigation-size);<span class=\"code-attribute\">margin-top</span>:<span class=\"code-built_in\">calc</span>(0px - (var(--swiper-navigation-size)/ <span class=\"code-number\">2</span>));<span class=\"code-attribute\">z-index</span>:<span class=\"code-number\">10</span>;<span class=\"code-attribute\">cursor</span>:pointer;<span class=\"code-attribute\">display</span>:flex;<span class=\"code-attribute\">align-items</span>:center;<span class=\"code-attribute\">justify-content</span>:center;<span class=\"code-attribute\">color</span>:<span class=\"code-built_in\">var</span>(--swiper-navigation-color,var(--swiper-theme-color))}<span class=\"hljs-selector-class\">.swiper-button-next</span><span class=\"hljs-selector-class\">.swiper-button-disabled</span>,<span class=\"hljs-selector-class\">.swiper-button-prev</span><span class=\"hljs-selector-class\">.swiper-button-disabled</span>{<span class=\"code-attribute\">opacity</span>:.<span class=\"code-number\">35</span>;<span class=\"code-attribute\">cursor</span>:auto;<span class=\"code-attribute\">pointer-events</span>:none}<span class=\"hljs-selector-class\">.swiper-button-next</span><span class=\"hljs-selector-pseudo\">:after</span>,<span class=\"hljs-selector-class\">.swiper-button-prev</span><span class=\"hljs-selector-pseudo\">:after</span>{<span class=\"code-attribute\">font-family</span>:swiper-icons;<span class=\"code-attribute\">font-size</span>:<span class=\"code-built_in\">var</span>(--swiper-navigation-size);<span class=\"code-attribute\">text-transform</span>:none<span class=\"code-meta\">!important</span>;<span class=\"code-attribute\">letter-spacing</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">text-transform</span>:none;<span class=\"code-attribute\">font-variant</span>:initial;<span class=\"code-attribute\">line-height</span>:<span class=\"code-number\">1</span>}<span class=\"hljs-selector-class\">.swiper-button-prev</span>,<span class=\"hljs-selector-class\">.swiper-rtl</span> <span class=\"hljs-selector-class\">.swiper-button-next</span>{<span class=\"code-attribute\">left</span>:<span class=\"code-number\">10px</span>;<span class=\"code-attribute\">right</span>:auto}<span class=\"hljs-selector-class\">.swiper-button-prev</span><span class=\"hljs-selector-pseudo\">:after</span>,<span class=\"hljs-selector-class\">.swiper-rtl</span> <span class=\"hljs-selector-class\">.swiper-button-next</span><span class=\"hljs-selector-pseudo\">:after</span>{<span class=\"code-attribute\">content</span>:<span class=\"code-string\">'prev'</span>}<span class=\"hljs-selector-class\">.swiper-button-next</span>,<span class=\"hljs-selector-class\">.swiper-rtl</span> <span class=\"hljs-selector-class\">.swiper-button-prev</span>{<span class=\"code-attribute\">right</span>:<span class=\"code-number\">10px</span>;<span class=\"code-attribute\">left</span>:auto}<span class=\"hljs-selector-class\">.swiper-button-next</span><span class=\"hljs-selector-pseudo\">:after</span>,<span class=\"hljs-selector-class\">.swiper-rtl</span> <span class=\"hljs-selector-class\">.swiper-button-prev</span><span class=\"hljs-selector-pseudo\">:after</span>{<span class=\"code-attribute\">content</span>:<span class=\"code-string\">'next'</span>}<span class=\"hljs-selector-class\">.swiper-button-lock</span>{<span class=\"code-attribute\">display</span>:none}<span class=\"hljs-selector-class\">.swiper-pagination</span>{<span class=\"code-attribute\">position</span>:absolute;<span class=\"code-attribute\">text-align</span>:center;<span class=\"code-attribute\">transition</span>:.<span class=\"code-number\">3s</span> opacity;<span class=\"code-attribute\">transform</span>:<span class=\"code-built_in\">translate3d</span>(0,0,0);<span class=\"code-attribute\">z-index</span>:<span class=\"code-number\">10</span>}<span class=\"hljs-selector-class\">.swiper-pagination</span><span class=\"hljs-selector-class\">.swiper-pagination-hidden</span>{<span class=\"code-attribute\">opacity</span>:<span class=\"code-number\">0</span>}<span class=\"hljs-selector-class\">.swiper-horizontal</span>&gt;<span class=\"hljs-selector-class\">.swiper-pagination-bullets</span>,<span class=\"hljs-selector-class\">.swiper-pagination-bullets</span><span class=\"hljs-selector-class\">.swiper-pagination-horizontal</span>,<span class=\"hljs-selector-class\">.swiper-pagination-custom</span>,<span class=\"hljs-selector-class\">.swiper-pagination-fraction</span>{<span class=\"code-attribute\">bottom</span>:<span class=\"code-number\">10px</span>;<span class=\"code-attribute\">left</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">width</span>:<span class=\"code-number\">100%</span>}<span class=\"hljs-selector-class\">.swiper-pagination-bullets-dynamic</span>{<span class=\"code-attribute\">overflow</span>:hidden;<span class=\"code-attribute\">font-size</span>:<span class=\"code-number\">0</span>}<span class=\"hljs-selector-class\">.swiper-pagination-bullets-dynamic</span> <span class=\"hljs-selector-class\">.swiper-pagination-bullet</span>{<span class=\"code-attribute\">transform</span>:<span class=\"code-built_in\">scale</span>(.33);<span class=\"code-attribute\">position</span>:relative}<span class=\"hljs-selector-class\">.swiper-pagination-bullets-dynamic</span> <span class=\"hljs-selector-class\">.swiper-pagination-bullet-active</span>{<span class=\"code-attribute\">transform</span>:<span class=\"code-built_in\">scale</span>(1)}<span class=\"hljs-selector-class\">.swiper-pagination-bullets-dynamic</span> <span class=\"hljs-selector-class\">.swiper-pagination-bullet-active-main</span>{<span class=\"code-attribute\">transform</span>:<span class=\"code-built_in\">scale</span>(1)}<span class=\"hljs-selector-class\">.swiper-pagination-bullets-dynamic</span> <span class=\"hljs-selector-class\">.swiper-pagination-bullet-active-prev</span>{<span class=\"code-attribute\">transform</span>:<span class=\"code-built_in\">scale</span>(.66)}<span class=\"hljs-selector-class\">.swiper-pagination-bullets-dynamic</span> <span class=\"hljs-selector-class\">.swiper-pagination-bullet-active-prev-prev</span>{<span class=\"code-attribute\">transform</span>:<span class=\"code-built_in\">scale</span>(.33)}<span class=\"hljs-selector-class\">.swiper-pagination-bullets-dynamic</span> <span class=\"hljs-selector-class\">.swiper-pagination-bullet-active-next</span>{<span class=\"code-attribute\">transform</span>:<span class=\"code-built_in\">scale</span>(.66)}<span class=\"hljs-selector-class\">.swiper-pagination-bullets-dynamic</span> <span class=\"hljs-selector-class\">.swiper-pagination-bullet-active-next-next</span>{<span class=\"code-attribute\">transform</span>:<span class=\"code-built_in\">scale</span>(.33)}<span class=\"hljs-selector-class\">.swiper-pagination-bullet</span>{<span class=\"code-attribute\">width</span>:<span class=\"code-built_in\">var</span>(--swiper-pagination-bullet-width,var(--swiper-pagination-bullet-size,8px));<span class=\"code-attribute\">height</span>:<span class=\"code-built_in\">var</span>(--swiper-pagination-bullet-height,var(--swiper-pagination-bullet-size,8px));<span class=\"code-attribute\">display</span>:inline-block;<span class=\"code-attribute\">border-radius</span>:<span class=\"code-number\">50%</span>;<span class=\"code-attribute\">background</span>:<span class=\"code-built_in\">var</span>(--swiper-pagination-bullet-inactive-color,#000);<span class=\"code-attribute\">opacity</span>:<span class=\"code-built_in\">var</span>(--swiper-pagination-bullet-inactive-opacity, .2)}<span class=\"hljs-selector-tag\">button</span><span class=\"hljs-selector-class\">.swiper-pagination-bullet</span>{<span class=\"code-attribute\">border</span>:none;<span class=\"code-attribute\">margin</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">padding</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">box-shadow</span>:none;<span class=\"code-attribute\">-webkit-appearance</span>:none;<span class=\"code-attribute\">appearance</span>:none}<span class=\"hljs-selector-class\">.swiper-pagination-clickable</span> <span class=\"hljs-selector-class\">.swiper-pagination-bullet</span>{<span class=\"code-attribute\">cursor</span>:pointer}<span class=\"hljs-selector-class\">.swiper-pagination-bullet</span><span class=\"hljs-selector-pseudo\">:only-child</span>{<span class=\"code-attribute\">display</span>:none<span class=\"code-meta\">!important</span>}<span class=\"hljs-selector-class\">.swiper-pagination-bullet-active</span>{<span class=\"code-attribute\">opacity</span>:<span class=\"code-built_in\">var</span>(--swiper-pagination-bullet-opacity, 1);<span class=\"code-attribute\">background</span>:<span class=\"code-built_in\">var</span>(--swiper-pagination-color,var(--swiper-theme-color))}<span class=\"hljs-selector-class\">.swiper-pagination-vertical</span><span class=\"hljs-selector-class\">.swiper-pagination-bullets</span>,<span class=\"hljs-selector-class\">.swiper-vertical</span>&gt;<span class=\"hljs-selector-class\">.swiper-pagination-bullets</span>{<span class=\"code-attribute\">right</span>:<span class=\"code-number\">10px</span>;<span class=\"code-attribute\">top</span>:<span class=\"code-number\">50%</span>;<span class=\"code-attribute\">transform</span>:<span class=\"code-built_in\">translate3d</span>(0px,-50%,0)}<span class=\"hljs-selector-class\">.swiper-pagination-vertical</span><span class=\"hljs-selector-class\">.swiper-pagination-bullets</span> <span class=\"hljs-selector-class\">.swiper-pagination-bullet</span>,<span class=\"hljs-selector-class\">.swiper-vertical</span>&gt;<span class=\"hljs-selector-class\">.swiper-pagination-bullets</span> <span class=\"hljs-selector-class\">.swiper-pagination-bullet</span>{<span class=\"code-attribute\">margin</span>:<span class=\"code-built_in\">var</span>(--swiper-pagination-bullet-vertical-gap,6px) <span class=\"code-number\">0</span>;<span class=\"code-attribute\">display</span>:block}<span class=\"hljs-selector-class\">.swiper-pagination-vertical</span><span class=\"hljs-selector-class\">.swiper-pagination-bullets</span><span class=\"hljs-selector-class\">.swiper-pagination-bullets-dynamic</span>,<span class=\"hljs-selector-class\">.swiper-vertical</span>&gt;<span class=\"hljs-selector-class\">.swiper-pagination-bullets</span><span class=\"hljs-selector-class\">.swiper-pagination-bullets-dynamic</span>{<span class=\"code-attribute\">top</span>:<span class=\"code-number\">50%</span>;<span class=\"code-attribute\">transform</span>:<span class=\"code-built_in\">translateY</span>(-50%);<span class=\"code-attribute\">width</span>:<span class=\"code-number\">8px</span>}<span class=\"hljs-selector-class\">.swiper-pagination-vertical</span><span class=\"hljs-selector-class\">.swiper-pagination-bullets</span><span class=\"hljs-selector-class\">.swiper-pagination-bullets-dynamic</span> <span class=\"hljs-selector-class\">.swiper-pagination-bullet</span>,<span class=\"hljs-selector-class\">.swiper-vertical</span>&gt;<span class=\"hljs-selector-class\">.swiper-pagination-bullets</span><span class=\"hljs-selector-class\">.swiper-pagination-bullets-dynamic</span> <span class=\"hljs-selector-class\">.swiper-pagination-bullet</span>{<span class=\"code-attribute\">display</span>:inline-block;<span class=\"code-attribute\">transition</span>:.<span class=\"code-number\">2s</span> transform,.<span class=\"code-number\">2s</span> top}<span class=\"hljs-selector-class\">.swiper-horizontal</span>&gt;<span class=\"hljs-selector-class\">.swiper-pagination-bullets</span> <span class=\"hljs-selector-class\">.swiper-pagination-bullet</span>,<span class=\"hljs-selector-class\">.swiper-pagination-horizontal</span><span class=\"hljs-selector-class\">.swiper-pagination-bullets</span> <span class=\"hljs-selector-class\">.swiper-pagination-bullet</span>{<span class=\"code-attribute\">margin</span>:<span class=\"code-number\">0</span> <span class=\"code-built_in\">var</span>(--swiper-pagination-bullet-horizontal-gap,4px)}<span class=\"hljs-selector-class\">.swiper-horizontal</span>&gt;<span class=\"hljs-selector-class\">.swiper-pagination-bullets</span><span class=\"hljs-selector-class\">.swiper-pagination-bullets-dynamic</span>,<span class=\"hljs-selector-class\">.swiper-pagination-horizontal</span><span class=\"hljs-selector-class\">.swiper-pagination-bullets</span><span class=\"hljs-selector-class\">.swiper-pagination-bullets-dynamic</span>{<span class=\"code-attribute\">left</span>:<span class=\"code-number\">50%</span>;<span class=\"code-attribute\">transform</span>:<span class=\"code-built_in\">translateX</span>(-50%);<span class=\"code-attribute\">white-space</span>:nowrap}<span class=\"hljs-selector-class\">.swiper-horizontal</span>&gt;<span class=\"hljs-selector-class\">.swiper-pagination-bullets</span><span class=\"hljs-selector-class\">.swiper-pagination-bullets-dynamic</span> <span class=\"hljs-selector-class\">.swiper-pagination-bullet</span>,<span class=\"hljs-selector-class\">.swiper-pagination-horizontal</span><span class=\"hljs-selector-class\">.swiper-pagination-bullets</span><span class=\"hljs-selector-class\">.swiper-pagination-bullets-dynamic</span> <span class=\"hljs-selector-class\">.swiper-pagination-bullet</span>{<span class=\"code-attribute\">transition</span>:.<span class=\"code-number\">2s</span> transform,.<span class=\"code-number\">2s</span> left}<span class=\"hljs-selector-class\">.swiper-horizontal</span><span class=\"hljs-selector-class\">.swiper-rtl</span>&gt;<span class=\"hljs-selector-class\">.swiper-pagination-bullets-dynamic</span> <span class=\"hljs-selector-class\">.swiper-pagination-bullet</span>{<span class=\"code-attribute\">transition</span>:.<span class=\"code-number\">2s</span> transform,.<span class=\"code-number\">2s</span> right}<span class=\"hljs-selector-class\">.swiper-pagination-progressbar</span>{<span class=\"code-attribute\">background</span>:<span class=\"code-built_in\">rgba</span>(0,0,0,.25);<span class=\"code-attribute\">position</span>:absolute}<span class=\"hljs-selector-class\">.swiper-pagination-progressbar</span> <span class=\"hljs-selector-class\">.swiper-pagination-progressbar-fill</span>{<span class=\"code-attribute\">background</span>:<span class=\"code-built_in\">var</span>(--swiper-pagination-color,var(--swiper-theme-color));<span class=\"code-attribute\">position</span>:absolute;<span class=\"code-attribute\">left</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">top</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">width</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">height</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">transform</span>:<span class=\"code-built_in\">scale</span>(0);<span class=\"code-attribute\">transform-origin</span>:left top}<span class=\"hljs-selector-class\">.swiper-rtl</span> <span class=\"hljs-selector-class\">.swiper-pagination-progressbar</span> <span class=\"hljs-selector-class\">.swiper-pagination-progressbar-fill</span>{<span class=\"code-attribute\">transform-origin</span>:right top}<span class=\"hljs-selector-class\">.swiper-horizontal</span>&gt;<span class=\"hljs-selector-class\">.swiper-pagination-progressbar</span>,<span class=\"hljs-selector-class\">.swiper-pagination-progressbar</span><span class=\"hljs-selector-class\">.swiper-pagination-horizontal</span>,<span class=\"hljs-selector-class\">.swiper-pagination-progressbar</span><span class=\"hljs-selector-class\">.swiper-pagination-vertical</span><span class=\"hljs-selector-class\">.swiper-pagination-progressbar-opposite</span>,<span class=\"hljs-selector-class\">.swiper-vertical</span>&gt;<span class=\"hljs-selector-class\">.swiper-pagination-progressbar</span><span class=\"hljs-selector-class\">.swiper-pagination-progressbar-opposite</span>{<span class=\"code-attribute\">width</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">height</span>:<span class=\"code-number\">4px</span>;<span class=\"code-attribute\">left</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">top</span>:<span class=\"code-number\">0</span>}<span class=\"hljs-selector-class\">.swiper-horizontal</span>&gt;<span class=\"hljs-selector-class\">.swiper-pagination-progressbar</span><span class=\"hljs-selector-class\">.swiper-pagination-progressbar-opposite</span>,<span class=\"hljs-selector-class\">.swiper-pagination-progressbar</span><span class=\"hljs-selector-class\">.swiper-pagination-horizontal</span><span class=\"hljs-selector-class\">.swiper-pagination-progressbar-opposite</span>,<span class=\"hljs-selector-class\">.swiper-pagination-progressbar</span><span class=\"hljs-selector-class\">.swiper-pagination-vertical</span>,<span class=\"hljs-selector-class\">.swiper-vertical</span>&gt;<span class=\"hljs-selector-class\">.swiper-pagination-progressbar</span>{<span class=\"code-attribute\">width</span>:<span class=\"code-number\">4px</span>;<span class=\"code-attribute\">height</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">left</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">top</span>:<span class=\"code-number\">0</span>}<span class=\"hljs-selector-class\">.swiper-pagination-lock</span>{<span class=\"code-attribute\">display</span>:none}<span class=\"hljs-selector-class\">.swiper-scrollbar</span>{<span class=\"code-attribute\">border-radius</span>:<span class=\"code-number\">10px</span>;<span class=\"code-attribute\">position</span>:relative;<span class=\"code-attribute\">-ms-touch-action</span>:none;<span class=\"code-attribute\">background</span>:<span class=\"code-built_in\">rgba</span>(0,0,0,.1)}<span class=\"hljs-selector-class\">.swiper-horizontal</span>&gt;<span class=\"hljs-selector-class\">.swiper-scrollbar</span>{<span class=\"code-attribute\">position</span>:absolute;<span class=\"code-attribute\">left</span>:<span class=\"code-number\">1%</span>;<span class=\"code-attribute\">bottom</span>:<span class=\"code-number\">3px</span>;<span class=\"code-attribute\">z-index</span>:<span class=\"code-number\">50</span>;<span class=\"code-attribute\">height</span>:<span class=\"code-number\">5px</span>;<span class=\"code-attribute\">width</span>:<span class=\"code-number\">98%</span>}<span class=\"hljs-selector-class\">.swiper-vertical</span>&gt;<span class=\"hljs-selector-class\">.swiper-scrollbar</span>{<span class=\"code-attribute\">position</span>:absolute;<span class=\"code-attribute\">right</span>:<span class=\"code-number\">3px</span>;<span class=\"code-attribute\">top</span>:<span class=\"code-number\">1%</span>;<span class=\"code-attribute\">z-index</span>:<span class=\"code-number\">50</span>;<span class=\"code-attribute\">width</span>:<span class=\"code-number\">5px</span>;<span class=\"code-attribute\">height</span>:<span class=\"code-number\">98%</span>}<span class=\"hljs-selector-class\">.swiper-scrollbar-drag</span>{<span class=\"code-attribute\">height</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">width</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">position</span>:relative;<span class=\"code-attribute\">background</span>:<span class=\"code-built_in\">rgba</span>(0,0,0,.5);<span class=\"code-attribute\">border-radius</span>:<span class=\"code-number\">10px</span>;<span class=\"code-attribute\">left</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">top</span>:<span class=\"code-number\">0</span>}<span class=\"hljs-selector-class\">.swiper-scrollbar-cursor-drag</span>{<span class=\"code-attribute\">cursor</span>:move}<span class=\"hljs-selector-class\">.swiper-scrollbar-lock</span>{<span class=\"code-attribute\">display</span>:none}<span class=\"hljs-selector-class\">.swiper-zoom-container</span>{<span class=\"code-attribute\">width</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">height</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">display</span>:flex;<span class=\"code-attribute\">justify-content</span>:center;<span class=\"code-attribute\">align-items</span>:center;<span class=\"code-attribute\">text-align</span>:center}<span class=\"hljs-selector-class\">.swiper-zoom-container</span>&gt;<span class=\"hljs-selector-tag\">canvas</span>,<span class=\"hljs-selector-class\">.swiper-zoom-container</span>&gt;<span class=\"hljs-selector-tag\">img</span>,<span class=\"hljs-selector-class\">.swiper-zoom-container</span>&gt;<span class=\"hljs-selector-tag\">svg</span>{<span class=\"code-attribute\">max-width</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">max-height</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">object-fit</span>:contain}<span class=\"hljs-selector-class\">.swiper-slide-zoomed</span>{<span class=\"code-attribute\">cursor</span>:move}<span class=\"hljs-selector-class\">.swiper-lazy-preloader</span>{<span class=\"code-attribute\">width</span>:<span class=\"code-number\">42px</span>;<span class=\"code-attribute\">height</span>:<span class=\"code-number\">42px</span>;<span class=\"code-attribute\">position</span>:absolute;<span class=\"code-attribute\">left</span>:<span class=\"code-number\">50%</span>;<span class=\"code-attribute\">top</span>:<span class=\"code-number\">50%</span>;<span class=\"code-attribute\">margin-left</span>:-<span class=\"code-number\">21px</span>;<span class=\"code-attribute\">margin-top</span>:-<span class=\"code-number\">21px</span>;<span class=\"code-attribute\">z-index</span>:<span class=\"code-number\">10</span>;<span class=\"code-attribute\">transform-origin</span>:<span class=\"code-number\">50%</span>;<span class=\"code-attribute\">animation</span>:swiper-preloader-spin <span class=\"code-number\">1s</span> infinite linear;<span class=\"code-attribute\">box-sizing</span>:border-box;<span class=\"code-attribute\">border</span>:<span class=\"code-number\">4px</span> solid <span class=\"code-built_in\">var</span>(--swiper-preloader-color,var(--swiper-theme-color));<span class=\"code-attribute\">border-radius</span>:<span class=\"code-number\">50%</span>;<span class=\"code-attribute\">border-top-color</span>:transparent}<span class=\"hljs-selector-class\">.swiper-lazy-preloader-white</span>{<span class=\"code-attribute\">--swiper-preloader-color</span>:<span class=\"code-number\">#fff</span>}<span class=\"hljs-selector-class\">.swiper-lazy-preloader-black</span>{<span class=\"code-attribute\">--swiper-preloader-color</span>:<span class=\"code-number\">#000</span>}@<span class=\"code-keyword\">keyframes</span> swiper-preloader-spin{100%{<span class=\"code-attribute\">transform</span>:<span class=\"code-built_in\">rotate</span>(360deg)}}<span class=\"hljs-selector-class\">.swiper</span> <span class=\"hljs-selector-class\">.swiper-notification</span>{<span class=\"code-attribute\">position</span>:absolute;<span class=\"code-attribute\">left</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">top</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">pointer-events</span>:none;<span class=\"code-attribute\">opacity</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">z-index</span>:-<span class=\"code-number\">1000</span>}<span class=\"hljs-selector-class\">.swiper-free-mode</span>&gt;<span class=\"hljs-selector-class\">.swiper-wrapper</span>{<span class=\"code-attribute\">transition-timing-function</span>:ease-out;<span class=\"code-attribute\">margin</span>:<span class=\"code-number\">0</span> auto}<span class=\"hljs-selector-class\">.swiper-grid</span>&gt;<span class=\"hljs-selector-class\">.swiper-wrapper</span>{<span class=\"code-attribute\">flex-wrap</span>:wrap}<span class=\"hljs-selector-class\">.swiper-grid-column</span>&gt;<span class=\"hljs-selector-class\">.swiper-wrapper</span>{<span class=\"code-attribute\">flex-wrap</span>:wrap;<span class=\"code-attribute\">flex-direction</span>:column}<span class=\"hljs-selector-class\">.swiper-fade</span><span class=\"hljs-selector-class\">.swiper-free-mode</span> <span class=\"hljs-selector-class\">.swiper-slide</span>{<span class=\"code-attribute\">transition-timing-function</span>:ease-out}<span class=\"hljs-selector-class\">.swiper-fade</span> <span class=\"hljs-selector-class\">.swiper-slide</span>{<span class=\"code-attribute\">pointer-events</span>:none;<span class=\"code-attribute\">transition-property</span>:opacity}<span class=\"hljs-selector-class\">.swiper-fade</span> <span class=\"hljs-selector-class\">.swiper-slide</span> <span class=\"hljs-selector-class\">.swiper-slide</span>{<span class=\"code-attribute\">pointer-events</span>:none}<span class=\"hljs-selector-class\">.swiper-fade</span> <span class=\"hljs-selector-class\">.swiper-slide-active</span>,<span class=\"hljs-selector-class\">.swiper-fade</span> <span class=\"hljs-selector-class\">.swiper-slide-active</span> <span class=\"hljs-selector-class\">.swiper-slide-active</span>{<span class=\"code-attribute\">pointer-events</span>:auto}<span class=\"hljs-selector-class\">.swiper-cube</span>{<span class=\"code-attribute\">overflow</span>:visible}<span class=\"hljs-selector-class\">.swiper-cube</span> <span class=\"hljs-selector-class\">.swiper-slide</span>{<span class=\"code-attribute\">pointer-events</span>:none;<span class=\"code-attribute\">-webkit-backface-visibility</span>:hidden;<span class=\"code-attribute\">backface-visibility</span>:hidden;<span class=\"code-attribute\">z-index</span>:<span class=\"code-number\">1</span>;<span class=\"code-attribute\">visibility</span>:hidden;<span class=\"code-attribute\">transform-origin</span>:<span class=\"code-number\">0</span> <span class=\"code-number\">0</span>;<span class=\"code-attribute\">width</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">height</span>:<span class=\"code-number\">100%</span>}<span class=\"hljs-selector-class\">.swiper-cube</span> <span class=\"hljs-selector-class\">.swiper-slide</span> <span class=\"hljs-selector-class\">.swiper-slide</span>{<span class=\"code-attribute\">pointer-events</span>:none}<span class=\"hljs-selector-class\">.swiper-cube</span><span class=\"hljs-selector-class\">.swiper-rtl</span> <span class=\"hljs-selector-class\">.swiper-slide</span>{<span class=\"code-attribute\">transform-origin</span>:<span class=\"code-number\">100%</span> <span class=\"code-number\">0</span>}<span class=\"hljs-selector-class\">.swiper-cube</span> <span class=\"hljs-selector-class\">.swiper-slide-active</span>,<span class=\"hljs-selector-class\">.swiper-cube</span> <span class=\"hljs-selector-class\">.swiper-slide-active</span> <span class=\"hljs-selector-class\">.swiper-slide-active</span>{<span class=\"code-attribute\">pointer-events</span>:auto}<span class=\"hljs-selector-class\">.swiper-cube</span> <span class=\"hljs-selector-class\">.swiper-slide-active</span>,<span class=\"hljs-selector-class\">.swiper-cube</span> <span class=\"hljs-selector-class\">.swiper-slide-next</span>,<span class=\"hljs-selector-class\">.swiper-cube</span> <span class=\"hljs-selector-class\">.swiper-slide-next</span>+<span class=\"hljs-selector-class\">.swiper-slide</span>,<span class=\"hljs-selector-class\">.swiper-cube</span> <span class=\"hljs-selector-class\">.swiper-slide-prev</span>{<span class=\"code-attribute\">pointer-events</span>:auto;<span class=\"code-attribute\">visibility</span>:visible}<span class=\"hljs-selector-class\">.swiper-cube</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-bottom</span>,<span class=\"hljs-selector-class\">.swiper-cube</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-left</span>,<span class=\"hljs-selector-class\">.swiper-cube</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-right</span>,<span class=\"hljs-selector-class\">.swiper-cube</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-top</span>{<span class=\"code-attribute\">z-index</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">-webkit-backface-visibility</span>:hidden;<span class=\"code-attribute\">backface-visibility</span>:hidden}<span class=\"hljs-selector-class\">.swiper-cube</span> <span class=\"hljs-selector-class\">.swiper-cube-shadow</span>{<span class=\"code-attribute\">position</span>:absolute;<span class=\"code-attribute\">left</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">bottom</span>:<span class=\"code-number\">0px</span>;<span class=\"code-attribute\">width</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">height</span>:<span class=\"code-number\">100%</span>;<span class=\"code-attribute\">opacity</span>:.<span class=\"code-number\">6</span>;<span class=\"code-attribute\">z-index</span>:<span class=\"code-number\">0</span>}<span class=\"hljs-selector-class\">.swiper-cube</span> <span class=\"hljs-selector-class\">.swiper-cube-shadow</span><span class=\"hljs-selector-pseudo\">:before</span>{<span class=\"code-attribute\">content</span>:<span class=\"code-string\">''</span>;<span class=\"code-attribute\">background</span>:<span class=\"code-number\">#000</span>;<span class=\"code-attribute\">position</span>:absolute;<span class=\"code-attribute\">left</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">top</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">bottom</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">right</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">filter</span>:<span class=\"code-built_in\">blur</span>(50px)}<span class=\"hljs-selector-class\">.swiper-flip</span>{<span class=\"code-attribute\">overflow</span>:visible}<span class=\"hljs-selector-class\">.swiper-flip</span> <span class=\"hljs-selector-class\">.swiper-slide</span>{<span class=\"code-attribute\">pointer-events</span>:none;<span class=\"code-attribute\">-webkit-backface-visibility</span>:hidden;<span class=\"code-attribute\">backface-visibility</span>:hidden;<span class=\"code-attribute\">z-index</span>:<span class=\"code-number\">1</span>}<span class=\"hljs-selector-class\">.swiper-flip</span> <span class=\"hljs-selector-class\">.swiper-slide</span> <span class=\"hljs-selector-class\">.swiper-slide</span>{<span class=\"code-attribute\">pointer-events</span>:none}<span class=\"hljs-selector-class\">.swiper-flip</span> <span class=\"hljs-selector-class\">.swiper-slide-active</span>,<span class=\"hljs-selector-class\">.swiper-flip</span> <span class=\"hljs-selector-class\">.swiper-slide-active</span> <span class=\"hljs-selector-class\">.swiper-slide-active</span>{<span class=\"code-attribute\">pointer-events</span>:auto}<span class=\"hljs-selector-class\">.swiper-flip</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-bottom</span>,<span class=\"hljs-selector-class\">.swiper-flip</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-left</span>,<span class=\"hljs-selector-class\">.swiper-flip</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-right</span>,<span class=\"hljs-selector-class\">.swiper-flip</span> <span class=\"hljs-selector-class\">.swiper-slide-shadow-top</span>{<span class=\"code-attribute\">z-index</span>:<span class=\"code-number\">0</span>;<span class=\"code-attribute\">-webkit-backface-visibility</span>:hidden;<span class=\"code-attribute\">backface-visibility</span>:hidden}<span class=\"hljs-selector-class\">.swiper-creative</span> <span class=\"hljs-selector-class\">.swiper-slide</span>{<span class=\"code-attribute\">-webkit-backface-visibility</span>:hidden;<span class=\"code-attribute\">backface-visibility</span>:hidden;<span class=\"code-attribute\">overflow</span>:hidden;<span class=\"code-attribute\">transition-property</span>:transform,opacity,height}<span class=\"hljs-selector-class\">.swiper-cards</span>{<span class=\"code-attribute\">overflow</span>:visible}<span class=\"hljs-selector-class\">.swiper-cards</span> <span class=\"hljs-selector-class\">.swiper-slide</span>{<span class=\"code-attribute\">transform-origin</span>:center bottom;<span class=\"code-attribute\">-webkit-backface-visibility</span>:hidden;<span class=\"code-attribute\">backface-visibility</span>:hidden;<span class=\"code-attribute\">overflow</span>:hidden}"]}}}</script></head><body><div id="root"><style data-emotion-css="gtfibm">html{box-sizing:border-box;}*,*:before,*:after{box-sizing:inherit;}html,body,#root{height:100%;margin:0;}body{font-family:-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;font-size:16px;line-height:1.5;overflow-wrap:break-word;background:white;color:black;}code{font-family:Menlo, Monaco, Lucida Console, Liberation Mono, DejaVu Sans Mono, Bitstream Vera Sans Mono, Courier New, monospace;}th,td{padding:0;}select{font-size:inherit;}#root{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;}</style><style data-emotion-css="1r6h1r6">.code-listing{background:#fbfdff;color:#383a42;}.code-comment,.code-quote{color:#a0a1a7;font-style:italic;}.code-doctag,.code-keyword,.code-link,.code-formula{color:#a626a4;}.code-section,.code-name,.code-selector-tag,.code-deletion,.code-subst{color:#e45649;}.code-literal{color:#0184bb;}.code-string,.code-regexp,.code-addition,.code-attribute,.code-meta-string{color:#50a14f;}.code-built_in,.code-class .code-title{color:#c18401;}.code-attr,.code-variable,.code-template-variable,.code-type,.code-selector-class,.code-selector-attr,.code-selector-pseudo,.code-number{color:#986801;}.code-symbol,.code-bullet,.code-meta,.code-selector-id,.code-title{color:#4078f2;}.code-emphasis{font-style:italic;}.code-strong{font-weight:bold;}</style><style data-emotion-css="1c3h18e">.css-1c3h18e{-webkit-flex:1 0 auto;-ms-flex:1 0 auto;flex:1 0 auto;}</style><div class="css-1c3h18e"><style data-emotion-css="1cfuj1t">.css-1cfuj1t{max-width:940px;padding:0 20px;margin:0 auto;}</style><div class="css-1cfuj1t"><style data-emotion-css="i51og3">.css-i51og3{margin-top:2rem;}</style><header class="css-i51og3"><style data-emotion-css="1y7u1xh">.css-1y7u1xh{text-align:center;font-size:3rem;-webkit-letter-spacing:0.05em;-moz-letter-spacing:0.05em;-ms-letter-spacing:0.05em;letter-spacing:0.05em;}</style><h1 class="css-1y7u1xh"><style data-emotion-css="1ydg16i">.css-1ydg16i{color:#000;-webkit-text-decoration:none;text-decoration:none;}</style><a href="/" class="css-1ydg16i">UNPKG</a></h1></header></div><div class="css-1cfuj1t"><style data-emotion-css="93o42g">.css-93o42g{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}@media (max-width:700px){.css-93o42g{-webkit-flex-direction:column-reverse;-ms-flex-direction:column-reverse;flex-direction:column-reverse;-webkit-align-items:flex-start;-webkit-box-align:flex-start;-ms-flex-align:flex-start;align-items:flex-start;}}</style><header class="css-93o42g"><style data-emotion-css="1dlpvgi">.css-1dlpvgi{font-size:1.5rem;font-weight:normal;-webkit-flex:1;-ms-flex:1;flex:1;word-break:break-all;}</style><h1 class="css-1dlpvgi"><nav><style data-emotion-css="xt128v">.css-xt128v{color:#0076ff;-webkit-text-decoration:none;text-decoration:none;}.css-xt128v:hover{-webkit-text-decoration:underline;text-decoration:underline;}</style><a href="/browse/swiper@7.0.5/" class="css-xt128v">swiper</a><style data-emotion-css="lllnmq">.css-lllnmq{padding-left:5px;padding-right:5px;}</style><span class="css-lllnmq">/</span><strong>swiper-bundle.min.css</strong></nav></h1><style data-emotion-css="1nr3dab">.css-1nr3dab{margin-left:20px;}@media (max-width:700px){.css-1nr3dab{margin-left:0;margin-bottom:0;}}</style><p class="css-1nr3dab"><label>Version:<!-- --> <style data-emotion-css="un3bt6">.css-un3bt6{-webkit-appearance:none;-moz-appearance:none;appearance:none;cursor:pointer;padding:4px 24px 4px 8px;font-weight:600;font-size:0.9em;color:#24292e;border:1px solid rgba(27,31,35,.2);border-radius:3px;background-color:#eff3f6;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAKCAYAAAC9vt6cAAAAAXNSR0IArs4c6QAAARFJREFUKBVjZAACNS39RhBNKrh17WI9o4quoT3Dn78HSNUMUs/CzOTI/O7Vi4dCYpJ3/jP+92BkYGAlyiBGhm8MjIxJt65e3MQM0vDu9YvLYmISILYZELOBxHABRkaGr0yMzF23r12YDFIDNgDEePv65SEhEXENBkYGFSAXuyGMjF8Z/jOsvX3tYiFIDwgwQSgIaaijnvj/P8M5IO8HsjiY/f//D4b//88A1SQhywG9jQr09PS4v/1mPAeUUPzP8B8cJowMjL+Bqu6xMQmaXL164AuyDgwDQJLa2qYSP//9vARkCoMVMzK8YeVkNbh+9uxzMB+JwGoASF5Vx0jz/98/18BqmZi171w9D2EjaaYKEwAEK00XQLdJuwAAAABJRU5ErkJggg==);background-position:right 8px center;background-repeat:no-repeat;background-size:auto 25%;}.css-un3bt6:hover{background-color:#e6ebf1;border-color:rgba(27,31,35,.35);}.css-un3bt6:active{background-color:#e9ecef;border-color:rgba(27,31,35,.35);box-shadow:inset 0 0.15em 0.3em rgba(27,31,35,.15);}</style><select name="version" class="css-un3bt6"><option value="0.9.0-beta.12">0.9.0-beta.12</option><option value="0.9.0-beta.14">0.9.0-beta.14</option><option value="0.9.0-beta.15">0.9.0-beta.15</option><option value="0.9.0-beta.16">0.9.0-beta.16</option><option value="2.7.0">2.7.0</option><option value="2.7.5">2.7.5</option><option value="2.7.6">2.7.6</option><option value="3.0.0">3.0.0</option><option value="3.0.1">3.0.1</option><option value="3.0.2">3.0.2</option><option value="3.0.3">3.0.3</option><option value="3.0.4">3.0.4</option><option value="3.0.5">3.0.5</option><option value="3.0.6">3.0.6</option><option value="3.0.7">3.0.7</option><option value="3.0.8">3.0.8</option><option value="3.1.0">3.1.0</option><option value="3.1.2">3.1.2</option><option value="3.1.5">3.1.5</option><option value="3.1.7">3.1.7</option><option value="3.2.0">3.2.0</option><option value="3.2.5">3.2.5</option><option value="3.2.6">3.2.6</option><option value="3.2.7">3.2.7</option><option value="3.3.0">3.3.0</option><option value="3.3.1">3.3.1</option><option value="3.4.0">3.4.0</option><option value="3.4.1">3.4.1</option><option value="3.4.2">3.4.2</option><option value="4.0.0-beta.1">4.0.0-beta.1</option><option value="4.0.0-beta.2">4.0.0-beta.2</option><option value="4.0.0-beta.3">4.0.0-beta.3</option><option value="4.0.0-beta.4">4.0.0-beta.4</option><option value="4.0.0">4.0.0</option><option value="4.0.1">4.0.1</option><option value="4.0.2">4.0.2</option><option value="4.0.3">4.0.3</option><option value="4.0.5">4.0.5</option><option value="4.0.6">4.0.6</option><option value="4.0.7">4.0.7</option><option value="4.1.0">4.1.0</option><option value="4.1.5">4.1.5</option><option value="4.1.6">4.1.6</option><option value="4.2.0">4.2.0</option><option value="4.2.2">4.2.2</option><option value="4.2.5">4.2.5</option><option value="4.2.6">4.2.6</option><option value="4.3.0">4.3.0</option><option value="4.3.2">4.3.2</option><option value="4.3.3">4.3.3</option><option value="4.3.5">4.3.5</option><option value="4.4.0">4.4.0</option><option value="4.4.1">4.4.1</option><option value="4.4.2">4.4.2</option><option value="4.4.5">4.4.5</option><option value="4.4.6">4.4.6</option><option value="4.5.0">4.5.0</option><option value="4.5.1">4.5.1</option><option value="5.0.0">5.0.0</option><option value="5.0.1">5.0.1</option><option value="5.0.2">5.0.2</option><option value="5.0.3">5.0.3</option><option value="5.0.4">5.0.4</option><option value="5.1.0">5.1.0</option><option value="5.2.0">5.2.0</option><option value="5.2.1">5.2.1</option><option value="5.3.0">5.3.0</option><option value="5.3.1">5.3.1</option><option value="5.3.5">5.3.5</option><option value="5.3.6">5.3.6</option><option value="5.3.7">5.3.7</option><option value="5.3.8">5.3.8</option><option value="5.4.0">5.4.0</option><option value="5.4.1">5.4.1</option><option value="5.4.2">5.4.2</option><option value="5.4.3">5.4.3</option><option value="5.4.4">5.4.4</option><option value="5.4.5">5.4.5</option><option value="6.0.0-alpha.1">6.0.0-alpha.1</option><option value="6.0.0-alpha.2">6.0.0-alpha.2</option><option value="6.0.0-alpha.3">6.0.0-alpha.3</option><option value="6.0.0-alpha.4">6.0.0-alpha.4</option><option value="6.0.0-alpha.5">6.0.0-alpha.5</option><option value="6.0.0-alpha.6">6.0.0-alpha.6</option><option value="6.0.0-alpha.7">6.0.0-alpha.7</option><option value="6.0.0-alpha.8">6.0.0-alpha.8</option><option value="6.0.0-alpha.9">6.0.0-alpha.9</option><option value="6.0.0-alpha.10">6.0.0-alpha.10</option><option value="6.0.0-alpha.11">6.0.0-alpha.11</option><option value="6.0.0-alpha.12">6.0.0-alpha.12</option><option value="6.0.0-alpha.15">6.0.0-alpha.15</option><option value="6.0.0-alpha.16">6.0.0-alpha.16</option><option value="6.0.0-alpha.17">6.0.0-alpha.17</option><option value="6.0.0-alpha.18">6.0.0-alpha.18</option><option value="6.0.0">6.0.0</option><option value="6.0.1">6.0.1</option><option value="6.0.2">6.0.2</option><option value="6.0.3">6.0.3</option><option value="6.0.4">6.0.4</option><option value="6.1.0">6.1.0</option><option value="6.1.1">6.1.1</option><option value="6.1.2">6.1.2</option><option value="6.1.3">6.1.3</option><option value="6.2.0">6.2.0</option><option value="6.3.0">6.3.0</option><option value="6.3.1">6.3.1</option><option value="6.3.2">6.3.2</option><option value="6.3.3">6.3.3</option><option value="6.3.4">6.3.4</option><option value="6.3.5">6.3.5</option><option value="6.4.0">6.4.0</option><option value="6.4.1">6.4.1</option><option value="6.4.2">6.4.2</option><option value="6.4.3">6.4.3</option><option value="6.4.4">6.4.4</option><option value="6.4.5">6.4.5</option><option value="6.4.6">6.4.6</option><option value="6.4.7">6.4.7</option><option value="6.4.8">6.4.8</option><option value="6.4.9">6.4.9</option><option value="6.4.10">6.4.10</option><option value="6.4.11">6.4.11</option><option value="6.4.12">6.4.12</option><option value="6.4.14">6.4.14</option><option value="6.4.15">6.4.15</option><option value="6.5.0">6.5.0</option><option value="6.5.1">6.5.1</option><option value="6.5.2">6.5.2</option><option value="6.5.3">6.5.3</option><option value="6.5.4">6.5.4</option><option value="6.5.5-beta.1">6.5.5-beta.1</option><option value="6.5.5">6.5.5</option><option value="6.5.6">6.5.6</option><option value="6.5.7">6.5.7</option><option value="6.5.8">6.5.8</option><option value="6.5.9">6.5.9</option><option value="6.6.0">6.6.0</option><option value="6.6.1">6.6.1</option><option value="6.6.2">6.6.2</option><option value="6.7.0">6.7.0</option><option value="6.7.1">6.7.1</option><option value="6.7.5">6.7.5</option><option value="6.8.0-beta.1">6.8.0-beta.1</option><option value="6.8.0">6.8.0</option><option value="6.8.1">6.8.1</option><option value="6.8.2">6.8.2</option><option value="6.8.3">6.8.3</option><option value="6.8.4">6.8.4</option><option value="7.0.0-alpha.1">7.0.0-alpha.1</option><option value="7.0.0-alpha.2">7.0.0-alpha.2</option><option value="7.0.0-alpha.3">7.0.0-alpha.3</option><option value="7.0.0-alpha.4">7.0.0-alpha.4</option><option value="7.0.0-alpha.5">7.0.0-alpha.5</option><option value="7.0.0-alpha.6">7.0.0-alpha.6</option><option value="7.0.0-alpha.7">7.0.0-alpha.7</option><option value="7.0.0-alpha.8">7.0.0-alpha.8</option><option value="7.0.0-alpha.9">7.0.0-alpha.9</option><option value="7.0.0-alpha.10">7.0.0-alpha.10</option><option value="7.0.0-alpha.11">7.0.0-alpha.11</option><option value="7.0.0-alpha.12">7.0.0-alpha.12</option><option value="7.0.0-alpha.14">7.0.0-alpha.14</option><option value="7.0.0-alpha.15">7.0.0-alpha.15</option><option value="7.0.0-alpha.16">7.0.0-alpha.16</option><option value="7.0.0-alpha.17">7.0.0-alpha.17</option><option value="7.0.0-alpha.18">7.0.0-alpha.18</option><option value="7.0.0-alpha.19">7.0.0-alpha.19</option><option value="7.0.0-alpha.20">7.0.0-alpha.20</option><option value="7.0.0-alpha.21">7.0.0-alpha.21</option><option value="7.0.0-alpha.22">7.0.0-alpha.22</option><option value="7.0.0-alpha.23">7.0.0-alpha.23</option><option value="7.0.0-alpha.24">7.0.0-alpha.24</option><option value="7.0.0-alpha.25">7.0.0-alpha.25</option><option value="7.0.0-alpha.26">7.0.0-alpha.26</option><option value="7.0.0-alpha.27">7.0.0-alpha.27</option><option value="7.0.0-alpha.28">7.0.0-alpha.28</option><option value="7.0.0-alpha.29">7.0.0-alpha.29</option><option value="7.0.0-alpha.30">7.0.0-alpha.30</option><option value="7.0.0-alpha.31">7.0.0-alpha.31</option><option value="7.0.0-alpha.32">7.0.0-alpha.32</option><option value="7.0.0-alpha.33">7.0.0-alpha.33</option><option value="7.0.0-alpha.34">7.0.0-alpha.34</option><option value="7.0.0-alpha.35">7.0.0-alpha.35</option><option value="7.0.0-alpha.36">7.0.0-alpha.36</option><option value="7.0.0-alpha.37">7.0.0-alpha.37</option><option value="7.0.0-alpha.38">7.0.0-alpha.38</option><option value="7.0.0-alpha.39">7.0.0-alpha.39</option><option value="7.0.0-alpha.40">7.0.0-alpha.40</option><option value="7.0.0">7.0.0</option><option value="7.0.1">7.0.1</option><option value="7.0.2">7.0.2</option><option value="7.0.3">7.0.3</option><option value="7.0.4">7.0.4</option><option selected="" value="7.0.5">7.0.5</option><option value="7.0.6">7.0.6</option><option value="7.0.7">7.0.7</option><option value="7.0.8">7.0.8</option><option value="7.0.9">7.0.9</option><option value="7.1.0">7.1.0</option><option value="7.2.0">7.2.0</option><option value="7.3.0">7.3.0</option><option value="7.3.1">7.3.1</option><option value="7.3.2">7.3.2</option><option value="7.3.3">7.3.3</option><option value="7.3.4">7.3.4</option><option value="7.4.0">7.4.0</option><option value="7.4.1">7.4.1</option><option value="8.0.0">8.0.0</option><option value="8.0.1">8.0.1</option><option value="8.0.2">8.0.2</option><option value="8.0.3">8.0.3</option><option value="8.0.4">8.0.4</option><option value="8.0.5">8.0.5</option><option value="8.0.6">8.0.6</option><option value="8.0.7">8.0.7</option><option value="8.1.0">8.1.0</option><option value="8.1.1">8.1.1</option><option value="8.1.2">8.1.2</option><option value="8.1.3">8.1.3</option><option value="8.1.4">8.1.4</option><option value="8.1.5">8.1.5</option><option value="8.1.6">8.1.6</option><option value="8.2.0">8.2.0</option><option value="8.2.1">8.2.1</option><option value="8.2.2">8.2.2</option><option value="8.2.3">8.2.3</option><option value="8.2.4">8.2.4</option><option value="8.2.5">8.2.5</option><option value="8.2.6">8.2.6</option><option value="8.3.0">8.3.0</option><option value="8.3.1">8.3.1</option><option value="8.3.2">8.3.2</option><option value="8.4.0">8.4.0</option><option value="8.4.1">8.4.1</option><option value="8.4.2">8.4.2</option><option value="8.4.3">8.4.3</option><option value="8.4.4">8.4.4</option><option value="8.4.5">8.4.5</option><option value="8.4.6">8.4.6</option><option value="8.4.7">8.4.7</option><option value="9.0.0-beta.1">9.0.0-beta.1</option><option value="9.0.0-beta.2">9.0.0-beta.2</option><option value="9.0.0-beta.3">9.0.0-beta.3</option><option value="9.0.0-beta.4">9.0.0-beta.4</option><option value="9.0.0-beta.5">9.0.0-beta.5</option><option value="9.0.0-beta.6">9.0.0-beta.6</option><option value="9.0.0-beta.7">9.0.0-beta.7</option><option value="9.0.0-beta.8">9.0.0-beta.8</option><option value="9.0.0-beta.9">9.0.0-beta.9</option><option value="9.0.0-beta.10">9.0.0-beta.10</option><option value="9.0.0-beta.11">9.0.0-beta.11</option><option value="9.0.0-beta.16">9.0.0-beta.16</option><option value="9.0.0-beta.17">9.0.0-beta.17</option><option value="9.0.0-beta.18">9.0.0-beta.18</option><option value="9.0.0-beta.19">9.0.0-beta.19</option><option value="9.0.0-beta.20">9.0.0-beta.20</option><option value="9.0.0-beta.21">9.0.0-beta.21</option><option value="9.0.0-beta.22">9.0.0-beta.22</option><option value="9.0.0-beta.23">9.0.0-beta.23</option><option value="9.0.0-beta.24">9.0.0-beta.24</option><option value="9.0.0-beta.25">9.0.0-beta.25</option><option value="9.0.0-beta.26">9.0.0-beta.26</option><option value="9.0.0-beta.28">9.0.0-beta.28</option><option value="9.0.0-beta.29">9.0.0-beta.29</option><option value="9.0.0-beta.30">9.0.0-beta.30</option><option value="9.0.0-beta.31">9.0.0-beta.31</option><option value="9.0.0-beta.32">9.0.0-beta.32</option><option value="9.0.0-beta.33">9.0.0-beta.33</option><option value="9.0.0-beta.34">9.0.0-beta.34</option><option value="9.0.0-beta.35">9.0.0-beta.35</option><option value="9.0.0-beta.36">9.0.0-beta.36</option><option value="9.0.0-beta.38">9.0.0-beta.38</option><option value="9.0.0-beta.40">9.0.0-beta.40</option><option value="9.0.0-beta.41">9.0.0-beta.41</option><option value="9.0.0-beta.42">9.0.0-beta.42</option><option value="9.0.0">9.0.0</option><option value="9.0.1">9.0.1</option><option value="9.0.2">9.0.2</option><option value="9.0.3">9.0.3</option><option value="9.0.4">9.0.4</option><option value="9.0.5">9.0.5</option><option value="9.1.0">9.1.0</option><option value="9.1.1">9.1.1</option><option value="9.2.0">9.2.0</option><option value="9.2.1">9.2.1</option><option value="9.2.2">9.2.2</option><option value="9.2.3">9.2.3</option><option value="9.2.4">9.2.4</option><option value="9.3.0-beta.1">9.3.0-beta.1</option><option value="9.3.0">9.3.0</option><option value="9.3.1">9.3.1</option><option value="9.3.2">9.3.2</option><option value="9.4.0">9.4.0</option><option value="9.4.1">9.4.1</option><option value="10.0.0-beta.1">10.0.0-beta.1</option><option value="10.0.0-beta.2">10.0.0-beta.2</option><option value="10.0.0-beta.3">10.0.0-beta.3</option><option value="10.0.0-beta.4">10.0.0-beta.4</option><option value="10.0.0-beta.5">10.0.0-beta.5</option><option value="10.0.0-beta.6">10.0.0-beta.6</option><option value="10.0.0">10.0.0</option><option value="10.0.1">10.0.1</option><option value="10.0.2">10.0.2</option><option value="10.0.3">10.0.3</option><option value="10.0.4">10.0.4</option><option value="10.1.0">10.1.0</option><option value="10.2.0">10.2.0</option><option value="10.3.0">10.3.0</option><option value="10.3.1">10.3.1</option><option value="11.0.0-beta.1">11.0.0-beta.1</option></select></label></p></header></div><style data-emotion-css="107j3ms">.css-107j3ms{max-width:940px;padding:0 20px;margin:0 auto;}@media (max-width:700px){.css-107j3ms{padding:0;margin:0;}}</style><div class="css-107j3ms"><style data-emotion-css="q3frg4">.css-q3frg4{border:1px solid #dfe2e5;border-radius:3px;}@media (max-width:700px){.css-q3frg4{border-right-width:0;border-left-width:0;}}</style><div class="css-q3frg4"><style data-emotion-css="10o5omr">.css-10o5omr{padding:10px;background:#f6f8fa;color:#424242;border:1px solid #d1d5da;border-top-left-radius:3px;border-top-right-radius:3px;margin:-1px -1px 0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;}@media (max-width:700px){.css-10o5omr{padding-right:20px;padding-left:20px;}}</style><div class="css-10o5omr"><span>15.6 kB</span><span>CSS</span><span><style data-emotion-css="18x593j">.css-18x593j{display:inline-block;margin-left:8px;padding:2px 8px;-webkit-text-decoration:none;text-decoration:none;font-weight:600;font-size:0.9rem;color:#24292e;background-color:#eff3f6;border:1px solid rgba(27,31,35,.2);border-radius:3px;}.css-18x593j:hover{background-color:#e6ebf1;border-color:rgba(27,31,35,.35);}.css-18x593j:active{background-color:#e9ecef;border-color:rgba(27,31,35,.35);box-shadow:inset 0 0.15em 0.3em rgba(27,31,35,.15);}</style><a href="/swiper@7.0.5/swiper-bundle.min.css" class="css-18x593j">View Raw</a></span></div><style data-emotion-css="1i31ihw">.css-1i31ihw{overflow-x:auto;overflow-y:hidden;padding-top:5px;padding-bottom:5px;}</style><div class="code-listing css-1i31ihw"><style data-emotion-css="173nir8">.css-173nir8{border:none;border-collapse:collapse;border-spacing:0;}</style><table class="css-173nir8"><tbody><tr><style data-emotion-css="a4x74f">.css-a4x74f{padding-left:10px;padding-right:10px;color:rgba(27,31,35,.3);text-align:right;vertical-align:top;width:1%;min-width:50px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;}</style><td id="L1" class="css-a4x74f"><span>1</span></td><style data-emotion-css="1dcdqdg">.css-1dcdqdg{padding-left:10px;padding-right:10px;color:#24292e;white-space:pre;}</style><td id="LC1" class="css-1dcdqdg"><code><span class="code-comment">/**
</span></code></td></tr><tr><td id="L2" class="css-a4x74f"><span>2</span></td><td id="LC2" class="css-1dcdqdg"><code><span class="code-comment"> * Swiper 7.0.5
</span></code></td></tr><tr><td id="L3" class="css-a4x74f"><span>3</span></td><td id="LC3" class="css-1dcdqdg"><code><span class="code-comment"> * Most modern mobile touch slider and framework with hardware accelerated transitions
</span></code></td></tr><tr><td id="L4" class="css-a4x74f"><span>4</span></td><td id="LC4" class="css-1dcdqdg"><code><span class="code-comment"> * https://swiperjs.com
</span></code></td></tr><tr><td id="L5" class="css-a4x74f"><span>5</span></td><td id="LC5" class="css-1dcdqdg"><code><span class="code-comment"> *
</span></code></td></tr><tr><td id="L6" class="css-a4x74f"><span>6</span></td><td id="LC6" class="css-1dcdqdg"><code><span class="code-comment"> * Copyright 2014-2021 Vladimir Kharlampidi
</span></code></td></tr><tr><td id="L7" class="css-a4x74f"><span>7</span></td><td id="LC7" class="css-1dcdqdg"><code><span class="code-comment"> *
</span></code></td></tr><tr><td id="L8" class="css-a4x74f"><span>8</span></td><td id="LC8" class="css-1dcdqdg"><code><span class="code-comment"> * Released under the MIT License
</span></code></td></tr><tr><td id="L9" class="css-a4x74f"><span>9</span></td><td id="LC9" class="css-1dcdqdg"><code><span class="code-comment"> *
</span></code></td></tr><tr><td id="L10" class="css-a4x74f"><span>10</span></td><td id="LC10" class="css-1dcdqdg"><code><span class="code-comment"> * Released on: September 9, 2021
</span></code></td></tr><tr><td id="L11" class="css-a4x74f"><span>11</span></td><td id="LC11" class="css-1dcdqdg"><code><span class="code-comment"> */</span>
</code></td></tr><tr><td id="L12" class="css-a4x74f"><span>12</span></td><td id="LC12" class="css-1dcdqdg"><code>
</code></td></tr><tr><td id="L13" class="css-a4x74f"><span>13</span></td><td id="LC13" class="css-1dcdqdg"><code>@<span class="code-keyword">font-face</span>{<span class="code-attribute">font-family</span>:swiper-icons;<span class="code-attribute">src</span>:<span class="code-built_in">url</span>(<span class="code-string">'data:application/font-woff;charset=utf-8;base64, 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'</span>);<span class="code-attribute">font-weight</span>:<span class="code-number">400</span>;<span class="code-attribute">font-style</span>:normal}<span class="hljs-selector-pseudo">:root</span>{<span class="code-attribute">--swiper-theme-color</span>:<span class="code-number">#007aff</span>}<span class="hljs-selector-class">.swiper</span>{<span class="code-attribute">margin-left</span>:auto;<span class="code-attribute">margin-right</span>:auto;<span class="code-attribute">position</span>:relative;<span class="code-attribute">overflow</span>:hidden;<span class="code-attribute">list-style</span>:none;<span class="code-attribute">padding</span>:<span class="code-number">0</span>;<span class="code-attribute">z-index</span>:<span class="code-number">1</span>}<span class="hljs-selector-class">.swiper-vertical</span>&gt;<span class="hljs-selector-class">.swiper-wrapper</span>{<span class="code-attribute">flex-direction</span>:column}<span class="hljs-selector-class">.swiper-wrapper</span>{<span class="code-attribute">position</span>:relative;<span class="code-attribute">width</span>:<span class="code-number">100%</span>;<span class="code-attribute">height</span>:<span class="code-number">100%</span>;<span class="code-attribute">z-index</span>:<span class="code-number">1</span>;<span class="code-attribute">display</span>:flex;<span class="code-attribute">transition-property</span>:transform;<span class="code-attribute">box-sizing</span>:content-box}<span class="hljs-selector-class">.swiper-android</span> <span class="hljs-selector-class">.swiper-slide</span>,<span class="hljs-selector-class">.swiper-wrapper</span>{<span class="code-attribute">transform</span>:<span class="code-built_in">translate3d</span>(0px,0,0)}<span class="hljs-selector-class">.swiper-pointer-events</span>{<span class="code-attribute">touch-action</span>:pan-y}<span class="hljs-selector-class">.swiper-pointer-events</span><span class="hljs-selector-class">.swiper-vertical</span>{<span class="code-attribute">touch-action</span>:pan-x}<span class="hljs-selector-class">.swiper-slide</span>{<span class="code-attribute">flex-shrink</span>:<span class="code-number">0</span>;<span class="code-attribute">width</span>:<span class="code-number">100%</span>;<span class="code-attribute">height</span>:<span class="code-number">100%</span>;<span class="code-attribute">position</span>:relative;<span class="code-attribute">transition-property</span>:transform}<span class="hljs-selector-class">.swiper-slide-invisible-blank</span>{<span class="code-attribute">visibility</span>:hidden}<span class="hljs-selector-class">.swiper-autoheight</span>,<span class="hljs-selector-class">.swiper-autoheight</span> <span class="hljs-selector-class">.swiper-slide</span>{<span class="code-attribute">height</span>:auto}<span class="hljs-selector-class">.swiper-autoheight</span> <span class="hljs-selector-class">.swiper-wrapper</span>{<span class="code-attribute">align-items</span>:flex-start;<span class="code-attribute">transition-property</span>:transform,height}<span class="hljs-selector-class">.swiper-3d</span>,<span class="hljs-selector-class">.swiper-3d</span><span class="hljs-selector-class">.swiper-css-mode</span> <span class="hljs-selector-class">.swiper-wrapper</span>{<span class="code-attribute">perspective</span>:<span class="code-number">1200px</span>}<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-cube-shadow</span>,<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-slide</span>,<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-slide-shadow</span>,<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-slide-shadow-bottom</span>,<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-slide-shadow-left</span>,<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-slide-shadow-right</span>,<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-slide-shadow-top</span>,<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-wrapper</span>{<span class="code-attribute">transform-style</span>:preserve-<span class="code-number">3</span>d}<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-slide-shadow</span>,<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-slide-shadow-bottom</span>,<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-slide-shadow-left</span>,<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-slide-shadow-right</span>,<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-slide-shadow-top</span>{<span class="code-attribute">position</span>:absolute;<span class="code-attribute">left</span>:<span class="code-number">0</span>;<span class="code-attribute">top</span>:<span class="code-number">0</span>;<span class="code-attribute">width</span>:<span class="code-number">100%</span>;<span class="code-attribute">height</span>:<span class="code-number">100%</span>;<span class="code-attribute">pointer-events</span>:none;<span class="code-attribute">z-index</span>:<span class="code-number">10</span>}<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-slide-shadow</span>{<span class="code-attribute">background</span>:<span class="code-built_in">rgba</span>(0,0,0,.15)}<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-slide-shadow-left</span>{<span class="code-attribute">background-image</span>:<span class="code-built_in">linear-gradient</span>(to left,rgba(0,0,0,.5),<span class="code-built_in">rgba</span>(0,0,0,0))}<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-slide-shadow-right</span>{<span class="code-attribute">background-image</span>:<span class="code-built_in">linear-gradient</span>(to right,rgba(0,0,0,.5),<span class="code-built_in">rgba</span>(0,0,0,0))}<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-slide-shadow-top</span>{<span class="code-attribute">background-image</span>:<span class="code-built_in">linear-gradient</span>(to top,rgba(0,0,0,.5),<span class="code-built_in">rgba</span>(0,0,0,0))}<span class="hljs-selector-class">.swiper-3d</span> <span class="hljs-selector-class">.swiper-slide-shadow-bottom</span>{<span class="code-attribute">background-image</span>:<span class="code-built_in">linear-gradient</span>(to bottom,rgba(0,0,0,.5),<span class="code-built_in">rgba</span>(0,0,0,0))}<span class="hljs-selector-class">.swiper-css-mode</span>&gt;<span class="hljs-selector-class">.swiper-wrapper</span>{<span class="code-attribute">overflow</span>:auto;<span class="code-attribute">scrollbar-width</span>:none;<span class="code-attribute">-ms-overflow-style</span>:none}<span class="hljs-selector-class">.swiper-css-mode</span>&gt;<span class="hljs-selector-class">.swiper-wrapper</span><span class="hljs-selector-pseudo">::-webkit-scrollbar</span>{<span class="code-attribute">display</span>:none}<span class="hljs-selector-class">.swiper-css-mode</span>&gt;<span class="hljs-selector-class">.swiper-wrapper</span>&gt;<span class="hljs-selector-class">.swiper-slide</span>{<span class="code-attribute">scroll-snap-align</span>:start start}<span class="hljs-selector-class">.swiper-horizontal</span><span class="hljs-selector-class">.swiper-css-mode</span>&gt;<span class="hljs-selector-class">.swiper-wrapper</span>{<span class="code-attribute">scroll-snap-type</span>:x mandatory}<span class="hljs-selector-class">.swiper-vertical</span><span class="hljs-selector-class">.swiper-css-mode</span>&gt;<span class="hljs-selector-class">.swiper-wrapper</span>{<span class="code-attribute">scroll-snap-type</span>:y mandatory}<span class="hljs-selector-class">.swiper-centered</span>&gt;<span class="hljs-selector-class">.swiper-wrapper</span><span class="hljs-selector-pseudo">::before</span>{<span class="code-attribute">content</span>:<span class="code-string">''</span>;<span class="code-attribute">flex-shrink</span>:<span class="code-number">0</span>;<span class="code-attribute">order</span>:<span class="code-number">9999</span>}<span class="hljs-selector-class">.swiper-centered</span><span class="hljs-selector-class">.swiper-horizontal</span>&gt;<span class="hljs-selector-class">.swiper-wrapper</span>&gt;<span class="hljs-selector-class">.swiper-slide</span><span class="hljs-selector-pseudo">:first-child</span>{<span class="code-attribute">margin-inline-start</span>:<span class="code-built_in">var</span>(--swiper-centered-offset-before)}<span class="hljs-selector-class">.swiper-centered</span><span class="hljs-selector-class">.swiper-horizontal</span>&gt;<span class="hljs-selector-class">.swiper-wrapper</span><span class="hljs-selector-pseudo">::before</span>{<span class="code-attribute">height</span>:<span class="code-number">100%</span>;<span class="code-attribute">min-height</span>:<span class="code-number">1px</span>;<span class="code-attribute">width</span>:<span class="code-built_in">var</span>(--swiper-centered-offset-after)}<span class="hljs-selector-class">.swiper-centered</span><span class="hljs-selector-class">.swiper-vertical</span>&gt;<span class="hljs-selector-class">.swiper-wrapper</span>&gt;<span class="hljs-selector-class">.swiper-slide</span><span class="hljs-selector-pseudo">:first-child</span>{<span class="code-attribute">margin-block-start</span>:<span class="code-built_in">var</span>(--swiper-centered-offset-before)}<span class="hljs-selector-class">.swiper-centered</span><span class="hljs-selector-class">.swiper-vertical</span>&gt;<span class="hljs-selector-class">.swiper-wrapper</span><span class="hljs-selector-pseudo">::before</span>{<span class="code-attribute">width</span>:<span class="code-number">100%</span>;<span class="code-attribute">min-width</span>:<span class="code-number">1px</span>;<span class="code-attribute">height</span>:<span class="code-built_in">var</span>(--swiper-centered-offset-after)}<span class="hljs-selector-class">.swiper-centered</span>&gt;<span class="hljs-selector-class">.swiper-wrapper</span>&gt;<span class="hljs-selector-class">.swiper-slide</span>{<span class="code-attribute">scroll-snap-align</span>:center center}<span class="hljs-selector-class">.swiper-virtual</span><span class="hljs-selector-class">.swiper-css-mode</span> <span class="hljs-selector-class">.swiper-wrapper</span><span class="hljs-selector-pseudo">::after</span>{<span class="code-attribute">content</span>:<span class="code-string">''</span>;<span class="code-attribute">position</span>:absolute;<span class="code-attribute">left</span>:<span class="code-number">0</span>;<span class="code-attribute">top</span>:<span class="code-number">0</span>;<span class="code-attribute">pointer-events</span>:none}<span class="hljs-selector-class">.swiper-virtual</span><span class="hljs-selector-class">.swiper-css-mode</span><span class="hljs-selector-class">.swiper-horizontal</span> <span class="hljs-selector-class">.swiper-wrapper</span><span class="hljs-selector-pseudo">::after</span>{<span class="code-attribute">height</span>:<span class="code-number">1px</span>;<span class="code-attribute">width</span>:<span class="code-built_in">var</span>(--swiper-virtual-size)}<span class="hljs-selector-class">.swiper-virtual</span><span class="hljs-selector-class">.swiper-css-mode</span><span class="hljs-selector-class">.swiper-vertical</span> <span class="hljs-selector-class">.swiper-wrapper</span><span class="hljs-selector-pseudo">::after</span>{<span class="code-attribute">width</span>:<span class="code-number">1px</span>;<span class="code-attribute">height</span>:<span class="code-built_in">var</span>(--swiper-virtual-size)}<span class="hljs-selector-pseudo">:root</span>{<span class="code-attribute">--swiper-navigation-size</span>:<span class="code-number">44px</span>}<span class="hljs-selector-class">.swiper-button-next</span>,<span class="hljs-selector-class">.swiper-button-prev</span>{<span class="code-attribute">position</span>:absolute;<span class="code-attribute">top</span>:<span class="code-number">50%</span>;<span class="code-attribute">width</span>:<span class="code-built_in">calc</span>(var(--swiper-navigation-size)/ <span class="code-number">44</span> * <span class="code-number">27</span>);<span class="code-attribute">height</span>:<span class="code-built_in">var</span>(--swiper-navigation-size);<span class="code-attribute">margin-top</span>:<span class="code-built_in">calc</span>(0px - (var(--swiper-navigation-size)/ <span class="code-number">2</span>));<span class="code-attribute">z-index</span>:<span class="code-number">10</span>;<span class="code-attribute">cursor</span>:pointer;<span class="code-attribute">display</span>:flex;<span class="code-attribute">align-items</span>:center;<span class="code-attribute">justify-content</span>:center;<span class="code-attribute">color</span>:<span class="code-built_in">var</span>(--swiper-navigation-color,var(--swiper-theme-color))}<span class="hljs-selector-class">.swiper-button-next</span><span class="hljs-selector-class">.swiper-button-disabled</span>,<span class="hljs-selector-class">.swiper-button-prev</span><span class="hljs-selector-class">.swiper-button-disabled</span>{<span class="code-attribute">opacity</span>:.<span class="code-number">35</span>;<span class="code-attribute">cursor</span>:auto;<span class="code-attribute">pointer-events</span>:none}<span class="hljs-selector-class">.swiper-button-next</span><span class="hljs-selector-pseudo">:after</span>,<span class="hljs-selector-class">.swiper-button-prev</span><span class="hljs-selector-pseudo">:after</span>{<span class="code-attribute">font-family</span>:swiper-icons;<span class="code-attribute">font-size</span>:<span class="code-built_in">var</span>(--swiper-navigation-size);<span class="code-attribute">text-transform</span>:none<span class="code-meta">!important</span>;<span class="code-attribute">letter-spacing</span>:<span class="code-number">0</span>;<span class="code-attribute">text-transform</span>:none;<span class="code-attribute">font-variant</span>:initial;<span class="code-attribute">line-height</span>:<span class="code-number">1</span>}<span class="hljs-selector-class">.swiper-button-prev</span>,<span class="hljs-selector-class">.swiper-rtl</span> <span class="hljs-selector-class">.swiper-button-next</span>{<span class="code-attribute">left</span>:<span class="code-number">10px</span>;<span class="code-attribute">right</span>:auto}<span class="hljs-selector-class">.swiper-button-prev</span><span class="hljs-selector-pseudo">:after</span>,<span class="hljs-selector-class">.swiper-rtl</span> <span class="hljs-selector-class">.swiper-button-next</span><span class="hljs-selector-pseudo">:after</span>{<span class="code-attribute">content</span>:<span class="code-string">'prev'</span>}<span class="hljs-selector-class">.swiper-button-next</span>,<span class="hljs-selector-class">.swiper-rtl</span> <span class="hljs-selector-class">.swiper-button-prev</span>{<span class="code-attribute">right</span>:<span class="code-number">10px</span>;<span class="code-attribute">left</span>:auto}<span class="hljs-selector-class">.swiper-button-next</span><span class="hljs-selector-pseudo">:after</span>,<span class="hljs-selector-class">.swiper-rtl</span> <span class="hljs-selector-class">.swiper-button-prev</span><span class="hljs-selector-pseudo">:after</span>{<span class="code-attribute">content</span>:<span class="code-string">'next'</span>}<span class="hljs-selector-class">.swiper-button-lock</span>{<span class="code-attribute">display</span>:none}<span class="hljs-selector-class">.swiper-pagination</span>{<span class="code-attribute">position</span>:absolute;<span class="code-attribute">text-align</span>:center;<span class="code-attribute">transition</span>:.<span class="code-number">3s</span> opacity;<span class="code-attribute">transform</span>:<span class="code-built_in">translate3d</span>(0,0,0);<span class="code-attribute">z-index</span>:<span class="code-number">10</span>}<span class="hljs-selector-class">.swiper-pagination</span><span class="hljs-selector-class">.swiper-pagination-hidden</span>{<span class="code-attribute">opacity</span>:<span class="code-number">0</span>}<span class="hljs-selector-class">.swiper-horizontal</span>&gt;<span class="hljs-selector-class">.swiper-pagination-bullets</span>,<span class="hljs-selector-class">.swiper-pagination-bullets</span><span class="hljs-selector-class">.swiper-pagination-horizontal</span>,<span class="hljs-selector-class">.swiper-pagination-custom</span>,<span class="hljs-selector-class">.swiper-pagination-fraction</span>{<span class="code-attribute">bottom</span>:<span class="code-number">10px</span>;<span class="code-attribute">left</span>:<span class="code-number">0</span>;<span class="code-attribute">width</span>:<span class="code-number">100%</span>}<span class="hljs-selector-class">.swiper-pagination-bullets-dynamic</span>{<span class="code-attribute">overflow</span>:hidden;<span class="code-attribute">font-size</span>:<span class="code-number">0</span>}<span class="hljs-selector-class">.swiper-pagination-bullets-dynamic</span> <span class="hljs-selector-class">.swiper-pagination-bullet</span>{<span class="code-attribute">transform</span>:<span class="code-built_in">scale</span>(.33);<span class="code-attribute">position</span>:relative}<span class="hljs-selector-class">.swiper-pagination-bullets-dynamic</span> <span class="hljs-selector-class">.swiper-pagination-bullet-active</span>{<span class="code-attribute">transform</span>:<span class="code-built_in">scale</span>(1)}<span class="hljs-selector-class">.swiper-pagination-bullets-dynamic</span> <span class="hljs-selector-class">.swiper-pagination-bullet-active-main</span>{<span class="code-attribute">transform</span>:<span class="code-built_in">scale</span>(1)}<span class="hljs-selector-class">.swiper-pagination-bullets-dynamic</span> <span class="hljs-selector-class">.swiper-pagination-bullet-active-prev</span>{<span class="code-attribute">transform</span>:<span class="code-built_in">scale</span>(.66)}<span class="hljs-selector-class">.swiper-pagination-bullets-dynamic</span> <span class="hljs-selector-class">.swiper-pagination-bullet-active-prev-prev</span>{<span class="code-attribute">transform</span>:<span class="code-built_in">scale</span>(.33)}<span class="hljs-selector-class">.swiper-pagination-bullets-dynamic</span> <span class="hljs-selector-class">.swiper-pagination-bullet-active-next</span>{<span class="code-attribute">transform</span>:<span class="code-built_in">scale</span>(.66)}<span class="hljs-selector-class">.swiper-pagination-bullets-dynamic</span> <span class="hljs-selector-class">.swiper-pagination-bullet-active-next-next</span>{<span class="code-attribute">transform</span>:<span class="code-built_in">scale</span>(.33)}<span class="hljs-selector-class">.swiper-pagination-bullet</span>{<span class="code-attribute">width</span>:<span class="code-built_in">var</span>(--swiper-pagination-bullet-width,var(--swiper-pagination-bullet-size,8px));<span class="code-attribute">height</span>:<span class="code-built_in">var</span>(--swiper-pagination-bullet-height,var(--swiper-pagination-bullet-size,8px));<span class="code-attribute">display</span>:inline-block;<span class="code-attribute">border-radius</span>:<span class="code-number">50%</span>;<span class="code-attribute">background</span>:<span class="code-built_in">var</span>(--swiper-pagination-bullet-inactive-color,#000);<span class="code-attribute">opacity</span>:<span class="code-built_in">var</span>(--swiper-pagination-bullet-inactive-opacity, .2)}<span class="hljs-selector-tag">button</span><span class="hljs-selector-class">.swiper-pagination-bullet</span>{<span class="code-attribute">border</span>:none;<span class="code-attribute">margin</span>:<span class="code-number">0</span>;<span class="code-attribute">padding</span>:<span class="code-number">0</span>;<span class="code-attribute">box-shadow</span>:none;<span class="code-attribute">-webkit-appearance</span>:none;<span class="code-attribute">appearance</span>:none}<span class="hljs-selector-class">.swiper-pagination-clickable</span> <span class="hljs-selector-class">.swiper-pagination-bullet</span>{<span class="code-attribute">cursor</span>:pointer}<span class="hljs-selector-class">.swiper-pagination-bullet</span><span class="hljs-selector-pseudo">:only-child</span>{<span class="code-attribute">display</span>:none<span class="code-meta">!important</span>}<span class="hljs-selector-class">.swiper-pagination-bullet-active</span>{<span class="code-attribute">opacity</span>:<span class="code-built_in">var</span>(--swiper-pagination-bullet-opacity, 1);<span class="code-attribute">background</span>:<span class="code-built_in">var</span>(--swiper-pagination-color,var(--swiper-theme-color))}<span class="hljs-selector-class">.swiper-pagination-vertical</span><span class="hljs-selector-class">.swiper-pagination-bullets</span>,<span class="hljs-selector-class">.swiper-vertical</span>&gt;<span class="hljs-selector-class">.swiper-pagination-bullets</span>{<span class="code-attribute">right</span>:<span class="code-number">10px</span>;<span class="code-attribute">top</span>:<span class="code-number">50%</span>;<span class="code-attribute">transform</span>:<span class="code-built_in">translate3d</span>(0px,-50%,0)}<span class="hljs-selector-class">.swiper-pagination-vertical</span><span class="hljs-selector-class">.swiper-pagination-bullets</span> <span class="hljs-selector-class">.swiper-pagination-bullet</span>,<span class="hljs-selector-class">.swiper-vertical</span>&gt;<span class="hljs-selector-class">.swiper-pagination-bullets</span> <span class="hljs-selector-class">.swiper-pagination-bullet</span>{<span class="code-attribute">margin</span>:<span class="code-built_in">var</span>(--swiper-pagination-bullet-vertical-gap,6px) <span class="code-number">0</span>;<span class="code-attribute">display</span>:block}<span class="hljs-selector-class">.swiper-pagination-vertical</span><span class="hljs-selector-class">.swiper-pagination-bullets</span><span class="hljs-selector-class">.swiper-pagination-bullets-dynamic</span>,<span class="hljs-selector-class">.swiper-vertical</span>&gt;<span class="hljs-selector-class">.swiper-pagination-bullets</span><span class="hljs-selector-class">.swiper-pagination-bullets-dynamic</span>{<span class="code-attribute">top</span>:<span class="code-number">50%</span>;<span class="code-attribute">transform</span>:<span class="code-built_in">translateY</span>(-50%);<span class="code-attribute">width</span>:<span class="code-number">8px</span>}<span class="hljs-selector-class">.swiper-pagination-vertical</span><span class="hljs-selector-class">.swiper-pagination-bullets</span><span class="hljs-selector-class">.swiper-pagination-bullets-dynamic</span> <span class="hljs-selector-class">.swiper-pagination-bullet</span>,<span class="hljs-selector-class">.swiper-vertical</span>&gt;<span class="hljs-selector-class">.swiper-pagination-bullets</span><span class="hljs-selector-class">.swiper-pagination-bullets-dynamic</span> <span class="hljs-selector-class">.swiper-pagination-bullet</span>{<span class="code-attribute">display</span>:inline-block;<span class="code-attribute">transition</span>:.<span class="code-number">2s</span> transform,.<span class="code-number">2s</span> top}<span class="hljs-selector-class">.swiper-horizontal</span>&gt;<span class="hljs-selector-class">.swiper-pagination-bullets</span> <span class="hljs-selector-class">.swiper-pagination-bullet</span>,<span class="hljs-selector-class">.swiper-pagination-horizontal</span><span class="hljs-selector-class">.swiper-pagination-bullets</span> <span class="hljs-selector-class">.swiper-pagination-bullet</span>{<span class="code-attribute">margin</span>:<span class="code-number">0</span> <span class="code-built_in">var</span>(--swiper-pagination-bullet-horizontal-gap,4px)}<span class="hljs-selector-class">.swiper-horizontal</span>&gt;<span class="hljs-selector-class">.swiper-pagination-bullets</span><span class="hljs-selector-class">.swiper-pagination-bullets-dynamic</span>,<span class="hljs-selector-class">.swiper-pagination-horizontal</span><span class="hljs-selector-class">.swiper-pagination-bullets</span><span class="hljs-selector-class">.swiper-pagination-bullets-dynamic</span>{<span class="code-attribute">left</span>:<span class="code-number">50%</span>;<span class="code-attribute">transform</span>:<span class="code-built_in">translateX</span>(-50%);<span class="code-attribute">white-space</span>:nowrap}<span class="hljs-selector-class">.swiper-horizontal</span>&gt;<span class="hljs-selector-class">.swiper-pagination-bullets</span><span class="hljs-selector-class">.swiper-pagination-bullets-dynamic</span> <span class="hljs-selector-class">.swiper-pagination-bullet</span>,<span class="hljs-selector-class">.swiper-pagination-horizontal</span><span class="hljs-selector-class">.swiper-pagination-bullets</span><span class="hljs-selector-class">.swiper-pagination-bullets-dynamic</span> <span class="hljs-selector-class">.swiper-pagination-bullet</span>{<span class="code-attribute">transition</span>:.<span class="code-number">2s</span> transform,.<span class="code-number">2s</span> left}<span class="hljs-selector-class">.swiper-horizontal</span><span class="hljs-selector-class">.swiper-rtl</span>&gt;<span class="hljs-selector-class">.swiper-pagination-bullets-dynamic</span> <span class="hljs-selector-class">.swiper-pagination-bullet</span>{<span class="code-attribute">transition</span>:.<span class="code-number">2s</span> transform,.<span class="code-number">2s</span> right}<span class="hljs-selector-class">.swiper-pagination-progressbar</span>{<span class="code-attribute">background</span>:<span class="code-built_in">rgba</span>(0,0,0,.25);<span class="code-attribute">position</span>:absolute}<span class="hljs-selector-class">.swiper-pagination-progressbar</span> <span class="hljs-selector-class">.swiper-pagination-progressbar-fill</span>{<span class="code-attribute">background</span>:<span class="code-built_in">var</span>(--swiper-pagination-color,var(--swiper-theme-color));<span class="code-attribute">position</span>:absolute;<span class="code-attribute">left</span>:<span class="code-number">0</span>;<span class="code-attribute">top</span>:<span class="code-number">0</span>;<span class="code-attribute">width</span>:<span class="code-number">100%</span>;<span class="code-attribute">height</span>:<span class="code-number">100%</span>;<span class="code-attribute">transform</span>:<span class="code-built_in">scale</span>(0);<span class="code-attribute">transform-origin</span>:left top}<span class="hljs-selector-class">.swiper-rtl</span> <span class="hljs-selector-class">.swiper-pagination-progressbar</span> <span class="hljs-selector-class">.swiper-pagination-progressbar-fill</span>{<span class="code-attribute">transform-origin</span>:right top}<span class="hljs-selector-class">.swiper-horizontal</span>&gt;<span class="hljs-selector-class">.swiper-pagination-progressbar</span>,<span class="hljs-selector-class">.swiper-pagination-progressbar</span><span class="hljs-selector-class">.swiper-pagination-horizontal</span>,<span class="hljs-selector-class">.swiper-pagination-progressbar</span><span class="hljs-selector-class">.swiper-pagination-vertical</span><span class="hljs-selector-class">.swiper-pagination-progressbar-opposite</span>,<span class="hljs-selector-class">.swiper-vertical</span>&gt;<span class="hljs-selector-class">.swiper-pagination-progressbar</span><span class="hljs-selector-class">.swiper-pagination-progressbar-opposite</span>{<span class="code-attribute">width</span>:<span class="code-number">100%</span>;<span class="code-attribute">height</span>:<span class="code-number">4px</span>;<span class="code-attribute">left</span>:<span class="code-number">0</span>;<span class="code-attribute">top</span>:<span class="code-number">0</span>}<span class="hljs-selector-class">.swiper-horizontal</span>&gt;<span class="hljs-selector-class">.swiper-pagination-progressbar</span><span class="hljs-selector-class">.swiper-pagination-progressbar-opposite</span>,<span class="hljs-selector-class">.swiper-pagination-progressbar</span><span class="hljs-selector-class">.swiper-pagination-horizontal</span><span class="hljs-selector-class">.swiper-pagination-progressbar-opposite</span>,<span class="hljs-selector-class">.swiper-pagination-progressbar</span><span class="hljs-selector-class">.swiper-pagination-vertical</span>,<span class="hljs-selector-class">.swiper-vertical</span>&gt;<span class="hljs-selector-class">.swiper-pagination-progressbar</span>{<span class="code-attribute">width</span>:<span class="code-number">4px</span>;<span class="code-attribute">height</span>:<span class="code-number">100%</span>;<span class="code-attribute">left</span>:<span class="code-number">0</span>;<span class="code-attribute">top</span>:<span class="code-number">0</span>}<span class="hljs-selector-class">.swiper-pagination-lock</span>{<span class="code-attribute">display</span>:none}<span class="hljs-selector-class">.swiper-scrollbar</span>{<span class="code-attribute">border-radius</span>:<span class="code-number">10px</span>;<span class="code-attribute">position</span>:relative;<span class="code-attribute">-ms-touch-action</span>:none;<span class="code-attribute">background</span>:<span class="code-built_in">rgba</span>(0,0,0,.1)}<span class="hljs-selector-class">.swiper-horizontal</span>&gt;<span class="hljs-selector-class">.swiper-scrollbar</span>{<span class="code-attribute">position</span>:absolute;<span class="code-attribute">left</span>:<span class="code-number">1%</span>;<span class="code-attribute">bottom</span>:<span class="code-number">3px</span>;<span class="code-attribute">z-index</span>:<span class="code-number">50</span>;<span class="code-attribute">height</span>:<span class="code-number">5px</span>;<span class="code-attribute">width</span>:<span class="code-number">98%</span>}<span class="hljs-selector-class">.swiper-vertical</span>&gt;<span class="hljs-selector-class">.swiper-scrollbar</span>{<span class="code-attribute">position</span>:absolute;<span class="code-attribute">right</span>:<span class="code-number">3px</span>;<span class="code-attribute">top</span>:<span class="code-number">1%</span>;<span class="code-attribute">z-index</span>:<span class="code-number">50</span>;<span class="code-attribute">width</span>:<span class="code-number">5px</span>;<span class="code-attribute">height</span>:<span class="code-number">98%</span>}<span class="hljs-selector-class">.swiper-scrollbar-drag</span>{<span class="code-attribute">height</span>:<span class="code-number">100%</span>;<span class="code-attribute">width</span>:<span class="code-number">100%</span>;<span class="code-attribute">position</span>:relative;<span class="code-attribute">background</span>:<span class="code-built_in">rgba</span>(0,0,0,.5);<span class="code-attribute">border-radius</span>:<span class="code-number">10px</span>;<span class="code-attribute">left</span>:<span class="code-number">0</span>;<span class="code-attribute">top</span>:<span class="code-number">0</span>}<span class="hljs-selector-class">.swiper-scrollbar-cursor-drag</span>{<span class="code-attribute">cursor</span>:move}<span class="hljs-selector-class">.swiper-scrollbar-lock</span>{<span class="code-attribute">display</span>:none}<span class="hljs-selector-class">.swiper-zoom-container</span>{<span class="code-attribute">width</span>:<span class="code-number">100%</span>;<span class="code-attribute">height</span>:<span class="code-number">100%</span>;<span class="code-attribute">display</span>:flex;<span class="code-attribute">justify-content</span>:center;<span class="code-attribute">align-items</span>:center;<span class="code-attribute">text-align</span>:center}<span class="hljs-selector-class">.swiper-zoom-container</span>&gt;<span class="hljs-selector-tag">canvas</span>,<span class="hljs-selector-class">.swiper-zoom-container</span>&gt;<span class="hljs-selector-tag">img</span>,<span class="hljs-selector-class">.swiper-zoom-container</span>&gt;<span class="hljs-selector-tag">svg</span>{<span class="code-attribute">max-width</span>:<span class="code-number">100%</span>;<span class="code-attribute">max-height</span>:<span class="code-number">100%</span>;<span class="code-attribute">object-fit</span>:contain}<span class="hljs-selector-class">.swiper-slide-zoomed</span>{<span class="code-attribute">cursor</span>:move}<span class="hljs-selector-class">.swiper-lazy-preloader</span>{<span class="code-attribute">width</span>:<span class="code-number">42px</span>;<span class="code-attribute">height</span>:<span class="code-number">42px</span>;<span class="code-attribute">position</span>:absolute;<span class="code-attribute">left</span>:<span class="code-number">50%</span>;<span class="code-attribute">top</span>:<span class="code-number">50%</span>;<span class="code-attribute">margin-left</span>:-<span class="code-number">21px</span>;<span class="code-attribute">margin-top</span>:-<span class="code-number">21px</span>;<span class="code-attribute">z-index</span>:<span class="code-number">10</span>;<span class="code-attribute">transform-origin</span>:<span class="code-number">50%</span>;<span class="code-attribute">animation</span>:swiper-preloader-spin <span class="code-number">1s</span> infinite linear;<span class="code-attribute">box-sizing</span>:border-box;<span class="code-attribute">border</span>:<span class="code-number">4px</span> solid <span class="code-built_in">var</span>(--swiper-preloader-color,var(--swiper-theme-color));<span class="code-attribute">border-radius</span>:<span class="code-number">50%</span>;<span class="code-attribute">border-top-color</span>:transparent}<span class="hljs-selector-class">.swiper-lazy-preloader-white</span>{<span class="code-attribute">--swiper-preloader-color</span>:<span class="code-number">#fff</span>}<span class="hljs-selector-class">.swiper-lazy-preloader-black</span>{<span class="code-attribute">--swiper-preloader-color</span>:<span class="code-number">#000</span>}@<span class="code-keyword">keyframes</span> swiper-preloader-spin{100%{<span class="code-attribute">transform</span>:<span class="code-built_in">rotate</span>(360deg)}}<span class="hljs-selector-class">.swiper</span> <span class="hljs-selector-class">.swiper-notification</span>{<span class="code-attribute">position</span>:absolute;<span class="code-attribute">left</span>:<span class="code-number">0</span>;<span class="code-attribute">top</span>:<span class="code-number">0</span>;<span class="code-attribute">pointer-events</span>:none;<span class="code-attribute">opacity</span>:<span class="code-number">0</span>;<span class="code-attribute">z-index</span>:-<span class="code-number">1000</span>}<span class="hljs-selector-class">.swiper-free-mode</span>&gt;<span class="hljs-selector-class">.swiper-wrapper</span>{<span class="code-attribute">transition-timing-function</span>:ease-out;<span class="code-attribute">margin</span>:<span class="code-number">0</span> auto}<span class="hljs-selector-class">.swiper-grid</span>&gt;<span class="hljs-selector-class">.swiper-wrapper</span>{<span class="code-attribute">flex-wrap</span>:wrap}<span class="hljs-selector-class">.swiper-grid-column</span>&gt;<span class="hljs-selector-class">.swiper-wrapper</span>{<span class="code-attribute">flex-wrap</span>:wrap;<span class="code-attribute">flex-direction</span>:column}<span class="hljs-selector-class">.swiper-fade</span><span class="hljs-selector-class">.swiper-free-mode</span> <span class="hljs-selector-class">.swiper-slide</span>{<span class="code-attribute">transition-timing-function</span>:ease-out}<span class="hljs-selector-class">.swiper-fade</span> <span class="hljs-selector-class">.swiper-slide</span>{<span class="code-attribute">pointer-events</span>:none;<span class="code-attribute">transition-property</span>:opacity}<span class="hljs-selector-class">.swiper-fade</span> <span class="hljs-selector-class">.swiper-slide</span> <span class="hljs-selector-class">.swiper-slide</span>{<span class="code-attribute">pointer-events</span>:none}<span class="hljs-selector-class">.swiper-fade</span> <span class="hljs-selector-class">.swiper-slide-active</span>,<span class="hljs-selector-class">.swiper-fade</span> <span class="hljs-selector-class">.swiper-slide-active</span> <span class="hljs-selector-class">.swiper-slide-active</span>{<span class="code-attribute">pointer-events</span>:auto}<span class="hljs-selector-class">.swiper-cube</span>{<span class="code-attribute">overflow</span>:visible}<span class="hljs-selector-class">.swiper-cube</span> <span class="hljs-selector-class">.swiper-slide</span>{<span class="code-attribute">pointer-events</span>:none;<span class="code-attribute">-webkit-backface-visibility</span>:hidden;<span class="code-attribute">backface-visibility</span>:hidden;<span class="code-attribute">z-index</span>:<span class="code-number">1</span>;<span class="code-attribute">visibility</span>:hidden;<span class="code-attribute">transform-origin</span>:<span class="code-number">0</span> <span class="code-number">0</span>;<span class="code-attribute">width</span>:<span class="code-number">100%</span>;<span class="code-attribute">height</span>:<span class="code-number">100%</span>}<span class="hljs-selector-class">.swiper-cube</span> <span class="hljs-selector-class">.swiper-slide</span> <span class="hljs-selector-class">.swiper-slide</span>{<span class="code-attribute">pointer-events</span>:none}<span class="hljs-selector-class">.swiper-cube</span><span class="hljs-selector-class">.swiper-rtl</span> <span class="hljs-selector-class">.swiper-slide</span>{<span class="code-attribute">transform-origin</span>:<span class="code-number">100%</span> <span class="code-number">0</span>}<span class="hljs-selector-class">.swiper-cube</span> <span class="hljs-selector-class">.swiper-slide-active</span>,<span class="hljs-selector-class">.swiper-cube</span> <span class="hljs-selector-class">.swiper-slide-active</span> <span class="hljs-selector-class">.swiper-slide-active</span>{<span class="code-attribute">pointer-events</span>:auto}<span class="hljs-selector-class">.swiper-cube</span> <span class="hljs-selector-class">.swiper-slide-active</span>,<span class="hljs-selector-class">.swiper-cube</span> <span class="hljs-selector-class">.swiper-slide-next</span>,<span class="hljs-selector-class">.swiper-cube</span> <span class="hljs-selector-class">.swiper-slide-next</span>+<span class="hljs-selector-class">.swiper-slide</span>,<span class="hljs-selector-class">.swiper-cube</span> <span class="hljs-selector-class">.swiper-slide-prev</span>{<span class="code-attribute">pointer-events</span>:auto;<span class="code-attribute">visibility</span>:visible}<span class="hljs-selector-class">.swiper-cube</span> <span class="hljs-selector-class">.swiper-slide-shadow-bottom</span>,<span class="hljs-selector-class">.swiper-cube</span> <span class="hljs-selector-class">.swiper-slide-shadow-left</span>,<span class="hljs-selector-class">.swiper-cube</span> <span class="hljs-selector-class">.swiper-slide-shadow-right</span>,<span class="hljs-selector-class">.swiper-cube</span> <span class="hljs-selector-class">.swiper-slide-shadow-top</span>{<span class="code-attribute">z-index</span>:<span class="code-number">0</span>;<span class="code-attribute">-webkit-backface-visibility</span>:hidden;<span class="code-attribute">backface-visibility</span>:hidden}<span class="hljs-selector-class">.swiper-cube</span> <span class="hljs-selector-class">.swiper-cube-shadow</span>{<span class="code-attribute">position</span>:absolute;<span class="code-attribute">left</span>:<span class="code-number">0</span>;<span class="code-attribute">bottom</span>:<span class="code-number">0px</span>;<span class="code-attribute">width</span>:<span class="code-number">100%</span>;<span class="code-attribute">height</span>:<span class="code-number">100%</span>;<span class="code-attribute">opacity</span>:.<span class="code-number">6</span>;<span class="code-attribute">z-index</span>:<span class="code-number">0</span>}<span class="hljs-selector-class">.swiper-cube</span> <span class="hljs-selector-class">.swiper-cube-shadow</span><span class="hljs-selector-pseudo">:before</span>{<span class="code-attribute">content</span>:<span class="code-string">''</span>;<span class="code-attribute">background</span>:<span class="code-number">#000</span>;<span class="code-attribute">position</span>:absolute;<span class="code-attribute">left</span>:<span class="code-number">0</span>;<span class="code-attribute">top</span>:<span class="code-number">0</span>;<span class="code-attribute">bottom</span>:<span class="code-number">0</span>;<span class="code-attribute">right</span>:<span class="code-number">0</span>;<span class="code-attribute">filter</span>:<span class="code-built_in">blur</span>(50px)}<span class="hljs-selector-class">.swiper-flip</span>{<span class="code-attribute">overflow</span>:visible}<span class="hljs-selector-class">.swiper-flip</span> <span class="hljs-selector-class">.swiper-slide</span>{<span class="code-attribute">pointer-events</span>:none;<span class="code-attribute">-webkit-backface-visibility</span>:hidden;<span class="code-attribute">backface-visibility</span>:hidden;<span class="code-attribute">z-index</span>:<span class="code-number">1</span>}<span class="hljs-selector-class">.swiper-flip</span> <span class="hljs-selector-class">.swiper-slide</span> <span class="hljs-selector-class">.swiper-slide</span>{<span class="code-attribute">pointer-events</span>:none}<span class="hljs-selector-class">.swiper-flip</span> <span class="hljs-selector-class">.swiper-slide-active</span>,<span class="hljs-selector-class">.swiper-flip</span> <span class="hljs-selector-class">.swiper-slide-active</span> <span class="hljs-selector-class">.swiper-slide-active</span>{<span class="code-attribute">pointer-events</span>:auto}<span class="hljs-selector-class">.swiper-flip</span> <span class="hljs-selector-class">.swiper-slide-shadow-bottom</span>,<span class="hljs-selector-class">.swiper-flip</span> <span class="hljs-selector-class">.swiper-slide-shadow-left</span>,<span class="hljs-selector-class">.swiper-flip</span> <span class="hljs-selector-class">.swiper-slide-shadow-right</span>,<span class="hljs-selector-class">.swiper-flip</span> <span class="hljs-selector-class">.swiper-slide-shadow-top</span>{<span class="code-attribute">z-index</span>:<span class="code-number">0</span>;<span class="code-attribute">-webkit-backface-visibility</span>:hidden;<span class="code-attribute">backface-visibility</span>:hidden}<span class="hljs-selector-class">.swiper-creative</span> <span class="hljs-selector-class">.swiper-slide</span>{<span class="code-attribute">-webkit-backface-visibility</span>:hidden;<span class="code-attribute">backface-visibility</span>:hidden;<span class="code-attribute">overflow</span>:hidden;<span class="code-attribute">transition-property</span>:transform,opacity,height}<span class="hljs-selector-class">.swiper-cards</span>{<span class="code-attribute">overflow</span>:visible}<span class="hljs-selector-class">.swiper-cards</span> <span class="hljs-selector-class">.swiper-slide</span>{<span class="code-attribute">transform-origin</span>:center bottom;<span class="code-attribute">-webkit-backface-visibility</span>:hidden;<span class="code-attribute">backface-visibility</span>:hidden;<span class="code-attribute">overflow</span>:hidden}</code></td></tr><tr><td class="css-a4x74f">\</td><style data-emotion-css="jhhjkz">.css-jhhjkz{padding-left:10px;color:rgba(27,31,35,.3);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;}</style><td class="css-jhhjkz">No newline at end of file</td></tr></tbody></table></div></div></div></div><style data-emotion-css="1teho9j">.css-1teho9j{margin-top:5rem;background:black;color:#aaa;}</style><footer class="css-1teho9j"><style data-emotion-css="1ui8put">.css-1ui8put{max-width:940px;padding:10px 20px;margin:0 auto;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;}</style><div class="css-1ui8put"><p><span>Build: <!-- -->a7ebffa</span></p><p><span>© <!-- -->2023<!-- --> UNPKG</span></p><style data-emotion-css="la3nd4">.css-la3nd4{font-size:1.5rem;}</style><p class="css-la3nd4"><style data-emotion-css="bogekj">.css-bogekj{color:#aaa;display:inline-block;}.css-bogekj:hover{color:white;}</style><a href="https://twitter.com/unpkg" class="css-bogekj"><style data-emotion-css="i6dzq1">.css-i6dzq1{vertical-align:text-bottom;}</style><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" class="css-i6dzq1" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"></path></svg></a><style data-emotion-css="3czw03">.css-3czw03{color:#aaa;display:inline-block;margin-left:1rem;}.css-3czw03:hover{color:white;}</style><a href="https://github.com/mjackson/unpkg" class="css-3czw03"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 496 512" class="css-i6dzq1" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z"></path></svg></a></p></div></footer></div><script src="/react@16.8.6/umd/react.production.min.js"></script><script src="/react-dom@16.8.6/umd/react-dom.production.min.js"></script><script src="/@emotion/core@10.0.6/dist/core.umd.min.js"></script><script>'use strict';(function(t,A,c){function w(){w=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var e=arguments[b],c;for(c in e)Object.prototype.hasOwnProperty.call(e,c)&&(a[c]=e[c])}return a};return w.apply(this,arguments)}function P(a,b){if(null==a)return{};var e={},c=Object.keys(a),d;for(d=0;d<c.length;d++){var h=c[d];0<=b.indexOf(h)||(e[h]=a[h])}return e}function Q(a,b){b||(b=a.slice(0));a.raw=b;return a}function R(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,
"default")?a["default"]:a}function D(a,b){return b={exports:{}},a(b,b.exports),b.exports}function J(a,b,e,c,d){for(var g in a)if(ua(a,g)){try{if("function"!==typeof a[g]){var r=Error((c||"React class")+": "+e+" type `"+g+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof a[g]+"`.");r.name="Invariant Violation";throw r;}var k=a[g](b,g,c,e,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(q){k=q}!k||k instanceof Error||K((c||"React class")+": type specification of "+
e+" `"+g+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+typeof k+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).");if(k instanceof Error&&!(k.message in L)){L[k.message]=!0;var B=d?d():"";K("Failed "+e+" type: "+k.message+(null!=B?B:""))}}}function G(){return null}function S(a){var b,e=a.children;a=a.css;return c.jsx("div",{css:w((b={border:"1px solid #dfe2e5",
borderRadius:3},b["@media (max-width: 700px)"]={borderRightWidth:0,borderLeftWidth:0},b),a)},e)}function T(a){var b,e=a.children;a=a.css;return c.jsx("div",{css:w((b={padding:10,background:"#f6f8fa",color:"#424242",border:"1px solid #d1d5da",borderTopLeftRadius:3,borderTopRightRadius:3,margin:"-1px -1px 0",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between"},b["@media (max-width: 700px)"]={paddingRight:20,paddingLeft:20},b),a)},e)}function U(a){return a&&a.map(function(a,
c){return t.createElement(a.tag,z({key:c},a.attr),U(a.child))})}function E(a){return function(b){return t.createElement(va,z({attr:z({},a.attr)},b),U(a.child))}}function va(a){var b=function(b){var c=a.size||b.size||"1em";if(b.className)var e=b.className;a.className&&(e=(e?e+" ":"")+a.className);var h=a.attr,r=a.title,k=["attr","title"],B={},q;for(q in a)Object.prototype.hasOwnProperty.call(a,q)&&0>k.indexOf(q)&&(B[q]=a[q]);if(null!=a&&"function"===typeof Object.getOwnPropertySymbols){var p=0;for(q=
Object.getOwnPropertySymbols(a);p<q.length;p++)0>k.indexOf(q[p])&&(B[q[p]]=a[q[p]])}return t.createElement("svg",z({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},b.attr,h,B,{className:e,style:z({color:a.color||b.color},b.style,a.style),height:c,width:c,xmlns:"http://www.w3.org/2000/svg"}),r&&t.createElement("title",null,r),a.children)};return void 0!==V?t.createElement(V.Consumer,null,function(a){return b(a)}):b(W)}function F(a,b){var e=b.css;b=P(b,["css"]);return c.jsx(a,w({css:w({},
e,{verticalAlign:"text-bottom"})},b))}function wa(a){return F(X,a)}function xa(a){return F(Y,a)}function ya(a){return F(Z,a)}function za(a){return F(aa,a)}function Aa(a){return F(ba,a)}function ca(a){var b=a.path,e=a.details,g=Object.keys(e).reduce(function(a,b){var c=a.subdirs,g=a.files;b=e[b];"directory"===b.type?c.push(b):"file"===b.type&&g.push(b);return a},{subdirs:[],files:[]});a=g.subdirs;g=g.files;a.sort(da("path"));g.sort(da("path"));var d=[];"/"!==b&&d.push(c.jsx("tr",{key:".."},c.jsx("td",
{css:M}),c.jsx("td",{css:y},c.jsx("a",{title:"Parent directory",href:"../",css:N},"..")),c.jsx("td",{css:y}),c.jsx("td",{css:O})));a.forEach(function(a){a=a.path.substr(1<b.length?b.length+1:1);var e=a+"/";d.push(c.jsx("tr",{key:a},c.jsx("td",{css:M},c.jsx(ya,null)),c.jsx("td",{css:y},c.jsx("a",{title:a,href:e,css:N},a)),c.jsx("td",{css:y},"-"),c.jsx("td",{css:O},"-")))});g.forEach(function(a){var e=a.size,g=a.contentType;a=a.path.substr(1<b.length?b.length+1:1);d.push(c.jsx("tr",{key:a},c.jsx("td",
{css:M},"text/plain"===g||"text/markdown"===g?c.jsx(wa,null):c.jsx(xa,null)),c.jsx("td",{css:y},c.jsx("a",{title:a,href:a,css:N},a)),c.jsx("td",{css:y},ea(e)),c.jsx("td",{css:O},g)))});var h=[];0<g.length&&h.push(g.length+" file"+(1===g.length?"":"s"));0<a.length&&h.push(a.length+" folder"+(1===a.length?"":"s"));return c.jsx(S,null,c.jsx(T,null,c.jsx("span",null,h.join(", "))),c.jsx("table",{css:{width:"100%",borderCollapse:"collapse",borderRadius:2,background:"#fff","@media (max-width: 700px)":{"& th + th + th + th, & td + td + td + td":{display:"none"}},
"& tr:first-of-type td":{borderTop:0}}},c.jsx("thead",null,c.jsx("tr",null,c.jsx("th",null,c.jsx(H,null,"Icon")),c.jsx("th",null,c.jsx(H,null,"Name")),c.jsx("th",null,c.jsx(H,null,"Size")),c.jsx("th",null,c.jsx(H,null,"Content Type")))),c.jsx("tbody",null,d)))}function Ba(a){a=a.split("/");return a[a.length-1]}function Ca(a){var b=a.uri;return c.jsx("div",{css:{padding:20,textAlign:"center"}},c.jsx("img",{alt:Ba(a.path),src:b}))}function Da(a){a=a.highlights.slice(0);var b=a.length&&""===a[a.length-
1];b&&a.pop();return c.jsx("div",{className:"code-listing",css:{overflowX:"auto",overflowY:"hidden",paddingTop:5,paddingBottom:5}},c.jsx("table",{css:{border:"none",borderCollapse:"collapse",borderSpacing:0}},c.jsx("tbody",null,a.map(function(a,b){var e=b+1;return c.jsx("tr",{key:b},c.jsx("td",{id:"L"+e,css:{paddingLeft:10,paddingRight:10,color:"rgba(27,31,35,.3)",textAlign:"right",verticalAlign:"top",width:"1%",minWidth:50,userSelect:"none"}},c.jsx("span",null,e)),c.jsx("td",{id:"LC"+e,css:{paddingLeft:10,
paddingRight:10,color:"#24292e",whiteSpace:"pre"}},c.jsx("code",{dangerouslySetInnerHTML:{__html:a}})))}),!b&&c.jsx("tr",{key:"no-newline"},c.jsx("td",{css:{paddingLeft:10,paddingRight:10,color:"rgba(27,31,35,.3)",textAlign:"right",verticalAlign:"top",width:"1%",minWidth:50,userSelect:"none"}},"\\"),c.jsx("td",{css:{paddingLeft:10,color:"rgba(27,31,35,.3)",userSelect:"none"}},"No newline at end of file")))))}function Ea(){return c.jsx("div",{css:{padding:20}},c.jsx("p",{css:{textAlign:"center"}},
"No preview available."))}function fa(a){var b=a.packageName,e=a.packageVersion,g=a.path;a=a.details;var d=a.highlights,h=a.uri,r=a.language;return c.jsx(S,null,c.jsx(T,null,c.jsx("span",null,ea(a.size)),c.jsx("span",null,r),c.jsx("span",null,c.jsx("a",{href:"/"+b+"@"+e+g,css:{display:"inline-block",marginLeft:8,padding:"2px 8px",textDecoration:"none",fontWeight:600,fontSize:"0.9rem",color:"#24292e",backgroundColor:"#eff3f6",border:"1px solid rgba(27,31,35,.2)",borderRadius:3,":hover":{backgroundColor:"#e6ebf1",
borderColor:"rgba(27,31,35,.35)"},":active":{backgroundColor:"#e9ecef",borderColor:"rgba(27,31,35,.35)",boxShadow:"inset 0 0.15em 0.3em rgba(27,31,35,.15)"}}},"View Raw"))),d?c.jsx(Da,{highlights:d}):h?c.jsx(Ca,{path:g,uri:h}):c.jsx(Ea,null))}function ha(){var a=Q(["\n  .code-listing {\n    background: #fbfdff;\n    color: #383a42;\n  }\n  .code-comment,\n  .code-quote {\n    color: #a0a1a7;\n    font-style: italic;\n  }\n  .code-doctag,\n  .code-keyword,\n  .code-link,\n  .code-formula {\n    color: #a626a4;\n  }\n  .code-section,\n  .code-name,\n  .code-selector-tag,\n  .code-deletion,\n  .code-subst {\n    color: #e45649;\n  }\n  .code-literal {\n    color: #0184bb;\n  }\n  .code-string,\n  .code-regexp,\n  .code-addition,\n  .code-attribute,\n  .code-meta-string {\n    color: #50a14f;\n  }\n  .code-built_in,\n  .code-class .code-title {\n    color: #c18401;\n  }\n  .code-attr,\n  .code-variable,\n  .code-template-variable,\n  .code-type,\n  .code-selector-class,\n  .code-selector-attr,\n  .code-selector-pseudo,\n  .code-number {\n    color: #986801;\n  }\n  .code-symbol,\n  .code-bullet,\n  .code-meta,\n  .code-selector-id,\n  .code-title {\n    color: #4078f2;\n  }\n  .code-emphasis {\n    font-style: italic;\n  }\n  .code-strong {\n    font-weight: bold;\n  }\n"]);
ha=function(){return a};return a}function ia(){var a=Q(["\n  html {\n    box-sizing: border-box;\n  }\n  *,\n  *:before,\n  *:after {\n    box-sizing: inherit;\n  }\n\n  html,\n  body,\n  #root {\n    height: 100%;\n    margin: 0;\n  }\n\n  body {\n    ","\n    font-size: 16px;\n    line-height: 1.5;\n    overflow-wrap: break-word;\n    background: white;\n    color: black;\n  }\n\n  code {\n    ","\n  }\n\n  th,\n  td {\n    padding: 0;\n  }\n\n  select {\n    font-size: inherit;\n  }\n\n  #root {\n    display: flex;\n    flex-direction: column;\n  }\n"]);
ia=function(){return a};return a}function ja(a){var b=a.css;a=P(a,["css"]);return c.jsx("a",w({},a,{css:w({color:"#0076ff",textDecoration:"none",":hover":{textDecoration:"underline"}},b)}))}function Fa(){return c.jsx("header",{css:{marginTop:"2rem"}},c.jsx("h1",{css:{textAlign:"center",fontSize:"3rem",letterSpacing:"0.05em"}},c.jsx("a",{href:"/",css:{color:"#000",textDecoration:"none"}},"UNPKG")))}function Ga(a){var b=a.packageName,e=a.packageVersion,g=a.availableVersions;a=a.filename;var d=[];if("/"===
a)d.push(b);else{var h="/browse/"+b+"@"+e;d.push(c.jsx(ja,{href:h+"/"},b));b=a.replace(/^\/+/,"").replace(/\/+$/,"").split("/");a=b.pop();b.forEach(function(a){h+="/"+a;d.push(c.jsx(ja,{href:h+"/"},a))});d.push(a)}return c.jsx("header",{css:{display:"flex",flexDirection:"row",alignItems:"center","@media (max-width: 700px)":{flexDirection:"column-reverse",alignItems:"flex-start"}}},c.jsx("h1",{css:{fontSize:"1.5rem",fontWeight:"normal",flex:1,wordBreak:"break-all"}},c.jsx("nav",null,d.map(function(a,
b,e){return c.jsx(t.Fragment,{key:b},0!==b&&c.jsx("span",{css:{paddingLeft:5,paddingRight:5}},"/"),b===e.length-1?c.jsx("strong",null,a):a)}))),c.jsx(Ha,{packageVersion:e,availableVersions:g,onChange:function(a){window.location.href=window.location.href.replace("@"+e,"@"+a)}}))}function Ha(a){var b=a.onChange;return c.jsx("p",{css:{marginLeft:20,"@media (max-width: 700px)":{marginLeft:0,marginBottom:0}}},c.jsx("label",null,"Version:"," ",c.jsx("select",{name:"version",defaultValue:a.packageVersion,
onChange:function(a){b&&b(a.target.value)},css:{appearance:"none",cursor:"pointer",padding:"4px 24px 4px 8px",fontWeight:600,fontSize:"0.9em",color:"#24292e",border:"1px solid rgba(27,31,35,.2)",borderRadius:3,backgroundColor:"#eff3f6",backgroundImage:"url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAKCAYAAAC9vt6cAAAAAXNSR0IArs4c6QAAARFJREFUKBVjZAACNS39RhBNKrh17WI9o4quoT3Dn78HSNUMUs/CzOTI/O7Vi4dCYpJ3/jP+92BkYGAlyiBGhm8MjIxJt65e3MQM0vDu9YvLYmISILYZELOBxHABRkaGr0yMzF23r12YDFIDNgDEePv65SEhEXENBkYGFSAXuyGMjF8Z/jOsvX3tYiFIDwgwQSgIaaijnvj/P8M5IO8HsjiY/f//D4b//88A1SQhywG9jQr09PS4v/1mPAeUUPzP8B8cJowMjL+Bqu6xMQmaXL164AuyDgwDQJLa2qYSP//9vARkCoMVMzK8YeVkNbh+9uxzMB+JwGoASF5Vx0jz/98/18BqmZi171w9D2EjaaYKEwAEK00XQLdJuwAAAABJRU5ErkJggg==)",
backgroundPosition:"right 8px center",backgroundRepeat:"no-repeat",backgroundSize:"auto 25%",":hover":{backgroundColor:"#e6ebf1",borderColor:"rgba(27,31,35,.35)"},":active":{backgroundColor:"#e9ecef",borderColor:"rgba(27,31,35,.35)",boxShadow:"inset 0 0.15em 0.3em rgba(27,31,35,.15)"}}},a.availableVersions.map(function(a){return c.jsx("option",{key:a,value:a},a)}))))}function Ia(a){var b=a.packageName,e=a.packageVersion;a=a.target;return"directory"===a.type?c.jsx(ca,{path:a.path,details:a.details}):
"file"===a.type?c.jsx(fa,{packageName:b,packageVersion:e,path:a.path,details:a.details}):null}function ka(a){var b=a.packageName,e=a.packageVersion,g=a.availableVersions;g=void 0===g?[]:g;var d=a.filename;a=a.target;return c.jsx(t.Fragment,null,c.jsx(c.Global,{styles:Ja}),c.jsx(c.Global,{styles:Ka}),c.jsx("div",{css:{flex:"1 0 auto"}},c.jsx("div",{css:{maxWidth:940,padding:"0 20px",margin:"0 auto"}},c.jsx(Fa,null)),c.jsx("div",{css:{maxWidth:940,padding:"0 20px",margin:"0 auto"}},c.jsx(Ga,{packageName:b,
packageVersion:e,availableVersions:g,filename:d})),c.jsx("div",{css:{maxWidth:940,padding:"0 20px",margin:"0 auto","@media (max-width: 700px)":{padding:0,margin:0}}},c.jsx(Ia,{packageName:b,packageVersion:e,target:a}))),c.jsx("footer",{css:{marginTop:"5rem",background:"black",color:"#aaa"}},c.jsx("div",{css:{maxWidth:940,padding:"10px 20px",margin:"0 auto",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between"}},c.jsx("p",null,c.jsx("span",null,"Build: ","a7ebffa")),
c.jsx("p",null,c.jsx("span",null,"\u00a9 ",(new Date).getFullYear()," UNPKG")),c.jsx("p",{css:{fontSize:"1.5rem"}},c.jsx("a",{href:"https://twitter.com/unpkg",css:{color:"#aaa",display:"inline-block",":hover":{color:"white"}}},c.jsx(za,null)),c.jsx("a",{href:"https://github.com/mjackson/unpkg",css:{color:"#aaa",display:"inline-block",":hover":{color:"white"},marginLeft:"1rem"}},c.jsx(Aa,null))))))}var la="default"in t?t["default"]:t;A=A&&A.hasOwnProperty("default")?A["default"]:A;var La="undefined"!==
typeof globalThis?globalThis:"undefined"!==typeof window?window:"undefined"!==typeof global?global:"undefined"!==typeof self?self:{},m=D(function(a,b){function c(a){if("object"===typeof a&&null!==a){var b=a.$$typeof;switch(b){case d:switch(a=a.type,a){case l:case f:case r:case m:case k:case v:return a;default:switch(a=a&&a.$$typeof,a){case p:case n:case q:return a;default:return b}}case x:case u:case h:return b}}}function g(a){return c(a)===f}Object.defineProperty(b,"__esModule",{value:!0});var d=
(a="function"===typeof Symbol&&Symbol.for)?Symbol.for("react.element"):60103,h=a?Symbol.for("react.portal"):60106,r=a?Symbol.for("react.fragment"):60107,k=a?Symbol.for("react.strict_mode"):60108,m=a?Symbol.for("react.profiler"):60114,q=a?Symbol.for("react.provider"):60109,p=a?Symbol.for("react.context"):60110,l=a?Symbol.for("react.async_mode"):60111,f=a?Symbol.for("react.concurrent_mode"):60111,n=a?Symbol.for("react.forward_ref"):60112,v=a?Symbol.for("react.suspense"):60113,u=a?Symbol.for("react.memo"):
60115,x=a?Symbol.for("react.lazy"):60116;b.typeOf=c;b.AsyncMode=l;b.ConcurrentMode=f;b.ContextConsumer=p;b.ContextProvider=q;b.Element=d;b.ForwardRef=n;b.Fragment=r;b.Lazy=x;b.Memo=u;b.Portal=h;b.Profiler=m;b.StrictMode=k;b.Suspense=v;b.isValidElementType=function(a){return"string"===typeof a||"function"===typeof a||a===r||a===f||a===m||a===k||a===v||"object"===typeof a&&null!==a&&(a.$$typeof===x||a.$$typeof===u||a.$$typeof===q||a.$$typeof===p||a.$$typeof===n)};b.isAsyncMode=function(a){return g(a)||
c(a)===l};b.isConcurrentMode=g;b.isContextConsumer=function(a){return c(a)===p};b.isContextProvider=function(a){return c(a)===q};b.isElement=function(a){return"object"===typeof a&&null!==a&&a.$$typeof===d};b.isForwardRef=function(a){return c(a)===n};b.isFragment=function(a){return c(a)===r};b.isLazy=function(a){return c(a)===x};b.isMemo=function(a){return c(a)===u};b.isPortal=function(a){return c(a)===h};b.isProfiler=function(a){return c(a)===m};b.isStrictMode=function(a){return c(a)===k};b.isSuspense=
function(a){return c(a)===v}});R(m);var na=D(function(a,b){(function(){function a(a){if("object"===typeof a&&null!==a){var b=a.$$typeof;switch(b){case h:switch(a=a.type,a){case f:case n:case k:case q:case m:case u:return a;default:switch(a=a&&a.$$typeof,a){case l:case v:case p:return a;default:return b}}case I:case x:case r:return b}}}function c(b){return a(b)===n}Object.defineProperty(b,"__esModule",{value:!0});var d="function"===typeof Symbol&&Symbol.for,h=d?Symbol.for("react.element"):60103,r=
d?Symbol.for("react.portal"):60106,k=d?Symbol.for("react.fragment"):60107,m=d?Symbol.for("react.strict_mode"):60108,q=d?Symbol.for("react.profiler"):60114,p=d?Symbol.for("react.provider"):60109,l=d?Symbol.for("react.context"):60110,f=d?Symbol.for("react.async_mode"):60111,n=d?Symbol.for("react.concurrent_mode"):60111,v=d?Symbol.for("react.forward_ref"):60112,u=d?Symbol.for("react.suspense"):60113,x=d?Symbol.for("react.memo"):60115,I=d?Symbol.for("react.lazy"):60116;d=function(){};var Ma=function(a){for(var b=
arguments.length,f=Array(1<b?b-1:0),c=1;c<b;c++)f[c-1]=arguments[c];var n=0;b="Warning: "+a.replace(/%s/g,function(){return f[n++]});"undefined"!==typeof console&&console.warn(b);try{throw Error(b);}catch(Xa){}},Na=d=function(a,b){if(void 0===b)throw Error("`lowPriorityWarning(condition, format, ...args)` requires a warning message argument");if(!a){for(var f=arguments.length,c=Array(2<f?f-2:0),n=2;n<f;n++)c[n-2]=arguments[n];Ma.apply(void 0,[b].concat(c))}},ma=!1;b.typeOf=a;b.AsyncMode=f;b.ConcurrentMode=
n;b.ContextConsumer=l;b.ContextProvider=p;b.Element=h;b.ForwardRef=v;b.Fragment=k;b.Lazy=I;b.Memo=x;b.Portal=r;b.Profiler=q;b.StrictMode=m;b.Suspense=u;b.isValidElementType=function(a){return"string"===typeof a||"function"===typeof a||a===k||a===n||a===q||a===m||a===u||"object"===typeof a&&null!==a&&(a.$$typeof===I||a.$$typeof===x||a.$$typeof===p||a.$$typeof===l||a.$$typeof===v)};b.isAsyncMode=function(b){ma||(ma=!0,Na(!1,"The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API."));
return c(b)||a(b)===f};b.isConcurrentMode=c;b.isContextConsumer=function(b){return a(b)===l};b.isContextProvider=function(b){return a(b)===p};b.isElement=function(a){return"object"===typeof a&&null!==a&&a.$$typeof===h};b.isForwardRef=function(b){return a(b)===v};b.isFragment=function(b){return a(b)===k};b.isLazy=function(b){return a(b)===I};b.isMemo=function(b){return a(b)===x};b.isPortal=function(b){return a(b)===r};b.isProfiler=function(b){return a(b)===q};b.isStrictMode=function(b){return a(b)===
m};b.isSuspense=function(b){return a(b)===u}})()});R(na);var oa=D(function(a){a.exports=na}),pa=Object.getOwnPropertySymbols,Oa=Object.prototype.hasOwnProperty,Pa=Object.prototype.propertyIsEnumerable,Qa=function(){try{if(!Object.assign)return!1;var a=new String("abc");a[5]="de";if("5"===Object.getOwnPropertyNames(a)[0])return!1;var b={};for(a=0;10>a;a++)b["_"+String.fromCharCode(a)]=a;if("**********"!==Object.getOwnPropertyNames(b).map(function(a){return b[a]}).join(""))return!1;var c={};"abcdefghijklmnopqrst".split("").forEach(function(a){c[a]=
a});return"abcdefghijklmnopqrst"!==Object.keys(Object.assign({},c)).join("")?!1:!0}catch(g){return!1}}()?Object.assign:function(a,b){if(null===a||void 0===a)throw new TypeError("Object.assign cannot be called with null or undefined");var c=Object(a);for(var g,d=1;d<arguments.length;d++){var h=Object(arguments[d]);for(var r in h)Oa.call(h,r)&&(c[r]=h[r]);if(pa){g=pa(h);for(var k=0;k<g.length;k++)Pa.call(h,g[k])&&(c[g[k]]=h[g[k]])}}return c},K=function(){},L={},ua=Function.call.bind(Object.prototype.hasOwnProperty);
K=function(a){a="Warning: "+a;"undefined"!==typeof console&&console.error(a);try{throw Error(a);}catch(b){}};J.resetWarningCache=function(){L={}};var Ra=Function.call.bind(Object.prototype.hasOwnProperty),C=function(){};C=function(a){a="Warning: "+a;"undefined"!==typeof console&&console.error(a);try{throw Error(a);}catch(b){}};var Sa=function(a,b){function c(a,b){return a===b?0!==a||1/a===1/b:a!==a&&b!==b}function g(a){this.message=a;this.stack=""}function d(a){function c(c,n,v,d,e,u,h){d=d||"<<anonymous>>";
u=u||v;if("SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"!==h){if(b)throw c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types"),c.name="Invariant Violation",c;"undefined"!==typeof console&&(h=d+":"+v,!f[h]&&3>l&&(C("You are manually calling a React.PropTypes validation function for the `"+u+"` prop on `"+d+"`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details."),
f[h]=!0,l++))}return null==n[v]?c?null===n[v]?new g("The "+e+" `"+u+"` is marked as required "+("in `"+d+"`, but its value is `null`.")):new g("The "+e+" `"+u+"` is marked as required in "+("`"+d+"`, but its value is `undefined`.")):null:a(n,v,d,e,u)}var f={},l=0,d=c.bind(null,!1);d.isRequired=c.bind(null,!0);return d}function h(a){return d(function(b,c,f,d,l,e){b=b[c];return k(b)!==a?(b=m(b),new g("Invalid "+d+" `"+l+"` of type "+("`"+b+"` supplied to `"+f+"`, expected ")+("`"+a+"`."))):null})}function r(b){switch(typeof b){case "number":case "string":case "undefined":return!0;
case "boolean":return!b;case "object":if(Array.isArray(b))return b.every(r);if(null===b||a(b))return!0;var c=b&&(p&&b[p]||b["@@iterator"]);var f="function"===typeof c?c:void 0;if(f)if(c=f.call(b),f!==b.entries)for(;!(b=c.next()).done;){if(!r(b.value))return!1}else for(;!(b=c.next()).done;){if((b=b.value)&&!r(b[1]))return!1}else return!1;return!0;default:return!1}}function k(a){var b=typeof a;return Array.isArray(a)?"array":a instanceof RegExp?"object":"symbol"===b||a&&("Symbol"===a["@@toStringTag"]||
"function"===typeof Symbol&&a instanceof Symbol)?"symbol":b}function m(a){if("undefined"===typeof a||null===a)return""+a;var b=k(a);if("object"===b){if(a instanceof Date)return"date";if(a instanceof RegExp)return"regexp"}return b}function q(a){a=m(a);switch(a){case "array":case "object":return"an "+a;case "boolean":case "date":case "regexp":return"a "+a;default:return a}}var p="function"===typeof Symbol&&Symbol.iterator,l={array:h("array"),bool:h("boolean"),func:h("function"),number:h("number"),object:h("object"),
string:h("string"),symbol:h("symbol"),any:d(G),arrayOf:function(a){return d(function(b,c,f,d,l){if("function"!==typeof a)return new g("Property `"+l+"` of component `"+f+"` has invalid PropType notation inside arrayOf.");b=b[c];if(!Array.isArray(b))return b=k(b),new g("Invalid "+d+" `"+l+"` of type "+("`"+b+"` supplied to `"+f+"`, expected an array."));for(c=0;c<b.length;c++){var n=a(b,c,f,d,l+"["+c+"]","SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");if(n instanceof Error)return n}return null})},
element:function(){return d(function(b,c,d,l,e){b=b[c];return a(b)?null:(b=k(b),new g("Invalid "+l+" `"+e+"` of type "+("`"+b+"` supplied to `"+d+"`, expected a single ReactElement.")))})}(),elementType:function(){return d(function(a,b,c,d,l){a=a[b];return oa.isValidElementType(a)?null:(a=k(a),new g("Invalid "+d+" `"+l+"` of type "+("`"+a+"` supplied to `"+c+"`, expected a single ReactElement type.")))})}(),instanceOf:function(a){return d(function(b,c,f,d,l){if(!(b[c]instanceof a)){var n=a.name||
"<<anonymous>>";b=b[c];b=b.constructor&&b.constructor.name?b.constructor.name:"<<anonymous>>";return new g("Invalid "+d+" `"+l+"` of type "+("`"+b+"` supplied to `"+f+"`, expected ")+("instance of `"+n+"`."))}return null})},node:function(){return d(function(a,b,c,d,l){return r(a[b])?null:new g("Invalid "+d+" `"+l+"` supplied to "+("`"+c+"`, expected a ReactNode."))})}(),objectOf:function(a){return d(function(b,c,f,d,l){if("function"!==typeof a)return new g("Property `"+l+"` of component `"+f+"` has invalid PropType notation inside objectOf.");
b=b[c];c=k(b);if("object"!==c)return new g("Invalid "+d+" `"+l+"` of type "+("`"+c+"` supplied to `"+f+"`, expected an object."));for(var n in b)if(Ra(b,n)&&(c=a(b,n,f,d,l+"."+n,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"),c instanceof Error))return c;return null})},oneOf:function(a){return Array.isArray(a)?d(function(b,f,d,l,e){b=b[f];for(f=0;f<a.length;f++)if(c(b,a[f]))return null;f=JSON.stringify(a,function(a,b){return"symbol"===m(b)?String(b):b});return new g("Invalid "+l+" `"+e+"` of value `"+
String(b)+"` "+("supplied to `"+d+"`, expected one of "+f+"."))}):(1<arguments.length?C("Invalid arguments supplied to oneOf, expected an array, got "+arguments.length+" arguments. A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z])."):C("Invalid argument supplied to oneOf, expected an array."),G)},oneOfType:function(a){if(!Array.isArray(a))return C("Invalid argument supplied to oneOfType, expected an instance of array."),G;for(var b=0;b<a.length;b++){var c=a[b];if("function"!==
typeof c)return C("Invalid argument supplied to oneOfType. Expected an array of check functions, but received "+q(c)+" at index "+b+"."),G}return d(function(b,c,f,d,l){for(var e=0;e<a.length;e++)if(null==(0,a[e])(b,c,f,d,l,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"))return null;return new g("Invalid "+d+" `"+l+"` supplied to "+("`"+f+"`."))})},shape:function(a){return d(function(b,c,d,l,f){b=b[c];c=k(b);if("object"!==c)return new g("Invalid "+l+" `"+f+"` of type `"+c+"` "+("supplied to `"+d+"`, expected `object`."));
for(var e in a)if(c=a[e])if(c=c(b,e,d,l,f+"."+e,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"))return c;return null})},exact:function(a){return d(function(b,c,d,l,f){var e=b[c],n=k(e);if("object"!==n)return new g("Invalid "+l+" `"+f+"` of type `"+n+"` "+("supplied to `"+d+"`, expected `object`."));n=Qa({},b[c],a);for(var h in n){n=a[h];if(!n)return new g("Invalid "+l+" `"+f+"` key `"+h+"` supplied to `"+d+"`.\nBad object: "+JSON.stringify(b[c],null,"  ")+"\nValid keys: "+JSON.stringify(Object.keys(a),
null,"  "));if(n=n(e,h,d,l,f+"."+h,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"))return n}return null})}};g.prototype=Error.prototype;l.checkPropTypes=J;l.resetWarningCache=J.resetWarningCache;return l.PropTypes=l};m=D(function(a){a.exports=Sa(oa.isElement,!0)});var Ta=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b],g;for(g in c)Object.prototype.hasOwnProperty.call(c,g)&&(a[g]=c[g])}return a},Ua={border:0,clip:"rect(0 0 0 0)",height:"1px",width:"1px",margin:"-1px",
padding:0,overflow:"hidden",position:"absolute"},H=function(a){return la.createElement("div",Ta({style:Ua},a))},qa=D(function(a){(function(b,c){a.exports=c()})(La,function(){function a(a){if(!a)return!0;if(!d(a)||0!==a.length)for(var b in a)if(q.call(a,b))return!1;return!0}function c(a){return"number"===typeof a||"[object Number]"===t.call(a)}function g(a){return"string"===typeof a||"[object String]"===t.call(a)}function d(a){return"object"===typeof a&&"number"===typeof a.length&&"[object Array]"===
t.call(a)}function h(a){var b=parseInt(a);return b.toString()===a?b:a}function m(b,d,e,k){c(d)&&(d=[d]);if(a(d))return b;if(g(d))return m(b,d.split("."),e,k);var f=h(d[0]);if(1===d.length)return d=b[f],void 0!==d&&k||(b[f]=e),d;void 0===b[f]&&(c(f)?b[f]=[]:b[f]={});return m(b[f],d.slice(1),e,k)}function k(b,f){c(f)&&(f=[f]);if(!a(b)){if(a(f))return b;if(g(f))return k(b,f.split("."));var e=h(f[0]),l=b[e];if(1===f.length)void 0!==l&&(d(b)?b.splice(e,1):delete b[e]);else if(void 0!==b[e])return k(b[e],
f.slice(1));return b}}var t=Object.prototype.toString,q=Object.prototype.hasOwnProperty,p={ensureExists:function(a,b,c){return m(a,b,c,!0)},set:function(a,b,c,d){return m(a,b,c,d)},insert:function(a,b,c,e){var f=p.get(a,b);e=~~e;d(f)||(f=[],p.set(a,b,f));f.splice(e,0,c)},empty:function(b,f){if(a(f))return b;if(!a(b)){var e,h;if(!(e=p.get(b,f)))return b;if(g(e))return p.set(b,f,"");if("boolean"===typeof e||"[object Boolean]"===t.call(e))return p.set(b,f,!1);if(c(e))return p.set(b,f,0);if(d(e))e.length=
0;else if("object"===typeof e&&"[object Object]"===t.call(e))for(h in e)q.call(e,h)&&delete e[h];else return p.set(b,f,null)}},push:function(a,b){var c=p.get(a,b);d(c)||(c=[],p.set(a,b,c));c.push.apply(c,Array.prototype.slice.call(arguments,2))},coalesce:function(a,b,c){for(var d,e=0,f=b.length;e<f;e++)if(void 0!==(d=p.get(a,b[e])))return d;return c},get:function(b,d,e){c(d)&&(d=[d]);if(a(d))return b;if(a(b))return e;if(g(d))return p.get(b,d.split("."),e);var f=h(d[0]);return 1===d.length?void 0===
b[f]?e:b[f]:p.get(b[f],d.slice(1),e)},del:function(a,b){return k(a,b)}};return p})});var ra=function(a){return function(b){return typeof b===a}};var Va=function(a,b){var c=1,g=b||function(a,b){return b};"-"===a[0]&&(c=-1,a=a.substr(1));return function(b,e){var d;b=g(a,qa.get(b,a));e=g(a,qa.get(e,a));b<e&&(d=-1);b>e&&(d=1);b===e&&(d=0);return d*c}};var da=function(){var a=Array.prototype.slice.call(arguments),b=a.filter(ra("string")),c=a.filter(ra("function"))[0];return function(a,d){for(var e=b.length,
g=0,k=0;0===g&&k<e;)g=Va(b[k],c)(a,d),k++;return g}};let sa="B kB MB GB TB PB EB ZB YB".split(" "),ta=(a,b)=>{let c=a;"string"===typeof b?c=a.toLocaleString(b):!0===b&&(c=a.toLocaleString());return c};var ea=(a,b)=>{if(!Number.isFinite(a))throw new TypeError(`Expected a finite number, got ${typeof a}: ${a}`);b=Object.assign({},b);if(b.signed&&0===a)return" 0 B";var c=0>a;let g=c?"-":b.signed?"+":"";c&&(a=-a);if(1>a)return a=ta(a,b.locale),g+a+" B";c=Math.min(Math.floor(Math.log10(a)/3),sa.length-
1);a=Number((a/Math.pow(1E3,c)).toPrecision(3));a=ta(a,b.locale);return g+a+" "+sa[c]},W={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},V=t.createContext&&t.createContext(W),z=function(){z=Object.assign||function(a){for(var b,c=1,g=arguments.length;c<g;c++){b=arguments[c];for(var d in b)Object.prototype.hasOwnProperty.call(b,d)&&(a[d]=b[d])}return a};return z.apply(this,arguments)},Y=function(a){return E({tag:"svg",attr:{viewBox:"0 0 12 16"},child:[{tag:"path",attr:{fillRule:"evenodd",
d:"M8.5 1H1c-.55 0-1 .45-1 1v12c0 .55.45 1 1 1h10c.55 0 1-.45 1-1V4.5L8.5 1zM11 14H1V2h7l3 3v9zM5 6.98L3.5 8.5 5 10l-.5 1L2 8.5 4.5 6l.5.98zM7.5 6L10 8.5 7.5 11l-.5-.98L8.5 8.5 7 7l.5-1z"}}]})(a)};Y.displayName="GoFileCode";var Z=function(a){return E({tag:"svg",attr:{viewBox:"0 0 14 16"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M13 4H7V3c0-.66-.31-1-1-1H1c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1V5c0-.55-.45-1-1-1zM6 4H1V3h5v1z"}}]})(a)};Z.displayName="GoFileDirectory";var X=function(a){return E({tag:"svg",
attr:{viewBox:"0 0 12 16"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M6 5H2V4h4v1zM2 8h7V7H2v1zm0 2h7V9H2v1zm0 2h7v-1H2v1zm10-7.5V14c0 .55-.45 1-1 1H1c-.55 0-1-.45-1-1V2c0-.55.45-1 1-1h7.5L12 4.5zM11 5L8 2H1v12h10V5z"}}]})(a)};X.displayName="GoFile";var ba=function(a){return E({tag:"svg",attr:{viewBox:"0 0 496 512"},child:[{tag:"path",attr:{d:"M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z"}}]})(a)};
ba.displayName="FaGithub";var aa=function(a){return E({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"}}]})(a)};
aa.displayName="FaTwitter";var N={color:"#0076ff",textDecoration:"none",":hover":{textDecoration:"underline"}},y={paddingTop:6,paddingRight:3,paddingBottom:6,paddingLeft:3,borderTop:"1px solid #eaecef"},M=w({},y,{color:"#424242",width:17,paddingRight:2,paddingLeft:10,"@media (max-width: 700px)":{paddingLeft:20}}),O=w({},y,{textAlign:"right",paddingRight:10,"@media (max-width: 700px)":{paddingRight:20}});ca.propTypes={path:m.string.isRequired,details:m.objectOf(m.shape({path:m.string.isRequired,type:m.oneOf(["directory",
"file"]).isRequired,contentType:m.string,integrity:m.string,size:m.number})).isRequired};fa.propTypes={path:m.string.isRequired,details:m.shape({contentType:m.string.isRequired,highlights:m.arrayOf(m.string),uri:m.string,integrity:m.string.isRequired,language:m.string.isRequired,size:m.number.isRequired}).isRequired};var Ja=c.css(ia(),'\nfont-family: -apple-system,\n  BlinkMacSystemFont,\n  "Segoe UI",\n  "Roboto",\n  "Oxygen",\n  "Ubuntu",\n  "Cantarell",\n  "Fira Sans",\n  "Droid Sans",\n  "Helvetica Neue",\n  sans-serif;\n',
"\nfont-family: Menlo,\n  Monaco,\n  Lucida Console,\n  Liberation Mono,\n  DejaVu Sans Mono,\n  Bitstream Vera Sans Mono,\n  Courier New,\n  monospace;\n"),Ka=c.css(ha()),Wa=m.shape({path:m.string.isRequired,type:m.oneOf(["directory","file"]).isRequired,details:m.object.isRequired});ka.propTypes={packageName:m.string.isRequired,packageVersion:m.string.isRequired,availableVersions:m.arrayOf(m.string),filename:m.string.isRequired,target:Wa.isRequired};A.hydrate(la.createElement(ka,window.__DATA__||
{}),document.getElementById("root"))})(React,ReactDOM,emotionCore);
</script></body></html>