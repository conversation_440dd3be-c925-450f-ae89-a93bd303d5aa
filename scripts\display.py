import re
from .colors import Colors as CLR

unicode_char_tupple = [
("20E3","\u20E3"),
("0038|20E3","8⃣"),
("0037|20E3","7⃣"),
("0036|20E3","6⃣"),
("0035|20E3","5⃣"),
("0034|20E3","4⃣"),
("0033|20E3","3⃣"),
("0032|20E3","2⃣"),
("3299","㊙"),
("3297","㊗"),
("0031|20E3","1⃣"),
("0030|20E3","0⃣"),
("303D","〽"),
("3030","〰"),
("2B55","⭕"),
("2B50","⭐"),
("2B1C","⬜"),
("2B1B","⬛"),
("2B07","⬇"),
("2B06","⬆"),
("2B05","⬅"),
("002A|20E3","*⃣"),
("2935","⤵"),
("2934","⤴"),
("27BF","➿"),
("27B0","➰"),
("27A1","➡"),
("2797","➗"),
("2796","➖"),
("2795","➕"),
("2764|FE0F|200D|1FA79","❤️‍🩹"),
("2764|FE0F|200D|1F525","❤️‍🔥"),
("2764","❤"),
("2763","❣"),
("2757","❗"),
("2755","❕"),
("2754","❔"),
("2753","❓"),
("274E","❎"),
("274C","❌"),
("2747","❇"),
("2744","❄"),
("2734","✴"),
("2733","✳"),
("2728","✨"),
("2721","✡"),
("271D","✝"),
("2716","✖"),
("2714","✔"),
("2712","✒"),
("270F","✏"),
("270D","✍"),
("270C","✌"),
("270B","✋"),
("270A","✊"),
("2709","✉"),
("2708","✈"),
("2705","✅"),
("2702","✂"),
("26FD","⛽"),
("26FA","⛺"),
("26F9|FE0F|200D|2642|FE0F","⛹️‍♂️"),
("26F9|FE0F|200D|2640|FE0F","⛹️‍♀️"),
("26F9","⛹"),
("26F8","⛸"),
("26F7","⛷"),
("26F5","⛵"),
("26F4","⛴"),
("26F3","⛳"),
("26F2","⛲"),
("26F1","⛱"),
("26F0","⛰"),
("26EA","⛪"),
("26D4","⛔"),
("26D3","⛓"),
("26D1","⛑"),
("26CF","⛏"),
("26CE","⛎"),
("26C8","⛈"),
("26C5","⛅"),
("26C4","⛄"),
("26BE","⚾"),
("26BD","⚽"),
("26B1","⚱"),
("26B0","⚰"),
("26AB","⚫"),
("26AA","⚪"),
("26A7","⚧"),
("26A1","⚡"),
("26A0","⚠"),
("269C","⚜"),
("269B","⚛"),
("2699","⚙"),
("2697","⚗"),
("2696","⚖"),
("2695","⚕"),
("2694","⚔"),
("2693","⚓"),
("2692","⚒"),
("267F","♿"),
("267E","♾"),
("267B","♻"),
("2668","♨"),
("2663156860520206","♣15686"),
("2653","♓"),
("2652","♒"),
("2651","♑"),
("2650","♐"),
("264F","♏"),
("264E","♎"),
("264D","♍"),
("264C","♌"),
("264B","♋"),
("264A","♊"),
("2649","♉"),
("2648","♈"),
("2642","♂"),
("2640","♀"),
("263A","☺"),
("2639","☹"),
("2638","☸"),
("262F","☯"),
("262E","☮"),
("262A","☪"),
("2626","☦"),
("2623","☣"),
("2622","☢"),
("2620","☠"),
("261D","☝"),
("2618","☘"),
("2615","☕"),
("2614","☔"),
("2611","☑"),
("260E","☎"),
("2604","☄"),
("2603","☃"),
("2602","☂"),
("2601","☁"),
("26000000000","☀00000"),
("2600","☀"),
("25FE","◾"),
("25FD","◽"),
("25FC","◼"),
("25FB","◻"),
("25C0","◀"),
("25B6","▶"),
("25AB","▫"),
("25AA","▪"),
("24C2","Ⓜ"),
("23FA","⏺"),
("23F9","⏹"),
("23F8","⏸"),
("23F3","⏳"),
("23F2","⏲"),
("23F1","⏱"),
("23F0","⏰"),
("23EF","⏯"),
("23EE","⏮"),
("23ED","⏭"),
("23EC","⏬"),
("23EB","⏫"),
("23EA","⏪"),
("23CF","⏏"),
("0023|20E3","#⃣"),
("2328","⌨"),
("231B","⌛"),
("231A","⌚"),
("23000000000","⌀00000"),
("21AA","↪"),
("21A9","↩"),
("2199","↙"),
("2198","↘"),
("2197","↗"),
("2196","↖"),
("2195","↕"),
("2139","ℹ"),
("2049","⁉"),
("203C","‼"),
("1FAF6","\U0001FAF6"),
("1FAF5","\U0001FAF5"),
("1FAF4","\U0001FAF4"),
("1FAF3","\U0001FAF3"),
("1FAF2","\U0001FAF2"),
("1FAF1","\U0001FAF1"),
("1FAF0","\U0001FAF0"),
("1FAE7","\U0001FAE7"),
("1FAE6","\U0001FAE6"),
("1FAE5","\U0001FAE5"),
("1FAE4","\U0001FAE4"),
("1FAE3","\U0001FAE3"),
("1FAE2","\U0001FAE2"),
("1FAE1","\U0001FAE1"),
("1FAE0","\U0001FAE0"),
("1FAD9","\U0001FAD9"),
("1FAD8","\U0001FAD8"),
("1FAD7","\U0001FAD7"),
("1FAD6","\U0001FAD6"),
("1FAD5","\U0001FAD5"),
("1FAD4","\U0001FAD4"),
("1FAD3","\U0001FAD3"),
("1FAD2","\U0001FAD2"),
("1FAD1","\U0001FAD1"),
("1FAD0","\U0001FAD0"),
("1FAC5","\U0001FAC5"),
("1FAC4","\U0001FAC4"),
("1FAC3","\U0001FAC3"),
("1FAC2","\U0001FAC2"),
("1FAC1","\U0001FAC1"),
("1FAC0","\U0001FAC0"),
("1FABA","\U0001FABA"),
("1FAB9","\U0001FAB9"),
("1FAB7","\U0001FAB7"),
("1FAB6","\U0001FAB6"),
("1FAB5","\U0001FAB5"),
("1FAB4","\U0001FAB4"),
("1FAB3","\U0001FAB3"),
("1FAB2","\U0001FAB2"),
("1FAB1","\U0001FAB1"),
("1FAB0","\U0001FAB0"),
("1FAAC","\U0001FAAC"),
("1FAAB","\U0001FAAB"),
("1FAA9","\U0001FAA9"),
("1FAA8","\U0001FAA8"),
("1FAA7","\U0001FAA7"),
("1FAA6","\U0001FAA6"),
("1FAA5","\U0001FAA5"),
("1FAA4","\U0001FAA4"),
("1FAA3","\U0001FAA3"),
("1FAA2","\U0001FAA2"),
("1FAA1","\U0001FAA1"),
("1FAA0","\U0001FAA0"),
("1FA9F","\U0001FA9F"),
("1FA9E","\U0001FA9E"),
("1FA9D","\U0001FA9D"),
("1FA9C","\U0001FA9C"),
("1FA9B","\U0001FA9B"),
("1FA9A","\U0001FA9A"),
("1FA99","\U0001FA99"),
("1FA98","\U0001FA98"),
("1FA97","\U0001FA97"),
("1FA96","\U0001FA96"),
("1FA95","🪕"),
("1FA94","🪔"),
("1FA93","🪓"),
("1FA92","🪒"),
("1FA91","🪑"),
("1FA90","🪐"),
("1FA86","\U0001FA86"),
("1FA85","\U0001FA85"),
("1FA84","\U0001FA84"),
("1FA83","\U0001FA83"),
("1FA82","🪂"),
("1FA81","🪁"),
("1FA80","🪀"),
("1FA7C","\U0001FA7C"),
("1FA7B","\U0001FA7B"),
("1FA7A","🩺"),
("1FA79","🩹"),
("1FA78","🩸"),
("1FA74","🩴"),
("1FA73","🩳"),
("1FA72","🩲"),
("1FA71","🩱"),
("1FA70","🩰"),
("1F9FF","🧿"),
("1F9FE","🧾"),
("1F9FD","🧽"),
("1F9FC","🧼"),
("1F9FB","🧻"),
("1F9FA","🧺"),
("1F9F9","🧹"),
("1F9F8","🧸"),
("1F9F7","🧷"),
("1F9F6","🧶"),
("1F9F5","🧵"),
("1F9F4","🧴"),
("1F9F3","🧳"),
("1F9F2","🧲"),
("1F9F1","🧱"),
("1F9F0","🧰"),
("1F9EF","🧯"),
("1F9EE","🧮"),
("1F9ED","🧭"),
("1F9EC","🧬"),
("1F9EB","🧫"),
("1F9EA","🧪"),
("1F9E9","🧩"),
("1F9E8","🧨"),
("1F9E7","🧧"),
("1F9E6","🧦"),
("1F9E5","🧥"),
("1F9E4","🧤"),
("1F9E3","🧣"),
("1F9E2","🧢"),
("1F9E1","🧡"),
("1F9E0","🧠"),
("1F9DF|200D|2642|FE0F","🧟‍♂️"),
("1F9DF|200D|2640|FE0F","🧟‍♀️"),
("1F9DF","🧟"),
("1F9DE|200D|2642|FE0F","🧞‍♂️"),
("1F9DE|200D|2640|FE0F","🧞‍♀️"),
("1F9DE","🧞"),
("1F9DD|200D|2642|FE0F","🧝‍♂️"),
("1F9DD|200D|2640|FE0F","🧝‍♀️"),
("1F9DD","🧝"),
("1F9DC|200D|2642|FE0F","🧜‍♂️"),
("1F9DC|200D|2640|FE0F","🧜‍♀️"),
("1F9DC","🧜"),
("1F9DB|200D|2642|FE0F","🧛‍♂️"),
("1F9DB|200D|2640|FE0F","🧛‍♀️"),
("1F9DB","🧛"),
("1F9DA|200D|2642|FE0F","🧚‍♂️"),
("1F9DA|200D|2640|FE0F","🧚‍♀️"),
("1F9DA","🧚"),
("1F9D9|200D|2642|FE0F","🧙‍♂️"),
("1F9D9|200D|2640|FE0F","🧙‍♀️"),
("1F9D9","🧙"),
("1F9D8|200D|2642|FE0F","🧘‍♂️"),
("1F9D8|200D|2640|FE0F","🧘‍♀️"),
("1F9D8","🧘"),
("1F9D7|200D|2642|FE0F","🧗‍♂️"),
("1F9D7|200D|2640|FE0F","🧗‍♀️"),
("1F9D7","🧗"),
("1F9D6|200D|2642|FE0F","🧖‍♂️"),
("1F9D6|200D|2640|FE0F","🧖‍♀️"),
("1F9D6","🧖"),
("1F9D5","🧕"),
("1F9D4|200D|2642|FE0F","🧔‍♂️"),
("1F9D4|200D|2640|FE0F","🧔‍♀️"),
("1F9D4","🧔"),
("1F9D3","🧓"),
("1F9D2","🧒"),
("1F9D1|200D|2708|FE0F","🧑‍✈️"),
("1F9D1|200D|2696|FE0F","🧑‍⚖️"),
("1F9D1|200D|2695|FE0F","🧑‍⚕️"),
("1F9D1|200D|1F9BD","🧑‍🦽"),
("1F9D1|200D|1F9BC","🧑‍🦼"),
("1F9D1|200D|1F9B3","🧑‍🦳"),
("1F9D1|200D|1F9B2","🧑‍🦲"),
("1F9D1|200D|1F9B1","🧑‍🦱"),
("1F9D1|200D|1F9B0","🧑‍🦰"),
("1F9D1|200D|1F9AF","🧑‍🦯"),
("1F9D1|200D|1F91D|200D|1F9D1","🧑‍🤝‍"),
("1F9D1|200D|1F692","🧑‍🚒"),
("1F9D1|200D|1F680","🧑‍🚀"),
("1F9D1|200D|1F52C","🧑‍🔬"),
("1F9D1|200D|1F527","🧑‍🔧"),
("1F9D1|200D|1F4BC","🧑‍💼"),
("1F9D1|200D|1F4BB","🧑‍💻"),
("1F9D1|200D|1F3ED","🧑‍🏭"),
("1F9D1|200D|1F3EB","🧑‍🏫"),
("1F9D1|200D|1F3A8","🧑‍🎨"),
("1F9D1|200D|1F3A4","🧑‍🎤"),
("1F9D1|200D|1F393","🧑‍🎓"),
("1F9D1|200D|1F384","🧑‍🎄"),
("1F9D1|200D|1F37C","🧑‍🍼"),
("1F9D1|200D|1F373","🧑‍🍳"),
("1F9D1|200D|1F33E","🧑‍🌾"),
("1F9D1","🧑"),
("1F9D0","🧐"),
("1F9CF|200D|2642|FE0F","🧏‍♂️"),
("1F9CF|200D|2640|FE0F","🧏‍♀️"),
("1F9CF","🧏"),
("1F9CE|200D|2642|FE0F","🧎‍♂️"),
("1F9CE|200D|2640|FE0F","🧎‍♀️"),
("1F9CE","🧎"),
("1F9CD|200D|2642|FE0F","🧍‍♂️"),
("1F9CD|200D|2640|FE0F","🧍‍♀️"),
("1F9CD","🧍"),
("1F9CC","\U0001F9CC"),
("1F9CB","\U0001F9CB"),
("1F9CA","🧊"),
("1F9C9","🧉"),
("1F9C8","🧈"),
("1F9C7","🧇"),
("1F9C6","🧆"),
("1F9C5","🧅"),
("1F9C4","🧄"),
("1F9C3","🧃"),
("1F9C2","🧂"),
("1F9C1","🧁"),
("1F9C0","🧀"),
("1F9BF","🦿"),
("1F9BE","🦾"),
("1F9BD","🦽"),
("1F9BC","🦼"),
("1F9BB","🦻"),
("1F9BA","🦺"),
("1F9B9|200D|2642|FE0F","🦹‍♂️"),
("1F9B9|200D|2640|FE0F","🦹‍♀️"),
("1F9B9","🦹"),
("1F9B8|200D|2642|FE0F","🦸‍♂️"),
("1F9B8|200D|2640|FE0F","🦸‍♀️"),
("1F9B8","🦸"),
("1F9B7","🦷"),
("1F9B6","🦶"),
("1F9B5","🦵"),
("1F9B4","🦴"),
("1F9B3","🦳"),
("1F9B2","🦲"),
("1F9B1","🦱"),
("1F9B0","🦰"),
("1F9AF","🦯"),
("1F9AE","🦮"),
("1F9AD","🦭"),
("1F9AC","🦬"),
("1F9AB","🦫"),
("1F9AA","🦪"),
("1F9A9","🦩"),
("1F9A8","🦨"),
("1F9A7","🦧"),
("1F9A6","🦦"),
("1F9A5","🦥"),
("1F9A4","\U0001F9A4"),
("1F9A3","\U0001F9A3"),
("1F9A2","🦢"),
("1F9A1","🦡"),
("1F9A0","🦠"),
("1F99F","🦟"),
("1F99E","🦞"),
("1F99D","🦝"),
("1F99C","🦜"),
("1F99B","🦛"),
("1F99A","🦚"),
("1F999","🦙"),
("1F998","🦘"),
("1F997","🦗"),
("1F996","🦖"),
("1F995","🦕"),
("1F994","🦔"),
("1F993","🦓"),
("1F992","🦒"),
("1F991","🦑"),
("1F990","🦐"),
("1F98F","🦏"),
("1F98E","🦎"),
("1F98D","🦍"),
("1F98C","🦌"),
("1F98B","🦋"),
("1F98A","🦊"),
("1F989","🦉"),
("1F988","🦈"),
("1F987","🦇"),
("1F986","🦆"),
("1F985","🦅"),
("1F984","🦄"),
("1F983","🦃"),
("1F982","🦂"),
("1F981","🦁"),
("1F980","🦀"),
("1F97F","🥿"),
("1F97E","🥾"),
("1F97D","🥽"),
("1F97C","🥼"),
("1F97B","🥻"),
("1F97A","🥺"),
("1F979","\U0001F979"),
("1F978","\U0001F978"),
("1F977","\U0001F977"),
("1F976","🥶"),
("1F975","🥵"),
("1F974","🥴"),
("1F973","🥳"),
("1F972","🥲"),
("1F971","🥱"),
("1F970","🥰"),
("1F96F","🥯"),
("1F96E","🥮"),
("1F96D","🥭"),
("1F96C","🥬"),
("1F96B","🥫"),
("1F96A","🥪"),
("1F969","🥩"),
("1F968","🥨"),
("1F967","🥧"),
("1F966","🥦"),
("1F965","🥥"),
("1F964","🥤"),
("1F963","🥣"),
("1F962","🥢"),
("1F961","🥡"),
("1F960","🥠"),
("1F95F","🥟"),
("1F95E","🥞"),
("1F95D","🥝"),
("1F95C","🥜"),
("1F95B","🥛"),
("1F95A","🥚"),
("1F959","🥙"),
("1F958","🥘"),
("1F957","🥗"),
("1F956","🥖"),
("1F955","🥕"),
("1F954","🥔"),
("1F953","🥓"),
("1F952","🥒"),
("1F951","🥑"),
("1F950","🥐"),
("1F94F","🥏"),
("1F94E","🥎"),
("1F94D","🥍"),
("1F94C","🥌"),
("1F94B","🥋"),
("1F94A","🥊"),
("1F949","🥉"),
("1F948","🥈"),
("1F947","🥇"),
("1F945","🥅"),
("1F944","🥄"),
("1F943","🥃"),
("1F942","🥂"),
("1F941","🥁"),
("1F940","🥀"),
("1F93F","🤿"),
("1F93E|200D|2642|FE0F","🤾‍♂️"),
("1F93E|200D|2640|FE0F","🤾‍♀️"),
("1F93E","🤾"),
("1F93D|200D|2642|FE0F","🤽‍♂️"),
("1F93D|200D|2640|FE0F","🤽‍♀️"),
("1F93D","🤽"),
("1F93C|200D|2642|FE0F","🤼‍♂️"),
("1F93C|200D|2640|FE0F","🤼‍♀️"),
("1F93C","🤼"),
("1F93A","🤺"),
("1F939|200D|2642|FE0F","🤹‍♂️"),
("1F939|200D|2640|FE0F","🤹‍♀️"),
("1F939","🤹"),
("1F938|200D|2642|FE0F","🤸‍♂️"),
("1F938|200D|2640|FE0F","🤸‍♀️"),
("1F938","🤸"),
("1F937|200D|2642|FE0F","🤷‍♂️"),
("1F937|200D|2640|FE0F","🤷‍♀️"),
("1F937","🤷"),
("1F936","🤶"),
("1F935|200D|2642|FE0F","🤵‍♂️"),
("1F935|200D|2640|FE0F","🤵‍♀️"),
("1F935","🤵"),
("1F934","🤴"),
("1F933","🤳"),
("1F932","🤲"),
("1F931","🤱"),
("1F930","🤰"),
("1F92F","🤯"),
("1F92E","🤮"),
("1F92D","🤭"),
("1F92C","🤬"),
("1F92B","🤫"),
("1F92A","🤪"),
("1F929","🤩"),
("1F928","🤨"),
("1F927","🤧"),
("1F926|200D|2642|FE0F","🤦‍♂️"),
("1F926|200D|2640|FE0F","🤦‍♀️"),
("1F926","🤦"),
("1F925","🤥"),
("1F924","🤤"),
("1F923","🤣"),
("1F922","🤢"),
("1F921","🤡"),
("1F920","🤠"),
("1F91F","🤟"),
("1F91E","🤞"),
("1F91D","🤝"),
("1F91C","🤜"),
("1F91B","🤛"),
("1F91A","🤚"),
("1F919","🤙"),
("1F918","🤘"),
("1F917","🤗"),
("1F916","🤖"),
("1F915","🤕"),
("1F914","🤔"),
("1F913","🤓"),
("1F912","🤒"),
("1F911","🤑"),
("1F910","🤐"),
("1F90F","🤏"),
("1F90E","🤎"),
("1F90D","🤍"),
("1F90C","\U0001F90C"),
("1F7F0","\U0001F7F0"),
("1F7EB","🟫"),
("1F7EA","🟪"),
("1F7E9","🟩"),
("1F7E8","🟨"),
("1F7E7","🟧"),
("1F7E6","🟦"),
("1F7E5","🟥"),
("1F7E4","🟤"),
("1F7E3","🟣"),
("1F7E2","🟢"),
("1F7E1","🟡"),
("1F7E0","🟠"),
("1F6FC","\U0001F6FC"),
("1F6FB","\U0001F6FB"),
("1F6FA","🛺"),
("1F6F9","🛹"),
("1F6F8","🛸"),
("1F6F7","🛷"),
("1F6F6","🛶"),
("1F6F5","🛵"),
("1F6F4","🛴"),
("1F6F3","🛳"),
("1F6F0","🛰"),
("1F6EC","🛬"),
("1F6EB","🛫"),
("1F6E9","🛩"),
("1F6E5","🛥"),
("1F6E4","🛤"),
("1F6E3","🛣"),
("1F6E2","🛢"),
("1F6E1","🛡"),
("1F6E0","🛠"),
("1F6DF","\U0001F6DF"),
("1F6DE","\U0001F6DE"),
("1F6DD","\U0001F6DD"),
("1F6D7","\U0001F6D7"),
("1F6D6","\U0001F6D6"),
("1F6D5","🛕"),
("1F6D2","🛒"),
("1F6D1","🛑"),
("1F6D0","🛐"),
("1F6CF","🛏"),
("1F6CD","🛍"),
("1F6CC","🛌"),
("1F6CB","🛋"),
("1F6C5","🛅"),
("1F6C4","🛄"),
("1F6C3","🛃"),
("1F6C2","🛂"),
("1F6C1","🛁"),
("1F6C0","🛀"),
("1F6BF","🚿"),
("1F6BE","🚾"),
("1F6BD","🚽"),
("1F6BC","🚼"),
("1F6BB","🚻"),
("1F6BA","🚺"),
("1F6B9","🚹"),
("1F6B8","🚸"),
("1F6B7","🚷"),
("1F6B6|200D|2642|FE0F","🚶‍♂️"),
("1F6B6|200D|2640|FE0F","🚶‍♀️"),
("1F6B6","🚶"),
("1F6B5|200D|2642|FE0F","🚵‍♂️"),
("1F6B5|200D|2640|FE0F","🚵‍♀️"),
("1F6B5","🚵"),
("1F6B4|200D|2642|FE0F","🚴‍♂️"),
("1F6B4|200D|2640|FE0F","🚴‍♀️"),
("1F6B4","🚴"),
("1F6B3","🚳"),
("1F6B2","🚲"),
("1F6B1","🚱"),
("1F6B0","🚰"),
("1F6AF","🚯"),
("1F6AE","🚮"),
("1F6AD","🚭"),
("1F6AC","🚬"),
("1F6AB","🚫"),
("1F6AA","🚪"),
("1F6A9","🚩"),
("1F6A8","🚨"),
("1F6A7","🚧"),
("1F6A6","🚦"),
("1F6A5","🚥"),
("1F6A4","🚤"),
("1F6A3|200D|2642|FE0F","🚣‍♂️"),
("1F6A3|200D|2640|FE0F","🚣‍♀️"),
("1F6A3","🚣"),
("1F6A2","🚢"),
("1F6A1","🚡"),
("1F6A0","🚠"),
("1F69F","🚟"),
("1F69E","🚞"),
("1F69D","🚝"),
("1F69C","🚜"),
("1F69B","🚛"),
("1F69A","🚚"),
("1F699","🚙"),
("1F698","🚘"),
("1F697","🚗"),
("1F696","🚖"),
("1F695","🚕"),
("1F694","🚔"),
("1F693","🚓"),
("1F692","🚒"),
("1F691","🚑"),
("1F690","🚐"),
("1F68F","🚏"),
("1F68E","🚎"),
("1F68D","🚍"),
("1F68C","🚌"),
("1F68B","🚋"),
("1F68A","🚊"),
("1F689","🚉"),
("1F688","🚈"),
("1F687","🚇"),
("1F686","🚆"),
("1F685","🚅"),
("1F684","🚄"),
("1F683","🚃"),
("1F682","🚂"),
("1F681","🚁"),
("1F680","🚀"),
("1F64F","🙏"),
("1F64E|200D|2642|FE0F","🙎‍♂️"),
("1F64E|200D|2640|FE0F","🙎‍♀️"),
("1F64E","🙎"),
("1F64D|200D|2642|FE0F","🙍‍♂️"),
("1F64D|200D|2640|FE0F","🙍‍♀️"),
("1F64D","🙍"),
("1F64C","🙌"),
("1F64B|200D|2642|FE0F","🙋‍♂️"),
("1F64B|200D|2640|FE0F","🙋‍♀️"),
("1F64B","🙋"),
("1F64A","🙊"),
("1F649","🙉"),
("1F648","🙈"),
("1F647|200D|2642|FE0F","🙇‍♂️"),
("1F647|200D|2640|FE0F","🙇‍♀️"),
("1F647","🙇"),
("1F646|200D|2642|FE0F","🙆‍♂️"),
("1F646|200D|2640|FE0F","🙆‍♀️"),
("1F646","🙆"),
("1F645|200D|2642|FE0F","🙅‍♂️"),
("1F645|200D|2640|FE0F","🙅‍♀️"),
("1F645","🙅"),
("1F644","🙄"),
("1F643","🙃"),
("1F642","🙂"),
("1F641","🙁"),
("1F640","🙀"),
("1F63F","😿"),
("1F63E","😾"),
("1F63D","😽"),
("1F63C","😼"),
("1F63B","😻"),
("1F63A","😺"),
("1F639","😹"),
("1F638","😸"),
("1F637","😷"),
("1F636|200D|1F32B|FE0F","😶‍🌫️"),
("1F636","😶"),
("1F635|200D|1F4AB","😵‍💫"),
("1F635","😵"),
("1F634","😴"),
("1F633","😳"),
("1F632","😲"),
("1F631","😱"),
("1F630","😰"),
("1F62F","😯"),
("1F62E|200D|1F4A8","😮‍💨"),
("1F62E","😮"),
("1F62D","😭"),
("1F62C","😬"),
("1F62B","😫"),
("1F62A","😪"),
("1F629","😩"),
("1F628","😨"),
("1F627","😧"),
("1F626","😦"),
("1F625","😥"),
("1F624","😤"),
("1F623","😣"),
("1F622","😢"),
("1F621","😡"),
("1F620","😠"),
("1F61F","😟"),
("1F61E","😞"),
("1F61D","😝"),
("1F61C","😜"),
("1F61B","😛"),
("1F61A","😚"),
("1F619","😙"),
("1F618","😘"),
("1F617","😗"),
("1F616","😖"),
("1F615","😕"),
("1F614","😔"),
("1F613","😓"),
("1F612","😒"),
("1F611","😑"),
("1F610","😐"),
("1F60F","😏"),
("1F60E","😎"),
("1F60D","😍"),
("1F60C","😌"),
("1F60B","😋"),
("1F60A","😊"),
("1F609","😉"),
("1F608","😈"),
("1F607","😇"),
("1F606","😆"),
("1F605","😅"),
("1F604","😄"),
("1F603","😃"),
("1F602","😂"),
("1F601","😁"),
("1F600","😀"),
("1F5FF","🗿"),
("1F5FE","🗾"),
("1F5FD","🗽"),
("1F5FC","🗼"),
("1F5FB","🗻"),
("1F5FA","🗺"),
("1F5F3","🗳"),
("1F5EF","🗯"),
("1F5E8","🗨"),
("1F5E3","🗣"),
("1F5E1","🗡"),
("1F5DE","🗞"),
("1F5DD","🗝"),
("1F5DC","🗜"),
("1F5D3","🗓"),
("1F5D2","🗒"),
("1F5D1","🗑"),
("1F5C4","🗄"),
("1F5C3","🗃"),
("1F5C2","🗂"),
("1F5BC","🖼"),
("1F5B2","🖲"),
("1F5B1","🖱"),
("1F5A8","🖨"),
("1F5A5","🖥"),
("1F5A4","🖤"),
("1F596","🖖"),
("1F595","🖕"),
("1F590","🖐"),
("1F58D","🖍"),
("1F58C","🖌"),
("1F58B","🖋"),
("1F58A","🖊"),
("1F587","🖇"),
("1F57A","🕺"),
("1F579","🕹"),
("1F578","🕸"),
("1F577","🕷"),
("1F576","🕶"),
("1F575|FE0F|200D|2642|FE0F","🕵️‍♂️"),
("1F575|FE0F|200D|2640|FE0F","🕵️‍♀️"),
("1F575","🕵"),
("1F574","🕴"),
("1F573","🕳"),
("1F570","🕰"),
("1F56F","🕯"),
("1F567","🕧"),
("1F566","🕦"),
("1F565","🕥"),
("1F564","🕤"),
("1F563","🕣"),
("1F562","🕢"),
("1F561","🕡"),
("1F560","🕠"),
("1F55F","🕟"),
("1F55E","🕞"),
("1F55D","🕝"),
("1F55C","🕜"),
("1F55B","🕛"),
("1F55A","🕚"),
("1F559","🕙"),
("1F558","🕘"),
("1F557","🕗"),
("1F556","🕖"),
("1F555","🕕"),
("1F554","🕔"),
("1F553","🕓"),
("1F552","🕒"),
("1F551","🕑"),
("1F550","🕐"),
("1F54E","🕎"),
("1F54D","🕍"),
("1F54C","🕌"),
("1F54B","🕋"),
("1F54A","🕊"),
("1F549","🕉"),
("1F53D","🔽"),
("1F53C","🔼"),
("1F53B","🔻"),
("1F53A","🔺"),
("1F539","🔹"),
("1F538","🔸"),
("1F537","🔷"),
("1F536","🔶"),
("1F535","🔵"),
("1F534","🔴"),
("1F533","🔳"),
("1F532","🔲"),
("1F531","🔱"),
("1F530","🔰"),
("1F52E","🔮"),
("1F52D","🔭"),
("1F52C","🔬"),
("1F52B","🔫"),
("1F52A","🔪"),
("1F529","🔩"),
("1F528","🔨"),
("1F527","🔧"),
("1F526","🔦"),
("1F525","🔥"),
("1F524","🔤"),
("1F523","🔣"),
("1F522","🔢"),
("1F521","🔡"),
("1F520","🔠"),
("1F51F","🔟"),
("1F51E","🔞"),
("1F51D","🔝"),
("1F51C","🔜"),
("1F51B","🔛"),
("1F51A","🔚"),
("1F519","🔙"),
("1F518","🔘"),
("1F517","🔗"),
("1F516","🔖"),
("1F515","🔕"),
("1F514","🔔"),
("1F513","🔓"),
("1F512","🔒"),
("1F511","🔑"),
("1F510","🔐"),
("1F50F","🔏"),
("1F50E","🔎"),
("1F50D","🔍"),
("1F50C","🔌"),
("1F50B","🔋"),
("1F50A","🔊"),
("1F509","🔉"),
("1F508","🔈"),
("1F507","🔇"),
("1F506","🔆"),
("1F505","🔅"),
("1F504","🔄"),
("1F503","🔃"),
("1F502","🔂"),
("1F501","🔁"),
("1F500","🔀"),
("1F4FF","📿"),
("1F4FD","📽"),
("1F4FC","📼"),
("1F4FB","📻"),
("1F4FA","📺"),
("1F4F9","📹"),
("1F4F8","📸"),
("1F4F7","📷"),
("1F4F6","📶"),
("1F4F5","📵"),
("1F4F4","📴"),
("1F4F3","📳"),
("1F4F2","📲"),
("1F4F1","📱"),
("1F4F0","📰"),
("1F4EF","📯"),
("1F4EE","📮"),
("1F4ED","📭"),
("1F4EC","📬"),
("1F4EB","📫"),
("1F4EA","📪"),
("1F4E9","📩"),
("1F4E8","📨"),
("1F4E7","📧"),
("1F4E6","📦"),
("1F4E5","📥"),
("1F4E4","📤"),
("1F4E3","📣"),
("1F4E2","📢"),
("1F4E1","📡"),
("1F4E0","📠"),
("1F4DF","📟"),
("1F4DE","📞"),
("1F4DD","📝"),
("1F4DC","📜"),
("1F4DB","📛"),
("1F4DA","📚"),
("1F4D9","📙"),
("1F4D8","📘"),
("1F4D7","📗"),
("1F4D6","📖"),
("1F4D5","📕"),
("1F4D4","📔"),
("1F4D3","📓"),
("1F4D2","📒"),
("1F4D1","📑"),
("1F4D0","📐"),
("1F4CF","📏"),
("1F4CE","📎"),
("1F4CD","📍"),
("1F4CC","📌"),
("1F4CB","📋"),
("1F4CA","📊"),
("1F4C9","📉"),
("1F4C8","📈"),
("1F4C7","📇"),
("1F4C6","📆"),
("1F4C5","📅"),
("1F4C4","📄"),
("1F4C3","📃"),
("1F4C2","📂"),
("1F4C1","📁"),
("1F4C0","📀"),
("1F4BF","💿"),
("1F4BE","💾"),
("1F4BD","💽"),
("1F4BC","💼"),
("1F4BB","💻"),
("1F4BA","💺"),
("1F4B9","💹"),
("1F4B8","💸"),
("1F4B7","💷"),
("1F4B6","💶"),
("1F4B5","💵"),
("1F4B4","💴"),
("1F4B3","💳"),
("1F4B2","💲"),
("1F4B1","💱"),
("1F4B0","💰"),
("1F4AF","💯"),
("1F4AE","💮"),
("1F4AD","💭"),
("1F4AC","💬"),
("1F4AB","💫"),
("1F4AA","💪"),
("1F4A9","💩"),
("1F4A8","💨"),
("1F4A7","💧"),
("1F4A6","💦"),
("1F4A5","💥"),
("1F4A4","💤"),
("1F4A3","💣"),
("1F4A2","💢"),
("1F4A1","💡"),
("1F4A0","💠"),
("1F49F","💟"),
("1F49E","💞"),
("1F49D","💝"),
("1F49C","💜"),
("1F49B","💛"),
("1F49A","💚"),
("1F499","💙"),
("1F498","💘"),
("1F497","💗"),
("1F496","💖"),
("1F495","💕"),
("1F494","💔"),
("1F493","💓"),
("1F492","💒"),
("1F491","💑"),
("1F490","💐"),
("1F48F","💏"),
("1F48E","💎"),
("1F48D","💍"),
("1F48C","💌"),
("1F48B","💋"),
("1F48A","💊"),
("1F489","💉"),
("1F488","💈"),
("1F487|200D|2642|FE0F","💇‍♂️"),
("1F487|200D|2640|FE0F","💇‍♀️"),
("1F487","💇"),
("1F486|200D|2642|FE0F","💆‍♂️"),
("1F486|200D|2640|FE0F","💆‍♀️"),
("1F486","💆"),
("1F485","💅"),
("1F484","💄"),
("1F483","💃"),
("1F482|200D|2642|FE0F","💂‍♂️"),
("1F482|200D|2640|FE0F","💂‍♀️"),
("1F482","💂"),
("1F481|200D|2642|FE0F","💁‍♂️"),
("1F481|200D|2640|FE0F","💁‍♀️"),
("1F481","💁"),
("1F480","💀"),
("1F47F","👿"),
("1F47E","👾"),
("1F47D","👽"),
("1F47C","👼"),
("1F47B","👻"),
("1F47A","👺"),
("1F479","👹"),
("1F478","👸"),
("1F477|200D|2642|FE0F","👷‍♂️"),
("1F477|200D|2640|FE0F","👷‍♀️"),
("1F477","👷"),
("1F476","👶"),
("1F475","👵"),
("1F474","👴"),
("1F473|200D|2642|FE0F","👳‍♂️"),
("1F473|200D|2640|FE0F","👳‍♀️"),
("1F473","👳"),
("1F472","👲"),
("1F471|200D|2642|FE0F","👱‍♂️"),
("1F471|200D|2640|FE0F","👱‍♀️"),
("1F471","👱"),
("1F470|200D|2642|FE0F","👰‍♂️"),
("1F470|200D|2640|FE0F","👰‍♀️"),
("1F470","👰"),
("1F46F|200D|2642|FE0F","👯‍♂️"),
("1F46F|200D|2640|FE0F","👯‍♀️"),
("1F46F","👯"),
("1F46E|200D|2642|FE0F","👮‍♂️"),
("1F46E|200D|2640|FE0F","👮‍♀️"),
("1F46E","👮"),
("1F46D","👭"),
("1F46C","👬"),
("1F46B","👫"),
("1F46A","👪"),
("1F469|200D|2764|FE0F|200D|1F48B|200D|1F469","👩‍❤️‍"),
("1F469|200D|2764|FE0F|200D|1F48B|200D|1F468","👩‍❤️‍"),
("1F469|200D|2764|FE0F|200D|1F469","👩‍❤️‍"),
("1F469|200D|2764|FE0F|200D|1F468","👩‍❤️‍"),
("1F469|200D|2708|FE0F","👩‍✈️"),
("1F469|200D|2696|FE0F","👩‍⚖️"),
("1F469|200D|2695|FE0F","👩‍⚕️"),
("1F469|200D|1F9BD","👩‍🦽"),
("1F469|200D|1F9BC","👩‍🦼"),
("1F469|200D|1F9B3","👩‍🦳"),
("1F469|200D|1F9B2","👩‍🦲"),
("1F469|200D|1F9B1","👩‍🦱"),
("1F469|200D|1F9B0","👩‍🦰"),
("1F469|200D|1F9AF","👩‍🦯"),
("1F469|200D|1F692","👩‍🚒"),
("1F469|200D|1F680","👩‍🚀"),
("1F469|200D|1F52C","👩‍🔬"),
("1F469|200D|1F527","👩‍🔧"),
("1F469|200D|1F4BC","👩‍💼"),
("1F469|200D|1F4BB","👩‍💻"),
("1F469|200D|1F469|200D|1F467|200D|1F467","👩‍👩‍"),
("1F469|200D|1F469|200D|1F467|200D|1F466","👩‍👩‍"),
("1F469|200D|1F469|200D|1F467","👩‍👩‍"),
("1F469|200D|1F469|200D|1F466|200D|1F466","👩‍👩‍"),
("1F469|200D|1F469|200D|1F466","👩‍👩‍"),
("1F469|200D|1F467|200D|1F467","👩‍👧‍"),
("1F469|200D|1F467|200D|1F466","👩‍👧‍"),
("1F469|200D|1F467","👩‍👧"),
("1F469|200D|1F466|200D|1F466","👩‍👦‍"),
("1F469|200D|1F466","👩‍👦"),
("1F469|200D|1F3ED","👩‍🏭"),
("1F469|200D|1F3EB","👩‍🏫"),
("1F469|200D|1F3A8","👩‍🎨"),
("1F469|200D|1F3A4","👩‍🎤"),
("1F469|200D|1F393","👩‍🎓"),
("1F469|200D|1F37C","👩‍🍼"),
("1F469|200D|1F373","👩‍🍳"),
("1F469|200D|1F33E","👩‍🌾"),
("1F469","👩"),
("1F468|200D|2764|FE0F|200D|1F48B|200D|1F468","👨‍❤️‍"),
("1F468|200D|2764|FE0F|200D|1F468","👨‍❤️‍"),
("1F468|200D|2708|FE0F","👨‍✈️"),
("1F468|200D|2696|FE0F","👨‍⚖️"),
("1F468|200D|2695|FE0F","👨‍⚕️"),
("1F468|200D|1F9BD","👨‍🦽"),
("1F468|200D|1F9BC","👨‍🦼"),
("1F468|200D|1F9B3","👨‍🦳"),
("1F468|200D|1F9B2","👨‍🦲"),
("1F468|200D|1F9B1","👨‍🦱"),
("1F468|200D|1F9B0","👨‍🦰"),
("1F468|200D|1F9AF","👨‍🦯"),
("1F468|200D|1F692","👨‍🚒"),
("1F468|200D|1F680","👨‍🚀"),
("1F468|200D|1F52C","👨‍🔬"),
("1F468|200D|1F527","👨‍🔧"),
("1F468|200D|1F4BC","👨‍💼"),
("1F468|200D|1F4BB","👨‍💻"),
("1F468|200D|1F469|200D|1F467|200D|1F467","👨‍👩‍"),
("1F468|200D|1F469|200D|1F467|200D|1F466","👨‍👩‍"),
("1F468|200D|1F469|200D|1F467","👨‍👩‍"),
("1F468|200D|1F469|200D|1F466|200D|1F466","👨‍👩‍"),
("1F468|200D|1F469|200D|1F466","👨‍👩‍"),
("1F468|200D|1F468|200D|1F467|200D|1F467","👨‍👨‍"),
("1F468|200D|1F468|200D|1F467|200D|1F466","👨‍👨‍"),
("1F468|200D|1F468|200D|1F467","👨‍👨‍"),
("1F468|200D|1F468|200D|1F466|200D|1F466","👨‍👨‍"),
("1F468|200D|1F468|200D|1F466","👨‍👨‍"),
("1F468|200D|1F467|200D|1F467","👨‍👧‍"),
("1F468|200D|1F467|200D|1F466","👨‍👧‍"),
("1F468|200D|1F467","👨‍👧"),
("1F468|200D|1F466|200D|1F466","👨‍👦‍"),
("1F468|200D|1F466","👨‍👦"),
("1F468|200D|1F3ED","👨‍🏭"),
("1F468|200D|1F3EB","👨‍🏫"),
("1F468|200D|1F3A8","👨‍🎨"),
("1F468|200D|1F3A4","👨‍🎤"),
("1F468|200D|1F393","👨‍🎓"),
("1F468|200D|1F37C","👨‍🍼"),
("1F468|200D|1F373","👨‍🍳"),
("1F468|200D|1F33E","👨‍🌾"),
("1F468","👨"),
("1F467","👧"),
("1F466","👦"),
("1F465","👥"),
("1F464","👤"),
("1F463","👣"),
("1F462","👢"),
("1F461","👡"),
("1F460","👠"),
("1F45F","👟"),
("1F45E","👞"),
("1F45D","👝"),
("1F45C","👜"),
("1F45B","👛"),
("1F45A","👚"),
("1F459","👙"),
("1F458","👘"),
("1F457","👗"),
("1F456","👖"),
("1F455","👕"),
("1F454","👔"),
("1F453","👓"),
("1F452","👒"),
("1F451","👑"),
("1F450","👐"),
("1F44F","👏"),
("1F44E","👎"),
("1F44D","👍"),
("1F44C","👌"),
("1F44B","👋"),
("1F44A","👊"),
("1F449","👉"),
("1F448","👈"),
("1F447","👇"),
("1F446","👆"),
("1F445","👅"),
("1F444","👄"),
("1F443","👃"),
("1F442","👂"),
("1F441","👁"),
("1F440","👀"),
("1F43F","🐿"),
("1F43E","🐾"),
("1F43D","🐽"),
("1F43C","🐼"),
("1F43B|200D|2744|FE0F","🐻‍❄️"),
("1F43B","🐻"),
("1F43A","🐺"),
("1F439","🐹"),
("1F438","🐸"),
("1F437","🐷"),
("1F436","🐶"),
("1F435","🐵"),
("1F434","🐴"),
("1F433","🐳"),
("1F432","🐲"),
("1F431","🐱"),
("1F430","🐰"),
("1F42F","🐯"),
("1F42E","🐮"),
("1F42D","🐭"),
("1F42C","🐬"),
("1F42B","🐫"),
("1F42A","🐪"),
("1F429","🐩"),
("1F428","🐨"),
("1F427","🐧"),
("1F426","🐦"),
("1F425","🐥"),
("1F424","🐤"),
("1F423","🐣"),
("1F422","🐢"),
("1F421","🐡"),
("1F420","🐠"),
("1F41F","🐟"),
("1F41E","🐞"),
("1F41D","🐝"),
("1F41C","🐜"),
("1F41B","🐛"),
("1F41A","🐚"),
("1F419","🐙"),
("1F418","🐘"),
("1F417","🐗"),
("1F416","🐖"),
("1F415|200D|1F9BA","🐕‍🦺"),
("1F415","🐕"),
("1F414","🐔"),
("1F413","🐓"),
("1F412","🐒"),
("1F411","🐑"),
("1F410","🐐"),
("1F40F","🐏"),
("1F40E","🐎"),
("1F40D","🐍"),
("1F40C","🐌"),
("1F40B","🐋"),
("1F40A","🐊"),
("1F409","🐉"),
("1F408|200D|2B1B","🐈‍⬛"),
("1F408","🐈"),
("1F407","🐇"),
("1F406","🐆"),
("1F405","🐅"),
("1F404","🐄"),
("1F403","🐃"),
("1F402","🐂"),
("1F401","🐁"),
("1F400","🐀"),
("1F3FA","🏺"),
("1F3F9","🏹"),
("1F3F8","🏸"),
("1F3F7","🏷"),
("1F3F5","🏵"),
("1F3F4|E0067|E0062|E0077|E006C|E0073|E007F","🏴72"),
("1F3F4|E0067|E0062|E0073|E0063|E0074|E007F","🏴72"),
("1F3F4|E0067|E0062|E0065|E006E|E0067|E007F","🏴72"),
("1F3F4|200D|2620|FE0F","🏴‍☠️"),
("1F3F4","🏴"),
("1F3F3|FE0F|200D|26A7|FE0F","🏳️‍⚧️"),
("1F3F3|FE0F|200D|1F308","🏳️‍🌈"),
("1F3F3","🏳"),
("1F3F0","🏰"),
("1F3EF","🏯"),
("1F3EE","🏮"),
("1F3ED","🏭"),
("1F3EC","🏬"),
("1F3EB","🏫"),
("1F3EA","🏪"),
("1F3E9","🏩"),
("1F3E8","🏨"),
("1F3E7","🏧"),
("1F3E6","🏦"),
("1F3E5","🏥"),
("1F3E4","🏤"),
("1F3E3","🏣"),
("1F3E2","🏢"),
("1F3E1","🏡"),
("1F3E0","🏠"),
("1F3DF","🏟"),
("1F3DE","🏞"),
("1F3DD","🏝"),
("1F3DC","🏜"),
("1F3DB","🏛"),
("1F3DA","🏚"),
("1F3D9","🏙"),
("1F3D8","🏘"),
("1F3D7","🏗"),
("1F3D6","🏖"),
("1F3D5","🏕"),
("1F3D4","🏔"),
("1F3D3","🏓"),
("1F3D2","🏒"),
("1F3D1","🏑"),
("1F3D0","🏐"),
("1F3CF","🏏"),
("1F3CE","🏎"),
("1F3CD","🏍"),
("1F3CC|FE0F|200D|2642|FE0F","🏌️‍♂️"),
("1F3CC|FE0F|200D|2640|FE0F","🏌️‍♀️"),
("1F3CC","🏌"),
("1F3CB|FE0F|200D|2642|FE0F","🏋️‍♂️"),
("1F3CB|FE0F|200D|2640|FE0F","🏋️‍♀️"),
("1F3CB","🏋"),
("1F3CA|200D|2642|FE0F","🏊‍♂️"),
("1F3CA|200D|2640|FE0F","🏊‍♀️"),
("1F3CA","🏊"),
("1F3C9","🏉"),
("1F3C8","🏈"),
("1F3C7","🏇"),
("1F3C6","🏆"),
("1F3C5","🏅"),
("1F3C4|200D|2642|FE0F","🏄‍♂️"),
("1F3C4|200D|2640|FE0F","🏄‍♀️"),
("1F3C4","🏄"),
("1F3C3|200D|2642|FE0F","🏃‍♂️"),
("1F3C3|200D|2640|FE0F","🏃‍♀️"),
("1F3C3","🏃"),
("1F3C2","🏂"),
("1F3C1","🏁"),
("1F3C0","🏀"),
("1F3BF","🎿"),
("1F3BE","🎾"),
("1F3BD","🎽"),
("1F3BC","🎼"),
("1F3BB","🎻"),
("1F3BA","🎺"),
("1F3B9","🎹"),
("1F3B8","🎸"),
("1F3B7","🎷"),
("1F3B6","🎶"),
("1F3B5","🎵"),
("1F3B4","🎴"),
("1F3B3","🎳"),
("1F3B2","🎲"),
("1F3B1","🎱"),
("1F3B0","🎰"),
("1F3AF","🎯"),
("1F3AE","🎮"),
("1F3AD","🎭"),
("1F3AC","🎬"),
("1F3AB","🎫"),
("1F3AA","🎪"),
("1F3A9","🎩"),
("1F3A8","🎨"),
("1F3A7","🎧"),
("1F3A6","🎦"),
("1F3A5","🎥"),
("1F3A4","🎤"),
("1F3A3","🎣"),
("1F3A2","🎢"),
("1F3A1","🎡"),
("1F3A0","🎠"),
("1F39F","🎟"),
("1F39E","🎞"),
("1F39B","🎛"),
("1F39A","🎚"),
("1F399","🎙"),
("1F397","🎗"),
("1F396","🎖"),
("1F393","🎓"),
("1F392","🎒"),
("1F391","🎑"),
("1F390","🎐"),
("1F38F","🎏"),
("1F38E","🎎"),
("1F38D","🎍"),
("1F38C","🎌"),
("1F38B","🎋"),
("1F38A","🎊"),
("1F389","🎉"),
("1F388","🎈"),
("1F387","🎇"),
("1F386","🎆"),
("1F385","🎅"),
("1F384","🎄"),
("1F383","🎃"),
("1F382","🎂"),
("1F381","🎁"),
("1F380","🎀"),
("1F37F","🍿"),
("1F37E","🍾"),
("1F37D","🍽"),
("1F37C","🍼"),
("1F37B","🍻"),
("1F37A","🍺"),
("1F379","🍹"),
("1F378","🍸"),
("1F377","🍷"),
("1F376","🍶"),
("1F375","🍵"),
("1F374","🍴"),
("1F373","🍳"),
("1F372","🍲"),
("1F371","🍱"),
("1F370","🍰"),
("1F36F","🍯"),
("1F36E","🍮"),
("1F36D","🍭"),
("1F36C","🍬"),
("1F36B","🍫"),
("1F36A","🍪"),
("1F369","🍩"),
("1F368","🍨"),
("1F367","🍧"),
("1F366","🍦"),
("1F365","🍥"),
("1F364","🍤"),
("1F363","🍣"),
("1F362","🍢"),
("1F361","🍡"),
("1F360","🍠"),
("1F35F","🍟"),
("1F35E","🍞"),
("1F35D","🍝"),
("1F35C","🍜"),
("1F35B","🍛"),
("1F35A","🍚"),
("1F359","🍙"),
("1F358","🍘"),
("1F357","🍗"),
("1F356","🍖"),
("1F355","🍕"),
("1F354","🍔"),
("1F353","🍓"),
("1F352","🍒"),
("1F351","🍑"),
("1F350","🍐"),
("1F34F","🍏"),
("1F34E","🍎"),
("1F34D","🍍"),
("1F34C","🍌"),
("1F34B","🍋"),
("1F34A","🍊"),
("1F349","🍉"),
("1F348","🍈"),
("1F347","🍇"),
("1F346","🍆"),
("1F345","🍅"),
("1F344","🍄"),
("1F343","🍃"),
("1F342","🍂"),
("1F341","🍁"),
("1F340","🍀"),
("1F33F","🌿"),
("1F33E","🌾"),
("1F33D","🌽"),
("1F33C","🌼"),
("1F33B","🌻"),
("1F33A","🌺"),
("1F339","🌹"),
("1F338","🌸"),
("1F337","🌷"),
("1F336","🌶"),
("1F335","🌵"),
("1F334","🌴"),
("1F333","🌳"),
("1F332","🌲"),
("1F331","🌱"),
("1F330","🌰"),
("1F32F","🌯"),
("1F32E","🌮"),
("1F32D","🌭"),
("1F32C","🌬"),
("1F32B","🌫"),
("1F32A","🌪"),
("1F329","🌩"),
("1F328","🌨"),
("1F327","🌧"),
("1F326","🌦"),
("1F325","🌥"),
("1F324","🌤"),
("1F321","🌡"),
("1F320","🌠"),
("1F31F","🌟"),
("1F31E","🌞"),
("1F31D","🌝"),
("1F31C","🌜"),
("1F31B","🌛"),
("1F31A","🌚"),
("1F319","🌙"),
("1F318","🌘"),
("1F317","🌗"),
("1F316","🌖"),
("1F315","🌕"),
("1F314","🌔"),
("1F313","🌓"),
("1F312","🌒"),
("1F311","🌑"),
("1F310","🌐"),
("1F30F","🌏"),
("1F30E","🌎"),
("1F30D","🌍"),
("1F30C","🌌"),
("1F30B","🌋"),
("1F30A","🌊"),
("1F309","🌉"),
("1F308","🌈"),
("1F307","🌇"),
("1F306","🌆"),
("1F305","🌅"),
("1F304","🌄"),
("1F303","🌃"),
("1F302","🌂"),
("1F301","🌁"),
("1F300","🌀"),
("1F251","🉑"),
("1F250","🉐"),
("1F23A","🈺"),
("1F239","🈹"),
("1F238","🈸"),
("1F237","🈷"),
("1F236","🈶"),
("1F235","🈵"),
("1F234","🈴"),
("1F233","🈳"),
("1F232","🈲"),
("1F22F","🈯"),
("1F21A","🈚"),
("1F202","🈂"),
("1F201","🈁"),
("1F1FF|1F1FC","🇿🇼"),
("1F1FF|1F1F2","🇿🇲"),
("1F1FF|1F1E6","🇿🇦"),
("1F1FE|1F1F9","🇾🇹"),
("1F1FE|1F1EA","🇾🇪"),
("1F1FD|1F1F0","🇽🇰"),
("1F1FC|1F1F8","🇼🇸"),
("1F1FC|1F1EB","🇼🇫"),
("1F1FB|1F1FA","🇻🇺"),
("1F1FB|1F1F3","🇻🇳"),
("1F1FB|1F1EE","🇻🇮"),
("1F1FB|1F1EC","🇻🇬"),
("1F1FB|1F1EA","🇻🇪"),
("1F1FB|1F1E8","🇻🇨"),
("1F1FB|1F1E6","🇻🇦"),
("1F1FA|1F1FF","🇺🇿"),
("1F1FA|1F1FE","🇺🇾"),
("1F1FA|1F1F8","🇺🇸"),
("1F1FA|1F1F3","🇺🇳"),
("1F1FA|1F1F2","🇺🇲"),
("1F1FA|1F1EC","🇺🇬"),
("1F1FA|1F1E6","🇺🇦"),
("1F1F9|1F1FF","🇹🇿"),
("1F1F9|1F1FC","🇹🇼"),
("1F1F9|1F1FB","🇹🇻"),
("1F1F9|1F1F9","🇹🇹"),
("1F1F9|1F1F7","🇹🇷"),
("1F1F9|1F1F4","🇹🇴"),
("1F1F9|1F1F3","🇹🇳"),
("1F1F9|1F1F2","🇹🇲"),
("1F1F9|1F1F1","🇹🇱"),
("1F1F9|1F1F0","🇹🇰"),
("1F1F9|1F1EF","🇹🇯"),
("1F1F9|1F1ED","🇹🇭"),
("1F1F9|1F1EC","🇹🇬"),
("1F1F9|1F1EB","🇹🇫"),
("1F1F9|1F1E9","🇹🇩"),
("1F1F9|1F1E8","🇹🇨"),
("1F1F9|1F1E6","🇹🇦"),
("1F1F8|1F1FF","🇸🇿"),
("1F1F8|1F1FE","🇸🇾"),
("1F1F8|1F1FD","🇸🇽"),
("1F1F8|1F1FB","🇸🇻"),
("1F1F8|1F1F9","🇸🇹"),
("1F1F8|1F1F8","🇸🇸"),
("1F1F8|1F1F7","🇸🇷"),
("1F1F8|1F1F4","🇸🇴"),
("1F1F8|1F1F3","🇸🇳"),
("1F1F8|1F1F2","🇸🇲"),
("1F1F8|1F1F1","🇸🇱"),
("1F1F8|1F1F0","🇸🇰"),
("1F1F8|1F1EF","🇸🇯"),
("1F1F8|1F1EE","🇸🇮"),
("1F1F8|1F1ED","🇸🇭"),
("1F1F8|1F1EC","🇸🇬"),
("1F1F8|1F1EA","🇸🇪"),
("1F1F8|1F1E9","🇸🇩"),
("1F1F8|1F1E8","🇸🇨"),
("1F1F8|1F1E7","🇸🇧"),
("1F1F8|1F1E6","🇸🇦"),
("1F1F7|1F1FC","🇷🇼"),
("1F1F7|1F1FA","🇷🇺"),
("1F1F7|1F1F8","🇷🇸"),
("1F1F7|1F1F4","🇷🇴"),
("1F1F7|1F1EA","🇷🇪"),
("1F1F6|1F1E6","🇶🇦"),
("1F1F5|1F1FE","🇵🇾"),
("1F1F5|1F1FC","🇵🇼"),
("1F1F5|1F1F9","🇵🇹"),
("1F1F5|1F1F8","🇵🇸"),
("1F1F5|1F1F7","🇵🇷"),
("1F1F5|1F1F3","🇵🇳"),
("1F1F5|1F1F2","🇵🇲"),
("1F1F5|1F1F1","🇵🇱"),
("1F1F5|1F1F0","🇵🇰"),
("1F1F5|1F1ED","🇵🇭"),
("1F1F5|1F1EC","🇵🇬"),
("1F1F5|1F1EB","🇵🇫"),
("1F1F5|1F1EA","🇵🇪"),
("1F1F5|1F1E6","🇵🇦"),
("1F1F4|1F1F2","🇴🇲"),
("1F1F3|1F1FF","🇳🇿"),
("1F1F3|1F1FA","🇳🇺"),
("1F1F3|1F1F7","🇳🇷"),
("1F1F3|1F1F5","🇳🇵"),
("1F1F3|1F1F4","🇳🇴"),
("1F1F3|1F1F1","🇳🇱"),
("1F1F3|1F1EE","🇳🇮"),
("1F1F3|1F1EC","🇳🇬"),
("1F1F3|1F1EB","🇳🇫"),
("1F1F3|1F1EA","🇳🇪"),
("1F1F3|1F1E8","🇳🇨"),
("1F1F3|1F1E6","🇳🇦"),
("1F1F2|1F1FF","🇲🇿"),
("1F1F2|1F1FE","🇲🇾"),
("1F1F2|1F1FD","🇲🇽"),
("1F1F2|1F1FC","🇲🇼"),
("1F1F2|1F1FB","🇲🇻"),
("1F1F2|1F1FA","🇲🇺"),
("1F1F2|1F1F9","🇲🇹"),
("1F1F2|1F1F8","🇲🇸"),
("1F1F2|1F1F7","🇲🇷"),
("1F1F2|1F1F6","🇲🇶"),
("1F1F2|1F1F5","🇲🇵"),
("1F1F2|1F1F4","🇲🇴"),
("1F1F2|1F1F3","🇲🇳"),
("1F1F2|1F1F2","🇲🇲"),
("1F1F2|1F1F1","🇲🇱"),
("1F1F2|1F1F0","🇲🇰"),
("1F1F2|1F1ED","🇲🇭"),
("1F1F2|1F1EC","🇲🇬"),
("1F1F2|1F1EB","🇲🇫"),
("1F1F2|1F1EA","🇲🇪"),
("1F1F2|1F1E9","🇲🇩"),
("1F1F2|1F1E8","🇲🇨"),
("1F1F2|1F1E6","🇲🇦"),
("1F1F1|1F1FE","🇱🇾"),
("1F1F1|1F1FB","🇱🇻"),
("1F1F1|1F1FA","🇱🇺"),
("1F1F1|1F1F9","🇱🇹"),
("1F1F1|1F1F8","🇱🇸"),
("1F1F1|1F1F7","🇱🇷"),
("1F1F1|1F1F0","🇱🇰"),
("1F1F1|1F1EE","🇱🇮"),
("1F1F1|1F1E8","🇱🇨"),
("1F1F1|1F1E7","🇱🇧"),
("1F1F1|1F1E6","🇱🇦"),
("1F1F0|1F1FF","🇰🇿"),
("1F1F0|1F1FE","🇰🇾"),
("1F1F0|1F1FC","🇰🇼"),
("1F1F0|1F1F7","🇰🇷"),
("1F1F0|1F1F5","🇰🇵"),
("1F1F0|1F1F3","🇰🇳"),
("1F1F0|1F1F2","🇰🇲"),
("1F1F0|1F1EE","🇰🇮"),
("1F1F0|1F1ED","🇰🇭"),
("1F1F0|1F1EC","🇰🇬"),
("1F1F0|1F1EA","🇰🇪"),
("1F1EF|1F1F5","🇯🇵"),
("1F1EF|1F1F4","🇯🇴"),
("1F1EF|1F1F2","🇯🇲"),
("1F1EF|1F1EA","🇯🇪"),
("1F1EE|1F1F9","🇮🇹"),
("1F1EE|1F1F8","🇮🇸"),
("1F1EE|1F1F7","🇮🇷"),
("1F1EE|1F1F6","🇮🇶"),
("1F1EE|1F1F4","🇮🇴"),
("1F1EE|1F1F3","🇮🇳"),
("1F1EE|1F1F2","🇮🇲"),
("1F1EE|1F1F1","🇮🇱"),
("1F1EE|1F1EA","🇮🇪"),
("1F1EE|1F1E9","🇮🇩"),
("1F1EE|1F1E8","🇮🇨"),
("1F1ED|1F1FA","🇭🇺"),
("1F1ED|1F1F9","🇭🇹"),
("1F1ED|1F1F7","🇭🇷"),
("1F1ED|1F1F3","🇭🇳"),
("1F1ED|1F1F2","🇭🇲"),
("1F1ED|1F1F0","🇭🇰"),
("1F1EC|1F1FE","🇬🇾"),
("1F1EC|1F1FC","🇬🇼"),
("1F1EC|1F1FA","🇬🇺"),
("1F1EC|1F1F9","🇬🇹"),
("1F1EC|1F1F8","🇬🇸"),
("1F1EC|1F1F7","🇬🇷"),
("1F1EC|1F1F6","🇬🇶"),
("1F1EC|1F1F5","🇬🇵"),
("1F1EC|1F1F3","🇬🇳"),
("1F1EC|1F1F2","🇬🇲"),
("1F1EC|1F1F1","🇬🇱"),
("1F1EC|1F1EE","🇬🇮"),
("1F1EC|1F1ED","🇬🇭"),
("1F1EC|1F1EC","🇬🇬"),
("1F1EC|1F1EB","🇬🇫"),
("1F1EC|1F1EA","🇬🇪"),
("1F1EC|1F1E9","🇬🇩"),
("1F1EC|1F1E7","🇬🇧"),
("1F1EC|1F1E6","🇬🇦"),
("1F1EB|1F1F7","🇫🇷"),
("1F1EB|1F1F4","🇫🇴"),
("1F1EB|1F1F2","🇫🇲"),
("1F1EB|1F1F0","🇫🇰"),
("1F1EB|1F1EF","🇫🇯"),
("1F1EB|1F1EE","🇫🇮"),
("1F1EA|1F1FA","🇪🇺"),
("1F1EA|1F1F9","🇪🇹"),
("1F1EA|1F1F8","🇪🇸"),
("1F1EA|1F1F7","🇪🇷"),
("1F1EA|1F1ED","🇪🇭"),
("1F1EA|1F1EC","🇪🇬"),
("1F1EA|1F1EA","🇪🇪"),
("1F1EA|1F1E8","🇪🇨"),
("1F1EA|1F1E6","🇪🇦"),
("1F1E9|1F1FF","🇩🇿"),
("1F1E9|1F1F4","🇩🇴"),
("1F1E9|1F1F2","🇩🇲"),
("1F1E9|1F1F0","🇩🇰"),
("1F1E9|1F1EF","🇩🇯"),
("1F1E9|1F1EC","🇩🇬"),
("1F1E9|1F1EA","🇩🇪"),
("1F1E8|1F1FF","🇨🇿"),
("1F1E8|1F1FE","🇨🇾"),
("1F1E8|1F1FD","🇨🇽"),
("1F1E8|1F1FC","🇨🇼"),
("1F1E8|1F1FB","🇨🇻"),
("1F1E8|1F1FA","🇨🇺"),
("1F1E8|1F1F7","🇨🇷"),
("1F1E8|1F1F5","🇨🇵"),
("1F1E8|1F1F4","🇨🇴"),
("1F1E8|1F1F3","🇨🇳"),
("1F1E8|1F1F2","🇨🇲"),
("1F1E8|1F1F1","🇨🇱"),
("1F1E8|1F1F0","🇨🇰"),
("1F1E8|1F1EE","🇨🇮"),
("1F1E8|1F1ED","🇨🇭"),
("1F1E8|1F1EC","🇨🇬"),
("1F1E8|1F1EB","🇨🇫"),
("1F1E8|1F1E9","🇨🇩"),
("1F1E8|1F1E8","🇨🇨"),
("1F1E8|1F1E6","🇨🇦"),
("1F1E7|1F1FF","🇧🇿"),
("1F1E7|1F1FE","🇧🇾"),
("1F1E7|1F1FC","🇧🇼"),
("1F1E7|1F1FB","🇧🇻"),
("1F1E7|1F1F9","🇧🇹"),
("1F1E7|1F1F8","🇧🇸"),
("1F1E7|1F1F7","🇧🇷"),
("1F1E7|1F1F6","🇧🇶"),
("1F1E7|1F1F4","🇧🇴"),
("1F1E7|1F1F3","🇧🇳"),
("1F1E7|1F1F2","🇧🇲"),
("1F1E7|1F1F1","🇧🇱"),
("1F1E7|1F1EF","🇧🇯"),
("1F1E7|1F1EE","🇧🇮"),
("1F1E7|1F1ED","🇧🇭"),
("1F1E7|1F1EC","🇧🇬"),
("1F1E7|1F1EB","🇧🇫"),
("1F1E7|1F1EA","🇧🇪"),
("1F1E7|1F1E9","🇧🇩"),
("1F1E7|1F1E7","🇧🇧"),
("1F1E7|1F1E6","🇧🇦"),
("1F1E6|1F1FF","🇦🇿"),
("1F1E6|1F1FD","🇦🇽"),
("1F1E6|1F1FC","🇦🇼"),
("1F1E6|1F1FA","🇦🇺"),
("1F1E6|1F1F9","🇦🇹"),
("1F1E6|1F1F8","🇦🇸"),
("1F1E6|1F1F7","🇦🇷"),
("1F1E6|1F1F6","🇦🇶"),
("1F1E6|1F1F4","🇦🇴"),
("1F1E6|1F1F2","🇦🇲"),
("1F1E6|1F1F1","🇦🇱"),
("1F1E6|1F1EE","🇦🇮"),
("1F1E6|1F1EC","🇦🇬"),
("1F1E6|1F1EB","🇦🇫"),
("1F1E6|1F1EA","🇦🇪"),
("1F1E6|1F1E9","🇦🇩"),
("1F1E6|1F1E8","🇦🇨"),
("1F19A","🆚"),
("1F199","🆙"),
("1F198","🆘"),
("1F197","🆗"),
("1F196","🆖"),
("1F195","🆕"),
("1F194","🆔"),
("1F193","🆓"),
("1F192","🆒"),
("1F191","🆑"),
("1F18E","🆎"),
("1F17F","🅿"),
("1F17E","🅾"),
("1F171","🅱"),
("1F170","🅰"),
("1F0CF","🃏"),
("1F004","🀄"),
]

c_h_a_r = unicode_char_tupple

qry = """
SELECT COALESCE(`fdr_fdr`.`FDRAmount`, 0) AS `fdr_amount`, (SELECT SUM(CASE WHEN (U0.`Sector` = INT) 
THEN U0.`Credit` ELSE 0 END) AS `interest_during_year` FROM `fdr_fdrledger` U0 WHERE (U0.`FDR_id` = (`fdr_fdr`.`id`) AND NOT U0.`IsArchive`) GROUP BY U0.`FDR_id`) AS `interest_during_year`, (SELECT SUM(CASE WHEN (U0.`Sector` = TAX) THEN U0.`Debit` ELSE 0 END) AS `income_tax_at_source` FROM `fdr_fdrledger` U0 WHERE (U0.`FDR_id` = (`fdr_fdr`.`id`) AND NOT U0.`IsArchive`) GROUP BY U0.`FDR_id`) AS `income_tax_at_source`, (SELECT SUM(CASE WHEN (U0.`Sector` = BAN) THEN U0.`Debit` ELSE 0 END) AS `bank_charge` FROM `fdr_fdrledger` U0 WHERE (U0.`FDR_id` = (`fdr_fdr`.`id`) AND NOT U0.`IsArchive`) GROUP BY U0.`FDR_id`) AS `bank_charge`, (SELECT SUM(CASE WHEN (NOT (U0.`Sector` = INT) AND NOT (U0.`Sector` = BAN) AND NOT (U0.`Sector` = TAX)) THEN U0.`Debit` ELSE 0 END) AS `other_deduction` FROM `fdr_fdrledger` U0 WHERE (U0.`FDR_id` = (`fdr_fdr`.`id`) AND NOT U0.`IsArchive`) GROUP BY U0.`FDR_id`) AS `other_deduction`, (SELECT COALESCE(SUM((COALESCE(U0.`Credit`, 0) - COALESCE(U0.`Debit`, 0))), 0) AS `net_interest` FROM `fdr_fdrledger` U0 WHERE (U0.`FDR_id` = (`fdr_fdr`.`id`) AND NOT U0.`IsArchive`) GROUP BY U0.`FDR_id`) AS `net_interest`, (SELECT COALESCE(U0.`BalanceOnMatureDate`, 0) AS `balance_on_mature_date` FROM `fdr_fdrrenewal` U0 WHERE (U0.`FDR_id` = (`fdr_fdr`.`id`) AND NOT U0.`IsArchive`)) AS `amount_before_maturity`, ((SELECT COALESCE(U0.`BalanceOnMatureDate`, 0) AS `balance_on_mature_date` FROM `fdr_fdrrenewal` U0 WHERE (U0.`FDR_id` = (`fdr_fdr`.`id`) AND Dates = 2024-02-31 AND NOT U0.`IsArchive`)) + (SELECT COALESCE(SUM((COALESCE(U0.`Credit`, 0) - COALESCE(U0.`Debit`, 0))), 0) AS `net_interest` FROM `fdr_fdrledger` U0 WHERE (U0.`FDR_id` = (`fdr_fdr`.`id`) AND NOT U0.`IsArchive`) GROUP BY U0.`FDR_id`)) AS `matured_amount` FROM `fdr_fdr` INNER JOIN `fdr_bankbranch` ON (`fdr_fdr`.`FDRBank_id` = `fdr_bankbranch`.`id`) WHERE (NOT `fdr_fdr`.`IsArchive` AND `fdr_fdr`.`id` = 22) ORDER BY `fdr_bankbranch`.`Bank_id` ASC, `fdr_fdr`.`FDRBank_id` ASC, `fdr_fdr`.`id` ASC;
"""
replacements = [
    ('INT\)', f'{chr(39)}INT{chr(39)})'),
    ('TAX\)', f'{chr(39)}TAX{chr(39)})'), 
    ('BAN\)', f'{chr(39)}BAN{chr(39)})'),
    ('PAR\)', f'{chr(39)}PAR{chr(39)})'),
    ('VAT\)', f'{chr(39)}VAT{chr(39)})'),
    ('SER\)', f'{chr(39)}SER{chr(39)})'),
    ('FIN\)', f'{chr(39)}FIN{chr(39)})'),
    ('OTH\)', f'{chr(39)}OTH{chr(39)})'),
    ('NEW\)', f'{chr(39)}NEW{chr(39)})'),
    (r'(\d{4}-\d{2}-\d{2})', r"'\1'")
]

# Function to replace patterns in a string
def replace_patterns(text, replacements):
    for pattern, replacement in replacements:
        text = re.sub(pattern, replacement, text)
    return text


# result = replace_patterns(text, replacements)
def display(text, query=False):
    print((f" {c_h_a_r[444][1]} ").join(c_h_a_r[1258][1]*15))
    print(f'\033[38;5;208m {CLR.Bg.cyan}{CLR.bold}\033[2:4m Returned Data 📋:{CLR.reset} \033[2;30m {text}')
    print((f" {c_h_a_r[421][1]} ").join(c_h_a_r[1489][1]*15))
    if query:
        query_result_txt = f'\033[38;5;208m {CLR.Bg.magenta}{CLR.bold} Query 📋:{CLR.reset} \033[2;30m\
{str(text.query).replace(chr(34), "`").replace("CAST(","").replace("AS NUMERIC)","")};\n\
{c_h_a_r[884][1]} Total Number of Records: {CLR.Fg.green}{CLR.bold}{text.count()}{CLR.reset}'
        # query_result = re.sub(r'(\d{4}-\d{2}-\d{2})', r"'\1'", query_result_txt)
        query_result = replace_patterns(query_result_txt,replacements)
        print(query_result)
        print((f" {c_h_a_r[1491][1]} ").join(c_h_a_r[1496][1]*15),"\n")
def run():
    display(qry)

#! TODO: Change `BETWEEN YYYY-mm-dd AND YYYY-mm-dd` with `BETWEEN "YYYY-mm-dd" AND "YYYY-mm-dd"`
# BETWEEN "2024-09-01" AND "2024-09-21"
# To replace a date range in the format `BETWEEN YYYY-mm-dd AND YYYY-mm-dd` 
# with `BETWEEN "YYYY-mm-dd" AND "YYYY-mm-dd"` in Python, 
# you can use the `re` module for regular expressions. Here's an example:

#! FIXME: Code ====================
# import re
# date_range_str = "BETWEEN 2024-09-01 AND 2024-09-21"
# formatted_date_range_str = re.sub(r'(\d{4}-\d{2}-\d{2})', r'"\1"', date_range_str)
# print(formatted_date_range_str)
#! FIXME: Code End ================
# ```
# This code will output:
# ```
# BETWEEN "2024-09-01" AND "2024-09-21"
# ```
"""
The `re.sub` function is used here to find all occurrences of the date pattern `YYYY-mm-dd` 
and replace them with `"YYYY-mm-dd"`.
"""
