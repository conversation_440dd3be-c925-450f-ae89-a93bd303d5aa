from django.urls import path, include
from . import views
from . import reports
from CONSTRUCTION_PROJECT.settings.base import config
from django.views.generic import TemplateView
from . reports_xhtml2pdf import (xhtml2pdf_sgareholder_list, 
                                 xhtml2pdf_expenditureDetailsReport)

app_name = "Accounts"

urlpatterns = [
    path("", views.index, name="index"),
    path("VisitorList/", views.VisitorList.as_view(), name="VisitorList"),
    path("VisitorList/<int:pk>", views.VisitorDetails.as_view(), name="VisitorDetails"),
    path("send_mail/", views.send_mail, name="send_mail"),
    path(
        "contractor/<int:pk>",
        views.ContractorDetailView.as_view(),
        name="contractor-details",
    ),
    path(
        "shareholder_list/",
        views.ShareholderListView.as_view(),
        name="shareholder_list",
    ),
    path(
        "contractor_list/",
        views.ContractorListView.as_view(),
        name="contractor_list",
    ),
    path(
        "contractor_bill_payment_history/",
        views.ContractorBillPaymentHistory.as_view(),
        name="contractor_bill_payment_history",
    ),
    path(
        "shareholder_deposit_list/<int:shareholder_id>",
        views.ShareholderDepositList.as_view(),
        name="shareholder_deposit_list",
    ),
    path(
        "shareholder/<int:pk>",
        views.ShareholderDetailView.as_view(),
        name="shareholder-details",
    ),
    # path("chart/", views.chart, name="chart"),
    path("plot/", views.plot_chart, name="plot_chart"),
    path(
        "expenditure_summary/",
        views.ExpenditureSummary.as_view(),
        name="expenditure_summary",
    ),
    path(
        "expenditure_details_list/",
        views.DateRangeExpenditure.as_view(),
        name="expenditure_details_list",
    ),
    # path(
    #     "expenditure_details_list/",
    #     views.ExpenditureDetailsList.as_view(),
    #     name="expenditure_details_list",
    # ),
    path(
        "date_range_expenditure/",
        views.DateRangeExpenditure.as_view(),
        name="date_range_expenditure",
    ),
    path(
        "get_shareholder_deposit_info/<str:shareholder_id>",
        views.get_shareholder_deposit_info,
        name="get_shareholder_deposit_info",
    ),
]
#! Dataset  =========================
Get_Dataset_URLs = [
    path("get_item/<str:itemCode_id>", views.get_item, name="get_item"),
    path("get_unit/<str:item_id>", views.get_unit, name="get_unit"),
    path("get_balance/", views.get_balance, name="get_balance"),
    path("get_income_item_unit/<str:item_id>", views.get_income_item_unit, name="get_income_item_unit"),
    path(
        "get_credit_purchase_rest_amount/<int:seller_id>",
        views.get_credit_purchase_rest_amount,
        name="get_credit_purchase_rest_amount",
    ),
    path(
        "get_contractor_bill_rest_amount/<int:bill_id>",
        views.get_contractor_bill_rest_amount,
        name="get_contractor_bill_rest_amount",
    ),
    path(
        "get_income_item/<int:IncomeSector_id>",
        views.get_income_item,
        name="get_income_item",
    ),
    path(
        "get_account_no/<int:id>/<str:table>",
        views.get_account_no,
        name="get_account_no",
    ),
    path(
        "get_current_workphase/",
        views.get_current_workphase,
        name="get_current_workphase",
    ),
]

Forms_URLs = [
    path(
        "targetedamount/", views.TargetedAmountPosting.as_view(), name="targetedamount"
    ),
    #! Shareholder ===============
    path("shareholder/", views.ShareholderView.as_view(), name="shareholder"),
    path(
        "shareholder_update/<int:pk>",
        views.ShareholderUpdate.as_view(),
        name="shareholder_update",
    ),
    path(
        "shareholder_deposit/",
        views.ShareholderDepositView.as_view(),
        name="shareholder_deposit",
    ),
    path(
        "shareholder_deposit_update/<int:pk>",
        views.ShareholderDepositUpdate.as_view(),
        name="shareholder_deposit_update",
    ),
    #! Contractor ===============
    path("contractortype/", views.ContractorTypeView.as_view(), name="contractortype"),
    path("contractor/", views.ContractorView.as_view(), name="contractor"),
    path(
        "contractor_update/<int:pk>",
        views.ContractorUpdate.as_view(),
        name="contractor_update",
    ),
    path(
        "expenditure_update/<int:pk>",
        views.ExpenditureUpdate.as_view(),
        name="expenditure_update",
    ),
    path(
        "contractor_bill_submission/",
        views.ContractorBillSubmissionView.as_view(),
        name="contractor_bill_submission",
    ),
    path(
        "contractor_bill_payment/",
        views.ContractorBillPaymentView.as_view(),
        name="contractor_bill_payment",
    ),
    path(
        "record_delete/<int:id>/<str:table>",
        views.recordDelete,
        name="record_delete",
    ),
    #! Item ===============
    path("itemcode/", views.ItemCodeView.as_view(), name="itemcode"),
    path("item/", views.ItemView.as_view(), name="item"),
    path("expenditure/", views.ExpenditureView.as_view(), name="expenditure_posting"),
    #! CreditPurchase ===============
    path(
        "credit_purchase/", views.CreditPurchaseView.as_view(), name="credit_purchase"
    ),
    path(
        "credit_purchase_payment/",
        views.CreditPurchasePaymentView.as_view(),
        name="credit_purchase_payment",
    ),
    path(
        "credit_purchase_payment_history/",
        views.CreditPurchasePaymentHistory.as_view(),
        name="credit_purchase_payment_history",
    ),
    #! BankAndCash ===============
    path(
        "add_new_bank_account/",
        views.BankAccountView.as_view(),
        name="add_new_bank_account",
    ),
    path(
        "bank_account_update/<int:pk>",
        views.BankAccountUpdate.as_view(),
        name="bank_account_update",
    ),
    path(
        "bank_transaction/",
        views.BankTransaction.as_view(),
        name="bank_transaction",
    ),
    path(
        "bank_list/",
        views.BankListView.as_view(),
        name="bank_list",
    ),
    #! Work Phase ===============
    path(
        "work_phase/", views.WorkPhaseView.as_view(), name="work_phase"
    ),
    path(
        "work_phase_change/", views.CurrentWorkPhaseView.as_view(), name="work_phase_change"
    ),
]

Income_URLs = [
    path(
        "income_sector_add/",
        views.IncomeSectorView.as_view(),
        name="income_sector_add",
    ),
    path(
        "income_item_add/",
        views.IncomeItemView.as_view(),
        name="income_item_add",
    ),
    path(
        "income_add/",
        views.IncomeView.as_view(),
        name="income_add",
    ),
    path(
        "income_update/<int:pk>",
        views.IncomeUpdate.as_view(),
        name="income_update",
    ),
    path(
        "details_income_list/",
        views.DateRangeIncome.as_view(),
        name="details_income_list",
    ),
    path(
        "date_range_income/",
        views.DateRangeIncome.as_view(),
        name="date_range_income",
    ),
]

#! PDF Reports ========================
Report_URLs = [
    path(
        "contractorListReport/",
        reports.contractorListReport,
        name="contractorListReport",
    ),
    path(
        "contractorDetails/<int:pk>",
        reports.contractorDetails,
        name="contractorDetails",
    ),
    path(
        "shareholderDetails/<int:pk>",
        reports.shareholderDetails,
        name="shareholderDetails",
    ),
    path(
        "expenditureSummaryReport/",
        reports.expenditureSummaryReport,
        name="expenditureSummaryReport",
    ),
    path(
        "expenditureDetailsReport/",
        reports.expenditureDetailsReport,
        name="expenditureDetailsReport",
    ),
    path(
        "dateRangeExpenditureReport/",
        reports.expenditureDetailsReport,
        name="dateRangeExpenditureReport",
    ),
    path(
        "incomeDetailsReport/",
        reports.incomeDetailsReport,
        name="incomeDetailsReport",
    ),
    path(
        "dateRangeIncomeReport/",
        reports.incomeDetailsReport,
        name="dateRangeIncomeReport",
    ),
    path(
        "shareholderListReport/",
        reports.shareholderListReport,
        name="shareholderListReport",
    ),
    path(
        "shareholderDepositReport/<int:shareholder>",
        reports.shareholderDepositReport,
        name="shareholderDepositReport",
    ),
    path(
        "sendMailshareholderDepositReport/",
        reports.sendMailshareholderDepositReport,
        name="sendMailshareholderDepositReport",
    ),

]
Xhtml2PDF_Report_URLs=[
        path(
        "xhtml2pdf_sgareholder_list/",
        xhtml2pdf_sgareholder_list,
        name="xhtml2pdf_sgareholder_list",
    ),
    path(
        "xhtml2pdf_expenditureDetailsReport/",
        xhtml2pdf_expenditureDetailsReport,
        name="xhtml2pdf_expenditureDetailsReport",
    ),
]

if int(config["NOT_AVAILABLE"]) == 0:
    urlpatterns += Forms_URLs
    urlpatterns += Income_URLs
    urlpatterns += Report_URLs
    urlpatterns += Get_Dataset_URLs
    urlpatterns += Xhtml2PDF_Report_URLs
else:
    urlpatterns = [path("", TemplateView.as_view(template_name="test_url.html"), 
                        {"greeting": "This Web Application/Site is not available."},
                        name="test_url")]


