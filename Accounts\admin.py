from django.contrib import admin
from .forms import ContractorForm
from .models import (WorkPhase, CurrentWorkPhase, ItemCode, Item, ContractorType, 
                     Contractor, ContractorBillSubmission, ContractorBillPayment, 
                     CreditPurchase, CreditPurchasePayment, Expenditure, Shareholder, 
                     ShareholderDeposit, OfficeItemCode, OfficeItem, OfficeExpenditure, 
                     UserLoggedinRecord, UserLoggedinFailed, TargetedAmount, IncomeSector, 
                     IncomeItem, Income, BankAccount, BankLedger, CashLedger)
from import_export.admin import ImportExportModelAdmin

# admin.site.register(Employee, ImportExportModelAdmin)

@admin.register(WorkPhase)
class WorkPhaseAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'description',
        'fromDate',
        'toDate',
        'estimatedCost',
    )
    list_filter = ('fromDate', 'toDate')


@admin.register(CurrentWorkPhase)
class CurrentWorkPhaseAdmin(admin.ModelAdmin):
    list_display = ('id', 'workPhase', 'dateOfChange')
    list_filter = ('workPhase', 'dateOfChange')


@admin.register(ItemCode)
class ItemCodeAdmin(admin.ModelAdmin):
    list_display = ('id', 'workSector')


@admin.register(Item)
class ItemAdmin(admin.ModelAdmin):
    list_display = ('id', 'itemName', 'unit', 'ItemCode')


@admin.register(ContractorType)
class ContractorTypeAdmin(admin.ModelAdmin):
    list_display = ('id', 'contractorType')


@admin.register(Contractor)
class ContractorAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'dateOfJoin',
        'contractor',
        'contractorType',
        'address',
        'NID',
        'TIN',
        'TelephoneNo',
        'Mobile',
        'Email',
        'image',
        'IsArchive',
    )
    list_filter = ('dateOfJoin', 'contractorType', 'IsArchive')
    form = ContractorForm


@admin.register(ContractorBillSubmission)
class ContractorBillSubmissionAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'dateOfBillSubmission',
        'description',
        'amount',
        'remarks',
        'contractor',
    )
    list_filter = ('dateOfBillSubmission', 'contractor')


@admin.register(ContractorBillPayment)
class ContractorBillPaymentAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'dateOfTransaction',
        'voucherNo',
        'modeOfTransaction',
        'amount',
        'remarks',
        'bill',
        'item',
        'unit',
        'rate',
        'quantity',
        'work_phase',
        'tid',
    )
    list_filter = ('dateOfTransaction', 'bill', 'item', 'work_phase')


@admin.register(CreditPurchase)
class CreditPurchaseAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'dateOfPurchase',
        'seller',
        'address',
        'description',
        'mobile',
        'email',
        'amount',
        'remarks',
    )
    list_filter = ('dateOfPurchase',)


@admin.register(CreditPurchasePayment)
class CreditPurchasePaymentAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'dateOfTransaction',
        'seller',
        'item',
        'description',
        'unit',
        'rate',
        'quantity',
        'voucherNo',
        'modeOfTransaction',
        'amount',
        'remarks',
        'work_phase',
        'tid',
    )
    list_filter = ('dateOfTransaction', 'seller', 'item', 'work_phase')


@admin.register(Expenditure)
class ExpenditureAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'dateOfTransaction',
        'item',
        'description',
        'unit',
        'rate',
        'quantity',
        'voucherNo',
        'modeOfTransaction',
        'amount',
        'contractor_bill_payment',
        'credit_purchase_payment',
        'remarks',
        'work_phase',
        'tid',
    )
    list_filter = ('dateOfTransaction',)
    raw_id_fields = (
        'item',
        'contractor_bill_payment',
        'credit_purchase_payment',
        'work_phase',
    )


@admin.register(Shareholder)
class ShareholderAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'dateOfJoin',
        'shareholderName',
        'address',
        'email',
        'mobile',
        'nid',
        'numberOfFlat',
        'image',
    )
    list_filter = ('dateOfJoin',)


@admin.register(ShareholderDeposit)
class ShareholderDepositAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'dateOfTransaction',
        'modeOfTransaction',
        'amount',
        'remarks',
        'shareholder',
        'work_phase',
        'tid',
    )
    list_filter = ('dateOfTransaction',)
    raw_id_fields = ('shareholder', 'work_phase')


@admin.register(OfficeItemCode)
class OfficeItemCodeAdmin(admin.ModelAdmin):
    list_display = ('id', 'Sector')


@admin.register(OfficeItem)
class OfficeItemAdmin(admin.ModelAdmin):
    list_display = ('id', 'itemName', 'unit', 'officeitem')
    list_filter = ('officeitem',)


@admin.register(OfficeExpenditure)
class OfficeExpenditureAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'dateOfTransaction',
        'item',
        'description',
        'unit',
        'quantity',
        'voucherNo',
        'modeOfTransaction',
        'amount',
        'remarks',
        'work_phase',
        'tid',
    )
    list_filter = ('dateOfTransaction', 'item', 'work_phase')


@admin.register(UserLoggedinRecord)
class UserLoggedinRecordAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'visitorIP',
        'country',
        'country_code',
        'continent',
        'continent_code',
        'city',
        'county',
        'region',
        'region_code',
        'timezone',
        'owner',
        'longitude',
        'latitude',
        'currency',
        'languages',
        'visitCount',
        'user',
        'visitDateTime',
    )
    list_filter = ('visitDateTime',)


@admin.register(UserLoggedinFailed)
class UserLoggedinFailedAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'visitorIP',
        'country',
        'country_code',
        'continent',
        'continent_code',
        'city',
        'county',
        'region',
        'region_code',
        'timezone',
        'owner',
        'longitude',
        'latitude',
        'currency',
        'languages',
        'visitCount',
        'user',
        'password',
        'visitDateTime',
    )
    list_filter = ('visitDateTime',)


@admin.register(TargetedAmount)
class TargetedAmountAdmin(admin.ModelAdmin):
    list_display = ('id', 'inputDate', 'amount')
    list_filter = ('inputDate',)


@admin.register(IncomeSector)
class IncomeSectorAdmin(admin.ModelAdmin):
    list_display = ('id', 'incomeSector')


@admin.register(IncomeItem)
class IncomeItemAdmin(admin.ModelAdmin):
    list_display = ('id', 'incomeSector', 'itemName', 'unit')
    list_filter = ('incomeSector',)


@admin.register(Income)
class IncomeAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'dateOfTransaction',
        'incomeItem',
        'description',
        'unit',
        'quantity',
        'rate',
        'voucherNo',
        'modeOfTransaction',
        'amount',
        'remarks',
        'work_phase',
        'tid',
    )
    list_filter = ('dateOfTransaction', 'incomeItem', 'work_phase')


admin.site.register(BankAccount,ImportExportModelAdmin)
# class BankAccountAdmin(admin.ModelAdmin):
#     list_display = (
#         'id',
#         'openingDate',
#         'bankName',
#         'branchName',
#         'accountNo',
#         'remarks',
#         'isArchive',
#     )
#     list_filter = ('openingDate', 'isArchive')


@admin.register(BankLedger)
class BankLedgerAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'dateOfTransaction',
        'description',
        'account',
        'debit',
        'credit',
        'remarks',
        'work_phase',
        'tid',
    )
    list_filter = ('dateOfTransaction',)
    raw_id_fields = ('account', 'work_phase')


@admin.register(CashLedger)
class CashLedgerAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'dateOfTransaction',
        'description',
        'debit',
        'credit',
        'remarks',
        'work_phase',
        'tid',
    )
    list_filter = ('dateOfTransaction',)
