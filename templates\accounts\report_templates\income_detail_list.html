{% extends 'main.html' %}
{% load static %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% load mahimsoft_tags %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}
  {{ company_name }}
{% endblock %}
{% block content %}
  {{ income.list.0}}
  <div class="container pt-3">
    <div class="p-2 my-2 text-center">
      <h3 class="text-muted">Income Details</h3>
    </div>
    <div class="d-flex justify-content-between mb-2">
      <h5 class="text-danger">Total Expenditure: {{ currency }} {{ grand_total.amount__sum|intcomma_bd }}</h5>
      <span>
          <a href="/incomeDetailsReport/" target="_blank" class="btn btn-sm btn-outline-danger"><i class="bi bi-download"></i> PDF</a>
          <a href='{{ request.META.HTTP_REFERER }}' class='btn btn-outline-primary btn-sm me-0 ms-1 mb-0' role='button'><i class="bi bi-arrow-left-square-fill"></i> Back</a>
        </span>
    </div>
    {% regroup income by income_sector as sectors %}
    <div class="nav_tabs">
      <ul class="nav nav-tabs justify-content-center card-header-tabs" role="tablist">
        {% for sector in sectors %}
          <li class="nav-item" role="presentation">
            <a class="nav-link x_nav-link {% if forloop.last %}
                active
                page
              {% endif %}" data-bs-toggle="pill" href="#{{ sector.grouper|remove_spaces_n_leading_digit }}">{{ sector.grouper }}</a>
            <hr class="text-white p-0 m-0 bg-white" />
          </li>
        {% endfor %}
      </ul>
    </div>
    <div class="tab-content">
      {% for sector in sectors %}
        <div id="{{ sector.grouper|remove_spaces_n_leading_digit }}" class="tab-pane fade {% if forloop.last %}show active{% endif %}">
          <div class="card">
            <h5 class="card-header text-success" style="background-color:rgb(188, 213, 203);">
              <div class="d-flex justify-content-between">
                <span><i class="bi bi-back"></i> {{ sector.grouper }}</span>
                <span class="fs-6">
                  Total: {{ currency }} {{ sector.list.0.incomesector_sum|intcomma_bd }}
                  <a href="{% url 'Accounts:incomeDetailsReport' %}?incomesector={{ sector.list.0.income_sector_id }}" target="_blank" style="text-decoration: none; padding-bottom: 4px;"><img src="{% static 'images/file-pdf.svg' %}" width="22px" heidht="22px" alt="PDF" /></a>
                </span>
              </div>
            </h5>
          </div>
          {% regroup sector.list by income_item_name as item %}
          {% for row in item %}
            <div class="card mt-2" style="background-color: #FCE6C9;">
              <div class="d-flex justify-content-between">
                <h5 class="p-0 ps-2 pt-1">{{ row.grouper }}</h5>
                <p class="pt-1 pe-2 pb-0 mb-0">Total: {{ currency }} {{ row.list.0.sum_amount|intcomma_bd }}</p>
              </div>
            </div>
            <div class="table-responsive-sm">
              <table class="table table-bordered table-striped table-sm">
                <thead>
                  <tr>
                    <th class="text-center" style="width: 5%;">#</th>
                    <th class="text-center" style="width: 15%;">Date</th>
                    <th class="text-center" style="width: 30%;">Description</th>
                    <th class="text-center" style="width: 10%;">Quantity</th>
                    <th class="text-center" style="width: 10%;">Unit</th>
                    <th class="text-center" style="width: 15%;">Voucher No.</th>
                    <th class="text-end pe-4" style="width: 15%;">Amount ({{ currency }}</th>
                    {% if request.user|has_group:'Admin' %}
                      <th colspan="2" class="text-center" style="width: 15%;">Edit</th>
                    {% endif %}
                  </tr>
                </thead>
                <tbody>
                  {% if request.user|has_group:'Admin' %}
                    {% for dt in row.list %}
                      <tr class="">
                        <td class="text-center">{{ forloop.counter }}</td>
                        <td>{{ dt.dateOfTransaction }}</td>
                        <td>{{ dt.description }}</td>
                        <td class="text-end pe-4">{{ dt.quantity|intcomma_bd }}</td>
                        <td>{{ dt.units }}</td>
                        <td class="text-center">{{ dt.voucherNo }}</td>
                        <td class="text-end pe-4">{{ dt.amount|intcomma_bd }}/-</td>
                        {% if dt.contractor_bill_payment or dt.credit_purchase_payment %}
                          <td class="text-center isDisabled">
                            <i class="bi bi-trash"></i>
                          </td>
                          <td class="text-center isDisabled">
                            <i class="bi bi-pencil-square"></i>
                          </td>
                        {% else %}
                          <td class="text-center">
                            <a class="a_link" href="#"><i class="bi bi-trash"></i></a>
                          </td>
                          <td class="text-center">
                            <a class="b_link" href="{{ dt.get_absolute_url }}"><i class="bi bi-pencil-square"></i></a>
                          </td>
                        {% endif %}
                      </tr>
                    {% endfor %}
                  {% else %}
                    {% for dt in row.list %}
                      <tr class="">
                        <td class="text-center">{{ forloop.counter }}</td>
                        <td>{{ dt.dateOfTransaction }}</td>
                        <td>{{ dt.description }}</td>
                        <td class="text-end pe-4">{{ dt.quantity|intcomma_bd }}</td>
                        <td>{{ dt.units }}</td>
                        <td class="text-center">{{ dt.voucherNo }}</td>
                        <td class="text-end pe-4">{{ dt.amount|intcomma_bd }}/-</td>
                      </tr>
                    {% endfor %}
                  {% endif %}
                  <tr>
                    <th colspan="3" class="text-end">Total Quantity =</th>
                    <th class="text-end pe-4">{{ row.list.0.sum_quantity|intcomma_bd }}</th>
                    <th>{{ row.list.0.units }}</th>
                    <th class="text-end">Total Amount =</th>
                    <th class="text-end pe-4">{{ row.list.0.sum_amount|intcomma_bd }}/-</th>
                    <th colspan="2"></th>
                  </tr>
                </tbody>
              </table>
            </div>
          {% endfor %}
        </div>
      {% endfor %}
    </div>
  </div>
{% endblock %}
