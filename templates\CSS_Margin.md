## ***<span style="color:red">Absolute Lengths</span>***

Absolute length units represent fixed measurements that are not affected by the user's screen or resolution. They're best used when the physical output matters—like in printed documents.

***Common absolute length units:***

| <code style="color:red">Unit</code> | <code style="color:green">Description</code>                  | <code style="color:#FF8282">Equivalent to</code>            |
|------|------------------------------|---------------------------|
| `cm` | Centimeters                  | 1 cm = 37.8 px            |
| `mm` | Millimeters                  | 10 mm = 1 cm              |
| `in` | Inches                       | 1 in = 96 px              |
| `pt` | Points                       | 1 pt = 1/72 in            |
| `pc` | Picas                        | 1 pc = 12 pt              |
| `px` | Pixels (technically relative but often treated as absolute for screen media) | 1 px = 1/96 in |

> 💡 Tip: Avoid absolute lengths for responsive web design. They're more suited for printed output where dimensions need to be precise.

## ***<span style="color:red">Relative Lengths</span>***

Relative length units are contextual—they depend on other values like the font size of an element or the viewport size. They're essential for building flexible, responsive designs.

***Common relative length units:***

| <code style="color:red">Unit</code>   | <code style="color:green">Description</code>                                              | <code style="color:#FF8282">Depends On</code>                                  |
|--------|-----------------------------------------------------------|----------------------------------------------|
| `em`   | Relative to the font-size of the element                  | 1em = current element’s font size            |
| `rem`  | Relative to the font-size of the root element (`html`)   | 1rem = root font size                        |
| `%`    | Percentage relative to parent element                     | Often used for width, height, margin         |
| `vw`   | 1% of the viewport's width                                | 100vw = full width of viewport               |
| `vh`   | 1% of the viewport's height                               | 100vh = full height of viewport              |
| `vmin` | 1% of the smaller of `vw` or `vh`                         | Useful for scale-based layouts               |
| `vmax` | 1% of the larger of `vw` or `vh`                          | Useful for adaptive spacing                  |
| `ch`   | Width of the “0” (zero) character in the current font     | Great for aligning monospace or numeric text |
| `ex`   | x-height of the current font                              | Rarely used, varies by font style            |

> 💡 Tip: `rem` is great for consistent scaling across a document, while `em` allows nested elements to adapt relatively.

> Source: [Markdown Guide](https://www.markdownguide.org/basic-syntax/)