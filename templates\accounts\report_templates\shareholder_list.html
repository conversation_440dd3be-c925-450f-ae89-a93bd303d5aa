{% extends "main.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% load mahimsoft_tags %}

{% block style %}
<style>
    td, th {
        font-size: small;
    }
</style>
    
{% endblock style %}
    
{% comment %} {% load i18n %} {% endcomment %}
{% block content %}

<div class="container pt-3">
    <div class="card">
        <h3 class="text-muted text-center"><i class="bi bi-people-fill"></i> {{heading}}</h3>
    </div>
    <p class="text-end my-2 d-flex justify-content-end">
        <a href="{% url 'Accounts:shareholderListReport' %}" target="_blank" class="btn btn-sm btn-outline-success">
        <i class="bi bi-download"></i> PDF</a>
        <a href='{{ request.META.HTTP_REFERER }}' class='btn btn-outline-primary btn-sm me-0 ms-1 mb-0' role='button'><i class="bi bi-arrow-left-square-fill"></i> Back</a>
    </p>
        <!-- shareholderListReport   xhtml2pdf_sgareholder_list      -->
    <div class="table table-active">
        <table class="table table-sm align-middle table-bordered table-striped table-hover table-responsive">
            <tr>
                <th class="text-center" style="width: 2%;">#</th>
                <th class="text-center" style="width: 7%;">Photo</th>
                <th class="text-center" style="width: 20%;">Name</th>
                <th class="text-center" style="width: 10%;">Mobile</th>
                <th class="text-center" style="width: 10%;">Email</th>
                <th class="text-center" style="width: 10%;">Date of Join</th>
                <th class="text-center" style="width: 10%;">Nos. of Share</th>
                <th class="text-center" style="width: 10%;">Deposited Amnt</th>
                <th class="text-center" style="width: 10%;">Rest Amnt to Pay</th>
                <th colspan="3" class="text-center">Action</th>
            </tr>
            {% with admin_user=request.user|has_group:"Admin" %}
            {% for dt in shareholder %}
            <tr>
                <td class="text-center">{{forloop.counter}}</td>
                <td><img src="{{dt.avatar}}" class="card-img-top" alt="masud"
                        style="border-radius: 15%; width: 60px; height: 60px;  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);">
                </td>
                <td>{{dt.shareholderName}}</td>
                <td>{{dt.mobile|default_if_none:"---"}}</td>
                <td>{{dt.email|default_if_none:"---"}}</td>
                <td>{{dt.dateOfJoin|date:"d b Y"|title}}</td>
                <td class="text-center">{{dt.numberOfFlat|floatformat:"1"}}</td>
                <td class="text-end pe-1">{{dt.deposit_amount_sum|default_if_none:0|intcomma_bd}}/-</td>

                {% if dt.numberOfFlat|number_product:targeted_amount_per_flat|subtract:dt.deposit_amount_sum > 0 %}
                <td class="text-end pe-1 text-danger">
                    {{dt.numberOfFlat|number_product:targeted_amount_per_flat|subtract:dt.deposit_amount_sum|number_product:-1|intcomma_bd}}/-
                </td>
                {% elif dt.numberOfFlat|number_product:targeted_amount_per_flat|subtract:dt.deposit_amount_sum == 0 %}
                <td class="text-end pe-1">
                    0/-
                </td>
                {% else %}
                <td class="text-end pe-1">
                    {{dt.numberOfFlat|number_product:targeted_amount_per_flat|subtract:dt.deposit_amount_sum|number_product:-1|intcomma_bd}}/-
                </td>
                {% endif %}

                <td class="text-center"><a class="a_link" href="{{ dt.get_absolute_url}}">
                        <i class="bi bi-file-earmark-text-fill"></i>
                    </a></td>
                {% if admin_user %}
                <td class="text-center"><a class="b_link" href="/shareholder_deposit_list/{{dt.id}}"><img
                            class="text-warning" src="{% static 'images/coins.svg' %}"
                            style="width: 16px; height: 16px;" alt=""></a></td>
                <td class="text-center"><a class="c_link" href="/shareholder_update/{{dt.id}}"><i
                            class="bi bi-pencil-square"></i></a></td>
                {% else %}
                <td colspan="2" class="text-center"><a class="b_link" href="/shareholder_deposit_list/{{dt.id}}"><img
                            class="text-warning" src="{% static 'images/coins.svg' %}"
                            style="width: 16px; height: 16px;" alt=""></a></td>
                {% endif %}
            </tr>
            {% endfor %}
            {% endwith %}
        </table>
    </div>
    <p class="text-danger">NB: Rest amount to pay calculated as per targeted amount per share as {{ currency }}:
        {{no_of_flat_per_share|number_product:targeted_amount_per_flat|intcomma_bd}}/-</p>
</div>
<br>
{% endblock %}