import random
from pathlib import Path
first_names_male = [
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "Lu<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "Omar",
    "Parvez",
    "Qadir",
    "Rakin",
    "Sufian",
    "Tan<PERSON>",
    "Usman",
    "<PERSON>ahi<PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "Cyrus",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON>shi<PERSON>",
    "Labeeb",
    "<PERSON><PERSON>r",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON>aru<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "Kamal",
    "Latif",
    "Mazhar",
    "Nadeem",
    "Omer",
    "Parvez",
    "Qadir",
    "Rafiq",
    "Salman",
    "Tanjil",
    "Uzair",
    "Vikram",
    "Waheed",
    "Yaqoob",
    "Zubair",
    "Aamir",
    "Bashar",
    "Chetan",
    "Dev",
    "Ehsan",
    "Faheem",
    "Haroon",
    "Irfan",
    "Jibran",
    "Karim",
    "Lakhman",
    "Moin",
    "Nihal",
    "Owais",
    "Pranav",
    "Qais",
    "Riaz",
    "Sadiq",
    "Tanveer",
    "Usama",
    "Virat",
    "Wali",
    "Yousuf",
    "Zainul",
    "Azhar",
    "Bahram",
    "Chand",
    "Deepak",
    "Ebrahim",
    "Fahad",
    "Hamid",
    "Iqbal",
    "Jehan",
    "Kabir",
    "Lutfullah",
    "Mushtaq",
    "Naushad",
    "Okan",
    "Pavan",
    "Quds",
    "Raheel",
    "Sarfaraz",
    "Tahsin",
    "Umar",
    "Vijay",
    "Wasim",
    "Yavar",
    "Zehan",
]

first_names_female = [
    "Ayesha",
    "Nadia",
    "Tasnim",
    "Maliha",
    "Sumaiya",
    "Rifat",
    "Sabrina",
    "Nusrat",
    "Sharmin",
    "Jannat",
    "Afreen",
    "Bilqis",
    "Chandni",
    "Daliya",
    "Elina",
    "Farzana",
    "Gulshan",
    "Hiba",
    "Isra",
    "Jalisa",
    "Kainat",
    "Laila",
    "Meher",
    "Naima",
    "Orin",
    "Parveen",
    "Qurratul",
    "Raida",
    "Saima",
    "Tabassum",
    "Uzma",
    "Vashti",
    "Warda",
    "Yasmina",
    "Zoya",
    "Amira",
    "Bushra",
    "Chitra",
    "Diya",
    "Eshe",
    "Fatima",
    "Gauri",
    "Hafsa",
    "Imani",
    "Jameela",
    "Khadija",
    "Latifa",
    "Munira",
    "Nahida",
    "Omera",
    "Poonam",
    "Qamar",
    "Rashida",
    "Sana",
    "Tanisha",
    "Umaira",
    "Varisha",
    "Wahida",
    "Yumna",
    "Zara",
    "Aziza",
    "Basma",
    "Chahida",
    "Durdana",
    "Ekram",
    "Fareeha",
    "Ghazala",
    "Haniya",
    "Israt",
    "Javeria",
    "Kamini",
    "Lubna",
    "Mahjabeen",
    "Naila",
    "Osheen",
    "Prisha",
    "Quaiser",
    "Rukhsana",
    "Shagufta",
    "Tahira",
    "Ulfat",
    "Vimala",
    "Wasima",
    "Yasira",
    "Zuleikha",
    "Alina",
    "Brinda",
    "Chahina",
    "Dhara",
    "Enaya",
    "Farhat",
    "Geeta",
    "Huma",
    "Ifrah",
    "Jannatul",
    "Karishma",
    "Labeeba",
    "Mehreen",
    "Nargis",
    "Oindrila",
    "Purvi",
    "Qirat",
    "Rehana",
    "Sadia",
    "Taniya",
    "Urvashi",
    "Vidya",
    "Wania",
    "Yashika",
    "Zaima",
    "Anila",
    "Bela",
    "Chandrima",
    "Devika",
    "Eshita",
    "Faria",
    "Gul",
    "Hina",
    "Irha",
    "Jaya",
    "Kashmira",
    "Lavanya",
    "Muskan",
    "Nilofer",
    "Orpita",
    "Pervin",
    "Qasira",
    "Rida",
    "Shaista",
    "Tamanna",
    "Uma",
    "Varina",
    "Warisha",
    "Yashna",
    "Zareen",
]

last_names = [
    "Khan",
    "Rahman",
    "Chowdhury",
    "Hossain",
    "Ahmed",
    "Islam",
    "Sarker",
    "Mollah",
    "Alam",
    "Biswas",
    "Siddiqui",
    "Das",
    "Sen",
    "Roy",
    "Bhattacharya",
    "Ghosh",
    "Dutta",
    "Chatterjee",
    "Majumdar",
    "Mukherjee",
    "Sengupta",
    "Banerjee",
    "Bose",
    "Guha",
    "Deb",
    "Pal",
    "Saha",
    "Basu",
    "Kar",
    "Mitra",
    "Bhowmick",
    "Halder",
    "Pramanik",
    "Goswami",
    "Mahmud",
    "Sheikh",
    "Reza",
    "Qadir",
    "Firoz",
    "Haque",
    "Kamal",
    "Nawaz",
    "Jahan",
    "Rafiq",
    "Masud",
    "Idris",
    "Salahuddin",
    "Tareq",
    "Imran",
    "Shams",
    "Naim",
    "Ehsan",
    "Farooq",
    "Hasan",
    "Sohail",
    "Anwar",
    "Shamim",
    "Wahid",
    "Rashid",
    "Tariq",
    "Shah",
    "Zaman",
    "Yousuf",
    "Sadiq",
    "Arif",
    "Pervez",
    "Zubair",
    "Azhar",
    "Nadeem",
    "Wasiq",
    "Mubarak",
    "Zaki",
    "Parveen",
    "Nargis",
    "Shahida",
    "Bushra",
    "Nusrat",
    "Yasmin",
    "Begum",
    "Roshan",
    "Moin",
    "Sarwar",
    "Shakir",
    "Saad",
    "Noor",
    "Sultan",
    "Faheem",
    "Tashnim",
    "Mir",
    "Chaudhary",
    "Qurban",
    "Tanvir",
    "Jamil",
    "Farid",
    "Mahbub",
    "Mohtashim",
    "Fahad",
    "Zahir",
    "Owais",
    "Rashed",
    "Kabir",
    "Mubeen",
    "Hossain",
    "Razzaq",
    "Suleiman",
    "Ashraf",
    "Shabbir",
    "Kamran",
    "Nizami",
    "Rashida",
    "Safwan",
    "Nafees",
    "Fahima",
    "Adeeb",
    "Sufian",
    "Lutfur",
    "Amaan",
    "Waqas",
    "Yaqoob",
    "Hassan",
    "Moiz",
    "Sohail",
    "Abrar",
    "Zia",
    "Tariq",
    "Iqbal",
    "Mirza",
    "Wahida",
    "Jameela",
    "Yasir",
    "Irfan",
    "Khurshed",
    "Naeem",
    "Rameez",
    "Shaheen",
    "Mehtab",
    "Arshad",
    "Yamin",
    "Javed",
    "Zohair",
    "Rayan",
    "Mahfuz",
]
# Example lists with 200 first names and 200 last names
# first_names = [f"FirstName{i}" for i in range(1, len(first_names_male) + 1)]
# last_names = [f"LastName{i}" for i in range(1, len(last_names) + 1)]

# Generate 50 random male names
name_list_male = [
    f"Mr. {random.choice(first_names_male)} {random.choice(last_names)}" for _ in range(50)
]
# Generate 50 random female names
name_list_female = [
    f"Mrs. {random.choice(first_names_female)} {random.choice(last_names)}" for _ in range(50)
]

BASE_DIR = Path(__file__).resolve().parent
print(BASE_DIR)

def open_file(path,open_mode):
    with open(path,open_mode) as f:
        return f.read()

def fetchAndSaveToFile(): 
    txt = str(name_list_male)
    with open(f"{BASE_DIR}/name_list_male.csv", "w") as f: 
        f.write(txt)
    txt2 = str(name_list_female)
    with open(f"{BASE_DIR}/name_list_female.csv", "w") as f: 
        f.write(txt2) 
# Print the names
# for name in name_list_female:
#     print(name)

if __name__ == "__main__":
    fetchAndSaveToFile()


