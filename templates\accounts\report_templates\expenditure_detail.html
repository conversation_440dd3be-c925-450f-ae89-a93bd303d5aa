{% extends "main.html" %}
{% load static %} 
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% load mahimsoft_tags %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}{{company_name}}{%endblock %}
{% block content %}

<div class="container pt-3">
    <div class="p-2 my-2 text-center">
        <h3 class="text-muted">Expenditure Details</h3>        
    </div>
    <div class="d-flex justify-content-end"><h5 class="text-danger">Total Expenditure: {{ currency }} {{grand_total.amount__sum|intcomma_bd}}</h5></div>
{% regroup expenditure by work_sector as sectors %} 
{% for sector in sectors %}
<div class="card bg-secondary">
<h4 class="text-white p-0">{{ sector.grouper }}</h4>
</div>
{% regroup sector by item_name as items %}
{% for item in items %}
<div class="card bg-secondary">
    <h4 class="text-white p-1">{{ item.grouper }}</h4>
    </div>
    {% for dt in item.list  %}
<div class="table-responsive-sm" >
    <table class="table table-bordered">
        <thead>
            <tr>
                <th class="text-center" style="width: 5%;">#</th>
                <th class="text-center" style="width: 30%;">Item Name</th>
                <th class="text-center" style="width: 20%;">Quantity</th>
                <th class="text-center" style="width: 10%;">Unit</th>
                <th class="text-center" style="width: 20%;">Amount</th>
            </tr>
        </thead>
        <tbody>

    {% for x in dt.list  %}
<tr class="">
    <td class = "text-center">{{forloop.counter}}</td>
    <td>{{x.description}}</td>
 <!-- {% comment %}Take a closer look at the widthratio template tag. Given {% widthratio a b c %} it computes (a/b)*c {% endcomment %}--> 
    <td class="text-end pe-5 me-5">{{x.amount}}</td>
    <td>{{x.unit}}</td>
    <td class="text-end pe-5 me-5">{{x.units}}/-</td>
</tr>
{% endfor %}

<td scope="row"></td>
<td></td>
<td></td>
<th>Total =</th>
<th class="text-end pe-5 me-5">{{dt.list.0.worksector_sum|intcomma_bd}}/-</th>

</tr>
</tbody>
</table>
</div> 
{% endfor %}
{% endfor %}
{% endfor %}
    
</div>

{% endblock %}