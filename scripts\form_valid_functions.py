def form_save_update(self, form, model_name, descriptions, is_credit):
    with transaction.atomic():
        qs = model_name.objects.filter(
            id=self.kwargs["pk"]
        ).values()
        get_transaction_mode = qs[0]["modeOfTransaction"]
        get_tid = qs[0]["tid"]
        qs_bank_ledger = BankLedger.objects.filter(tid=get_tid)
        qs_cash_ledger = CashLedger.objects.filter(tid=get_tid)
        form.save()
        init_credit = 0
        init_debit = 0
        if is_credit:
            init_credit=form["amount"].value()
            init_debit = 0
        else:
            init_credit = 0
            init_debit = form["amount"].value()
        if form["modeOfTransaction"].value() != get_transaction_mode:
            if qs_bank_ledger.count() > 0:
                qs_bank_ledger.delete()
                CashLedger.objects.create(
                    dateOfTransaction=form["dateOfTransaction"].value(),
                    description=descriptions,
                    credit=init_credit,
                    debit=init_debit,
                    remarks=form["remarks"].value(),
                    tid=get_tid,
                )
            else:
                qs_cash_ledger.delete()
                BankLedger.objects.create(
                    dateOfTransaction=form["dateOfTransaction"].value(),
                    description=descriptions,
                    account=BankAccount.objects.get(id=form["account"].value()),
                    credit=init_credit,
                    debit=init_debit,
                    remarks=form["remarks"].value(),
                    tid=get_tid,
                )
        else:
            if qs_bank_ledger.count() > 0:
                qs_bank_ledger_item = BankLedger.objects.get(tid=get_tid)
                qs_bank_ledger_item.dateOfTransaction = form["dateOfTransaction"].value() 
                qs_bank_ledger_item.description = descriptions
                qs_bank_ledger_item.account = BankAccount.objects.get(
                        id=form["account"].value()
                )
                qs_bank_ledger_item.credit = init_credit
                qs_bank_ledger_item.debit = init_debit
                qs_bank_ledger_item.remarks = form["remarks"].value()
                qs_bank_ledger_item.save()
            else:
                qs_cash_ledger_item = CashLedger.objects.get(tid=get_tid)
                qs_cash_ledger_item.dateOfTransaction=form["dateOfTransaction"].value()
                qs_cash_ledger_item.description= descriptions
                qs_cash_ledger_item.credit=init_credit
                qs_cash_ledger_item.debit=init_debit
                qs_cash_ledger_item.remarks=form["remarks"].value()
                qs_cash_ledger_item.save()