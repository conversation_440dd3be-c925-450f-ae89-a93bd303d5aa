import uuid
from django.utils.crypto import get_random_string
from Accounts.models import ShareholderDeposit, Income, Expenditure,ContractorBillPayment, CreditPurchasePayment, OfficeExpenditure
from . import script_settings as ss
from itertools import chain
from django.db.models import (
    <PERSON>,
    CharField,
    Count,
    DecimalField,
    DurationField,
    ExpressionWrapper,
    F,
    FloatField,
    IntegerField,
    OuterRef,
    Q,
    Subquery,
    Sum,
    Value,
    When,
)
from django.db.models.functions import Cast, Coalesce, Concat, Extract, Round, Trunc
# get_random_string(10, allowed_chars='**********')
# def random_id():
#    random_str = get_random_string(10, allowed_chars='**********')
#    return int(random_str)
# id = models.IntegerField(primary_key=True, default=random_id, editable=False)


# def random_id():
#    random_int = int(get_random_string(10, allowed_chars='**********'))
#    while YourModel.objects.filter(id=random_int).exists():
#       random_int = int(get_random_string(10, allowed_chars='**********'))
#    return random_int
# id = models.IntegerField(primary_key=True, default=random_id, editable=False, unique=True)
def query_sets():
    # qs = ShareholderDeposit.objects.all().values()
    # qs = Expenditure.objects.all().values()
    qs = Income.objects.all().values()
    # qs = ContractorBillPayment.objects.all().values()
    # qs = CreditPurchasePayment.objects.all().values()
    # qs = OfficeExpenditure.objects.all().values()
    return qs
def contractor_bill_payment():
    qs = ContractorBillPayment.objects.all()
    qs= qs.annotate(
            contractor = F("bill__contractor__contractor")
        ).order_by("contractor","bill")
    subquery_contractor_total_paid_bill = (
            ContractorBillPayment.objects.filter(bill__contractor__contractor=OuterRef("contractor"))
            .values("bill__contractor__id")
            .annotate(
                contractor_bill_sum=Coalesce(
                    Sum(F("amount")), 0, output_field=DecimalField()
                )
            )
        )
    qs = qs.annotate(
            contractor_total_paid_bill=Subquery(
                subquery_contractor_total_paid_bill.values("contractor_bill_sum")
            )
        ).values().order_by("contractor","bill")
    return qs

def get_unique_tid():
    get_tid = get_random_string(10)
    qs_1 = ShareholderDeposit.objects.filter(tid = get_tid).count()
    qs_2 = Expenditure.objects.filter(tid = get_tid).count()
    qs_3 = Income.objects.filter(tid = get_tid).count()
    qs_4 = ContractorBillPayment.objects.filter(tid = get_tid).count()
    qs_5 = CreditPurchasePayment.objects.filter(tid = get_tid).count()
    qs_6 = OfficeExpenditure.objects.filter(tid = get_tid).count()

    while (qs_1+qs_2+qs_3+qs_4+qs_5+qs_6) > 0:
        print("Found:", 1)
        get_tid = get_random_string(10)
        qs_1 = ShareholderDeposit.objects.filter(tid = get_tid).count()
        qs_2 = Expenditure.objects.filter(tid = get_tid).count()
        qs_3 = Income.objects.filter(tid = get_tid).count()
        qs_4 = ContractorBillPayment.objects.filter(tid = get_tid).count()
        qs_5 = CreditPurchasePayment.objects.filter(tid = get_tid).count()
        qs_6 = OfficeExpenditure.objects.filter(tid = get_tid).count()
    return get_tid
    
# 🏶
# py manage.py runscript random_unique_id
def run():
    # y = uuid.uuid4().hex
    # random_uuid = uuid.uuid4()
    # x = get_random_string(10)
    query_set = contractor_bill_payment()
    # for row in query_set:
    #     # row_for_update = ShareholderDeposit.objects.get(id=row["id"])
    #     # row_for_update = Expenditure.objects.get(id=row["id"])
    #     row_for_update = Income.objects.get(id=row["id"])
    #     # row_for_update = ContractorBillPayment.objects.get(id=row["id"])
    #     # row_for_update = CreditPurchasePayment.objects.get(id=row["id"])
    #     # row_for_update = OfficeExpenditure.objects.get(id=row["id"])
    #     row_for_update.tid = get_unique_tid()
    #     row_for_update.save()


    print((" \u26BD\uFE0B ").join("\U0001F3F5" * 15))
    print(
        f"\033[38;5;208m {ss.BG_YELLOW}{ss.TXT_BOLD}\033[2:4m Returned Data 📜 :{ss.RESET}\033[2;30m {query_set}"
    )
    print((" 👁️ 👀 ").join("\U0001F3F5" * 10))
    try:
        print(
            f'\033[38;5;208m {ss.BG_YELLOW}{ss.TXT_BOLD} Query 📜 :{ss.RESET}\033[2;30m {str(query_set.query).replace(chr(34), "`")}, No. of Record:{query_set.count()}'
        )
    except:
        print(f"\U0001F3F5 🟔{ss.RED} No SQL Query Exist!{ss.RESET}")
