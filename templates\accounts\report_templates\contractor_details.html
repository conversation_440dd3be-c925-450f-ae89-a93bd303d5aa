{% extends "main.html" %}
{% load static %} 
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}{{company_name}}{%endblock %}
{% block content %}

<div class="container pt-3" style="width:50%;">
    <div class="card">
        <div class="d-flex justify-content-center">
        <img src="{{object.avatar}}" 
        class="card-img-top" 
        alt="masud"
        style="border-radius: 15%; width: 200px; height: 200px;  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);">
        </div>
        <div class="card-body">
          <h5 class="card card-title text-white bg-secondary bg-opacity-50 p-1 ps-2">Contractor : {{object.contractor}}</h5>
        <div class="row mb-3">
            <div class="col-4 fw-bold"><p class="card-text text-end">Date of Join:</p></div>
            <div class="col"><p class="card-text text-start">{{object.dateOfJoin|date:'d-b-Y'}}</p></div>
        </div>
        <div class="row mb-3">
            <div class="col-4 fw-bold"><p class="card-text text-end">Contractor Type:</p></div>
            <div class="col"><p class="card-text text-start">{{object.contractorType|default_if_none:"---"}}</p></div>
        </div>
        <div class="row mb-3">
            <div class="col-4 fw-bold"><p class="card-text text-end">Address:</p></div>
            <div class="col"><p class="card-text text-start">{{object.address}}</p></div>
        </div>
        <div class="row mb-3">
            <div class="col-4 fw-bold"><p class="card-text text-end">Email:</p></div>
            <div class="col"><p class="card-text text-start">{{object.Email|default_if_none:"---"}}</p></div>
        </div>
        <div class="row mb-3">
            <div class="col-4 fw-bold"><p class="card-text text-end">NID:</p></div>
            <div class="col"><p class="card-text text-start">{{object.NID|default_if_none:"---"}}</p></div>
        </div>
        <div class="row mb-3">
            <div class="col-4 fw-bold"><p class="card-text text-end">TIN:</p></div>
            <div class="col"><p class="card-text text-start">{{object.TIN|default_if_none:"---"}}</p></div>
        </div>
        <div class="row mb-3">
            <div class="col-4 fw-bold"><p class="card-text text-end">Telephone No:</p></div>
            <div class="col"><p class="card-text text-start">{{object.TelephoneNo|default_if_none:"---"}}</p></div>
        </div>
        <div class="row mb-3">
            <div class="col-4 fw-bold"><p class="card-text text-end">Mobile No:</p></div>
            <div class="col"><p class="card-text text-start">{{object.Mobile|default_if_none:"---"}}</p></div>
      </div>
        <div class="row mb-3">
            <div class="col-4 fw-bold"><p class="card-text text-end">Status:</p></div>
            <div class="col"><p class="card-text text-start {{object.IsArchive|yesno:'text-danger,text-success'}}">{{object.IsArchive|yesno:'Inactive,Active,maybe'}}</p></div>
        </p></div>
      </div>
    </div>
</div>
<div class="d-flex justify-content-end py-3">
<a href="/contractorDetails/{{object.id}}" target="_blank" class="btn btn-sm btn-outline-danger"><i class="bi bi-download"></i> PDF</a>
<a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 ms-1 mb-0' role='button'>Go Back</a>
</div>
</div>

{% endblock %}