{% extends "main.html" %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}{{company_name}}{%endblock %}
{% block style %}
<style>
    th {
    background-color: #D9D9D9;
    border: 1px solid rgb(186, 191, 192);
    /* vertical-align: middle; */
    text-align: center;
    margin: 0px 0px 0px 0px;
    padding: 0px 5px 0px 5px;
    font-size: 14px;
}

td {
    font-family: 'BanglaFontSejuti';
    /* vertical-align: middle; */
    margin: 0px 0px 0px 0px;
    padding: 0px 5px 0px 5px;
    font-size: 13px;
}
</style>
{% endblock style %}

{% block content %}

<div class="container pt-3" style="width:100%; margin: auto;">
    <div class="card">
        <div class="card-header ps-2 py-1">
            <h4>{{heading}}</h4>
        </div>
        <div class="card-body">
            {% crispy form %}
        </div>
    </div>
    <br>
    {% if update_tag is None %}
    <div class="card">
        <div class="card-header ps-2 py-1">
            <h4>{{data_heading}}</h4>
        </div>
            <table class="table table-sm align-middle table-striped table-bordered table-hover table-responsive">
                <tr>
                    <th>SL</th>
                    <th>Photo</th>
                    <th>Shareholder</th>
                    <th>Address</th>
                    <th>Mobile</th>
                    <th>Email</th>
                    <th>Date of Join</th>
                    <th>Nos. of Share</th>
                    <th>&#128270;</th>
                </tr>
                {% for dt in data %}
                <tr>
                    <td class="text-center">{{forloop.counter}}</td>
                    <td><img src="{{dt.avatar}}" class="card-img-top" alt="masud"
                            style="border-radius: 15%; width: 80px; height: 80px;  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);">
                    </td>
                    <td>{{dt.shareholderName}}</td>
                    <td>{{dt.address}}</td>
                    <td>{{dt.mobile}}</td>
                    <td>{{dt.email}}</td>
                    <td>{{dt.dateOfJoin}}</td>
                    <td>{{dt.numberOfFlat}}</td>
                    <td><a href="{{ dt.get_absolute_url}}"><i class="bi bi-file-earmark-text-fill"></i></a></td>
                </tr>
                {% endfor %}
            </table>

        {% endif %}
    </div>
    </div>
        <br>
        {% comment %} {{request.META}} {% endcomment %}
        {% endblock %}
        {% block script %}

        <script>
            {% if update_tag %}
            button_div = document.getElementById("button_div")
            button_div.innerHTML = `<input type="submit" name="submit" value="Update" class="btn btn-primary btn btn-success me-2 mb-0" id="submit-id-submit">
	<a href="{%url 'Accounts:expenditure_details_list'%}" class="btn btn-danger">Cancel</a>`
            {% endif %}
            {% endblock script %}
        </script>