{% extends "accounts/reports/reportbase.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% load mahimsoft_tags %}
{% block title %}<title>Shareholder List {% now 'd-M-Y' %}</title>{% endblock title %}
{% block style %}
<style>
  table, th, td {
  border: 1px solid black;
  border-collapse: collapse;
  font-size: small;
  padding: 2px;
}
  tr {
  page-break-inside: avoid;
}
  th {
  background-color:rgb(232, 167, 62);
}
  tr:nth-child(even) {
  background-color:rgb(208, 208, 202);
}
  @page {
    size: a4 landscape;
    margin: 5mm 5mm 10mm 5mm;
    counter-increment: page;

    /* @bottom-left {
      bottom:100px;
      content: attr(hr);
    } */
    @bottom-right {
      font-family: 'Times New Roman';
      content: '\00A9 {{data.copy_right}} Page No : ' counter(page) ' of ' counter(pages);
      white-space: pre;
      color: grey;
    }
  }
</style>

{% endblock style %}
{% block contents %}

<p style="margin:0; text-align: right;">Date: {% now 'd-M-Y' %}</p>
<h2 style="margin-top: 3px; text-align: center; vertical-align: middle; color: dimgrey;">{{data.heading}}</h2>
<div style="margin-top: 3px; margin-bottom: 3px;">
  <table>
    <thead>
    <tr>
      <th style="width: 2%;  text-align: center;">#</th>
      <th style="width: 5%;  text-align: center;">Photo</th>
      <th style="width: 20%; text-align: center;">Name</th>
      <th style="width: 10%; text-align: center;">NID</th>
      <th style="width: 10%; text-align: center;">Mobile & Email</th>
      <th style="width: 10%; text-align: center;">Nos. of Flat</th>
      <th style="width: 10%; text-align: center;">Deposited Amnt</th>
      <th style="width: 10%; text-align: center;">Rest Amnt to Pay</th>
    </tr>
    </thead>
    <tbody>
    {% for dt in data.shareholder %}
    <tr>
      <td style="text-align: center;">{{forloop.counter}}</td>
      <td style="text-align: center;"><img src="{{dt.avatar}}" alt="masud"
          style="width: 50px; height: 50px;">
      </td>
      <td style="vertical-align: middle;">{{dt.shareholderName}}</td>

      {% if dt.nid == None %}
      <td style="text-align: left; vertical-align: middle;">---</td>
      {% else %}
      <td style="text-align: left; vertical-align: middle;">{{dt.nid|default_if_none:"---"}}</td>
      {% endif %}
      <td style="text-align: left; vertical-align: middle;">📞 {{dt.mobile|default_if_none:"---"}}
        <br>📧 {{dt.email|default_if_none:"---"}}
      </td>
      <td style="text-align: center;">{{dt.numberOfFlat|floatformat:"1"}}</td>
      <td  >{{dt.sum_amount|default_if_none:0|intcomma_bd}}/-</td>
      {% if dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount > 0 %}
      <td style="text-align: right; color:red;">
        {{dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount|number_product:-1|intcomma_bd}}/-
      </td>
      {% elif dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount == 0 %}
      <td>
        0/-
      </td>
      {% else %}
      <td>
        {{dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount|number_product:-1|intcomma_bd}}/-
      </td>
      {% endif %}
    </tr>
    {% endfor %}
    </tbody>
  </table>
</div>
<p style="color: firebrick;">NB: Rest amount to pay calculated as per targeted amount per share as Taka:
  {{data.no_of_flat_per_share|number_product:data.targeted_amount_per_flat|intcomma_bd}}/-</p>
{% endblock %}