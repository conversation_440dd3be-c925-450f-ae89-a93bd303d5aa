{% extends "main.html" %}
{% load i18n %}
{% load crispy_forms_tags %}
{% block extend_header%}

<style>
	body,
	html {
		margin: 0;
		padding: 0;
		height: 100%;
		background: #7abecc !important;
	}

	.user_card {
		width: 350px;
		margin-top: auto;
		margin-bottom: auto;
		background: #74cfbf;
		position: relative;
		display: flex;
		justify-content: center;
		flex-direction: column;
		padding: 10px;
		box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
		-webkit-box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
		-moz-box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
		border-radius: 5px;

	}

	.form_container {
		margin-top: 20px;
	}

	#form-title {
		color: #fff;
	}

	.login_btn {
		width: 100%;
		background: #33ccff !important;
		color: white !important;
	}

	.login_btn:focus {
		box-shadow: none !important;
		outline: 0px !important;
	}

	.login_container {
		padding: 0 2rem;
	}

	.input-group-text {
		background: #f7ba5b !important;
		color: white !important;
		border: 0 !important;
		border-radius: 0.25rem 0 0 0.25rem !important;
	}

	.input_user,
	.input_pass:focus {
		box-shadow: none !important;
		outline: 0px !important;
	}
</style>

{%endblock extend_header%}
{% block content %}
<div class="container h-100 mt-4">
	<div class="d-flex justify-content-center h-100">
		<div class="user_card">
			<div class="d-flex justify-content-center">
				<h3 id="form-title">REGISTER USER</h3>
			</div>
			<div class="d-flex justify-content-center form_container">

				<form method="POST" action="">
					{% csrf_token %}
					<div class="input-group mb-3">
						<div class="input-group-append">
							<span class="input-group-text fs-5"><i class="bu bi-person-fill"></i></span>
						</div>
						{{form.username}}
					</div>
					<div class="input-group mb-2">
						<div class="input-group-append">
							<span class="input-group-text fs-5"><i class="bu bi-envelope-fill"></i></span>
						</div>
						{{form.email}}
					</div>
					<div class="input-group mb-2">
						<div class="input-group-append">
							<span class="input-group-text fs-5"><i class="bu bi-key-fill"></i></span>
						</div>
						{{form.password1}}
					</div>
					<div class="input-group mb-2">
						<div class="input-group-append">
							<span class="input-group-text fs-5"><i class="bu bi-key-fill"></i></span>
						</div>
						{{form.password2}}
					</div>
					<div class="input-group mb-2">
						<div class="input-group-append">
							<span class="input-group-text fs-5"><i class="bu bi-people-fill"></i></span>
						</div>
						{{form.group}}
					</div>



					<div class="d-flex justify-content-center mt-3 login_container">
						<input class="btn login_btn" type="submit" value="Register Account">
					</div>

				</form>
			</div>

			{{form.errors}}
<br>
			{% comment %} <!-- <div class="mt-4">
				<div class="d-flex justify-content-center links">
					Already have an account? &nbsp;<a style="text-decoration: inherit;" href="{% url 'login' %}"
						class="ml-2">Login</a>
				</div>
			</div> --> {% endcomment %}
		</div>
	</div>
</div>
{% endblock content %}

{% block script %}
<script>
	/* Because i didnt set placeholder values in forms.py they will be set here using vanilla Javascript
//We start indexing at one because CSRF_token is considered and input field 
*/

	//Query All input fields
	var form_fields = document.getElementsByTagName('input')
	form_fields[1].placeholder = 'Username..';
	form_fields[2].placeholder = 'Email..';
	form_fields[3].placeholder = 'Enter password...';
	form_fields[4].placeholder = 'Re-enter Password...';
	// form_fields[5].options.value["selected"] = 'Select User Group...';

	var group_selecttion = document.getElementById("id_group")
	group_selecttion.classList.add('form-control')
	for (var field in form_fields) {
		form_fields[field].className += ' form-control'
	}
</script>

{% endblock script %}