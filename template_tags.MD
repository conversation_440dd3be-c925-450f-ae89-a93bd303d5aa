App: django.contrib.admin
    load: admin_list
        Tag: paginator_number
            Generate an individual page index link in a paginated list.

        Tag: pagination
        Tag: result_list
        Tag: date_hierarchy
        Tag: search_form
        Tag: admin_list_filter
        Tag: admin_actions
        Tag: change_list_object_tools
            Display the row of change list object tools.

    load: admin_urls
        Tag: add_preserved_filters
        Filter: admin_urlname
        Filter: admin_urlquote
    load: log
        Tag: get_admin_log
            Populate a template variable with the admin log for the given criteria.

            Usage::

                {% get_admin_log [limit] as [varname] for_user [context_var_with_user_obj] %}

            Examples::

                {% get_admin_log 10 as admin_log for_user 23 %}
                {% get_admin_log 10 as admin_log for_user user %}
                {% get_admin_log 10 as admin_log %}

            Note that ``context_var_containing_user_obj`` can be a hard-coded integer
            (user ID) or the name of a template context variable containing the user
            object whose ID you want.

App: allauth
    load: allauth
        Tag: slot
        Tag: element
        Tag: setvar
App: allauth.account
    load: account
        Tag: user_display
            Example usage::

                {% user_display user %}

            or if you need to use in a {% blocktrans %}::

                {% user_display user as user_display %}
                {% blocktrans %}
                {{ user_display }} has sent you a gift.
                {% endblocktrans %}

App: allauth.socialaccount
    load: socialaccount
        Tag: provider_login_url
            {% provider_login_url "facebook" next=bla %}
            {% provider_login_url "openid" openid="http://me.yahoo.com" next=bla %}

        Tag: providers_media_js
        Tag: get_social_accounts
            {% get_social_accounts user as accounts %}

            Then:
                {{accounts.twitter}} -- a list of connected Twitter accounts
                {{accounts.twitter.0}} -- the first Twitter account
                {% if accounts %} -- if there is at least one social account

        Tag: get_providers
            Returns a list of social authentication providers.

            Usage: `{% get_providers as socialaccount_providers %}`.

            Then within the template context, `socialaccount_providers` will hold
            a list of social providers configured for the current site.

App: django_htmx
    load: django_htmx
        Tag: django_htmx_script
App: rest_framework
    load: rest_framework
        Tag: code
        Tag: form_for_link
        Tag: render_markdown
        Tag: get_pagination_html
        Tag: render_form
        Tag: render_field
        Tag: optional_login
            Include a login snippet if REST framework's login view is in the URLconf.

        Tag: optional_docs_login
            Include a login snippet if REST framework's login view is in the URLconf.

        Tag: optional_logout
            Include a logout snippet if REST framework's logout view is in the URLconf.

        Tag: add_query_param
            Add a query parameter to the current request url, and return the new url.

        Filter: with_location
        Filter: as_string
        Filter: as_list_of_strings
        Filter: add_class
            https://stackoverflow.com/questions/4124220/django-adding-css-classes-when-rendering-form-fields-in-a-template

            Inserts classes into template variables that contain HTML tags,
            useful for modifying forms without needing to change the Form objects.

            Usage:

                {{ field.label_tag|add_class:"control-label" }}

            In the case of REST Framework, the filter is used to add Bootstrap-specific
            classes to the forms.

        Filter: format_value
        Filter: items
            Simple filter to return the items of the dict. Useful when the dict may
            have a key 'items' which is resolved first in Django template dot-notation
            lookup.  See issue #4931
            Also see: https://stackoverflow.com/questions/15416662/django-template-loop-over-dictionary-items-with-items-as-key

        Filter: data
            Simple filter to access `data` attribute of object,
            specifically coreapi.Document.

            As per `items` filter above, allows accessing `document.data` when
            Document contains Link keyed-at "data".

            See issue #5395

        Filter: schema_links
            Recursively find every link in a schema, even nested.

        Filter: add_nested_class
        Filter: break_long_headers
            Breaks headers longer than 160 characters (~page length)
            when possible (are comma separated)

App: django_extensions
    load: debugger_tags
        Filter: ipdb
            Interactive Python debugger filter.

        Filter: pdb
            Python debugger filter.

        Filter: wdb
            Web debugger filter.

    load: highlighting
        Tag: highlight
            Tag to put a highlighted source code <pre> block in your code.
            This takes two arguments, the language and a little explaination message
            that will be generated before the code.  The second argument is optional.

            Your code will be fed through pygments so you can use any language it
            supports.

            Usage::

              {% load highlighting %}
              {% highlight 'python' 'Excerpt: blah.py' %}
              def need_food(self):
                  print("Love is colder than death")
              {% endhighlight %}

        Filter: parse_template
    load: indent_text
        Tag: indentby
            Add indentation to text between the tags by the given indentation level.

            {% indentby <indent_level> [if <statement>] %}
            ...
            {% endindentby %}

            Arguments:
              indent_level - Number of spaces to indent text with.
              statement - Only apply indent_level if the boolean statement evalutates to True.

    load: syntax_color
        Tag: pygments_css
        Filter: colorize
        Filter: colorize_table
        Filter: colorize_noclasses
    load: widont
        Filter: widont
            Add an HTML non-breaking space between the final two words of the string to
            avoid "widowed" words.

            Examples:

            >>> print(widont('Test   me   out'))
            Test   me&nbsp;out

            >>> print("'",widont('It works with trailing spaces too  '), "'")
            ' It works with trailing spaces&nbsp;too   '

            >>> print(widont('NoEffect'))
            NoEffect

        Filter: widont_html
            Add an HTML non-breaking space between the final two words at the end of
            (and in sentences just outside of) block level tags to avoid "widowed"
            words.

            Examples:

            >>> print(widont_html('<h2>Here is a simple  example  </h2> <p>Single</p>'))
            <h2>Here is a simple&nbsp;example  </h2> <p>Single</p>

            >>> print(widont_html('<p>test me<br /> out</p><h2>Ok?</h2>Not in a p<p title="test me">and this</p>'))
            <p>test&nbsp;me<br /> out</p><h2>Ok?</h2>Not in a&nbsp;p<p title="test me">and&nbsp;this</p>

            >>> print(widont_html('leading text  <p>test me out</p>  trailing text'))
            leading&nbsp;text  <p>test me&nbsp;out</p>  trailing&nbsp;text

App: crispy_forms
    load: crispy_forms_field
        Tag: cr