from datetime import datetime, timedelta
# from django import forms
from django.db.models.expressions import Window
from django.db.models.functions import RowNumber

from django.urls import reverse # type: ignore
import pandas as pd
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.utils.decorators import method_decorator
from .decorators import admin_only, allowed_users, unauthenticated_user, time_of_execution
from CONSTRUCTION_PROJECT.settings.construction_project_tags import *
from django.core import serializers
import json
from django.shortcuts import render, redirect, HttpResponse, HttpResponseRedirect
from django.utils.crypto import get_random_string
from django.views.generic import (
    TemplateView,
    ListView,
    DetailView,
    CreateView,
    UpdateView,
    DeleteView,
)
from django.views.generic.edit import FormView
from django.contrib import messages
from django.db import transaction
from django.db.models import (
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ield,
    ExpressionWrapper,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>te<PERSON><PERSON>ield,
    OuterRef,
    Q,
    Subquery,
    Sum,
    Value,
    When,
)
from django.db.models.functions import Cast, Coalesce, Concat, Extract, Round, Trunc # type: ignore
from django.core.mail import EmailMessage # type: ignore

from .forms import (
    SendMailForm,
    ChartForm,
    ExpenditureForm,
    ContractorTypeForm,
    ContractorForm,
    ItemCodeForm,
    ItemForm,
    ContractorBillSubmissionForm,
    ContractorBillPaymentForm,
    ShareholderDepositForm,
    ShareholderForm,
    TargetedAmountForm,
    CreditPurchaseForm,
    CreditPurchasePaymentForm,
    IncomeSectorForm,
    IncomeItemForm,
    IncomeForm,
    BankAccountForm,
    BankTransactionForm,
    WorkPhaseForm,
    CurrentWorkPhaseForm,
)
import plotly.express as px
from .models import (
    Item,
    ItemCode,
    Shareholder,
    ShareholderDeposit,
    Expenditure,
    ContractorType,
    Contractor,
    ContractorBillSubmission,
    ContractorBillPayment,
    ShareholderDeposit,
    Shareholder,
    TargetedAmount,
    CreditPurchase,
    CreditPurchasePayment,
    UserLoggedinRecord,
    IncomeSector,
    IncomeItem,
    Income,
    BankAccount,
    BankLedger,
    CashLedger,
    OfficeExpenditure,
    WorkPhase,
    CurrentWorkPhase,
)
from .chart import chart, dummy_chart 
from CONSTRUCTION_PROJECT.settings.context_processors import company_info_settings

try:
    current_work_phase = WorkPhase.objects.get(id=CurrentWorkPhase.objects.last().workPhase_id) 
except:
    current_work_phase = 0

company_info = company_info_settings()
currency = company_info["currency"]
currency_symbol = company_info["currency_symbol"]
from .utils import expenditure_summary_qs, expenditure_details_qs

def get_unique_tid():
    get_tid = get_random_string(10)
    qs_1 = ShareholderDeposit.objects.filter(tid = get_tid).count()
    qs_2 = Expenditure.objects.filter(tid = get_tid).count()
    qs_3 = Income.objects.filter(tid = get_tid).count()
    qs_4 = ContractorBillPayment.objects.filter(tid = get_tid).count()
    qs_5 = CreditPurchasePayment.objects.filter(tid = get_tid).count()
    qs_6 = OfficeExpenditure.objects.filter(tid = get_tid).count()

    while (qs_1+qs_2+qs_3+qs_4+qs_5+qs_6) > 0:
        get_tid = get_random_string(10)
        qs_1 = ShareholderDeposit.objects.filter(tid = get_tid).count()
        qs_2 = Expenditure.objects.filter(tid = get_tid).count()
        qs_3 = Income.objects.filter(tid = get_tid).count()
        qs_4 = ContractorBillPayment.objects.filter(tid = get_tid).count()
        qs_5 = CreditPurchasePayment.objects.filter(tid = get_tid).count()
        qs_6 = OfficeExpenditure.objects.filter(tid = get_tid).count()
    return get_tid

def form_save(form, descriptions, is_credit):
    with transaction.atomic():
        get_tid = get_unique_tid()
        instance = form.save(commit=False)
        instance.tid = get_tid
        instance.work_phase = current_work_phase
        instance.save()
        init_credit = 0
        init_debit = 0
        if is_credit:
            init_credit=form["amount"].value()
            init_debit = 0
        else:      
            init_credit = 0
            init_debit = form["amount"].value()

        if form["modeOfTransaction"].value() == "BANK":
            BankLedger.objects.create(
                dateOfTransaction=form["dateOfTransaction"].value(),
                description=descriptions,
                account=BankAccount.objects.get(id=form["account"].value()),
                credit=init_credit,
                debit=init_debit,
                remarks=form["remarks"].value(),
                work_phase=current_work_phase,
                tid=get_tid,
            )
        else:
            CashLedger.objects.create(
                dateOfTransaction=form["dateOfTransaction"].value(),
                description=descriptions,
                credit=init_credit,
                debit=init_debit,
                remarks=form["remarks"].value(),
                work_phase=current_work_phase,
                tid=get_tid,
            )

def form_save_update(self_kwargs, form, model_name, descriptions, is_credit):
    with transaction.atomic():
        qs = model_name.objects.filter(id=self_kwargs.kwargs["pk"]).values()
        get_transaction_mode = qs[0]["modeOfTransaction"]
        get_tid = qs[0]["tid"]
        qs_bank_ledger = BankLedger.objects.filter(tid=get_tid)
        qs_cash_ledger = CashLedger.objects.filter(tid=get_tid)
        instance = form.save(commit=False)
        instance.work_phase = current_work_phase
        instance.save()
        init_credit = 0
        init_debit = 0
        if is_credit:
            init_credit=form["amount"].value()
            init_debit = 0
        else:
            init_credit = 0
            init_debit = form["amount"].value()
        if form["modeOfTransaction"].value() != get_transaction_mode:
            if qs_bank_ledger.count() > 0:
                qs_bank_ledger.delete()
                CashLedger.objects.create(
                    dateOfTransaction=form["dateOfTransaction"].value(),
                    description=descriptions,
                    credit=init_credit,
                    debit=init_debit,
                    remarks=form["remarks"].value(),
                    work_phase=current_work_phase,
                    tid=get_tid,
                )
            else:
                qs_cash_ledger.delete()
                BankLedger.objects.create(
                    dateOfTransaction=form["dateOfTransaction"].value(),
                    description=descriptions,
                    account=BankAccount.objects.get(id=form["account"].value()),
                    credit=init_credit,
                    debit=init_debit,
                    remarks=form["remarks"].value(),
                    work_phase=current_work_phase,
                    tid=get_tid,
                )
        else:
            if qs_bank_ledger.count() > 0:
                qs_bank_ledger_item = BankLedger.objects.get(tid=get_tid)
                qs_bank_ledger_item.dateOfTransaction = form["dateOfTransaction"].value() 
                qs_bank_ledger_item.description = descriptions
                qs_bank_ledger_item.account = BankAccount.objects.get(
                        id=form["account"].value()
                )
                qs_bank_ledger_item.credit = init_credit
                qs_bank_ledger_item.debit = init_debit
                qs_bank_ledger_item.remarks = form["remarks"].value()
                qs_bank_ledger_item.work_phase = current_work_phase
                qs_bank_ledger_item.save()
            else:
                qs_cash_ledger_item = CashLedger.objects.get(tid=get_tid)
                qs_cash_ledger_item.dateOfTransaction=form["dateOfTransaction"].value()
                qs_cash_ledger_item.description= descriptions
                qs_cash_ledger_item.credit=init_credit
                qs_cash_ledger_item.debit=init_debit
                qs_cash_ledger_item.remarks=form["remarks"].value()
                qs_cash_ledger_item.work_phase=current_work_phase
                qs_cash_ledger_item.save() 

class TargetedAmountPosting(LoginRequiredMixin, FormView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_targeted_amount.html"
    form_class = TargetedAmountForm
    success_url = "/"

    def form_valid(self, form):
        form.save()
        messages.success(self.request, "Thank you. A new Deposit target assigned.")
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["heading"] = "Add New Shareholder Deposit Target"
        context["data"] = TargetedAmount.objects.all()
        context["data_heading"] = "Previous Shareholder Deposit targets:"
        context["detail_tag"] = False
        return context

#!Contractor and contractor bills ====================

class ContractorTypeView(LoginRequiredMixin, FormView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_single_column.html"
    form_class = ContractorTypeForm
    success_url = "./"

    def form_valid(self, form):
        form.save()
        messages.success(self.request, "Thank you. A new contractor type added.")
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = ContractorType.objects.all()
        context["heading"] = "Add New Contractor Type"
        context["data_heading"] = "Existing Contractor Types:"
        context["detail_tag"] = False
        return context


class ContractorView(LoginRequiredMixin, FormView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_contractor.html"
    form_class = ContractorForm
    success_url = "./"

    def form_valid(self, form):
        form.save()
        messages.success(self.request, "Thank you. A new contractor added.")
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = Contractor.objects.all()
        context["heading"] = "Add New Contractor"
        context["data_heading"] = "Existing Contractors:"
        context["detail_tag"] = True

        return context


class ContractorDetailView(LoginRequiredMixin, DetailView):
    model = Contractor
    template_name = "accounts/report_templates/contractor_details.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context


class ContractorListView(LoginRequiredMixin, ListView):
    model = Contractor
    template_name = "accounts/report_templates/contractor_list.html"
    context_object_name = "contractor"

    def get_queryset(self):
        qs = super().get_queryset()
        return qs.order_by("IsArchive","contractor")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["heading"] = "Contractor's List"
        return context

class ContractorBillSubmissionView(LoginRequiredMixin, FormView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_contractor_bill_submission.html"
    form_class = ContractorBillSubmissionForm
    success_url = "./"

    def form_valid(self, form):
        form.save()
        messages.success(
            self.request, "Thank you. Contractor Bill Submission Posting done."
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = ContractorBillSubmission.objects.all().order_by("-dateOfBillSubmission")[
            :50
        ]
        context["heading"] = "Contractor Bill Submission"
        context["data_heading"] = "Last 50 Bill Submission History:"
        return context


class ContractorBillPaymentView(LoginRequiredMixin, CreateView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_contractor_bill_payment.html"
    form_class = ContractorBillPaymentForm
    success_url = "./"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = ContractorBillPayment.objects.order_by("-dateOfTransaction")[:10]
        context["heading"] = "Contractor Bill Payment Entry"
        context["data_heading"] = "Last 10 Bill Payment:"
        return context

    def form_valid(self, form):
        bank_balance = BankLedger.objects.filter(
        account=form["account"].value()).aggregate(bank_balance=Sum(F("credit") - F("debit")))
        cash_balance = CashLedger.objects.aggregate(cash_balance=Sum(F("credit") - F("debit")))
        if form["modeOfTransaction"].value() == "BANK" and bank_balance[
            "bank_balance"
            ] < int(form["amount"].value()):
            messages.success(
                self.request,
                f"""{currency}: {form['amount'].value()}/- is not available in bank account.""",
            )
            return redirect("./")
        
        elif form["modeOfTransaction"].value() == "CASH" and cash_balance[
                "cash_balance"
                ] < int(form["amount"].value()):
            messages.success(
                self.request,
                f"""{currency}: {form['amount'].value()}/- is not available in cash.""",
            )
            return redirect("./")
    
        with transaction.atomic():
            description = f"Payment of {ContractorBillSubmission.objects.get(id=form['bill'].value())}"
            form_save(form, description, False)
            Expenditure.objects.create(
                dateOfTransaction=form["dateOfTransaction"].value(),
                item=Item.objects.get(id=form["item"].value()),
                description=description,
                unit=form["unit"].value(),
                rate=form["rate"].value(),
                quantity=form["quantity"].value(),
                voucherNo=form["voucherNo"].value(),
                contractor_bill_payment=ContractorBillPayment.objects.all().order_by(
                    "-id"
                )[0],
                modeOfTransaction = form["modeOfTransaction"].value(),
                amount=int(form["amount"].value()),
                remarks=form["remarks"].value(),
                work_phase=current_work_phase,
                tid=ContractorBillPayment.objects.all().order_by(
                    "-id"
                ).values()[0]["tid"]
            )

        messages.success(
            self.request, "Thank you. Contractor Bill Payment Posting done."
        )
        return super(ContractorBillPaymentView, self).form_valid(form)


@login_required
@allowed_users(allowed_roles=["Admin"])
def recordDelete(request, id, table):
    tables = {"ShareholderDeposit":ShareholderDeposit,
                "Expenditure":Expenditure,
                "Income":Income,
                "cbp":ContractorBillPayment,
                "crp":CreditPurchasePayment,
                "OfficeExpenditure":OfficeExpenditure
                }
    tbl=tables[table]

    A_Record = tbl.objects.get(pk=id)
    if request.method == "POST":
        with transaction.atomic():
            if A_Record.modeOfTransaction == "BANK":
                bank_record = BankLedger.objects.get(tid=A_Record.tid)
                bank_record.delete()
            elif A_Record.modeOfTransaction == "CASH":
                cash_record = CashLedger.objects.get(tid=A_Record.tid)
                cash_record.delete()
            
            if table == "cbp" or table == "crp":
                expenditure_record = Expenditure.objects.get(tid=A_Record.tid)
                expenditure_record.delete()

            A_Record.delete()
            messages.success(request, f"{A_Record} deleted successfully!")
            if table == "cbp":
                url = "contractor_bill_payment_history"
            elif table == "crp":
                url = "credit_purchase_payment_history"

            return redirect(f"Accounts:{url}")

    return render(request, "accounts/deleteConfirmation.html", {"record": A_Record})
        

class ContractorBillPaymentHistory(LoginRequiredMixin, ListView):
    model = ContractorBillPayment
    template_name = "accounts/report_templates/contractor_bill_payment_history.html"
    context_object_name = "bill_payment"
    def get_queryset(self):
        qs = super().get_queryset()
        qs= qs.annotate(
            contractor = F("bill__contractor__contractor"),
            bills_id = F("bill__id"),
            contractor_id = F("bill__contractor__id"),
        )

        subquery_individual_bill_payment_total = (
            ContractorBillPayment.objects.filter(bill__id=OuterRef("bills_id"))).values("bill__id").annotate(
                individual_bill_payment_total=Coalesce(
                    Sum(F("amount")), 0, output_field=DecimalField()
            )
            )

        subquery_contractor_total_paid_bill = (
            ContractorBillPayment.objects.filter(bill__contractor__id=OuterRef("contractor_id"))
            .values("bill__contractor__id")
            .annotate(
                contractor_bill_sum=Coalesce(
                    Sum(F("amount")), 0, output_field=DecimalField()
                )
            )
        )
        qs = qs.annotate(
            individual_bill_payment_total=Subquery(
                subquery_individual_bill_payment_total.values("individual_bill_payment_total")
            )
        )

        qs = qs.annotate(
            contractor_total_paid_bill=Subquery(
                subquery_contractor_total_paid_bill.values("contractor_bill_sum")
            )
        ).order_by("contractor","bill","dateOfTransaction")
        
        return qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["grand_total_paid_bill"] = ContractorBillPayment.objects.aggregate(
            grand_total_paid_bill = Coalesce(Sum(F("amount")),0, output_field=FloatField())
        )
        context["heading"] = "Contractors List"
        return context
#!Contractor and contractor bills end====================


class ShareholderListView(LoginRequiredMixin, ListView):
    model = Shareholder
    template_name = "accounts/report_templates/shareholder_list.html"
    context_object_name = "shareholder"

    def get_queryset(self):
        qs = super().get_queryset()
        subquery_deposit_amount_sum = (
            ShareholderDeposit.objects.filter(shareholder_id=OuterRef("id"))
            .values("shareholder_id")
            .annotate(
                deposit_amount_sum=Coalesce(
                    Sum(F("amount")), 0, output_field=DecimalField()
                )
            )
        )

        qs = qs.annotate(
            deposit_amount_sum=Subquery(
                subquery_deposit_amount_sum.values("deposit_amount_sum")
            ),
        )
        return qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["targeted_amount_per_flat"] = (TargetedAmount.objects.values_list("amount").order_by(
            "-inputDate","-id"
        )[0][0]/company_info["no_of_flat_per_share"])
        # context["targeted_amount_per_flat"] = (
        #     targeted_amount / company_info["no_of_flat_per_share"]
        # )
        context["heading"] = "Shareholders List"
        return context


class ShareholderDetailView(LoginRequiredMixin, DetailView):
    model = Shareholder
    template_name = "accounts/report_templates/shareholder_details.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # print(f"Masud :{self.kwargs=}: {self.request.GET=}")
        return context


class ItemCodeView(LoginRequiredMixin, FormView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_single_column.html"
    form_class = ItemCodeForm
    success_url = "./"

    def form_valid(self, form):
        form.save()
        messages.success(self.request, "Thank you. A new Work Sector added.")
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = ItemCode.objects.all()
        context["heading"] = "Add New Work Sector"
        context["data_heading"] = "Existing Work Sectors:"
        context["detail_tag"] = False
        return context


class ItemView(LoginRequiredMixin, FormView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_item_add.html"
    form_class = ItemForm
    success_url = "/"

    def form_valid(self, form):
        form.save()
        messages.success(self.request, "Thank you. A new Item added.")
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = Item.objects.all().order_by("ItemCode","itemName")
        context["heading"] = "Add New Item"
        context["data_heading"] = "Existing Items:"
        context["detail_tag"] = False
        return context


class ExpenditureView(LoginRequiredMixin, FormView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_expenditure.html"
    form_class = ExpenditureForm
    success_url = "./"

    def form_valid(self, form):
        bank_balance = BankLedger.objects.filter(
        account=form["account"].value()).aggregate(bank_balance=
            Sum(F("credit") - F("debit") ,default=0))
        cash_balance = CashLedger.objects.aggregate(cash_balance= Sum(F("credit") - F("debit"),default=0))
        if form["modeOfTransaction"].value() == "BANK" and bank_balance[
            "bank_balance"
            ] < int(form["amount"].value()):
            messages.success(
                self.request,
                f"""{currency}: {form['amount'].value()}/- is not available in bank account.""",
            )
            return redirect("./")
        
        elif form["modeOfTransaction"].value() == "CASH" and cash_balance[
                "cash_balance"
                ] < int(form["amount"].value()):
            messages.success(
                self.request,
                f"""{currency}: {form['amount'].value()}/- is not available in cash.""",
            )
            return redirect("./")
    
        description = form['description'].value()
        form_save(form,description, False)
        messages.success(self.request, "Thank you. Expenditure posting done.")
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = Expenditure.objects.order_by("-dateOfTransaction")[:10]
        context["heading"] = "Expenditure"
        context["data_heading"] = "Last 10 Expenditure Posting:"
        return context


class ExpenditureUpdate(LoginRequiredMixin, UpdateView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    model = Expenditure
    form_class = ExpenditureForm
    template_name = "accounts/forms/form_expenditure.html"
    success_url = "/expenditure_details_list/"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["heading"] = "Expenditure's Information Update"
        context["update_tag"] = True
        context["redirect_url"] = "/expenditure_details_list/"
        return context
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        work_sector_id=Item.objects.filter(id=form.initial["item"]).values()[0]["ItemCode_id"]
        form.initial["ItemCode"]=work_sector_id
        return form

    def form_valid(self, form):
        print(f"Masud: {form['account'].value()=}")
        previous_amount = Expenditure.objects.filter(id=self.kwargs["pk"]).values()[0]["amount"]
        bank_balance = BankLedger.objects.filter(account_id = form["account"].value()).aggregate(bank_balance=Sum(F("credit") - F("debit")))
        print(f"Masud: {bank_balance=}")
        print(f"Masud: {previous_amount=}")
        print(f"Masud: {int(bank_balance['bank_balance'])+previous_amount=}")
        print(f"Masud: {int(float(form['amount'].value()))=}")
        print(f"Masud: {int(bank_balance['bank_balance'])+previous_amount < int(float(form['amount'].value()))}")
        cash_balance = CashLedger.objects.aggregate(cash_balance=Sum(F("credit") - F("debit")))
        if form["modeOfTransaction"].value() == "BANK" and (int(bank_balance[
            "bank_balance"
            ])+previous_amount) < int(float(form["amount"].value())):
            messages.success(
                self.request,
                f"""{currency}: {form['amount'].value()}/- is not available in bank account.""",
            )
            return redirect(f"./{self.kwargs['pk']}")
        
        elif form["modeOfTransaction"].value() == "CASH" and (int(cash_balance[
                "cash_balance"
                ])+previous_amount) < int(float(form["amount"].value())):
            messages.success(
                self.request,
                f"""{currency}: {form['amount'].value()}/- is not available in cash.""",
            )
            return redirect(f"./{self.kwargs['pk']}")
        
        model_nm = Expenditure
        description = form['description'].value()
        is_credit = False
        form_save_update(self, form, model_nm, description, is_credit)
        messages.success(self.request, "The Expenditure data updated successfully")
        return super(ExpenditureUpdate, self).form_valid(form)


#!Credit Purchase and payment ====================

class CreditPurchaseView(LoginRequiredMixin, CreateView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_credit_purchase.html"
    form_class = CreditPurchaseForm
    success_url = "./"

    def form_valid(self, form):
        with transaction.atomic():
            form.save()
        messages.success(self.request, "Thank you. Credit Purchase Entry Posting done.")
        return super(CreditPurchaseView, self).form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = CreditPurchase.objects.all().order_by("-dateOfPurchase")[:10]
        context["heading"] = "Credit Purchase Entry"
        context["data_heading"] = "Last 10 Credit Purchase:"
        return context


class CreditPurchasePaymentView(LoginRequiredMixin, CreateView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_credit_purchase_payment.html"
    form_class = CreditPurchasePaymentForm
    success_url = "./"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = CreditPurchasePayment.objects.order_by("-dateOfTransaction")[:10]
        context["heading"] = "Credit Purchase Payment Entry"
        context["data_heading"] = "Last 10 Credit Purchase Payment:"
        return context

    def form_valid(self, form):
        bank_balance = BankLedger.objects.filter(
        account=form["account"].value()).aggregate(bank_balance=Sum(F("credit") - F("debit")))
        cash_balance = CashLedger.objects.aggregate(cash_balance=Sum(F("credit") - F("debit")))
        if form["modeOfTransaction"].value() == "BANK" and bank_balance[
            "bank_balance"
            ] < int(form["amount"].value()):
            messages.success(
                self.request,
                f"""{currency}: {form['amount'].value()}/- is not available in bank account.""",
            )
            return redirect("./")
        
        elif form["modeOfTransaction"].value() == "CASH" and cash_balance[
                "cash_balance"
                ] < int(form["amount"].value()):
            messages.success(
                self.request,
                f"""{currency}: {form['amount'].value()}/- is not available in cash.""",
            )
            return redirect("./")
    
        with transaction.atomic():
            description = f"Payment of {CreditPurchase.objects.get(id=form['seller'].value())}"
            form_save(form, description, False)
            Expenditure.objects.create(
                dateOfTransaction=form["dateOfTransaction"].value(),
                item=Item.objects.get(id=form["item"].value()),
                description=description,
                unit=form["unit"].value(),
                rate=form["rate"].value(),
                quantity=form["quantity"].value(),
                voucherNo=form["voucherNo"].value(),
                credit_purchase_payment=CreditPurchasePayment.objects.all().order_by(
                    "-id"
                )[0],
                modeOfTransaction = form["modeOfTransaction"].value(),
                amount=int(form["amount"].value()),
                remarks=form["remarks"].value(),
                work_phase=current_work_phase,
                tid=CreditPurchasePayment.objects.all().order_by(
                    "-id"
                ).values()[0]["tid"]
            )

            messages.success(
                self.request, "Thank you. Credit Purchase Entry Posting done."
            )
            return super(CreditPurchasePaymentView, self).form_valid(form)

class CreditPurchasePaymentHistory(LoginRequiredMixin, ListView):
    model = CreditPurchasePayment
    template_name = "accounts/report_templates/credit_purchase_payment_history.html"
    context_object_name = "credit_payment"
    def get_queryset(self):
        qs = super().get_queryset()
        qs= qs.annotate(
            sellers = F("seller__seller"),
            sellers_id = F("seller__id"),
        )

        subquery_individual_credit_payment_total = (
            CreditPurchasePayment.objects.filter(seller__id=OuterRef("sellers_id"))).values("seller__id").annotate(
                individual_credit_payment_total=Coalesce(
                    Sum(F("amount")), 0, output_field=DecimalField()
            )
            )

        qs = qs.annotate(
            individual_credit_payment_total=Subquery(
                subquery_individual_credit_payment_total.values("individual_credit_payment_total")
            )
        ).order_by("seller","dateOfTransaction")
        return qs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["grand_total_paid_bill"] = CreditPurchasePayment.objects.aggregate(
            grand_total_paid_bill = Coalesce(Sum(F("amount")),0, output_field=FloatField())
        )
        context["heading"] = "Contractors List"
        return context
#!Credit Purchase and payment end ====================

class ShareholderDepositList(LoginRequiredMixin, ListView):
    model = ShareholderDeposit
    template_name = "accounts/report_templates/shareholder_deposit.html"
    context_object_name = "shareholder_deposit"

    def get_queryset(self, *args, **kwargs):
        shareholder = self.kwargs["shareholder_id"]
        qs = super().get_queryset()
        qs = qs.filter(shareholder_id=shareholder)

        subquery_deposit_amount_sum = (
            Shareholder.objects.filter(id=OuterRef("shareholder_id"))
            .values("id")
            .annotate(
                deposit_amount_sum=Coalesce(
                    Sum(F("shareholder_deposit__amount")), 0, output_field=DecimalField()
                ),
                shareholderName=F("shareholderName"),
                numberOfFlat=F("numberOfFlat"),
                # image=F("image"),
            )
        )

        if qs.count()==0:
            qs = Shareholder.objects.filter(id=self.kwargs["shareholder_id"]).annotate(
                deposit_amount_sum=Value(0, output_field=DecimalField()),
                shareholder_id = F("id"),
            )
        else:
            qs = qs.annotate(
                deposit_amount_sum=Subquery(
                    subquery_deposit_amount_sum.values("deposit_amount_sum"),
                ),
                shareholderName=Subquery(
                    subquery_deposit_amount_sum.values("shareholderName"),
                ),
                numberOfFlat=Subquery(
                    subquery_deposit_amount_sum.values("numberOfFlat"),
                ),
            )
        return qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["targeted_amount_per_flat"] = (TargetedAmount.objects.values_list("amount").order_by(
            "-inputDate","-id"
        )[0][0]/company_info["no_of_flat_per_share"])
        context["heading"] = "Details Deposit Information"
        context["avatar"] = str(Shareholder.objects.get(id=self.kwargs["shareholder_id"]).avatar)
        return context


class ShareholderView(LoginRequiredMixin, FormView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_shareholder.html"
    form_class = ShareholderForm
    success_url = "/"

    def form_valid(self, form):
        form.save()
        messages.success(self.request, "Thank you. A New Shareholder Added.")
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = Shareholder.objects.all().order_by("-id")
        context["heading"] = "Add New Shareholder"
        context["data_heading"] = "Existing Shareholder List:"
        return context

 # region #!======================
# @login_required
# def chart(request):
#     qs = ShareholderDeposit.objects.values("shareholder__shareholderName").annotate(
#         Amount=Sum(Coalesce(F("amount"), 0, output_field=FloatField())),
#         Shareholder=F("shareholder__shareholderName"),
#     )
#     qs_avg = ShareholderDeposit.objects.values("shareholder__shareholderName").annotate(
#         Deposited_Amount=ExpressionWrapper(
#             Sum(Coalesce(F("amount"), 0, output_field=FloatField()))
#             / F("shareholder__numberOfFlat"),
#             output_field=FloatField(),
#         )
#     )

#     x_avg = qs_avg.values_list("shareholder__shareholderName", flat=True)
#     y_avg = qs_avg.values_list("Deposited_Amount", flat=True)
#     text_avg = [f"{(amnt/10**3):,.2f}K" for amnt in y_avg]
#     fig_avg = px.bar(
#         x=x_avg,
#         y=y_avg,
#         # text=text_avg,
#         text_auto=".2s",
#         title="Amount Deposited",
#         labels={"x": "Share Holders", "y": "Average Amount per Flat"},
#         color=y_avg,
#         range_y=[10000, 3000000],
#         # color_discrete_map = ['gold', 'mediumturquoise', 'darkorange', 'lightgreen']
#         color_discrete_map={
#             "Thur": "lightcyan",
#             "Fahim Amin": "cyan",
#             "Sat": "royalblue",
#             "Sun": "darkblue",
#         },
#     )

#     fig = px.pie(
#         qs,
#         values="Amount",
#         names="Shareholder",
#         title="Amount Deposited",
#         #  color_discrete_sequence=px.colors.sequential.RdBu
#     )

#     # fig_avg.title = "Amount Deposited"
#     # fig_avg.labels = {"x": "Share Holders", "y": "Average Amount per Flat"}
#     fig_avg.update_traces(textangle=-90, textposition="outside", cliponaxis=False)
#     fig_avg.update_layout(
#         title={"font_size": 24, "xanchor": "center", "x": 0.5}, barmode="group"
#     )

#     fig.update_traces(textinfo="label+percent", textposition="outside")
#     fig.update_layout(
#         showlegend=False, title={"font_size": 24, "xanchor": "auto", "x": 0.5}
#     )

#     chart = fig.to_html()
#     chart_avg = fig_avg.to_html()

#     #! Bar Chart for shareholder deposit and to pay ================
#     qs_shareholder = Shareholder.objects.all()

#     qs_deposit_subquery = (
#         ShareholderDeposit.objects.filter(shareholder_id=OuterRef("id"))
#         .values("shareholder_id")
#         .annotate(
#             deposited_amount_sum=Coalesce(
#                 Sum(F("amount")), 0, output_field=DecimalField()
#             )
#         )
#     )
#     targeted_amount = TargetedAmount.objects.values_list("amount").order_by(
#         "-inputDate","-id"
#     )[0]
#     targeted_amount_per_flat = targeted_amount[0] / company_info["no_of_flat_per_share"]
#     qs_data = qs_shareholder.annotate(
#         deposited_sum=qs_deposit_subquery.values("deposited_amount_sum"),
#         amount_to_deposit=(F("numberOfFlat") * targeted_amount_per_flat),
#     )

#     data = [
#         {
#             "Shareholder": x.shareholderName,
#             "Amount Deposited": int(x.deposited_sum),
#             "Amount to Deposit": int(x.amount_to_deposit - x.deposited_sum),
#         }
#         for x in qs_data
#     ]
#     fig_deposit_target = px.bar(
#         data,
#         barmode="stack",
#         # barmode="group",
#         x="Shareholder",
#         y=["Amount Deposited", "Amount to Deposit"],
#         title="Shareholders Amount Deposit Info:",
#         labels={"value": "Amount", "variable": "Deposit Type:"},
#     )
#     chart_deposit_target = fig_deposit_target.to_html()
#     #! Bar Chart for shareholder deposit and to pay end ================
#     context = {
#         "chart": chart,
#         "chart_avg": chart_avg,
#         "chart_deposit_target": chart_deposit_target,
#     }
#     return render(request, "accounts/dashboard1.html", context)
 # endregion #!======================

@login_required
def send_mail(request):
    total_recipient = (
        Shareholder.objects.exclude(email__isnull=True)
        .exclude(email__exact="")
        .aggregate(nos_of_shareholders=Count("id"))
    )
    email_address_list_qs = (
        Shareholder.objects.exclude(email__isnull=True)
        .exclude(email__exact="")
        .values_list("shareholderName", "email")
    )
    recipient_name_dic = {email: name for name, email in email_address_list_qs}

    email_addresses = ""
    for address in email_address_list_qs:
        email_addresses += f"{address[1]},"

    if request.method == "POST":
        fm = SendMailForm(request.POST or None, request.FILES or None)
        if fm.is_valid():
            subject = fm.cleaned_data["subject"]
            message = fm.cleaned_data["msg"]
            from_mail = request.user.email
            to_mail = fm.cleaned_data["email_id"].split(",")
            to_cc = fm.cleaned_data["email_cc"]
            to_bcc = fm.cleaned_data["email_bcc"]
            attach = fm.cleaned_data["attachment"]
            attach_name = attach.name
            attach_content_type = attach.content_type
            attach_content = attach.read()

            if from_mail and to_mail:
                for x in to_mail:
                    recipient_name = (
                        recipient_name_dic.get(x)
                        if recipient_name_dic.get(x) != None
                        else ""
                    )
                    try:
                        mail = EmailMessage(
                            subject=subject,
                            body=message,
                            from_email=from_mail,
                            to=[recipient_name, x],
                            bcc=[to_bcc],
                            cc=[to_cc],
                        )
                        mail.attach(attach_name, attach_content, attach_content_type)
                        mail.send()
                    # except Exception as ex:
                    except ArithmeticError as aex:
                        return HttpResponse("Invalid header found")
                recipient_list = ""
                for x in email_address_list_qs:
                    recipient_list += f"{x[0]}, "

                messages.success(
                    request, f"Thank you.\nEmail sent to {recipient_list}."
                )
                return HttpResponseRedirect("/")
            else:
                return HttpResponse("Make sure all fields are entered and valid.")
    else:
        fm = SendMailForm(initial={"email_id": email_addresses})
        # fm.initial
    return render(request, "accounts/send_mail.html", {"fm": fm, "qs": total_recipient})


@login_required
def index(request):
    qs_shareholder = Shareholder.objects.all()
    qs_deposit_subquery = (
        ShareholderDeposit.objects.filter(shareholder_id=OuterRef("id"))
        .values("shareholder_id")
        .annotate(
            deposited_amount_sum=Coalesce(
                Sum(F("amount")), 0, output_field=DecimalField()
            )
        )
    )
    try:
        targeted_amount = TargetedAmount.objects.values_list("amount","inputDate","deadline").order_by(
            "-inputDate","-id"
        )[0]
    except:
        targeted_amount = [0]
        
    targeted_amount_per_flat = targeted_amount[0] / company_info["no_of_flat_per_share"]
    qs_data = qs_shareholder.annotate(
        deposited_sum=Coalesce(qs_deposit_subquery.values("deposited_amount_sum"),0,output_field=DecimalField()),
        amount_to_deposit=(F("numberOfFlat") * targeted_amount_per_flat),
    )

    total_shareholder_deposited = ShareholderDeposit.objects.aggregate(
        Sum("amount", default=0)
    )

    amount_payable_for_shareholder_advance_deposit = 0
    total_amount_receivable_from_shareholder =0
    nos_of_advance_payer = 0
    nos_of_defaulter = 0
    try:
        for row in qs_data.values():
            if row["deposited_sum"]-row["amount_to_deposit"]>0:
                amount_payable_for_shareholder_advance_deposit+=row["deposited_sum"]-row["amount_to_deposit"]
                nos_of_advance_payer += 1
            elif row["deposited_sum"]-row["amount_to_deposit"]<0:
                total_amount_receivable_from_shareholder +=row["deposited_sum"]-row["amount_to_deposit"]
                nos_of_defaulter +=1
    except:
        pass

    # total_amount_receivable_from_shareholder = ((targeted_amount_per_flat*company_info["total_number_of_flat"]) - total_shareholder_deposited["amount__sum"])
    project_income = Income.objects.aggregate(Sum("amount", default=0))
    total_deposited_amount = total_shareholder_deposited["amount__sum"] + (
        0 if project_income["amount__sum"] == None else project_income["amount__sum"]
    )

    total_Expenditure = Expenditure.objects.aggregate(Sum("amount"))

    #! Contractor Bill Payment Status ==================
    qs_contractor_bill_status = ContractorBillSubmission.objects.values("id").annotate(
        sum_amount=Sum(F("bill_submission__amount")),
        submission_date=F("dateOfBillSubmission"),
        amount=F("amount"),
        contractor=F("contractor__contractor"),
    )
    qs_contractor_bill_status = (
        qs_contractor_bill_status.annotate(
            submission_date=F("submission_date"),
            amount=F("amount"),
            rest_amount=(
                Coalesce(
                    (F("amount") - Coalesce(F("sum_amount"), 0)),
                    0,
                    output_field=IntegerField(),
                )
            ),
            contractor=F("contractor"),
        )
        .filter(rest_amount__gt=0)
        .order_by("contractor")
    )
    qs_contractor_bill_status_sum = qs_contractor_bill_status.aggregate(
       qs_contractor_bill_status_sum = Coalesce(Sum(F("rest_amount")),0,output_field=FloatField())
    )
    #! Contractor Bill Payment Status End ==================

    #! Credit Purchase Payment Status ==================
    qs_credit_purchase_payment_status = CreditPurchase.objects.values("id").annotate(
        sum_amount=Sum(F("Seller__amount")),
        purchase_date=F("dateOfPurchase"),
        amount=F("amount"),
        seller=F("seller"),
    )
    qs_credit_purchase_payment_status = (
        qs_credit_purchase_payment_status.annotate(
            purchase_date=F("purchase_date"),
            amount=F("amount"),
            rest_amount=(
                Coalesce(
                    (F("amount") - Coalesce(F("sum_amount"), 0)),
                    0,
                    output_field=IntegerField(),
                )
            ),
            seller=F("seller"),
        )
        .filter(rest_amount__gt=0)
        .order_by("seller")
    )
    qs_credit_purchase_payment_status_sum = qs_credit_purchase_payment_status.aggregate(
      credit_purchase_payment_status_sum = Coalesce(Sum(F("rest_amount")),0,output_field=FloatField())
    )
    bankLedgerBalance = BankLedger.objects.aggregate(
        bank_balance=Coalesce(
            Sum(F("credit") - F("debit")), 0, output_field=IntegerField()
        )
    )
    cashLedgerBalance = CashLedger.objects.aggregate(
        cash_balance=Coalesce(
            Sum(F("credit") - F("debit")), 0, output_field=IntegerField()
        )
    )
    totalBalance = {
        "total_balance": bankLedgerBalance["bank_balance"]
        + cashLedgerBalance["cash_balance"]
    }
    bank_and_cash = bankLedgerBalance | cashLedgerBalance | totalBalance
    #! Credit Purchase Payment Status End ==================
    # targeted_amount_per_share = targeted_amount[0]
    try:
        fig_pie_chart = chart()["fig_pie_chart"]
        chart_deposit_target = chart()["chart_deposit_target"]
    except:
        fig_pie_chart = dummy_chart()["fig_pie_chart"]
        chart_deposit_target = dummy_chart()["chart_deposit_target"]

    try:
        current_work_phase = current_work_phase.work_phase_description 
    except:
        current_work_phase = "Dummy Work Phase" 

    context = {
        "fig_pie_chart": fig_pie_chart,
        "chart_deposit_target": chart_deposit_target,
        "total_shareholder_deposited": total_shareholder_deposited,
        "project_income": project_income,
        "total_deposited_amount": total_deposited_amount,
        "total_Expenditure": total_Expenditure,
        "targeted_amount": int(targeted_amount[0]),
        "start_date": targeted_amount[1],
        "deadline": targeted_amount[2],
        "qs_data": qs_data,
        "qs_contractor_bill_status": qs_contractor_bill_status,
        "qs_contractor_bill_status_sum": qs_contractor_bill_status_sum["qs_contractor_bill_status_sum"],
        "qs_credit_purchase_payment_status": qs_credit_purchase_payment_status,
        "qs_credit_purchase_payment_status_sum": qs_credit_purchase_payment_status_sum["credit_purchase_payment_status_sum"],
        "total_amount_receivable_from_shareholder": total_amount_receivable_from_shareholder,
        "amount_payable_for_shareholder_advance_deposit": amount_payable_for_shareholder_advance_deposit,
        "nos_of_advance_payer":nos_of_advance_payer,
        "nos_of_defaulter":nos_of_defaulter,
        "bank_and_cash": bank_and_cash,
        "current_work_phase": current_work_phase,
    }  
    return render(request, "accounts/dashboard.html", context)


#!JSON Data ===============================

@login_required
def get_item(request, itemCode_id):
    item = Item.objects.filter(ItemCode_id=itemCode_id)
    data = serializers.serialize("json", item)
    return HttpResponse(data, content_type="application/json")


def get_balance(request):
    transaction_type = request.GET.get("transaction_type")

    if transaction_type == "BANK":
        account_id = request.GET.get("account_id")
        bank_balance = BankLedger.objects.filter(account=account_id).aggregate(
            total_credit=Sum("credit", default=Decimal(0)),
            total_debit=Sum("debit", default=Decimal(0))
        )
        # Convert Decimal values to float
        bank_balance = float(bank_balance["total_credit"] - bank_balance["total_debit"])
        return HttpResponse(json.dumps(bank_balance))

    else:
        cash_balance = CashLedger.objects.aggregate(
            total_credit=Sum("credit", default=Decimal(0)),
            total_debit=Sum("debit", default=Decimal(0))
        )
        cash_balance = float(cash_balance["total_credit"] - cash_balance["total_debit"])
        print(f"🍁 Masud: {cash_balance=}")
        return HttpResponse(json.dumps(cash_balance))


@login_required
def get_unit(request, item_id):
    unit = Item.objects.filter(id=item_id)
    data = serializers.serialize("json", unit)
    return HttpResponse(data, content_type="application/json")

@login_required
def get_income_item_unit(request, item_id):
    unit = IncomeItem.objects.filter(id=item_id)
    data = serializers.serialize("json", unit)
    return HttpResponse(data, content_type="application/json")


@login_required
def get_account_no(request, id, table):
    tables = {"ShareholderDeposit":ShareholderDeposit,
                "Expenditure":Expenditure,
                "Income":Income,
                "ContractorBillPayment":ContractorBillPayment,
                "CreditPurchasePayment":CreditPurchasePayment,
                "OfficeExpenditure":OfficeExpenditure
                }
    tbl=tables[table]
    get_tid = tbl.objects.filter(id=id).values()[0][
        "tid"
    ]
    transaction = BankLedger.objects.filter(tid=get_tid)
    data = serializers.serialize("json", transaction)
    return HttpResponse(data, content_type="application/json")


@login_required
def get_income_item(request, IncomeSector_id):
    incomeItem = IncomeItem.objects.filter(incomeSector_id=IncomeSector_id)
    data = serializers.serialize("json", incomeItem)
    return HttpResponse(data, content_type="application/json")


@login_required
def get_shareholder_deposit_info(request, shareholder_id):
    avatar = str(Shareholder.objects.get(id=shareholder_id).avatar)
    deposit = (
        ShareholderDeposit.objects.filter(shareholder_id=shareholder_id)
        .values(
            "shareholder__shareholderName",
            "dateOfTransaction",
            "modeOfTransaction",
            "amount",
            "remarks",
        )
        .order_by("-dateOfTransaction")
    )

    total_deposit = (
        ShareholderDeposit.objects.values("shareholder")
        .annotate(total_amount=Sum("amount"))
        .get(shareholder_id=shareholder_id)
    )

    total_deposited_amount = {
        "total_deposit": str(intcomma_bd(total_deposit["total_amount"]))
    }

    deposit_info = []

    for item in deposit:
        shareholderName = str(item["shareholder__shareholderName"])
        date_of_transaction = str(item["dateOfTransaction"].strftime("%d %b %Y"))
        modeOfTransaction = str(item["modeOfTransaction"])
        amount = f'{str(intcomma_bd(item["amount"]))}/-'
        remarks = str((item["remarks"] if item["remarks"] != None else "----"))
        
        dict_items = {
            "shareholderName": shareholderName,
            "avatar": avatar,
            "date_of_transaction": date_of_transaction,
            "modeOfTransaction": modeOfTransaction,
            "amount": amount,
            "remarks": remarks,
        }
        deposit_info.append(dict_items)

    deposit_info.append(total_deposited_amount)

    info = json.dumps(deposit_info)
    return HttpResponse(info)


@login_required
def get_credit_purchase_rest_amount(request, seller_id):
    query_set = (
        CreditPurchase.objects.filter(id=seller_id)
        .values("id")
        .annotate(
            sum_amount=Sum(F("Seller__amount")),
            amount=F("amount"),
        )
    )
    query_set = query_set.annotate(
        rest_amount=(
            Coalesce(
                (F("amount") - Coalesce(F("sum_amount"), 0)),
                0,
                output_field=IntegerField(),
            )
        )
    )
    rest_amount = {"rest_amount": str(query_set[0]["rest_amount"])}

    info = json.dumps(rest_amount)
    return HttpResponse(info)


@login_required
def get_contractor_bill_rest_amount(request, bill_id):
    query_set = (
        ContractorBillSubmission.objects.filter(id=bill_id)
        .values("id")
        .annotate(
            sum_amount=Sum(F("bill_submission__amount")),
            amount=F("amount"),
        )
    )
    query_set = query_set.annotate(
        rest_amount=(
            Coalesce(
                (F("amount") - Coalesce(F("sum_amount"), 0)),
                0,
                output_field=IntegerField(),
            )
        )
    )
    rest_amount = {"rest_amount": str(query_set[0]["rest_amount"])}

    info = json.dumps(rest_amount)
    return HttpResponse(info)

@login_required
def get_current_workphase(request):
    qs=WorkPhase.objects.filter(id=CurrentWorkPhase.objects.last().workPhase_id).annotate(
        current_workphase = F("description"),
        row_number =  Window(expression=RowNumber())
    ).values("current_workphase", "row_number")
    # current_workphase = {"current_work_phase": str(WorkPhase.objects.get(id=CurrentWorkPhase.objects.last().workPhase_id).description),
    # "row_number":WorkPhase.objects.get(id=CurrentWorkPhase.objects.last().workPhase_id Window(
    # expression=RowNumber(),
    # )}
    info = json.dumps(qs[0])
    return HttpResponse(info)
#!JSON Data End ===========================================

#! Reports =================================================

class ExpenditureSummary(LoginRequiredMixin, ListView):
    # model = Expenditure
    template_name = "accounts/report_templates/expenditure_summary.html"
    context_object_name = "expenditure"

    def get_queryset(self):
        qs = expenditure_summary_qs(self.request)
        return qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["grand_total"] = Expenditure.objects.aggregate(Sum("amount"))
        return context


def plot_chart(request):
    qs_shareholder = Shareholder.objects.all()

    qs_deposit_subquery = (
        ShareholderDeposit.objects.filter(shareholder_id=OuterRef("id"))
        .values("shareholder_id")
        .annotate(
            deposited_amount_sum=Coalesce(
                Sum(F("amount")), 0, output_field=DecimalField()
            )
        )
    )
    targeted_amount = TargetedAmount.objects.values_list("amount").order_by(
        "-inputDate","-id"
    )[0]
    targeted_amount_per_flat = targeted_amount[0] / company_info["no_of_flat_per_share"]
    qs_data = qs_shareholder.annotate(
        deposited_sum=qs_deposit_subquery.values("deposited_amount_sum"),
        amount_to_deposit=(F("numberOfFlat") * targeted_amount_per_flat),
    )

    data_deposit_target = [
        [
            x.shareholderName,
            int(x.deposited_sum),
            int(x.amount_to_deposit - x.deposited_sum),
        ]
        for x in qs_data
    ]

    # Create a DataFrame
    df_deposit_target = pd.DataFrame(
        data_deposit_target,
        columns=["Shareholders", "Amount Deposited", "Amount to Deposit"],
    )

    # Create a stacked bar chart using Plotly
    fig_deposit_target = px.bar(
        df_deposit_target,
        barmode="stack",
        # barmode="group",
        text_auto=".3s",
        x="Shareholders",
        y=["Amount Deposited", "Amount to Deposit"],
        title="Shareholders Amount Deposit Info:",
        labels={"value": f"Amount ({currency})", "variable": "Deposit Type"},
    )
    fig_deposit_target.update_layout(  # customize font and legend orientation & position
        # font_family="Rockwell",
        legend=dict(
            title=None, orientation="h", y=1, yanchor="bottom", x=0.5, xanchor="center"
        ),
        title={"font_size": 24, "xanchor": "center", "x": 0.5},
    )
    # Convert the Plotly figure to an image
    # img_bytes = fig.to_image(format="png")
    img_bytes = fig_deposit_target.to_html()

    # Serve the image as an HTTP response
    response = HttpResponse(img_bytes)
    return response


class ContractorUpdate(LoginRequiredMixin, UpdateView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    model = Contractor
    form_class = ContractorForm
    template_name = "accounts/forms/form_contractor.html"
    success_url = "/contractor_list/"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["heading"] = "Contractor Information Update"
        context["update_tag"] = True
        context["redirect_url"] = "/contractor_list/"
        return context

    def form_valid(self, form):
        messages.success(
            self.request, "The Contractor's information updated successfully."
        )
        return super(ContractorUpdate, self).form_valid(form)


class ShareholderUpdate(LoginRequiredMixin, UpdateView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    model = Shareholder
    form_class = ShareholderForm
    template_name = "accounts/forms/form_shareholder.html"
    success_url = "/shareholder_list/"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["heading"] = "Shareholder's Information Update"
        context["update_tag"] = True
        context["redirect_url"] = "/shareholder_list/"
        return context

    def form_valid(self, form):
        messages.success(
            self.request, "The shareholder's information updated successfully."
        )
        return super(ShareholderUpdate, self).form_valid(form)


class ShareholderDepositView(LoginRequiredMixin, FormView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_shareholder_deposit.html"
    form_class = ShareholderDepositForm
    success_url = "./"

    def form_valid(self, form):
        description = f"Deposit from {Shareholder.objects.get(id=form['shareholder'].value())}"
        form_save(form, description, True)
        messages.success(
                self.request, "Thank you. Shareholder Deposit Posting done."
            )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = ShareholderDeposit.objects.order_by("-dateOfTransaction")[:10]
        context["heading"] = "Shareholder Deposit"
        context["data_heading"] = "Last 10 Shareholder Deposit Posting:"
        return context

class ShareholderDepositUpdate(LoginRequiredMixin, UpdateView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    model = ShareholderDeposit
    form_class = ShareholderDepositForm
    template_name = "accounts/forms/form_shareholder_deposit.html"
    success_url = "/shareholder_list/"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["heading"] = "Shareholder Deposit Update"
        context["update_tag"] = True
        context["redirect_url"] = "/shareholder_list/"
        return context

    def form_valid(self, form):
        if form.is_valid():
            model_nm = ShareholderDeposit
            description = f"Deposited from: {Shareholder.objects.get(id=form['shareholder'].value())}"
            is_credit = True
            form_save_update(self, form, model_nm, description, is_credit)                     
        messages.success(
            self.request, "The shareholder's Deposit Information updated successfully."
        )
        return super(ShareholderDepositUpdate, self).form_valid(form)

    #! form_valid() – the method is called once the form is posted successfully.
    #! In this example, we create a flash message and
    #! return the result of the form_valid() method of the superclass.


class DateRangeExpenditure(LoginRequiredMixin, ListView):
    # model = Expenditure
    template_details_list = "accounts/report_templates/expenditure_detail_list_with_tab.html"
    template_date_range = "accounts/report_templates/date_range_expenditure_detail.html"
    context_object_name = "expenditure"

    def get_template_names(self, *args, **kwargs):
        if self.request.path == reverse("Accounts:expenditure_details_list"):
            return [self.template_details_list]
        return [self.template_date_range]

    def get_queryset(self):
        qs_data = expenditure_details_qs(self.request)
        return qs_data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from_date = self.request.GET.get("fromdate")
        to_date = self.request.GET.get("todate")

        table_data = Expenditure.objects.all()
        if from_date:
            table_data = table_data.filter(dateOfTransaction__gte=from_date)

        if to_date:
            table_data = table_data.filter(dateOfTransaction__lte=to_date)
        
        context["grand_total"] = table_data.aggregate(Sum("amount"))

        return context


# TODO: =======================


class IncomeSectorView(LoginRequiredMixin, FormView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_single_column.html"
    form_class = IncomeSectorForm
    success_url = "/"

    def form_valid(self, form):
        form.save()
        messages.success(self.request, "Thank you. A new Income Sector added.")
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = IncomeSector.objects.all()
        context["heading"] = "Add New Income Sector"
        context["data_heading"] = "Existing Income Sectors:"
        context["detail_tag"] = False
        return context


class IncomeItemView(LoginRequiredMixin, FormView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_item_add.html"
    form_class = IncomeItemForm
    success_url = "/"

    def form_valid(self, form):
        form.save()
        messages.success(self.request, "Thank you. A new Income Item added.")
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = IncomeItem.objects.all().order_by("incomeSector", "itemName")
        context["heading"] = "Add A New Income Item"
        context["data_heading"] = "Existing Income Items:"
        context["detail_tag"] = False
        return context


class IncomeView(LoginRequiredMixin, FormView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_income.html"
    form_class = IncomeForm
    success_url = "/"

    def form_valid(self, form):
        description= f"Income From: {form['description'].value()}"
        form_save(form, description, True)
        messages.success(self.request, "Thank you. Income posting done.")
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = Income.objects.order_by("-dateOfTransaction")[:10]
        context["heading"] = "Income Posting"
        context["data_heading"] = "Last 10 Income Posting:"
        return context


class IncomeUpdate(LoginRequiredMixin, UpdateView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    model = Income
    form_class = IncomeForm
    template_name = "accounts/forms/form_income.html"
    success_url = "/details_income_list/"

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        # initial_values = self.get_initial() 
        # initial_values['incomeSector'] = Income.objects.filter(id=pk)
        # form.initial = initial_values
        income_sector_id=IncomeItem.objects.filter(id=form.initial["incomeItem"]).values()[0]["incomeSector_id"]
        # print("🗣 Result: ",income_sector_id)
        form.initial["incomeSector"]=income_sector_id
        return form

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["heading"] = "Income's Information Update"
        context["update_tag"] = True
        context["redirect_url"] = "/expenditure_details_list/"
        return context

    def form_valid(self, form):
        model_nm = Income
        description = f"Income From: {form['description'].value()}"
        is_credit = True
        form_save_update(self, form, model_nm, description, is_credit)
        messages.success(self.request, "The Income transaction updated successfully.")
        return super(IncomeUpdate, self).form_valid(form)


class DateRangeIncome(LoginRequiredMixin, ListView):
    model = Income
    template_details_list = "accounts/report_templates/income_detail_list.html"
    template_date_range = "accounts/report_templates/date_range_income_detail.html"
    context_object_name = "income"

    def get_template_names(self, *args, **kwargs):
        if self.request.path == reverse("Accounts:details_income_list"):
            return [self.template_details_list]
        return [self.template_date_range]

    def get_queryset(self):
        from_date = self.request.GET.get("fromdate")
        to_date = self.request.GET.get("todate")
        qs = super().get_queryset()
        if from_date:
            qs = qs.filter(dateOfTransaction__gte=from_date)

        if to_date:
            qs = qs.filter(dateOfTransaction__lte=to_date)

        subquery_sum = (
            qs.filter(
                incomeItem=OuterRef("incomeItem__id"),
                # incomeItem__incomeSector=OuterRef("incomeItem__incomeSector__id"),
            )
            .values(
                # "incomeItem__incomeSector__incomeSector",
                "incomeItem__itemName",
            )
            .annotate(
                sum_amount=Round(Sum(F("amount")), 0),
                sum_quantity=Round(Sum(F("quantity")), 0),
                units=F("incomeItem__unit"),
            )
        )
        subquery_income_sector_sum = (
            qs.filter(incomeItem__incomeSector=OuterRef("incomeItem__incomeSector__id"))
            .values("incomeItem__incomeSector__incomeSector")
            .annotate(
                incomesector_sum=Coalesce(
                    Sum(F("amount")), 0, output_field=DecimalField()
                ),
                income_sector_id = F("incomeItem__incomeSector_id"),
            )
        )

        qs = qs.annotate(
            income_sector=Subquery(
                subquery_sum.values("incomeItem__incomeSector__incomeSector")
            ),
            income_item_name=Subquery(subquery_sum.values("incomeItem__itemName")),
            sum_amount=Subquery(subquery_sum.values("sum_amount")),
            sum_quantity=Subquery(subquery_sum.values("sum_quantity")),
            units=Subquery(subquery_sum.values("units")),
        )
        if from_date or to_date:
            qs = qs.annotate(
                incomesector_sum=Subquery(
                    subquery_income_sector_sum.values("incomesector_sum")
                ),
                income_sector_id=Subquery(
                    subquery_income_sector_sum.values("income_sector_id")
                ),
            ).order_by("income_sector", "income_item_name", "dateOfTransaction")
        else:
            qs = qs.annotate(
                incomesector_sum=Subquery(
                    subquery_income_sector_sum.values("incomesector_sum")
                ),
                income_sector_id=Subquery(
                    subquery_income_sector_sum.values("income_sector_id")
                ),
            ).order_by("income_sector", "income_item_name", "-dateOfTransaction")

        return qs

    def get_context_data(self, **kwargs):
        from_date = self.request.GET.get("fromdate")
        to_date = self.request.GET.get("todate")

        qs = Income.objects.all()
        if from_date:
            qs = qs.filter(dateOfTransaction__gte=from_date)

        if to_date:
            qs = qs.filter(dateOfTransaction__lte=to_date)

        context = super().get_context_data(**kwargs)
        context["grand_total"] = qs.aggregate(Sum("amount"))
        if from_date:
            context["fromdate"] = datetime.strptime(from_date, "%Y-%m-%d")
        if to_date:
            context["todate"] = datetime.strptime(to_date, "%Y-%m-%d")
        return context


class VisitorList(LoginRequiredMixin, ListView):
    login_url = "/accounts/login/"
    # redirect_field_name = "../VisitorList/"
    model = UserLoggedinRecord
    template_name = "visitors/visitor_list.html"
    context_object_name = "visitors"
    # paginate_by = 10

    def get_queryset(self):
        data = super().get_queryset().order_by("-id")
        return data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context


class VisitorDetails(DetailView):
    model = UserLoggedinRecord
    context_object_name = "visitors_data"
    template_name = "visitors/visitor_details.html"


# TODO: Bank Account Management ==========================
class BankAccountView(LoginRequiredMixin, CreateView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_bank_account_add.html"
    form_class = BankAccountForm
    success_url = "./"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = BankAccount.objects.order_by("-id")
        context["data_heading"] = "Existing Bank Accounts:"
        context["heading"] = "<h5>Add New Bank Account</h5>"
        return context

    def form_valid(self, form):
        form.save()
        messages.success(self.request, "Thank you. New Bank Account Created.")
        return super(BankAccountView, self).form_valid(form)


class BankAccountUpdate(LoginRequiredMixin, UpdateView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    model = BankAccount
    form_class = BankAccountForm
    template_name = "accounts/forms/form_bank_account_add.html"
    success_url = "/bank_list/"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["heading"] = "<h5>Bank Account Information Update</h5>"
        context["update_tag"] = True
        context["redirect_url"] = f"./{self.kwargs['pk']}"
        return context

    def form_valid(self, form):
        messages.success(
            self.request, "The Bank Account's information updated successfully."
        )
        return super(BankAccountUpdate, self).form_valid(form)

class BankListView(LoginRequiredMixin, ListView):
    model = BankAccount
    template_name = "accounts/report_templates/bank_list.html"
    context_object_name = "bank"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["heading"] = "Existing Bank List"
        return context
    

class BankTransaction(LoginRequiredMixin, FormView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_bank_transaction.html"
    form_class = BankTransactionForm
    success_url = "./"

    def form_valid(self, form):
        bank_balance = BankLedger.objects.filter(
            account=form["account"].value()
        ).aggregate(bank_balance=Sum(F("credit") - F("debit")))
        cash_balance = CashLedger.objects.aggregate(
            cash_balance=Sum(F("credit") - F("debit"))
        )

        if form["transactionType"].value() == "D" and cash_balance[
            "cash_balance"
        ] < int(form["amount"].value()):
            messages.success(
                self.request,
                f"""{currency}: {form['amount'].value()}/- is not available in cash.""",
            )
            return redirect("./")

        if form["transactionType"].value() == "W" and bank_balance[
            "bank_balance"
        ] < int(form["amount"].value()):
            messages.success(
                self.request,
                f"""{currency}: {form['amount'].value()}/- is not available in bank account.""",
            )
            return redirect("./")

        with transaction.atomic():
            get_tid = get_unique_tid()
            if form["transactionType"].value() == "D":
                BankLedger.objects.create(
                    dateOfTransaction=form["dateOfTransaction"].value(),
                    description=form["description"].value(),
                    account=BankAccount.objects.get(id=form["account"].value()),
                    credit=form["amount"].value(),
                    debit=0,
                    remarks=form["remarks"].value(),
                    tid=get_tid,
                )
                CashLedger.objects.create(
                    dateOfTransaction=form["dateOfTransaction"].value(),
                    description=form["description"].value(),
                    credit=0,
                    debit=form["amount"].value(),
                    remarks=form["remarks"].value(),
                    tid=get_tid,
                )
            elif form["transactionType"].value() == "W":
                BankLedger.objects.create(
                    dateOfTransaction=form["dateOfTransaction"].value(),
                    description=form["description"].value(),
                    account=BankAccount.objects.get(id=form["account"].value()),
                    credit=0,
                    debit=form["amount"].value(),
                    remarks=0,
                    tid=get_tid,
                )
                CashLedger.objects.create(
                    dateOfTransaction=form["dateOfTransaction"].value(),
                    description=form["description"].value(),
                    credit=form["amount"].value(),
                    debit=0,
                    remarks=form["remarks"].value(),
                    tid=get_tid,
                )

            messages.success(self.request, "Thank you. Transaction Completed.")
            return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = BankLedger.objects.order_by("-dateOfTransaction")[:10]
        context["bank_balance"] = BankLedger.objects.aggregate(
            bank_balance=Sum(F("credit") - F("debit"))
        )
        context["cash_balance"] = CashLedger.objects.aggregate(
            cash_balance=Sum(F("credit") - F("debit"))
        )
        context["heading"] = "Bank Transaction"
        context["data_heading"] = "Last 10 Bank Transactions:"
        return context

#TODO Bank and CASH =============================
# ShareholderDepositView -- Done
# ShareholderDepositUpdate -- Done

# ExpenditureView -- Done
# ExpenditureUpdate -- Done

# IncomeView -- Done
# IncomeUpdate -- Done

# BankTransaction -- Done

# ContractorBillPayment -- Done
# CreditPurchasePayment -- Done
#! OfficeExpenditure
#TODO Bank and CASH End ========================

# TODO: Work Phase ==========================
class WorkPhaseView(LoginRequiredMixin, CreateView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_bank_account_add.html"
    form_class = WorkPhaseForm
    success_url = "./"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = WorkPhase.objects.order_by("-id")
        context["heading"] = "<h5>Add New Working Phase</h5>"
        context["data_heading"] = "Existing Work Phases:"
        return context

    def form_valid(self, form):
        form.save()
        messages.success(self.request, "Thank you. A New Work Phase Created.")
        return super(WorkPhaseView, self).form_valid(form)


class CurrentWorkPhaseView(LoginRequiredMixin, CreateView):
    @method_decorator(allowed_users(["Admin", "Manager"]))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    template_name = "accounts/forms/form_bank_account_add.html"
    form_class = CurrentWorkPhaseForm
    success_url = "./"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["data"] = CurrentWorkPhase.objects.all().order_by("-id")
        current_phase = context["data"].values("workPhase__description")[0]["workPhase__description"]
        context["heading"] = f"""<div class='d-flex justify-content-between p-0 m-0'><h5>Change Work Phase</h5> <p class='text-warning fs-5 p-0 m-0'>[Current Work phase: <span class='text-white'>{current_phase}</span>]</p></div>"""
        context["data_heading"] = "Work Phases Change History:"
        return context

    def form_valid(self, form):
        form.save()
        global current_work_phase
        current_work_phase = WorkPhase.objects.get(id=CurrentWorkPhase.objects.last().workPhase_id) 
        messages.success(self.request, "Work Phase Changed.")
        return super(CurrentWorkPhaseView, self).form_valid(form)
        # CurrentWorkPhaseForm