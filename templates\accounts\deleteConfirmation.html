{% extends "main.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load mahimsoft_tags %}

{% block extrahead %}
<!-- block extrahead -->
<style>
    div>ol>li:nth-child(odd) {
        background: #f4f5ed;
    }
</style>
<!-- endblock extrahead -->
{% endblock extrahead %}

{% block content %}

<div class="container legend mt-5">
    <h6 class="text-center">Payment: {{record}}</h6>
    <div class="row row-cols-1 row-cols-lg-3 ps-5">
        <div class="col-lg-4"></div>
        <div class="col">
            <div class="table-active table-responsive">
                <table>
                    {% if record.contractor_name %}
                    <tr>
                        <th class="text-end pe-2 py-2">Contractor Name :</th>
                        <td class="ps-2">
                            <h5 class="text-success pt-2">{{record.contractor_name}}</h5>
                        </td>
                    </tr>
                    {% elif record.seller_name %}
                    <tr>
                        <th class="text-end pe-2 py-2">Seller Name :</th>
                        <td class="ps-2">
                            <h5 class="text-success pt-2">{{record.seller_name}}</h5>
                        </td>
                    </tr>
                    {% endif %}
                    <tr>
                        <th class="text-end pe-2 py-2">Date of Payment :</th>
                        <td class="ps-2">{{record.dateOfTransaction}}</td>
                    </tr>
                    <tr>
                        <th class="text-end pe-2 py-2">Paid Amount :</th>
                        <td class="ps-2">{{record.amount|intcomma_bd}}</td>
                    </tr>
                    <tr>
                        <th class="text-end pe-2 py-2">Voucher No :</th>
                        <td class="ps-2">{{record.voucherNo}}</td>
                    </tr>
                    <tr>
                        <th class="text-end pe-2 py-2">Remarks :</th>
                        <td class="ps-2">{{record.remarks}}</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="col-lg-4"></div>
    </div>
    <div class="text-center">
        <form method="post" id="frm_delete" action="">
            {% csrf_token %}
            <h4 class="text-danger">Are you sure! You want to delete this record?</h4><br>
            <input class="btn btn-sm btn-danger" type="submit" value="Yes" />
            <a href="{% url 'Accounts:contractor_bill_payment_history' %}">
                <div class="btn btn-sm btn-primary">Cancel</div>
            </a>
        </form>
    </div>
</div>
{% endblock content %}

{% block script %}
<script src="{% static 'js/mahimsoft.js' %}"></script>
<script>
    confirmationAlert("#frm_delete", "Are you sure you want to Delete this record?")
</script>
{% endblock script %}