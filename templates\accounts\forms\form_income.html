{% extends "main.html" %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}

{% block title %}{{company_name}}{%endblock %}
{% block content %}

<div class="container pt-3">
    <div class="card">
        <div class="card-header ps-2 py-1">
            <h4><i class="bi bi-server"></i> {{heading}}</h4>
        </div>
        <div class="card-body">
            {% crispy form %}
            {% comment %} <div class="d-flex justify-content-end mb-1" id="button_div">
                {% if update_tag is None %}
                <input type="submit" name="submit" value="Submit" class="btn btn-primary btn btn-success me-2 mb-0"
                    id="submit-id-submit">
                <input type="reset" name="reset" value="Reset" class="btn btn-inverse btn btn-danger me-0 mb-0"
                    id="reset-id-reset">
                {% else %}
                <input type="submit" name="submit" value="Update" class="btn btn-primary btn btn-success me-2 mb-0"
                    id="submit-id-submit">
                <a href="/expenditure_details_list/" class="btn btn-danger">Cancel</a>
                {% endif %}
            </div> {% endcomment %}

        </div>
    </div>
    <br>
    {% if update_tag is None %}
    <div class="card">
        <div class="card-header ps-2 py-1">
            <h5>{{data_heading}}</h5>
        </div>
        <ul class="list-group list-group-flush">
            {% for dt in data %}
            {% if forloop.counter|divisibleby:2 %}
            <li class="ps-5 pe-5 list-group-item list-group-item-light"><i class="bi bi-record-fill"></i> {{dt}}</li>
            {% else %}
            <li class="ps-5 pe-5 list-group-item list-group-item-secondary"><i class="bi bi-record-fill"></i> {{dt}}
            </li>
            {% endif %}
            {% endfor %}
        </ul>
    </div>
    {% endif %}
</div>
    <br>

    {% endblock %}

    {% block script %}
    <script>
        var account_input = document.getElementById("id_account")
        {% if update_tag is None %}
        $('#id_incomeItem').empty();
        $('#id_incomeItem').append(`<option class='text-muted' value selected>------------</option>`);
        {% else %}
        button_div = document.getElementById("button_div")
        button_div.innerHTML = `<input type="submit" name="submit" value="Update" class="btn btn-primary btn btn-success me-2 mb-0" id="submit-id-submit">
	<a href="{%url 'Accounts:expenditure_details_list'%}" class="btn btn-danger">Cancel</a>`
        // Bank Account Field Initialization start =========
        if ($('#id_modeOfTransaction').val() == "BANK") {
            $.ajax({
                url: '/get_account_no/' + {{ object.pk }} + "/Income",
        dataType: "json",
            success: function (data) {
                $('#id_account').val(data[0]["fields"]["account"]);
            }
            });
        }

        if ($('#id_modeOfTransaction').val() == "CASH") {
            account_input.disabled = true;
        }
        // Bank Account Field Initialization end =========
        {% endif %}
        $('#id_incomeSector').change(function () {
            let id_IncomeItem_id = $(this).val();
            $('#id_incomeItem').empty();
            $('#id_incomeItem').append(`<option class='text-muted' value selected>------------</option>`);
            $.ajax({
                url: '/get_income_item/' + id_IncomeItem_id,
                success: function (data) {
                    $.each(data, function (index, Item) {
                        $('#id_incomeItem').append($('<option>', {
                            value: Item.pk,
                            text: Item.fields.itemName
                        }));
                    });
                }
            });
        });

        // ======= Auto Calculate income amount.========
        $('#id_rate').change(function () {
            let quantity = $('#id_quantity').val() ?? 0;
            let rate = $(this).val()
            $('#id_amount').val(quantity * rate);
        })
        // ========Auto Calculate income amount. end ====
        // ======= Enable and Disable Bank Account Choice.========

        {% if not update_tag %}
        account_input.disabled = true;
        {% endif %}

        $('#id_modeOfTransaction').change(function () {
            if ($(this).val() == "BANK") {
                account_input.disabled = false;
                account_input.required = true;
            } else {
                account_input.disabled = true;
                account_input.required = false;
                account_input.value = "";
            }
        })
        // =======Enable and Disable Bank Account Choice end ============
        // ======= Change Item Unit =========================
        $('#id_incomeItem').change(function () {
            let item_id = parseInt($('#id_incomeItem').val() ?? 0);
            $.ajax({
                url: '/get_income_item_unit/' + item_id,
                success: function (data) {
                    $('#id_unit').val(data[0].fields.unit);
                }
            });
        });
        // ===================================================
    </script>

    {% endblock script %}