{% extends "accounts/reports/reportbase.html" %}
{% load static %}
{% load mahimsoft_tags %}
{% load mathfilters %}
{% block title %}<title>Deposit Information of {{data.shareholder_deposit.0.shareholderName}}</title>{% endblock title %}
{% block link %}
  <link href="{% static 'page_css_portrait.css' %}" rel="stylesheet">
{% endblock link %}
{% block style %}
<style>
    @page {
        @bottom-right {
            content: '\00A9 {{data.company_name}}, Page No : ' counter(page) ' of ' counter(pages);
            
        }
    }
    td:last-child {
        text-align: left;
    }
</style>
{% endblock style %}
{% block contents %}
<p class="report_date">Date: {% now 'd-M-Y' %}</p>
<div class="container">
    <div style="width: 90%; margin: auto; color: #7a7b7d;">
    <h3 style="text-align: left;">Shareholder Name: 
        <span style="color: red;">
        {{data.shareholder_deposit.0.shareholderName}}</span></h3>
    </div>
    <div class="card">
        <div class="flex_content_between">
                    <table style="width: 50%; margin: 0px 0px 0px 0px;">
                        <tbody>
                            <tr>
                                <td style="text-align: right; width: 40%;">Number{{ data.shareholder_deposit.0.numberOfFlat|pluralize:"s" }} of Share</th>
                                <td>: {{data.shareholder_deposit.0.numberOfFlat|floatformat:"1"}}
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align: right; width: 40%;">Targeted Amount</th>
                                <td>:
                                    {{ data.currency }} {{data.shareholder_deposit.0.numberOfFlat|number_product:data.targeted_amount_per_flat|intcomma_bd}}
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align: right; width: 40%;">Amount Deposited</th>
                                <td>: {{ data.currency }}
                                    {{data.shareholder_deposit.0.deposit_amount_sum|intcomma_bd}}
                                </td>
                            </tr>
                            <tr>
                                {% if data.shareholder_deposit.0.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:data.shareholder_deposit.0.deposit_amount_sum > 0 %}
                                <td style="text-align: right; width: 40%;">Amount to Deposit</th>
                                <td style="color: red;">
                                : {{ data.currency }} {{data.shareholder_deposit.0.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:data.shareholder_deposit.0.deposit_amount_sum|number_product:-1|intcomma_bd}}
                            </td>
                                    {% elif data.shareholder_deposit.0.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:data.shareholder_deposit.0.deposit_amount_sum == 0 %}
                                    <td style="text-align: right; width: 40%;">Amount to Deposit</th>
                                    <td>: ---</td>
                                    {% else %}
                                    <td style="text-align: right; width: 40%;">Advance Deposited</th>
                                    <td style="color: green;">: {{ data.currency }}
                                        {{data.shareholder_deposit.0.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:data.shareholder_deposit.0.deposit_amount_sum|number_product:-1|intcomma_bd}}
                                    </td>
                                {% endif %}
                            </tr>
                        </tbody>
                    </table>

            <div style="width: 50%; text-align: center; align-items: center;">
                <img src="{{data.avatar}}" class="r_corners" alt="shareholder"
                    style="width: 87px;">
            </div>
        </div>
    </div>

<h3 class="card" style="color: seagreen;">{{data.heading}}</h3>

<div class="card">
    <table style="width: 100%;">
        <thead>
            <tr>
                <th>#</th>
                <th style="width: 13%;">Date</th>
                <th>Mode of Deposit</th>
                <th>Amount ({{data.currency}})</th>
                <th>Remarks</th>

            </tr>
        </thead>
        {% for item in data.shareholder_deposit %}
        <tbody>
            <tr style=" 
            {% if forloop.counter|mod:2 == 0 %}
            background-color: #F0FFF0;
            {% endif %}
                ">
                <td style="text-align: center;">{{forloop.counter}}</td>
                <td>{{item.dateOfTransaction|date:"d b Y"|title}}</td>
                <td style="text-align: center;">{{item.modeOfTransaction}}</td>
                <td style="text-align: right;">{{item.amount|intcomma_bd}}</td>
                {% if item.remarks == None %}
                <td>
                    ----
                </td>
                {% else %}
                <td>
                    {{item.remarks}}
                </td>
                {% endif %}
            </tr>
            {% endfor %}
        </tbody>
    </table>
    </div>
</div>

    {% endblock %}