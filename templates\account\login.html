{% extends "account/base_entrance.html" %}
{% load i18n %}
{% load allauth account %}
{% block head_title %}
    {% trans "Sign In" %}
{% endblock head_title %}


  


{% block content %}

    {% element h1 %}
        {% trans "Sign In" %}
    {% endelement %}
    {% if not SOCIALACCOUNT_ONLY %}
        {% setvar link %}
            <a href="{{ signup_url }}">
            {% endsetvar %}
            {% setvar end_link %}
            </a>
        {% endsetvar %}
        {% element p %}
            {% blocktranslate %}If you have not created an account yet, then please {{ link }}sign up{{ end_link }} first.{% endblocktranslate %}
        {% endelement %}
        {% url 'account_login' as login_url %}
        {% element form form=form method="post" action=login_url tags="entrance,login" %}
            {% slot body %}
                {% csrf_token %}
                {% element fields form=form unlabeled=True %}
                {% endelement %}
                {{ redirect_field }}
            {% endslot %}
            {% slot actions %}
                {% element button type="submit" tags="prominent,login" %}
                    {% trans "Sign In" %}
                {% endelement %}
            {% endslot %}
        {% endelement %}
    {% endif %}
    {% if LOGIN_BY_CODE_ENABLED or PASSKEY_LOGIN_ENABLED %}
        {% element hr %}
        {% endelement %}
        {% element button_group vertical=True %}
            {% if PASSKEY_LOGIN_ENABLED %}
                {% element button type="submit" form="mfa_login" id="passkey_login" tags="prominent,login,outline,primary" %}
                    {% trans "Sign in with a passkey" %}
                {% endelement %}
            {% endif %}
            {% if LOGIN_BY_CODE_ENABLED %}
                {% element button href=request_login_code_url tags="prominent,login,outline,primary" %}
                    {% trans "Mail me a sign-in code" %}
                {% endelement %}
            {% endif %}
        {% endelement %}
    {% endif %}
    {% if SOCIALACCOUNT_ENABLED %}
        {% include "socialaccount/snippets/login.html" with page_layout="entrance" %}
    {% endif %}
{% endblock content %}

{% block extra_body %}
    {{ block.super }}
    {% if PASSKEY_LOGIN_ENABLED %}
        {% include "mfa/webauthn/snippets/login_script.html" with button_id="passkey_login" %}
    {% endif %}
{% endblock %}
