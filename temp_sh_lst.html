{% extends "accounts/reports/reportbase.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load mahimsoft_tags %}
{% load mathfilters %}
{% block title %}<title>Shareholder List {% now 'd-M-Y' %}</title>{% endblock title %}
{% get_static_prefix as static_prefix %}
{% get_media_prefix as media_prefix %}
 {% block link %}
 <meta charset="UTF-8">
 {% endblock link %}

{% block style %}
<style>
  @font-face {
    font-family: "Kalpurush";
    src: url("{{ font_paths.Kalpurush }}");
    font-weight: normal;
    font-style: normal;
  }
  
  @font-face {
    font-family: "SolaimanLipi";
    src: url("{{ font_paths.SolaimanLipi }}");
    font-weight: normal;
    font-style: normal;
  }
  
  @font-face {
    font-family: "BanglaFontNikosh";
    src: url("{{ font_paths.Nikosh }}");
    font-weight: normal;
    font-style: normal;
  }

  /* Set default font for the entire document */
  body, table, tr, td, th, p, div, span, h1, h2, h3, h4, h5, h6 {
    font-family: <PERSON><PERSON><PERSON><PERSON>, SolaimanLipi, BanglaFontNikosh, sans-serif;
  }

  table, th, td {
    border: 1px solid rgb(205, 210, 212);
    border-collapse: collapse;
    padding: 4px 5px 0px 5px;
  }
  
  tr {
    page-break-inside: avoid;
  }
  
  th {
    background-color:#D9D9D9;
    vertical-align: middle;
    margin: 4px 0px 0px 0px;
  }

  td {
    vertical-align: middle;
    margin: 5px 0px 0px 0px;
  }
  
  tr:nth-child(even) {
    background-color:rgb(208, 208, 202);
  }

  .bangla-text {
    font-family: Kalpurush, SolaimanLipi, BanglaFontNikosh, sans-serif !important;
    font-size: 14px !important;
  }

  @page {
    size: a4 portrait;
    margin: 5mm 10mm 5mm 10mm;
    counter-increment: page;
    margin-bottom: 8mm;
    @frame footer {
      -pdf-frame-content: footerContent;
      bottom: 0mm;
      right: 10mm;
      height: 7mm;
    }
  }
</style>
{% endblock style %}
{% block contents %}

<p style="margin:0; text-align: right; font-size: 12px;">Date: {% now 'd-M-Y' %}</p>
<h1 style="text-align: center; color: #7a7b7d; font-size: 20px; margin: 0px 0px 0px 0px;;">{{data.heading}}</h1>
<div class="table table-active my-1">
  <table repeat="1">
    <thead>
    <tr style="height: 32px;">
      <th style="width: 3%; ">#</th>
      <th style="width: 7%; ">Photo</th>
      <th style="width: 20%;">Name</th>
      <th style="width: 10%;">NID</th>
      <th style="width: 22%;">Mobile & Email</th>
      <th style="width: 10%;">Nos. of Flat</th>
      <th style="width: 14%;">Deposited Amnt</th>
      <th style="width: 14%;">Rest Amnt to Pay</th>
    </tr>
    </thead>
    <tbody>
    {% for dt in data.shareholder %}
    <tr style="page-break-inside: avoid; height: 60 px; 
    {% if forloop.counter|mod:2 == 0 %}
      background-color: #F0FFF0;
    {% endif %}">
      <td style="text-align: center;">{{forloop.counter}}</td>
      <td style="text-align: center; padding: auto;">
        <img src="{{ dt.avatar }}" 
        style="border-radius: 15%; width: 50px; height: 50px;" alt="Avatar">
      </td>
      <td style="font-family: Kalpurush, SolaimanLipi, BanglaFontNikosh; font-size: 14px;">{{dt.shareholderName}}</td>

      {% if dt.nid == None %}
      <td style="text-align: center;">---</td>
      {% else %}
      <td style="text-align: center;">{{dt.nid|default_if_none:"---"}}</td>
      {% endif %}
      <td style="padding: 0px 0px 0px 5px;">
      <img style="width: 14px; height: 14px;"
                src="{% static 'images/ic_phone_24px.svg' %}" alt="telephone">&nbsp; {{dt.mobile|default_if_none:"---"}}
      <br>
      <img style="width: 14px; height: 14px;"
                src="{% static 'images/email.svg' %}" alt="telephone">&nbsp; &nbsp; {{dt.email|default_if_none:"---"}}
      </td>
      <td style="text-align: center;">{{dt.numberOfFlat|floatformat:"1"}}</td>
      <td style="text-align: right; font-family: Kalpurush, SolaimanLipi, BanglaFontNikosh; font-size: 14px;">{{dt.sum_amount|default_if_none:0|intcomma_bd}}/-</td>
      {% if dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount > 0 %}
      <td style="text-align: right; color:red; font-family: Kalpurush, SolaimanLipi, BanglaFontNikosh; font-size: 14px;">
        {{dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount|number_product:-1|intcomma_bd}}/-
      </td>
      {% elif dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount == 0 %}
      <td style="text-align: right;">
        0/-
      </td>
      {% else %}
      <td style="text-align: right; color:green; font-family: Kalpurush, SolaimanLipi, BanglaFontNikosh; font-size: 14px;">
        {{dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount|intcomma_bd}}/-
      </td>
      {% endif %}
    </tr>
    {% endfor %}
    </tbody>
  </table>
</div>
<p style="color: firebrick; font-size: 12px;">NB: Rest amount to pay calculated as per targeted amount per share as Taka:
  {{data.no_of_flat_per_share|number_product:data.targeted_amount_per_flat|intcomma_bd}}/-</p>
  <div id="footerContent" style="text-align: right; font-size: 12px; color: grey; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;  ">&copy; {{data.copy_right}} Page No : <pdf:pagenumber /> of <pdf:pagecount /></div>
{% endblock %}
