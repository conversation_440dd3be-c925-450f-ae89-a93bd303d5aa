
#!Django Backup and Restore Data

"""
#? To Empty database : python manage.py flush

#? To Backup Data:
python manage.py dumpdata --natural-foreign --natural-primary -e contenttypes -e auth.Permission --indent 4 > data_1.json


python manage.py dumpdata --exclude=contenttypes --indent 4 > DreamConsProject.json

#? To Restore Data:
python manage.py loaddata DreamConsProject.json


#! Notes:
Applies to MYSQLite
PostGres - Uses for example pg_dump

//all
py manage.py dumpdata 
//app
py manage.py dumpdata blog 
//table
py manage.py dumpdata blog.post 
//auth table
py manage.py dumpdata auth.user 
//exclude
py manage.py dumpdata --exclude blog.post 
//indent
py manage.py dumpdata --indent 2 
//format
pip install pyyaml
py manage.py dumpdata --indent 2 --format yaml 

//Empty Database
py manage.py flush

//Load data
py manage.py loaddata 

========================================================
manage.py dumpdata --natural-foreign --natural-primary -e contenttypes -e auth.Permission --indent 4
===========
"""