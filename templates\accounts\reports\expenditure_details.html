{% extends "accounts/reports/reportbase.html" %}
{% load static %}
{% load mahimsoft_tags %}
{% load bangla_text %}
{% block title %}<title>{{data.company_abr}}</title>{% endblock title %}

{% block link %}
<link href="{% static 'page_css_portrait.css' %}" rel="stylesheet">
{% endblock link %}

{% block style %}
<style>
  @page {
    @bottom-right {
        content: '\00A9 {{data.company_name}}, Page No : ' counter(page) ' of ' counter(pages);

    }
}
</style>
{% endblock style %}
{% block contents %}
<div class="container" style="width: 90%; margin: auto;">
<p class="report_date" style="margin: 0px 0px 10px 0px;">Date: {% now 'd-M-Y' %}</p>
  <div class="flex_content_between">
    <h3 id = "heading">Extenditure Details</h3>
    <h4 style="color: #EE6A50; margin: 0px 0px 10px 0px;">Total Expenditure: {{ data.currency }} {{data.grand_total.amount__sum|intcomma_bd}}</h4>
  </div>
  
  {% regroup data.expenditure by work_phase_description as work_phase %}
  {% for phase in work_phase %}
    <div class="pagebreak_after">
      <div style="border: 1px solid #ccc; border-radius: 6px; margin-bottom: 10px;">
        <h3 style="font-size: 16px; font-weight: bold; padding: 10px; border-radius-top: 7px; background-color:rgb(196, 206, 194); margin:0px 0px 0px 0px; color: #f14242;">
        {{ phase.grouper }}:
      </h3>
    <table style="width: 100%; margin: auto; margin-bottom: 0px;">
      <tr>
        <th style="background-color: inherit; text-align:right; width:20%;">Start Date:</th>
        <td style="width:30%;">{{phase.list.0.work_phase_from_date}}</td>
        <th style="background-color: inherit; text-align:right; width:20%;">End Date:</th>
        <td style="text-align: left;">{{phase.list.0.work_phase_to_date}}</td>
      </tr>
      <tr>
        <th style="background-color: inherit; text-align:right; width:20%;">Estimated Cost:</th>
        <td style="width:30%;">{{ data.currency }} {{phase.list.0.estimated_cost|intcomma_bd}}</td>
        <th style="background-color: inherit; text-align:right; width:20%;">Total Spent:</th>
        <td style="text-align: left;">{{ data.currency }} {{phase.list.0.work_phase_sum|intcomma_bd}}</td>
      </tr>
    </table>
</div>
{% regroup phase.list by work_sector as sectors %}
  {% for sector in sectors %}
      <div class="r_corners_5px" style="display: flex; justify-content: space-between; align-items: middle;">
        <h4 style="font-weight: bold; padding: 3px; margin:3px 0px 0px 0px; color: darkolivegreen;">
          <img style="width: 14px; height: 14px;"
          src="{% static 'images/back.svg' %}" alt="telephone">&nbsp;{{ sector.grouper }}</h4>
        <p style="margin:4px 0px 0px 0px; padding: 3px; color: darkolivegreen;">Total Spent: {{ data.currency }} {{sector.list.0.worksector_sum|intcomma_bd}}</p>
      </div>
      <div>
      {% regroup sector.list by item_name as item %}
      {% for row in item %}
      <div style="margin: 5px 0px 0px 0px;">
        <table style="width: 100%; margin-top: 5px 0px 0px 0px;">
          <thead>
          <tr style="height: 24px;">
              <td colspan="7" style="padding: 0px; margin: 0px;">
                  <div style="display: flex; justify-content: space-between; align-items: middle; padding: 5px; background-color: #ffffff; border: #ffffff;">
                      <h3 style="padding: 0px; margin: 0px;"><span style="margin: 0; padding: 0px 0px 0px 0px;">&#128309;</span> {{ row.grouper }}</h3>
                      <p style="padding: 0px; margin: 0px;">Total Spent: {{ data.currency }} {{row.list.0.item_sum_amount|intcomma_bd}}</p>
                  </div>
              </td>
          </tr>
          <tr style="height: 24px; vertical-align: middle;">
            <th style="text-align: center; width: 5%;">#</th>
            <th style="text-align: center; width: 15%;">Date</th>
            <th style="text-align: center; width: 30%;">Description</th>
            <th style="text-align: center; width: 10%;">Quantity</th>
            <th style="text-align: center; width: 10%;">Unit</th>
            <th style="text-align: center; width: 15%;">Voucher No.</th>
            <th style="text-align: center; width: 15%;">Amount ({{ data.currency }})</th>
          </tr>
              </thead>
              <tbody>
          {% for dt in row.list %}
          <tr>
            <td>{{forloop.counter}}</td>
            <td>{{dt.dateOfTransaction}}</td>
            <td>{{dt.description|bangla_text_span|safe}}</td>
            <td>{{dt.quantity|intcomma_bd}}</td>
            <td>{{dt.units}}</td>
            <td>{{dt.voucherNo}}</td>
            <td>{{dt.amount|intcomma_bd}}/-</td>
          </tr>
          {% endfor %}
                <tr style="height: 24px;">
                  <th colspan="3">Total Quantity =</th>
                  <th>{{row.list.0.item_sum_quantity|intcomma_bd}}</th>
                  <th>{{row.list.0.units}}</th>
                  <th>Total Amount =</th>
                  <th>{{row.list.0.item_sum_amount|intcomma_bd}}/-</th>
                </tr>
              </tbody>
            </table>
          </div>
            {% endfor %}
          </div>
          {% endfor %}
        </div>
    {% endfor %}
    </div>
  {% endblock %}