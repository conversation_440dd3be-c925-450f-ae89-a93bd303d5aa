{"cells": [{"cell_type": "code", "execution_count": 9, "id": "2ac7aadf-2641-4fd3-8058-6aeb6be416a2", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'CMySQLConnection' object has no attribute 'save'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[9], line 26\u001b[0m\n\u001b[0;32m     23\u001b[0m val \u001b[38;5;241m=\u001b[39m myresult\n\u001b[0;32m     25\u001b[0m c\u001b[38;5;241m.\u001b[39mexecutemany(sql, val)\n\u001b[1;32m---> 26\u001b[0m \u001b[43mdb_samprity\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msave\u001b[49m()\n\u001b[0;32m     27\u001b[0m conn\u001b[38;5;241m.\u001b[39mcommit\n\u001b[0;32m     28\u001b[0m dt\u001b[38;5;241m.\u001b[39mexecute(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mSelect * from Accounts_itemcode\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[1;31mAttributeError\u001b[0m: 'CMySQLConnection' object has no attribute 'save'"]}], "source": ["import mysql.connector\n", "import sqlite3\n", "\n", "#! MySql ====================================\n", "db_samprity = mysql.connector.connect(\n", "    host=\"localhost\", user=\"root\", password=\"root12\", database=\"samprity_tower\"\n", ")\n", "\n", "item_code = db_samprity.cursor()\n", "item_code.execute(\"SELECT Sector_Name FROM item_code\")\n", "\n", "myresult = item_code.fetchall()\n", "\n", "\n", "\n", "#! Sqlite3 ==================================\n", "conn = sqlite3.connect(\"../ConstructionProject.sqlite3\")\n", "c = conn.cursor()\n", "dt = conn.cursor()\n", "\n", "# region <Database Creation ===============================>\n", "sql = \"INSERT INTO Accounts_itemcode (workSector) VALUES (?)\"\n", "val = myresult\n", "\n", "c.<PERSON>many(sql, val)\n", "conn.commit\n", "dt.execute(\"Select * from Accounts_itemcode\")\n", "dt_recset = dt.fetchall()\n", "\n", "conn.commit\n", "conn.close\n", "db_samprity.commit\n", "db_samprity.close\n", "# endregion\n", "\n", "print(dt_recset)\n"]}, {"cell_type": "code", "execution_count": null, "id": "76f8d889-1e4b-4a8f-9556-7a9e7d092359", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.7"}}, "nbformat": 4, "nbformat_minor": 5}