import os
from PIL import Image
import random
# from . valiators import validate_file_extension
from django.db import models
from django.urls import reverse
from datetime import date, datetime, time, timedelta
from django.db.models import F, Q, Sum
from django.templatetags.static import static
from . import choices
from uuid import uuid4

# from . import views
from .templatetags.mahimsoft_tags import intcomma_bd


def resize_and_convert(image_path, new_size=(400, 400)):
    try:
        img = Image.open(image_path).convert("RGBA")
        width, height = img.size
        new_image_width, new_image_height = new_size
        output_size = new_size
 
        if width > height:
            new_image_height = int(new_image_width*(height/width))
            output_size = (new_image_width,new_image_height)
        else:
            new_image_width = int(new_image_height*(width/height))
            output_size = (new_image_width,new_image_height)

    except FileNotFoundError:
        print(f"Error: Image not found at {image_path}")
        return
    except Exception as e:
        print(f"Error opening image: {e}")
        return

    # Create a new image with the specified background color
    background = Image.new('RGBA', new_size, (0,0,0,0))
    # background = Image.new('RGBA', new_size, (0,0,255) + (255,))

    # Resize the input image
    resized_img = img.resize(output_size, Image.Resampling.LANCZOS)

    # Paste the resized image onto the background
 
    background.paste(resized_img, (int((new_size[0]-new_image_width)/2), 
                                   int((new_size[1]-new_image_height)/2)), resized_img)

    # background.paste(resized_img, (0, 0), resized_img)

    # Save as WebP
    try:
        background.save(image_path, "webp", lossless=True)
        # print(f"Image saved to {image_path}")
    except Exception as e:
        print(f"Error saving image: {e}")

def path_and_rename(instance, filename):
    upload_to = "ContractorsImage"
    # ext = filename.split(".")[-1]
    # get filename
    if instance.pk:
        filename = "{}_{}.{}".format(instance.pk, uuid4().hex, "webp")
    else:
        # set filename as random string
        filename = "{}.{}".format(uuid4().hex, "webp")
    # return the whole path to the file
    return os.path.join(upload_to, filename)


def path_and_rename_(instance, filename):
    upload_to = "ShareholdersImage"
    # ext = filename.split(".")[-1]
    # get filename
    if instance.pk:
        filename = "{}_{}.{}".format(instance.pk, uuid4().hex, "webp")
    else:
        # set filename as random string
        filename = "{}.{}".format(uuid4().hex, "webp")
    # return the whole path to the file
    return os.path.join(upload_to, filename)


class ProjectInfo(models.Model):
    name = models.CharField(max_length=100, blank=False, null=False)
    address = models.CharField(max_length=100, blank=False, null=False)
    longitude = models.CharField(max_length=200, blank=True, null=True)
    latitude = models.CharField(max_length=200, blank=True, null=True)
    currency = models.CharField(max_length=200, blank=True, null=True)
    estimatedCost = models.DecimalField(max_digits=15, decimal_places=2)
    logo = models.ImageField(
        upload_to=path_and_rename,
        max_length=255,
        # validators=[validate_file_extension],
        # default="ContractorsImage/default.png",
        null=True,
        blank=True,
    )

    def save(self):
        super().save()
        try:
            resize_and_convert(self.logo.path)
        except:
            pass


class WorkPhase(models.Model):
    description     = models.CharField(max_length=150, blank=True, null=True)
    fromDate        = models.DateField(default=datetime.now)
    toDate          = models.DateField(default=datetime.now)
    estimatedCost   = models.DecimalField(max_digits=15, decimal_places=2)

    def __str__(self):
        return f"""{self.description}, 
Estimated Date range : {self.fromDate.strftime('%d %b %Y')} to {self.toDate.strftime('%d %b %Y')},
Estimated Cost : {self.estimatedCost}
"""
    
    @property
    def work_phase_description(self):
        return self.description


class CurrentWorkPhase(models.Model):
    workPhase = models.ForeignKey(
        WorkPhase,
        blank=False,
        null=False,
        related_name="current_work_phase",
        on_delete=models.CASCADE,
    )
    dateOfChange = models.DateField(default=datetime.now)

    def __str__(self):
        return f"{self.workPhase.description}, Date: {self.dateOfChange.strftime('%d %b %Y')}"
    
    @property
    def work_phase_description(self):
        return self.workPhase.description

class ItemCode(models.Model):
    workSector = models.CharField(max_length=100, blank=False, null=False)

    class Meta:
        ordering = ("workSector",)

    def __str__(self):
        return self.workSector


class Item(models.Model):
    itemName = models.CharField(max_length=100, blank=False, null=False)
    unit = models.CharField(max_length=20, blank=False, null=False)
    ItemCode = models.ForeignKey(
        ItemCode,
        blank=False,
        null=False,
        on_delete=models.CASCADE,
        related_name="ItemCode",
    )

    class Meta:
        ordering = (
            "ItemCode",
            "itemName",
        )

    def __str__(self):
        return f"{self.itemName} [Unit: {self.unit}]"


class ContractorType(models.Model):
    contractorType = models.CharField(
        max_length=100,
        blank=False,
        null=False,
    )

    def __str__(self):
        return self.contractorType


class Contractor(models.Model):
    dateOfJoin = models.DateField(default=datetime.now)
    contractor = models.CharField(max_length=100, blank=False, null=False)
    contractorType = models.ForeignKey(
        ContractorType,
        blank=False,
        null=False,
        related_name="Contr_Type",
        on_delete=models.CASCADE,
    )
    address = models.TextField(blank=True, null=True)
    NID = models.CharField(max_length=30, blank=True, null=True)
    TIN = models.CharField(max_length=50, blank=True, null=True)
    TelephoneNo = models.CharField(max_length=50, blank=True, null=True)
    Mobile = models.CharField(max_length=20, blank=True, null=True)
    Email = models.CharField(max_length=100, blank=True, null=True)
    image = models.ImageField(
        upload_to=path_and_rename,
        max_length=255,
        # validators=[validate_file_extension],
        # default="ContractorsImage/default.png",
        null=True,
        blank=True,
    )
    IsArchive = models.BooleanField(default=False)

    class Meta:
        ordering = ("contractor",)

    def save(self):
        super().save()
        try:
            resize_and_convert(self.image.path, new_size=(400, 400))
        except:
            pass
        # resize_and_convert(self.image.path)


    def get_absolute_url(self):
        # return reverse("Accounts:contractor", args=[str(self.id)])
        return f"/contractor/{str(self.id)}"
    
    @property
    def avatar(self):
        try:
            avatar = self.image.url
        except:
            avatar = static('images/avatar_default.svg')
        return avatar

    def __str__(self):
        return self.contractor
    


class ContractorBillSubmission(models.Model):
    dateOfBillSubmission = models.DateField(default=datetime.now)
    description = models.CharField(max_length=200, blank=True, null=True, default="")
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    remarks = models.CharField(max_length=200, blank=True, null=True)
    contractor = models.ForeignKey(
        Contractor,
        blank=False,
        null=False,
        related_name="contractor_bill",
        on_delete=models.CASCADE,
    )

    def __str__(self):
        return f"Bill No.{self.pk}, {self.description}, Bill Amount: {intcomma_bd(self.amount)}, Date: {self.dateOfBillSubmission.strftime('%d %b %Y')}"
    
    @property
    def contractor_name(self):
        return self.contractor

class ContractorBillPayment(models.Model):
    dateOfTransaction = models.DateField(default=datetime.now)
    voucherNo = models.CharField(max_length=100, blank=False, null=False, default="")
    modeOfTransaction = models.CharField(
        max_length=4,
        blank=False,
        null=False,
        choices=choices.modeOfTransaction,
        default="",
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    # labor_fooding = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    remarks = models.CharField(max_length=200, blank=True, null=True)
    bill = models.ForeignKey(
        ContractorBillSubmission,
        blank=False,
        null=False,
        related_name="bill_submission",
        on_delete=models.CASCADE,
    )
    item = models.ForeignKey(
        Item,
        blank=False,
        null=False,
        related_name="contractor_bill_item",
        default=1,
        on_delete=models.CASCADE,
    )
    unit = models.CharField(max_length=100, blank=False, null=False, default="")
    rate = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    work_phase = models.ForeignKey(
        WorkPhase,
        blank=False,
        null=False,
        related_name="contractor_bill_work_phase",
        on_delete=models.CASCADE,
        default=1
    )
    tid = models.CharField(max_length=10, blank=False, null=False, default="")

    def __str__(self):
        return f"{self.bill}, Paid Amount: {intcomma_bd(self.amount)}, Date: {self.dateOfTransaction.strftime('%d-%b-%Y')}"
   
    @property
    def contractor_name(self):
        return self.bill.contractor
    
    @property
    def bill_amount(self):
        return self.bill.amount

class CreditPurchase(models.Model):
    dateOfPurchase = models.DateField(default=datetime.now)
    seller = models.CharField(max_length=150, blank=False, null=False, default="")
    address = models.CharField(max_length=250, blank=False, null=False, default="")
    description = models.CharField(max_length=250, blank=False, null=False, default="")
    mobile = models.CharField(max_length=20, blank=True, null=True)
    email = models.CharField(max_length=100, blank=True, null=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    remarks = models.CharField(max_length=250, blank=False, null=False, default="")

    class Meta:
        ordering = ("-dateOfPurchase",)

    def get_absolute_url(self):
        return f"/credit_purchase_update/{str(self.id)}"  #! Done Pending ==========

    def __str__(self):
        return f"{self.seller}, {self.description}, Date: {(self.dateOfPurchase.strftime('%d %b %Y'))}, Amount: {intcomma_bd(self.amount)}"
    
    


class CreditPurchasePayment(models.Model):
    dateOfTransaction = models.DateField(default=datetime.now)
    seller = models.ForeignKey(
        CreditPurchase,
        blank=False,
        null=False,
        related_name="Seller",
        on_delete=models.CASCADE,
    )
    item = models.ForeignKey(
        Item,
        blank=False,
        null=False,
        related_name="Item",
        on_delete=models.CASCADE,
    )
    description = models.CharField(max_length=250, blank=False, null=False, default="")
    unit = models.CharField(max_length=100, blank=False, null=False, default="")
    rate = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    voucherNo = models.CharField(max_length=100, blank=False, null=False, default="")
    modeOfTransaction = models.CharField(
        max_length=4,
        blank=False,
        null=False,
        choices=choices.modeOfTransaction,
        default="",
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    remarks = models.CharField(max_length=250, blank=False, null=False, default="")
    work_phase = models.ForeignKey(
        WorkPhase,
        blank=False,
        null=False,
        related_name="credit_purchase_payment_work_phase",
        on_delete=models.CASCADE,
        default=1
    )
    tid = models.CharField(max_length=10, blank=False, null=False, default="")

    class Meta:
        ordering = ("-dateOfTransaction",)

    def get_absolute_url(self):
        return f"/credit_purchase_payment_update/{str(self.id)}"  #! Done Pending ==========
    
    @property
    def bill_amount(self):
        return self.seller.amount
    
    @property
    def seller_name(self):
        return self.seller.seller
    
    def __str__(self):
        return f"{self.seller}, Date: {(self.dateOfTransaction).strftime('%d-%b-%Y')}, Amount: {intcomma_bd(self.amount)}"


class Expenditure(models.Model):
    dateOfTransaction = models.DateField(default=datetime.now)
    item = models.ForeignKey(
        Item,
        blank=False,
        null=False,
        related_name="Expenditure_Item",
        on_delete=models.CASCADE,
    )
    description = models.CharField(max_length=200, blank=True, null=True, default="")
    unit = models.CharField(max_length=100, blank=False, null=False, default="")
    rate = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    voucherNo = models.CharField(max_length=100, blank=False, null=False, default="")
    modeOfTransaction = models.CharField(
        max_length=4,
        blank=False,
        null=False,
        choices=choices.modeOfTransaction,
        default="",
    )
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    contractor_bill_payment = models.ForeignKey(
        ContractorBillPayment,
        blank=True,
        null=True,
        related_name="contractor_bill",
        on_delete=models.CASCADE,
    )
    credit_purchase_payment = models.ForeignKey(
        CreditPurchasePayment,
        blank=True,
        null=True,
        related_name="CreditPurchasePayment",
        on_delete=models.CASCADE,
    )
    remarks = models.CharField(max_length=200, blank=True, null=True)
    work_phase = models.ForeignKey(
        WorkPhase,
        blank=False,
        null=False,
        related_name="expenditure_work_phase",
        on_delete=models.CASCADE,
        default=1
    )
    tid = models.CharField(max_length=10, blank=False, null=False, default="")

    class Meta:
        ordering = ("-dateOfTransaction",)

    def get_absolute_url(self):
        return f"/expenditure_update/{str(self.id)}"

    def __str__(self):
        return f"Date: {(self.dateOfTransaction).strftime('%d-%b-%Y')}, {self.description}, Sector: {self.item}, Quantity:{self.quantity} {self.unit}, Amount: {intcomma_bd(self.amount)}"


class Shareholder(models.Model):
    dateOfJoin = models.DateField(default=datetime.now)
    shareholderName = models.CharField("Name", max_length=100)
    address = models.CharField("Address", max_length=300, blank=True, null=True)
    email = models.EmailField("Email", max_length=300, blank=True, null=True)
    mobile = models.CharField("Mobile No.", max_length=20, blank=True, null=True)
    nid = models.CharField("NID No.", max_length=20, blank=True, null=True)
    numberOfFlat = models.DecimalField(
        "Numbers of Share", max_digits=4, decimal_places=2
    )
    gender = models.CharField(
        max_length=1,
        blank=False,
        null=False,
        choices=choices.Gender,
        default="M",
    )
    image = models.ImageField(
        upload_to=path_and_rename_,
        max_length=255,
        # validators=[validate_file_extension],
        # default="ShareholdersImage/default.png",
        null=True,
        blank=True,
    )

    class Meta:
        ordering = ("id",)

    def save(self):
        super().save()
        try:
            resize_and_convert(self.image.path, new_size=(400, 400))
        except:
            pass

    def get_absolute_url(self):
        return f"/shareholder/{str(self.id)}"

    @property
    def avatar(self):
        try:
            avatar = self.image.url
        except:
            if self.gender == "F":
                random_number = random.randint(1, 23)
                formatted_number = f"{random_number:02d}"  # Ensures two-digit format
                avatar = static(f'images/avatar/F{formatted_number}.jpg')
            else:
                random_number = random.randint(1, 34)
                formatted_number = f"{random_number:02d}"  # Ensures two-digit format
                avatar = static(f'images/avatar/M{formatted_number}.jpg')
        return avatar

    def __str__(self):
        return self.shareholderName
    
    @property
    def details(self):
        return f'Name: {self.shareholderName}, Address: {self.address}'


class ShareholderDeposit(models.Model):
    dateOfTransaction = models.DateField(default=datetime.now)
    modeOfTransaction = models.CharField(
        max_length=4,
        blank=False,
        null=False,
        choices=choices.modeOfTransaction,
        default="",
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    remarks = models.CharField(max_length=200, blank=True, null=True)
    shareholder = models.ForeignKey(
        Shareholder,
        blank=False,
        null=False,
        related_name="shareholder_deposit",
        on_delete=models.CASCADE,
    )
    work_phase = models.ForeignKey(
        WorkPhase,
        blank=False,
        null=False,
        related_name="shareholder_deposit_work_phase",
        on_delete=models.CASCADE,
        default=1
    )
    tid = models.CharField(max_length=10, blank=False, null=False, default="")

    class Meta:
        ordering = ("-dateOfTransaction",)

    def __str__(self):
        return f"{self.shareholder}: Amount:{intcomma_bd(self.amount)}, Date:{self.dateOfTransaction.strftime('%d %b %Y')}"

    def get_absolute_url(self):
        return f"/get_shareholder_deposit_info/{str(self.shareholder)}"


class OfficeItemCode(models.Model):
    Sector = models.CharField(max_length=100, blank=False, null=False)

    class Meta:
        ordering = ("Sector",)

    def __str__(self):
        return self.Sector


class OfficeItem(models.Model):
    itemName = models.CharField(max_length=100, blank=False, null=False)
    unit = models.CharField(max_length=20, blank=False, null=False)
    officeitem = models.ForeignKey(
        OfficeItemCode,
        blank=False,
        null=False,
        on_delete=models.CASCADE,
        related_name="officeitem",
    )

    class Meta:
        ordering = ("itemName",)

    def __str__(self):
        return self.itemName


class OfficeExpenditure(models.Model):
    dateOfTransaction = models.DateField(default=datetime.now)
    item = models.ForeignKey(
        Item,
        blank=False,
        null=False,
        related_name="office_expenditure",
        on_delete=models.CASCADE,
    )
    description = models.CharField(max_length=200, blank=True, null=True)
    unit = models.CharField(max_length=100, blank=False, null=False, default="")
    quantity = models.DecimalField(max_digits=10, decimal_places=2)
    voucherNo = models.CharField(max_length=100, blank=False, null=False, default="")
    modeOfTransaction = models.CharField(
        max_length=4,
        blank=False,
        null=False,
        choices=choices.modeOfTransaction,
        default="",
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    remarks = models.CharField(max_length=200, blank=True, null=True)
    work_phase = models.ForeignKey(
        WorkPhase,
        blank=False,
        null=False,
        related_name="office_expenditure_work_phase",
        on_delete=models.CASCADE,
        default=1
    )
    tid = models.CharField(max_length=10, blank=False, null=False, default="")

    class Meta:
        ordering = ("-dateOfTransaction",)

    def __str__(self):
        return f"{self.item}, Quantity:{self.quantity}{self.unit}, Amount:{intcomma_bd(self.amount)}"


class UserLoggedinRecord(models.Model):
    visitorIP = models.CharField("visitorIP", max_length=200, blank=True, null=True)
    # ip_address = models.CharField("visitorIP",max_length=200, blank=True, null=True)
    country = models.CharField(max_length=200, blank=True, null=True)
    country_code = models.CharField(max_length=200, blank=True, null=True)
    continent = models.CharField(max_length=200, blank=True, null=True)
    continent_code = models.CharField(max_length=200, blank=True, null=True)
    city = models.CharField(max_length=200, blank=True, null=True)
    county = models.CharField(max_length=200, blank=True, null=True)
    region = models.CharField(max_length=200, blank=True, null=True)
    region_code = models.CharField(max_length=200, blank=True, null=True)
    timezone = models.CharField(max_length=200, blank=True, null=True)
    owner = models.CharField(max_length=200, blank=True, null=True)
    longitude = models.CharField(max_length=200, blank=True, null=True)
    latitude = models.CharField(max_length=200, blank=True, null=True)
    currency = models.CharField(max_length=200, blank=True, null=True)
    languages = models.CharField(max_length=200, blank=True, null=True)
    visitCount = models.IntegerField("Visitor Count", default=0, blank=True, null=True)
    user = models.CharField(max_length=200, default="")
    visitDateTime = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.visitorIP


class UserLoggedinFailed(models.Model):
    visitorIP = models.CharField("visitorIP", max_length=200, blank=True, null=True)
    # ip_address = models.CharField("visitorIP",max_length=200, blank=True, null=True)
    country = models.CharField(max_length=200, blank=True, null=True)
    country_code = models.CharField(max_length=200, blank=True, null=True)
    continent = models.CharField(max_length=200, blank=True, null=True)
    continent_code = models.CharField(max_length=200, blank=True, null=True)
    city = models.CharField(max_length=200, blank=True, null=True)
    county = models.CharField(max_length=200, blank=True, null=True)
    region = models.CharField(max_length=200, blank=True, null=True)
    region_code = models.CharField(max_length=200, blank=True, null=True)
    timezone = models.CharField(max_length=200, blank=True, null=True)
    owner = models.CharField(max_length=200, blank=True, null=True)
    longitude = models.CharField(max_length=200, blank=True, null=True)
    latitude = models.CharField(max_length=200, blank=True, null=True)
    currency = models.CharField(max_length=200, blank=True, null=True)
    languages = models.CharField(max_length=200, blank=True, null=True)
    visitCount = models.IntegerField("Visitor Count", default=0, blank=True, null=True)
    user = models.CharField(max_length=200, default="")
    password = models.CharField(max_length=200, default="")
    visitDateTime = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.visitorIP


class TargetedAmount(models.Model):
    inputDate = models.DateField(default=datetime.now)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    deadline = models.DateField(default=datetime.now)

    class Meta:
        ordering = ("-inputDate","-id")

    def __str__(self):
        return f"Targeted Amount: {intcomma_bd(self.amount)} | Start Date: {self.inputDate.strftime('%d %b %Y')}, Deadline: {self.deadline.strftime('%d %b %Y')}"


class IncomeSector(models.Model):
    incomeSector = models.CharField(max_length=200, default="")

    def __str__(self):
        return self.incomeSector


class IncomeItem(models.Model):
    incomeSector = models.ForeignKey(
        IncomeSector,
        blank=False,
        null=False,
        related_name="Income_Sector",
        on_delete=models.CASCADE,
    )
    itemName = models.CharField(max_length=200, default="")
    unit = models.CharField(max_length=200, default="")

    def __str__(self):
        return self.itemName


class Income(models.Model):
    dateOfTransaction = models.DateField(default=datetime.now)
    incomeItem = models.ForeignKey(
        IncomeItem,
        blank=False,
        null=False,
        related_name="Income_Item",
        on_delete=models.CASCADE,
    )
    description = models.CharField(max_length=200, default="")
    unit = models.CharField(max_length=100, blank=False, null=False, default="")
    quantity = models.DecimalField(max_digits=10, decimal_places=2)
    rate = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    voucherNo = models.CharField(max_length=100, blank=False, null=False, default="")
    modeOfTransaction = models.CharField(
        max_length=4,
        blank=False,
        null=False,
        choices=choices.modeOfTransaction,
        default="",
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    remarks = models.CharField(max_length=200, blank=False, null=False, default="")
    work_phase = models.ForeignKey(
        WorkPhase,
        blank=False,
        null=False,
        related_name="income_work_phase",
        on_delete=models.CASCADE,
        default=1
    )
    tid = models.CharField(max_length=10, blank=False, null=False, default="")

    class Meta:
        ordering = ("-dateOfTransaction",)

    def get_absolute_url(self):
        return reverse("Accounts:income_update", args=[str(self.id)])
        # return f"/income_update/{str(self.id)}"

    def __str__(self):
        return f"Date: {(self.dateOfTransaction).strftime('%d-%b-%Y')}, {self.description}, Sector: {self.incomeItem}, Quantity:{self.quantity} {self.unit}, Amount: {intcomma_bd(self.amount)}"


# TODO:=========================
class BankAccount(models.Model):
    openingDate = models.DateField(default=datetime.now)
    accountName = models.CharField(max_length=100, blank=False, null=False, default="")
    bankName = models.CharField(max_length=100, blank=False, null=False, default="")
    branchName = models.CharField(max_length=100, blank=False, null=False, default="")
    accountNo = models.CharField(max_length=100, blank=False, null=False, default="")
    remarks = models.CharField(max_length=200, default="")
    isArchive = models.BooleanField(default=False)

    class Meta:
        ordering = ("accountName","bankName", "branchName", "accountNo")

    def __str__(self):
        return f"{self.bankName}, {self.branchName}, AC No: {self.accountNo}, {self.accountName}"


class BankLedger(models.Model):
    dateOfTransaction = models.DateField(default=datetime.now)
    description = models.CharField(max_length=255, blank=True, null=True)
    account = models.ForeignKey(
        "BankAccount",
        blank=False,
        null=False,
        related_name="bank_account",
        on_delete=models.CASCADE,
    )
    debit = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    credit = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    remarks = models.CharField(max_length=200, default="")
    work_phase = models.ForeignKey(
        WorkPhase,
        blank=False,
        null=False,
        related_name="bank_ledger_work_phase",
        on_delete=models.CASCADE,
        default=1
    )
    tid = models.CharField(max_length=10, blank=False, null=False, default="")

    class Meta:
        ordering = ("-dateOfTransaction",)

    def __str__(self):
        debit_or_credit = (
            f"Debit: {self.debit}" if self.debit > 0 else f"Credit: {self.credit}"
        )
        return f"Date: {self.dateOfTransaction.strftime('%d %b %Y')}, Account: {self.account}, {debit_or_credit}"



class CashLedger(models.Model):
    dateOfTransaction = models.DateField(default=datetime.now)
    description = models.CharField(max_length=255, blank=True, null=True)
    debit = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    credit = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    remarks = models.CharField(max_length=200, default="")
    work_phase = models.ForeignKey(
        WorkPhase,
        blank=False,
        null=False,
        related_name="cash_ledger_work_phase",
        on_delete=models.CASCADE,
        default=1
    )
    tid = models.CharField(max_length=10, blank=False, null=False, default="")

    class Meta:
        ordering = ("-dateOfTransaction",)

    def __str__(self):
        debit_or_credit = (
            f"Debit: {self.debit}" if self.debit > 0 else f"Credit: {self.credit}"
        )
        return f"Date: {self.dateOfTransaction.strftime('%d %b %Y')}, {self.description}: {debit_or_credit}"
