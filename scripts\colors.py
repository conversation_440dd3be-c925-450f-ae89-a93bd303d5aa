"""Colors class:
reset all Colors with Colors.reset
two sub classes
Fg for foreground
Bg for background
use as Colors.subclass.colorname.
i.e. Colors.Fg.red or Colors.Bg.green
also, the generic bold, disable, underline, reverse, strike through, and invisible
work with the main class i.e. Colors.bold"""

class Colors:
    reset = '\033[0m'
    bold = '\033[01m'
    disable = '\033[02m'
    underline = '\033[04m'
    reverse = '\033[07m'
    strikethrough = '\033[09m'
    invisible = '\033[08m'

    class Fg:
        black = '\033[38;5;0m'
        red = '\033[38;5;1m'
        green = '\033[38;5;2m'
        yellow = '\033[38;5;3m'
        blue = '\033[38;5;4m'
        magenta = '\033[38;5;5m'
        cyan = '\033[38;5;6m'
        light_gray = '\033[38;5;7m'
        dark_gray = '\033[38;5;8m'
        light_red = '\033[38;5;9m'
        light_green = '\033[38;5;10m'
        light_yellow = '\033[38;5;11m'
        light_blue = '\033[38;5;12m'
        light_magenta = '\033[38;5;13m'
        light_cyan = '\033[38;5;14m'
        white = '\033[38;5;15m'
        grey_0 = '\033[38;5;16m'
        navy_blue = '\033[38;5;17m'
        dark_blue = '\033[38;5;18m'
        blue_3a = '\033[38;5;19m'
        blue_3b = '\033[38;5;20m'
        blue_1 = '\033[38;5;21m'
        dark_green = '\033[38;5;22m'
        deep_sky_blue_4a = '\033[38;5;23m'
        deep_sky_blue_4b = '\033[38;5;24m'
        deep_sky_blue_4c = '\033[38;5;25m'
        dodger_blue_3 = '\033[38;5;26m'
        dodger_blue_2 = '\033[38;5;27m'
        green_4 = '\033[38;5;28m'
        spring_green_4 = '\033[38;5;29m'
        turquoise_4 = '\033[38;5;30m'
        deep_sky_blue_3a = '\033[38;5;31m'
        deep_sky_blue_3b = '\033[38;5;32m'
        dodger_blue_1 = '\033[38;5;33m'
        green_3a = '\033[38;5;34m'
        spring_green_3a = '\033[38;5;35m'
        dark_cyan = '\033[38;5;36m'
        light_sea_green = '\033[38;5;37m'
        deep_sky_blue_2 = '\033[38;5;38m'
        deep_sky_blue_1 = '\033[38;5;39m'
        green_3b = '\033[38;5;40m'
        spring_green_3b = '\033[38;5;41m'
        spring_green_2a = '\033[38;5;42m'
        cyan_3 = '\033[38;5;43m'
        dark_turquoise = '\033[38;5;44m'
        turquoise_2 = '\033[38;5;45m'
        green_1 = '\033[38;5;46m'
        spring_green_2b = '\033[38;5;47m'
        spring_green_1 = '\033[38;5;48m'
        medium_spring_green = '\033[38;5;49m'
        cyan_2 = '\033[38;5;50m'
        cyan_1 = '\033[38;5;51m'
        dark_red_1 = '\033[38;5;52m'
        deep_pink_4a = '\033[38;5;53m'
        purple_4a = '\033[38;5;54m'
        purple_4b = '\033[38;5;55m'
        purple_3 = '\033[38;5;56m'
        blue_violet = '\033[38;5;57m'
        orange_4a = '\033[38;5;58m'
        grey_37 = '\033[38;5;59m'
        medium_purple_4 = '\033[38;5;60m'
        slate_blue_3a = '\033[38;5;61m'
        slate_blue_3b = '\033[38;5;62m'
        royal_blue_1 = '\033[38;5;63m'
        chartreuse_4 = '\033[38;5;64m'
        dark_sea_green_4a = '\033[38;5;65m'
        pale_turquoise_4 = '\033[38;5;66m'
        steel_blue = '\033[38;5;67m'
        steel_blue_3 = '\033[38;5;68m'
        cornflower_blue = '\033[38;5;69m'
        chartreuse_3a = '\033[38;5;70m'
        dark_sea_green_4b = '\033[38;5;71m'
        cadet_blue_2 = '\033[38;5;72m'
        cadet_blue_1 = '\033[38;5;73m'
        sky_blue_3 = '\033[38;5;74m'
        steel_blue_1a = '\033[38;5;75m'
        chartreuse_3b = '\033[38;5;76m'
        pale_green_3a = '\033[38;5;77m'
        sea_green_3 = '\033[38;5;78m'
        aquamarine_3 = '\033[38;5;79m'
        medium_turquoise = '\033[38;5;80m'
        steel_blue_1b = '\033[38;5;81m'
        chartreuse_2a = '\033[38;5;82m'
        sea_green_2 = '\033[38;5;83m'
        sea_green_1a = '\033[38;5;84m'
        sea_green_1b = '\033[38;5;85m'
        aquamarine_1a = '\033[38;5;86m'
        dark_slate_gray_2 = '\033[38;5;87m'
        dark_red_2 = '\033[38;5;88m'
        deep_pink_4b = '\033[38;5;89m'
        dark_magenta_1 = '\033[38;5;90m'
        dark_magenta_2 = '\033[38;5;91m'
        dark_violet_1a = '\033[38;5;92m'
        purple_1a = '\033[38;5;93m'
        orange_4b = '\033[38;5;94m'
        light_pink_4 = '\033[38;5;95m'
        plum_4 = '\033[38;5;96m'
        medium_purple_3a = '\033[38;5;97m'
        medium_purple_3b = '\033[38;5;98m'
        slate_blue_1 = '\033[38;5;99m'
        yellow_4a = '\033[38;5;100m'
        wheat_4 = '\033[38;5;101m'
        grey_53 = '\033[38;5;102m'
        light_slate_grey = '\033[38;5;103m'
        medium_purple = '\033[38;5;104m'
        light_slate_blue = '\033[38;5;105m'
        yellow_4b = '\033[38;5;106m'
        dark_olive_green_3a = '\033[38;5;107m'
        dark_green_sea = '\033[38;5;108m'
        light_sky_blue_3a = '\033[38;5;109m'
        light_sky_blue_3b = '\033[38;5;110m'
        sky_blue_2 = '\033[38;5;111m'
        chartreuse_2b = '\033[38;5;112m'
        dark_olive_green_3b = '\033[38;5;113m'
        pale_green_3b = '\033[38;5;114m'
        dark_sea_green_3a = '\033[38;5;115m'
        dark_slate_gray_3 = '\033[38;5;116m'
        sky_blue_1 = '\033[38;5;117m'
        chartreuse_1 = '\033[38;5;118m'
        light_green_2 = '\033[38;5;119m'
        light_green_3 = '\033[38;5;120m'
        pale_green_1a = '\033[38;5;121m'
        aquamarine_1b = '\033[38;5;122m'
        dark_slate_gray_1 = '\033[38;5;123m'
        red_3a = '\033[38;5;124m'
        deep_pink_4c = '\033[38;5;125m'
        medium_violet_red = '\033[38;5;126m'
        magenta_3a = '\033[38;5;127m'
        dark_violet_1b = '\033[38;5;128m'
        purple_1b = '\033[38;5;129m'
        dark_orange_3a = '\033[38;5;130m'
        indian_red_1a = '\033[38;5;131m'
        hot_pink_3a = '\033[38;5;132m'
        medium_orchid_3 = '\033[38;5;133m'
        medium_orchid = '\033[38;5;134m'
        medium_purple_2a = '\033[38;5;135m'
        dark_goldenrod = '\033[38;5;136m'
        light_salmon_3a = '\033[38;5;137m'
        rosy_brown = '\033[38;5;138m'
        grey_63 = '\033[38;5;139m'
        medium_purple_2b = '\033[38;5;140m'
        medium_purple_1 = '\033[38;5;141m'
        gold_3a = '\033[38;5;142m'
        dark_khaki = '\033[38;5;143m'
        navajo_white_3 = '\033[38;5;144m'
        grey_69 = '\033[38;5;145m'
        light_steel_blue_3 = '\033[38;5;146m'
        light_steel_blue = '\033[38;5;147m'
        yellow_3a = '\033[38;5;148m'
        dark_olive_green_3 = '\033[38;5;149m'
        dark_sea_green_3b = '\033[38;5;150m'
        dark_sea_green_2 = '\033[38;5;151m'
        light_cyan_3 = '\033[38;5;152m'
        light_sky_blue_1 = '\033[38;5;153m'
        green_yellow = '\033[38;5;154m'
        dark_olive_green_2 = '\033[38;5;155m'
        pale_green_1b = '\033[38;5;156m'
        dark_sea_green_5b = '\033[38;5;157m'
        dark_sea_green_5a = '\033[38;5;158m'
        pale_turquoise_1 = '\033[38;5;159m'
        red_3b = '\033[38;5;160m'
        deep_pink_3a = '\033[38;5;161m'
        deep_pink_3b = '\033[38;5;162m'
        magenta_3b = '\033[38;5;163m'
        magenta_3c = '\033[38;5;164m'
        magenta_2a = '\033[38;5;165m'
        dark_orange_3b = '\033[38;5;166m'
        indian_red_1b = '\033[38;5;167m'
        hot_pink_3b = '\033[38;5;168m'
        hot_pink_2 = '\033[38;5;169m'
        orchid = '\033[38;5;170m'
        medium_orchid_1a = '\033[38;5;171m'
        orange_3 = '\033[38;5;172m'
        light_salmon_3b = '\033[38;5;173m'
        light_pink_3 = '\033[38;5;174m'
        pink_3 = '\033[38;5;175m'
        plum_3 = '\033[38;5;176m'
        violet = '\033[38;5;177m'
        gold_3b = '\033[38;5;178m'
        light_goldenrod_3 = '\033[38;5;179m'
        tan = '\033[38;5;180m'
        misty_rose_3 = '\033[38;5;181m'
        thistle_3 = '\033[38;5;182m'
        plum_2 = '\033[38;5;183m'
        yellow_3b = '\033[38;5;184m'
        khaki_3 = '\033[38;5;185m'
        light_goldenrod_2a = '\033[38;5;186m'
        light_yellow_3 = '\033[38;5;187m'
        grey_84 = '\033[38;5;188m'
        light_steel_blue_1 = '\033[38;5;189m'
        yellow_2 = '\033[38;5;190m'
        dark_olive_green_1a = '\033[38;5;191m'
        dark_olive_green_1b = '\033[38;5;192m'
        dark_sea_green_1 = '\033[38;5;193m'
        honeydew_2 = '\033[38;5;194m'
        light_cyan_1 = '\033[38;5;195m'
        red_1 = '\033[38;5;196m'
        deep_pink_2 = '\033[38;5;197m'
        deep_pink_1a = '\033[38;5;198m'
        deep_pink_1b = '\033[38;5;199m'
        magenta_2b = '\033[38;5;200m'
        magenta_1 = '\033[38;5;201m'
        orange_red_1 = '\033[38;5;202m'
        indian_red_1c = '\033[38;5;203m'
        indian_red_1d = '\033[38;5;204m'
        hot_pink_1a = '\033[38;5;205m'
        hot_pink_1b = '\033[38;5;206m'
        medium_orchid_1b = '\033[38;5;207m'
        dark_orange = '\033[38;5;208m'
        salmon_1 = '\033[38;5;209m'
        light_coral = '\033[38;5;210m'
        pale_violet_red_1 = '\033[38;5;211m'
        orchid_2 = '\033[38;5;212m'
        orchid_1 = '\033[38;5;213m'
        orange_1 = '\033[38;5;214m'
        sandy_brown = '\033[38;5;215m'
        light_salmon_1 = '\033[38;5;216m'
        light_pink_1 = '\033[38;5;217m'
        pink_1 = '\033[38;5;218m'
        plum_1 = '\033[38;5;219m'
        gold_1 = '\033[38;5;220m'
        light_goldenrod_2b = '\033[38;5;221m'
        light_goldenrod_2c = '\033[38;5;222m'
        navajo_white_1 = '\033[38;5;223m'
        misty_rose1 = '\033[38;5;224m'
        thistle_1 = '\033[38;5;225m'
        yellow_1 = '\033[38;5;226m'
        light_goldenrod_1 = '\033[38;5;227m'
        khaki_1 = '\033[38;5;228m'
        wheat_1 = '\033[38;5;229m'
        cornsilk_1 = '\033[38;5;230m'
        grey_100 = '\033[38;5;231m'
        grey_3 = '\033[38;5;232m'
        grey_7 = '\033[38;5;233m'
        grey_11 = '\033[38;5;234m'
        grey_15 = '\033[38;5;235m'
        grey_19 = '\033[38;5;236m'
        grey_23 = '\033[38;5;237m'
        grey_27 = '\033[38;5;238m'
        grey_30 = '\033[38;5;239m'
        grey_35 = '\033[38;5;240m'
        grey_39 = '\033[38;5;241m'
        grey_42 = '\033[38;5;242m'
        grey_46 = '\033[38;5;243m'
        grey_50 = '\033[38;5;244m'
        grey_54 = '\033[38;5;245m'
        grey_58 = '\033[38;5;246m'
        grey_62 = '\033[38;5;247m'
        grey_66 = '\033[38;5;248m'
        grey_70 = '\033[38;5;249m'
        grey_74 = '\033[38;5;250m'
        grey_78 = '\033[38;5;251m'
        grey_82 = '\033[38;5;252m'
        grey_85 = '\033[38;5;253m'
        grey_89 = '\033[38;5;254m'
        grey_93 = '\033[38;5;255m'
        default = '\033[38;5;256m'

    class Bg:
        black = '\033[48;5;0m'
        red = '\033[48;5;1m'
        green = '\033[48;5;2m'
        yellow = '\033[48;5;3m'
        blue = '\033[48;5;4m'
        magenta = '\033[48;5;5m'
        cyan = '\033[48;5;6m'
        light_gray = '\033[48;5;7m'
        dark_gray = '\033[48;5;8m'
        light_red = '\033[48;5;9m'
        light_green = '\033[48;5;10m'
        light_yellow = '\033[48;5;11m'
        light_blue = '\033[48;5;12m'
        light_magenta = '\033[48;5;13m'
        light_cyan = '\033[48;5;14m'
        white = '\033[48;5;15m'
        grey_0 = '\033[48;5;16m'
        navy_blue = '\033[48;5;17m'
        dark_blue = '\033[48;5;18m'
        blue_3a = '\033[48;5;19m'
        blue_3b = '\033[48;5;20m'
        blue_1 = '\033[48;5;21m'
        dark_green = '\033[48;5;22m'
        deep_sky_blue_4a = '\033[48;5;23m'
        deep_sky_blue_4b = '\033[48;5;24m'
        deep_sky_blue_4c = '\033[48;5;25m'
        dodger_blue_3 = '\033[48;5;26m'
        dodger_blue_2 = '\033[48;5;27m'
        green_4 = '\033[48;5;28m'
        spring_green_4 = '\033[48;5;29m'
        turquoise_4 = '\033[48;5;30m'
        deep_sky_blue_3a = '\033[48;5;31m'
        deep_sky_blue_3b = '\033[48;5;32m'
        dodger_blue_1 = '\033[48;5;33m'
        green_3a = '\033[48;5;34m'
        spring_green_3a = '\033[48;5;35m'
        dark_cyan = '\033[48;5;36m'
        light_sea_green = '\033[48;5;37m'
        deep_sky_blue_2 = '\033[48;5;38m'
        deep_sky_blue_1 = '\033[48;5;39m'
        green_3b = '\033[48;5;40m'
        spring_green_3b = '\033[48;5;41m'
        spring_green_2a = '\033[48;5;42m'
        cyan_3 = '\033[48;5;43m'
        dark_turquoise = '\033[48;5;44m'
        turquoise_2 = '\033[48;5;45m'
        green_1 = '\033[48;5;46m'
        spring_green_2b = '\033[48;5;47m'
        spring_green_1 = '\033[48;5;48m'
        medium_spring_green = '\033[48;5;49m'
        cyan_2 = '\033[48;5;50m'
        cyan_1 = '\033[48;5;51m'
        dark_red_1 = '\033[48;5;52m'
        deep_pink_4a = '\033[48;5;53m'
        purple_4a = '\033[48;5;54m'
        purple_4b = '\033[48;5;55m'
        purple_3 = '\033[48;5;56m'
        blue_violet = '\033[48;5;57m'
        orange_4a = '\033[48;5;58m'
        grey_37 = '\033[48;5;59m'
        medium_purple_4 = '\033[48;5;60m'
        slate_blue_3a = '\033[48;5;61m'
        slate_blue_3b = '\033[48;5;62m'
        royal_blue_1 = '\033[48;5;63m'
        chartreuse_4 = '\033[48;5;64m'
        dark_sea_green_4a = '\033[48;5;65m'
        pale_turquoise_4 = '\033[48;5;66m'
        steel_blue = '\033[48;5;67m'
        steel_blue_3 = '\033[48;5;68m'
        cornflower_blue = '\033[48;5;69m'
        chartreuse_3a = '\033[48;5;70m'
        dark_sea_green_4b = '\033[48;5;71m'
        cadet_blue_2 = '\033[48;5;72m'
        cadet_blue_1 = '\033[48;5;73m'
        sky_blue_3 = '\033[48;5;74m'
        steel_blue_1a = '\033[48;5;75m'
        chartreuse_3b = '\033[48;5;76m'
        pale_green_3a = '\033[48;5;77m'
        sea_green_3 = '\033[48;5;78m'
        aquamarine_3 = '\033[48;5;79m'
        medium_turquoise = '\033[48;5;80m'
        steel_blue_1b = '\033[48;5;81m'
        chartreuse_2a = '\033[48;5;82m'
        sea_green_2 = '\033[48;5;83m'
        sea_green_1a = '\033[48;5;84m'
        sea_green_1b = '\033[48;5;85m'
        aquamarine_1a = '\033[48;5;86m'
        dark_slate_gray_2 = '\033[48;5;87m'
        dark_red_2 = '\033[48;5;88m'
        deep_pink_4b = '\033[48;5;89m'
        dark_magenta_1 = '\033[48;5;90m'
        dark_magenta_2 = '\033[48;5;91m'
        dark_violet_1a = '\033[48;5;92m'
        purple_1a = '\033[48;5;93m'
        orange_4b = '\033[48;5;94m'
        light_pink_4 = '\033[48;5;95m'
        plum_4 = '\033[48;5;96m'
        medium_purple_3a = '\033[48;5;97m'
        medium_purple_3b = '\033[48;5;98m'
        slate_blue_1 = '\033[48;5;99m'
        yellow_4a = '\033[48;5;100m'
        wheat_4 = '\033[48;5;101m'
        grey_53 = '\033[48;5;102m'
        light_slate_grey = '\033[48;5;103m'
        medium_purple = '\033[48;5;104m'
        light_slate_blue = '\033[48;5;105m'
        yellow_4b = '\033[48;5;106m'
        dark_olive_green_3a = '\033[48;5;107m'
        dark_green_sea = '\033[48;5;108m'
        light_sky_blue_3a = '\033[48;5;109m'
        light_sky_blue_3b = '\033[48;5;110m'
        sky_blue_2 = '\033[48;5;111m'
        chartreuse_2b = '\033[48;5;112m'
        dark_olive_green_3b = '\033[48;5;113m'
        pale_green_3b = '\033[48;5;114m'
        dark_sea_green_3a = '\033[48;5;115m'
        dark_slate_gray_3 = '\033[48;5;116m'
        sky_blue_1 = '\033[48;5;117m'
        chartreuse_1 = '\033[48;5;118m'
        light_green_2 = '\033[48;5;119m'
        light_green_3 = '\033[48;5;120m'
        pale_green_1a = '\033[48;5;121m'
        aquamarine_1b = '\033[48;5;122m'
        dark_slate_gray_1 = '\033[48;5;123m'
        red_3a = '\033[48;5;124m'
        deep_pink_4c = '\033[48;5;125m'
        medium_violet_red = '\033[48;5;126m'
        magenta_3a = '\033[48;5;127m'
        dark_violet_1b = '\033[48;5;128m'
        purple_1b = '\033[48;5;129m'
        dark_orange_3a = '\033[48;5;130m'
        indian_red_1a = '\033[48;5;131m'
        hot_pink_3a = '\033[48;5;132m'
        medium_orchid_3 = '\033[48;5;133m'
        medium_orchid = '\033[48;5;134m'
        medium_purple_2a = '\033[48;5;135m'
        dark_goldenrod = '\033[48;5;136m'
        light_salmon_3a = '\033[48;5;137m'
        rosy_brown = '\033[48;5;138m'
        grey_63 = '\033[48;5;139m'
        medium_purple_2b = '\033[48;5;140m'
        medium_purple_1 = '\033[48;5;141m'
        gold_3a = '\033[48;5;142m'
        dark_khaki = '\033[48;5;143m'
        navajo_white_3 = '\033[48;5;144m'
        grey_69 = '\033[48;5;145m'
        light_steel_blue_3 = '\033[48;5;146m'
        light_steel_blue = '\033[48;5;147m'
        yellow_3a = '\033[48;5;148m'
        dark_olive_green_3 = '\033[48;5;149m'
        dark_sea_green_3b = '\033[48;5;150m'
        dark_sea_green_2 = '\033[48;5;151m'
        light_cyan_3 = '\033[48;5;152m'
        light_sky_blue_1 = '\033[48;5;153m'
        green_yellow = '\033[48;5;154m'
        dark_olive_green_2 = '\033[48;5;155m'
        pale_green_1b = '\033[48;5;156m'
        dark_sea_green_5b = '\033[48;5;157m'
        dark_sea_green_5a = '\033[48;5;158m'
        pale_turquoise_1 = '\033[48;5;159m'
        red_3b = '\033[48;5;160m'
        deep_pink_3a = '\033[48;5;161m'
        deep_pink_3b = '\033[48;5;162m'
        magenta_3b = '\033[48;5;163m'
        magenta_3c = '\033[48;5;164m'
        magenta_2a = '\033[48;5;165m'
        dark_orange_3b = '\033[48;5;166m'
        indian_red_1b = '\033[48;5;167m'
        hot_pink_3b = '\033[48;5;168m'
        hot_pink_2 = '\033[48;5;169m'
        orchid = '\033[48;5;170m'
        medium_orchid_1a = '\033[48;5;171m'
        orange_3 = '\033[48;5;172m'
        light_salmon_3b = '\033[48;5;173m'
        light_pink_3 = '\033[48;5;174m'
        pink_3 = '\033[48;5;175m'
        plum_3 = '\033[48;5;176m'
        violet = '\033[48;5;177m'
        gold_3b = '\033[48;5;178m'
        light_goldenrod_3 = '\033[48;5;179m'
        tan = '\033[48;5;180m'
        misty_rose_3 = '\033[48;5;181m'
        thistle_3 = '\033[48;5;182m'
        plum_2 = '\033[48;5;183m'
        yellow_3b = '\033[48;5;184m'
        khaki_3 = '\033[48;5;185m'
        light_goldenrod_2a = '\033[48;5;186m'
        light_yellow_3 = '\033[48;5;187m'
        grey_84 = '\033[48;5;188m'
        light_steel_blue_1 = '\033[48;5;189m'
        yellow_2 = '\033[48;5;190m'
        dark_olive_green_1a = '\033[48;5;191m'
        dark_olive_green_1b = '\033[48;5;192m'
        dark_sea_green_1 = '\033[48;5;193m'
        honeydew_2 = '\033[48;5;194m'
        light_cyan_1 = '\033[48;5;195m'
        red_1 = '\033[48;5;196m'
        deep_pink_2 = '\033[48;5;197m'
        deep_pink_1a = '\033[48;5;198m'
        deep_pink_1b = '\033[48;5;199m'
        magenta_2b = '\033[48;5;200m'
        magenta_1 = '\033[48;5;201m'
        orange_red_1 = '\033[48;5;202m'
        indian_red_1c = '\033[48;5;203m'
        indian_red_1d = '\033[48;5;204m'
        hot_pink_1a = '\033[48;5;205m'
        hot_pink_1b = '\033[48;5;206m'
        medium_orchid_1b = '\033[48;5;207m'
        dark_orange = '\033[48;5;208m'
        salmon_1 = '\033[48;5;209m'
        light_coral = '\033[48;5;210m'
        pale_violet_red_1 = '\033[48;5;211m'
        orchid_2 = '\033[48;5;212m'
        orchid_1 = '\033[48;5;213m'
        orange_1 = '\033[48;5;214m'
        sandy_brown = '\033[48;5;215m'
        light_salmon_1 = '\033[48;5;216m'
        light_pink_1 = '\033[48;5;217m'
        pink_1 = '\033[48;5;218m'
        plum_1 = '\033[48;5;219m'
        gold_1 = '\033[48;5;220m'
        light_goldenrod_2b = '\033[48;5;221m'
        light_goldenrod_2c = '\033[48;5;222m'
        navajo_white_1 = '\033[48;5;223m'
        misty_rose1 = '\033[48;5;224m'
        thistle_1 = '\033[48;5;225m'
        yellow_1 = '\033[48;5;226m'
        light_goldenrod_1 = '\033[48;5;227m'
        khaki_1 = '\033[48;5;228m'
        wheat_1 = '\033[48;5;229m'
        cornsilk_1 = '\033[48;5;230m'
        grey_100 = '\033[48;5;231m'
        grey_3 = '\033[48;5;232m'
        grey_7 = '\033[48;5;233m'
        grey_11 = '\033[48;5;234m'
        grey_15 = '\033[48;5;235m'
        grey_19 = '\033[48;5;236m'
        grey_23 = '\033[48;5;237m'
        grey_27 = '\033[48;5;238m'
        grey_30 = '\033[48;5;239m'
        grey_35 = '\033[48;5;240m'
        grey_39 = '\033[48;5;241m'
        grey_42 = '\033[48;5;242m'
        grey_46 = '\033[48;5;243m'
        grey_50 = '\033[48;5;244m'
        grey_54 = '\033[48;5;245m'
        grey_58 = '\033[48;5;246m'
        grey_62 = '\033[48;5;247m'
        grey_66 = '\033[48;5;248m'
        grey_70 = '\033[48;5;249m'
        grey_74 = '\033[48;5;250m'
        grey_78 = '\033[48;5;251m'
        grey_82 = '\033[48;5;252m'
        grey_85 = '\033[48;5;253m'
        grey_89 = '\033[48;5;254m'
        grey_93 = '\033[48;5;255m'
        default = '\033[48;5;256m'

color_names = ['black','red','green','yellow','blue','magenta','cyan','light_gray','dark_gray',
'light_red','light_green','light_yellow','light_blue','light_magenta','light_cyan','white','grey_0',
'navy_blue','dark_blue','blue_3a','blue_3b','blue_1','dark_green','deep_sky_blue_4a','deep_sky_blue_4b',
'deep_sky_blue_4c','dodger_blue_3','dodger_blue_2','green_4','spring_green_4','turquoise_4','deep_sky_blue_3a',
'deep_sky_blue_3b','dodger_blue_1','green_3a','spring_green_3a','dark_cyan','light_sea_green',
'deep_sky_blue_2','deep_sky_blue_1','green_3b','spring_green_3b','spring_green_2a','cyan_3','dark_turquoise',
'turquoise_2','green_1','spring_green_2b','spring_green_1','medium_spring_green','cyan_2','cyan_1','dark_red_1',
'deep_pink_4a','purple_4a','purple_4b','purple_3','blue_violet','orange_4a','grey_37','medium_purple_4',
'slate_blue_3a','slate_blue_3b','royal_blue_1','chartreuse_4','dark_sea_green_4a','pale_turquoise_4',
'steel_blue','steel_blue_3','cornflower_blue','chartreuse_3a','dark_sea_green_4b','cadet_blue_2',
'cadet_blue_1','sky_blue_3','steel_blue_1a','chartreuse_3b','pale_green_3a','sea_green_3','aquamarine_3',
'medium_turquoise','steel_blue_1b','chartreuse_2a','sea_green_2','sea_green_1a','sea_green_1b','aquamarine_1a',
'dark_slate_gray_2','dark_red_2','deep_pink_4b','dark_magenta_1','dark_magenta_2','dark_violet_1a',
'purple_1a','orange_4b','light_pink_4','plum_4','medium_purple_3a','medium_purple_3b','slate_blue_1',
'yellow_4a','wheat_4','grey_53','light_slate_grey','medium_purple','light_slate_blue','yellow_4b',
'dark_olive_green_3a','dark_green_sea','light_sky_blue_3a','light_sky_blue_3b','sky_blue_2','chartreuse_2b',
'dark_olive_green_3b','pale_green_3b','dark_sea_green_3a','dark_slate_gray_3','sky_blue_1','chartreuse_1',
'light_green_2','light_green_3','pale_green_1a','aquamarine_1b','dark_slate_gray_1','red_3a','deep_pink_4c',
'medium_violet_red','magenta_3a','dark_violet_1b','purple_1b','dark_orange_3a','indian_red_1a','hot_pink_3a',
'medium_orchid_3','medium_orchid','medium_purple_2a','dark_goldenrod','light_salmon_3a','rosy_brown',
'grey_63','medium_purple_2b','medium_purple_1','gold_3a','dark_khaki','navajo_white_3','grey_69',
'light_steel_blue_3','light_steel_blue','yellow_3a','dark_olive_green_3','dark_sea_green_3b','dark_sea_green_2',
'light_cyan_3','light_sky_blue_1','green_yellow','dark_olive_green_2','pale_green_1b','dark_sea_green_5b',
'dark_sea_green_5a','pale_turquoise_1','red_3b','deep_pink_3a','deep_pink_3b','magenta_3b','magenta_3c',
'magenta_2a','dark_orange_3b','indian_red_1b','hot_pink_3b','hot_pink_2','orchid','medium_orchid_1a',
'orange_3','light_salmon_3b','light_pink_3','pink_3','plum_3','violet','gold_3b','light_goldenrod_3',
'tan','misty_rose_3','thistle_3','plum_2','yellow_3b','khaki_3','light_goldenrod_2a','light_yellow_3',
'grey_84','light_steel_blue_1','yellow_2','dark_olive_green_1a','dark_olive_green_1b','dark_sea_green_1',
'honeydew_2','light_cyan_1','red_1','deep_pink_2','deep_pink_1a','deep_pink_1b','magenta_2b','magenta_1',
'orange_red_1','indian_red_1c','indian_red_1d','hot_pink_1a','hot_pink_1b','medium_orchid_1b','dark_orange',
'salmon_1','light_coral','pale_violet_red_1','orchid_2','orchid_1','orange_1','sandy_brown','light_salmon_1',
'light_pink_1','pink_1','plum_1','gold_1','light_goldenrod_2b','light_goldenrod_2c','navajo_white_1','misty_rose1',
'thistle_1','yellow_1','light_goldenrod_1','khaki_1','wheat_1','cornsilk_1','grey_100','grey_3','grey_7',
'grey_11','grey_15','grey_19','grey_23','grey_27','grey_30','grey_35','grey_39','grey_42','grey_46','grey_50',
'grey_54','grey_58','grey_62','grey_66','grey_70','grey_74','grey_78','grey_82','grey_85','grey_89','grey_93',
'default']

def run():
    count = 0
    while count < len(color_names):
        x = Colors.Fg()
        y = color_names[count]
        z = Colors.Bg()
        value1 = getattr(x, y)
        value2 = getattr(z, y)
        print(
            value1,
            Colors.reverse,
            f"{color_names[count].ljust(22) + str(count).rjust(6)}",
            Colors.reset,
            value1,
            " Foreground Ansi Code ",
            r'\033[38;5;',
            count,
            'm ',
            Colors.reset,
            Colors.Fg.black,
            value2,
            " ---- Background Ansi Code ",
            r'\033[48;5;',
            count,
            'm ',
            Colors.reset,
            sep='',
        )
        count += 1
    