{% extends "main.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% load mahimsoft_tags %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}{{company_name}}{%endblock %}
{% block style %}
<style>
    td, th {
       font-size: small;
    }
</style>
{% endblock style %}
{% block content %}

<div class="container pt-3" style="width: 90%; margin: auto;">
    <!--{{expenditure.from_date}}-->
    {% if expenditure.from_date %}
    <div class="p-2 my-2 text-start">
        <h4 class="text-muted">Expenditure Details <span class="text-success fs-5">[Date Range:
                {{expenditure.from_date|date:"d-M-Y"|title}}
                to {{expenditure.to_date|date:"d-M-Y"|title}}]</span></h4>
    </div>
    {% else %}
    <div class="p-2 my-2 text-center">
        <h3 class="text-muted">Expenditure Details</h3>
    </div>
    {% endif %}
        <div class="card text-dark border-warning bg-light">
        <div class="card-header text-muted"><h5>Select date range:</h5></div>
        <div class="card-body col-auto p-0 m-0 ps-2">
            <form class="row g-3 p-2">
                <div class="col-auto">
                    From Date: <input type="date" id="fromdate" name="fromdate" />
                </div>
                <div class="col-auto">
                    To Date: <input type="date" id="todate" name="todate" />
                </div>
                <div class="col-auto">
                    <button type="submit" class="btn btn-sm btn-success mt-0">Show</button>
                </div>
            </form>
        </div>
    </div>
    {% if expenditure.from_date %}
    
    {% if expenditure.count > 0 %}

    <div class="d-flex justify-content-between mt-2 mb-0">
        <h5 class="text-danger ps-2">Total Expenditure during the period: {{ currency }} {{grand_total.amount__sum|intcomma_bd}}
        </h5>
        <div class="justify-content-end pb-2">
        <a href="/dateRangeExpenditureReport/?fromdate={{expenditure.from_date|date:'Y-m-d'}}&todate={{expenditure.to_date|date:'Y-m-d'}}" target="_blank" class="btn btn-sm btn-outline-danger me-0 mb-0">
            <i class="bi bi-download"></i> PDF</a>
            <a href='{{ request.META.HTTP_REFERER }}' class='btn btn-outline-primary btn-sm me-0 ms-1 mb-0' role='button'><i class="bi bi-arrow-left-square-fill"></i> Back</a>
        </div>
        </div>



          {% regroup expenditure by work_phase_description as work_phase %}
              <div class="nav_tabs">
        <ul class="nav nav-tabs justify-content-center card-header-tabs" role="tablist">
            {% for phase in work_phase %}
            <li class="nav-item" role="presentation">
                <a class="nav-link {% if forloop.last %}active" aria-current="page{% endif %}" data-bs-toggle="pill" href="#{{ phase.grouper|remove_spaces_n_leading_digit }}" style="text-decoration: none;">{{ phase.grouper }}</a>
            <hr class="text-white p-0 m-0 bg-white">
            </li>
            {% endfor %}
        </ul>
    </div>
    <div class="tab-content">
  {% for phase in work_phase %}
    <div id="{{ phase.grouper|remove_spaces_n_leading_digit }}" class="tab-pane fade {% if forloop.last %}show active{% endif %}">
    <div class="card mb-2 rounded">

      <h5 class="card-header text-success" style="background-color:rgb(188, 213, 203);">
                <div class="d-flex justify-content-between">
                <span>{{ phase.grouper }}:</span>
                <a href="{% url 'Accounts:dateRangeExpenditureReport' %}?workphase={{phase.list.0.work_phase_id}}&fromdate={{expenditure.from_date|date:'Y-m-d'}}&todate={{expenditure.to_date|date:'Y-m-d'}}" target="_blank" style="text-decoration: none; padding-bottom: 4px;">
                    <img src="{% static 'images/file-pdf.svg' %}" width="22px" heidht="22px" alt="PDF"></a>
            </div>
            </h5>
      <div class="card-body p-1 m-0" style="margin: auto;">
    <table class="table table-bordered table-striped table-sm" style="margin: auto;">
      <tr>
        <th style="background-color: inherit; text-align:right; width:20%;">Start Date:</th>
        <td style="width:30%;">{{phase.list.0.work_phase_from_date}}</td>
        <th style="background-color: inherit; text-align:right; width:20%;">End Date:</th>
        <td style="width:30%;">{{phase.list.0.work_phase_to_date}}</td>
      </tr>
      <tr>
        <th style="background-color: inherit; text-align:right; width:20%;">Estimated Cost:</th>
        <td style="width:30%;">{{ currency }} {{phase.list.0.estimated_cost|intcomma_bd}}</td>
        <th style="background-color: inherit; text-align:right; width:20%;">Total Spent:</th>
        <td style="width:30%;">{{ currency }} {{phase.list.0.work_phase_sum|intcomma_bd}}</td>
      </tr>
    </table>
</div>
</div>

{% regroup phase.list by work_sector as sectors %}
    {% for sector in sectors %}
    <div class="card bg-secondary">
        <div class="d-flex justify-content-between">
            <h5 class="text-white p-0 pt-1 ps-2"><i class="bi bi-back"></i> {{ sector.grouper }}</h5>
            <p class="text-white pt-1 pe-2 mb-0">Total: {{ currency }} {{sector.list.0.worksector_sum|intcomma_bd}}</p>
        </div>
    </div>
    {% regroup sector.list by item_name as item %}
    {% for row in item %}
    <div class="card mt-2" style="background-color: #FCE6C9;">
        <div class="d-flex justify-content-between">
            <h5 class="p-0 ps-2 pt-1">{{ row.grouper }}</h5>
            <p class="pt-1 pe-2 pb-0 mb-0">Total: {{ currency }} {{row.list.0.item_sum_amount|intcomma_bd}}</p>
        </div>
    </div>
    <div class="table-responsive-sm">
        <table class="table table-bordered table-striped table-sm">
            <thead>
                <tr>
                    <th class="text-center" style="width: 5%;">#</th>
                    <th class="text-center" style="width: 15%;">Date</th>
                    <th class="text-center" style="width: 30%;">Description</th>
                    <th class="text-center" style="width: 10%;">Quantity</th>
                    <th class="text-center" style="width: 10%;">Unit</th>
                    <th class="text-center" style="width: 15%;">Voucher No.</th>
                    <th class="text-end pe-4" style="width: 15%;">Amount ({{ currency }})</th>
                    {% if request.user|has_group:"Admin"%}
                    <th colspan="2" class="text-center" style="width: 15%;">Edit</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% if request.user|has_group:"Admin"%}
                {% for dt in row.list %}
                                <tr class="">
                                    <td class="text-center">{{forloop.counter}}</td>
                                    <td>{{dt.dateOfTransaction}}</td>
                                    <td>{{dt.description}}</td>
                                    <td class="text-end pe-4">{{dt.quantity|intcomma_bd}}</td>
                                    <td>{{dt.units}}</td>
                                    <td class="text-center">{{dt.voucherNo}}</td>
                                    <td class="text-end pe-4">{{dt.amount|intcomma_bd}}/-</td>
                                    {% if dt.contractor_bill_payment or dt.credit_purchase_payment %}
                                    <td class="text-center isDisabled"><i class="bi bi-trash"></i></td>
                                    <td class="text-center isDisabled"><i class="bi bi-pencil-square"></i></td>
                                    {% else %}
                                    <td class="text-center"><a class="a_link" href="#"><i class="bi bi-trash"></i></a></td>
                                    <td class="text-center"><a class="b_link" href="/expenditure_update/{{dt.id}}"><i
                                                class="bi bi-pencil-square"></i></a></td>
                                    {% endif %}
                                </tr>
                                {% endfor %}
                {% else %}
                
                {% for dt in row.list %}
                                <tr class="">
                                    <td class="text-center">{{forloop.counter}}</td>
                                    <td>{{dt.dateOfTransaction}}</td>
                                    <td>{{dt.description}}</td>
                                    <td class="text-end pe-4">{{dt.quantity|intcomma_bd}}</td>
                                    <td>{{dt.units}}</td>
                                    <td class="text-center">{{dt.voucherNo}}</td>
                                    <td class="text-end pe-4">{{dt.amount|intcomma_bd}}/-</td>
                                </tr>
                                {% endfor %}
                {% endif %}
                <tr>
                    <th colspan="3" class="text-end">Total Quantity =</th>
                    <th class="text-end pe-4">{{row.list.0.item_sum_quantity|intcomma_bd}}</th>
                    <th>{{row.list.0.units}}</th>
                    <th class="text-end">Total Amount =</th>
                    <th class="text-end pe-4">{{row.list.0.item_sum_amount|intcomma_bd}}/-</th>
                    <th colspan="2"></th>
                </tr>
            </tbody>
        </table>
    </div>

        {% endfor %}
        {% endfor %}
        </div>
        {% endfor %}
        {% else %}
        <div class="d-flex justify-content-center mt-2 mb-0">
            <h5 class="text-danger ps-2">No expenditure found during the period.</h5>
        </div>
        {% endif %}
        {% endif %}
    </div>
    </div>

{% endblock %}

{% block script %}
<script>
    const to_date = new Date();
    to_date.setDate(1);
    document.getElementById('fromdate').valueAsDate = to_date;
    // from_date.setMonth(from_date.getMonth()-1);
    document.getElementById('todate').valueAsDate = new Date();
</script>
{% endblock script %}
