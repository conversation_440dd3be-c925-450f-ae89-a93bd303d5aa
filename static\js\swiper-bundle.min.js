<!DOCTYPE html><html lang="en"><head><script async="" src="https://www.googletagmanager.com/gtag/js?id=UA-140352188-1"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'UA-140352188-1');</script><meta charSet="utf-8"/><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/><meta name="description" content="The CDN for swiper"/><meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1"/><meta name="timestamp" content="2023-10-13T12:55:24.236Z"/><link rel="shortcut icon" href="/favicon.ico"/><title>UNPKG - swiper</title><script>window.Promise || document.write('\x3Cscript src="/es6-promise@4.2.5/dist/es6-promise.min.js">\x3C/script>\x3Cscript>ES6Promise.polyfill()\x3C/script>')</script><script>window.fetch || document.write('\x3Cscript src="/whatwg-fetch@3.0.0/dist/fetch.umd.js">\x3C/script>')</script><script>window.__DATA__ = {"packageName":"swiper","packageVersion":"7.0.5","availableVersions":["0.9.0-beta.12","0.9.0-beta.14","0.9.0-beta.15","0.9.0-beta.16","2.7.0","2.7.5","2.7.6","3.0.0","3.0.1","3.0.2","3.0.3","3.0.4","3.0.5","3.0.6","3.0.7","3.0.8","3.1.0","3.1.2","3.1.5","3.1.7","3.2.0","3.2.5","3.2.6","3.2.7","3.3.0","3.3.1","3.4.0","3.4.1","3.4.2","4.0.0-beta.1","4.0.0-beta.2","4.0.0-beta.3","4.0.0-beta.4","4.0.0","4.0.1","4.0.2","4.0.3","4.0.5","4.0.6","4.0.7","4.1.0","4.1.5","4.1.6","4.2.0","4.2.2","4.2.5","4.2.6","4.3.0","4.3.2","4.3.3","4.3.5","4.4.0","4.4.1","4.4.2","4.4.5","4.4.6","4.5.0","4.5.1","5.0.0","5.0.1","5.0.2","5.0.3","5.0.4","5.1.0","5.2.0","5.2.1","5.3.0","5.3.1","5.3.5","5.3.6","5.3.7","5.3.8","5.4.0","5.4.1","5.4.2","5.4.3","5.4.4","5.4.5","6.0.0-alpha.1","6.0.0-alpha.2","6.0.0-alpha.3","6.0.0-alpha.4","6.0.0-alpha.5","6.0.0-alpha.6","6.0.0-alpha.7","6.0.0-alpha.8","6.0.0-alpha.9","6.0.0-alpha.10","6.0.0-alpha.11","6.0.0-alpha.12","6.0.0-alpha.15","6.0.0-alpha.16","6.0.0-alpha.17","6.0.0-alpha.18","6.0.0","6.0.1","6.0.2","6.0.3","6.0.4","6.1.0","6.1.1","6.1.2","6.1.3","6.2.0","6.3.0","6.3.1","6.3.2","6.3.3","6.3.4","6.3.5","6.4.0","6.4.1","6.4.2","6.4.3","6.4.4","6.4.5","6.4.6","6.4.7","6.4.8","6.4.9","6.4.10","6.4.11","6.4.12","6.4.14","6.4.15","6.5.0","6.5.1","6.5.2","6.5.3","6.5.4","6.5.5-beta.1","6.5.5","6.5.6","6.5.7","6.5.8","6.5.9","6.6.0","6.6.1","6.6.2","6.7.0","6.7.1","6.7.5","6.8.0-beta.1","6.8.0","6.8.1","6.8.2","6.8.3","6.8.4","7.0.0-alpha.1","7.0.0-alpha.2","7.0.0-alpha.3","7.0.0-alpha.4","7.0.0-alpha.5","7.0.0-alpha.6","7.0.0-alpha.7","7.0.0-alpha.8","7.0.0-alpha.9","7.0.0-alpha.10","7.0.0-alpha.11","7.0.0-alpha.12","7.0.0-alpha.14","7.0.0-alpha.15","7.0.0-alpha.16","7.0.0-alpha.17","7.0.0-alpha.18","7.0.0-alpha.19","7.0.0-alpha.20","7.0.0-alpha.21","7.0.0-alpha.22","7.0.0-alpha.23","7.0.0-alpha.24","7.0.0-alpha.25","7.0.0-alpha.26","7.0.0-alpha.27","7.0.0-alpha.28","7.0.0-alpha.29","7.0.0-alpha.30","7.0.0-alpha.31","7.0.0-alpha.32","7.0.0-alpha.33","7.0.0-alpha.34","7.0.0-alpha.35","7.0.0-alpha.36","7.0.0-alpha.37","7.0.0-alpha.38","7.0.0-alpha.39","7.0.0-alpha.40","7.0.0","7.0.1","7.0.2","7.0.3","7.0.4","7.0.5","7.0.6","7.0.7","7.0.8","7.0.9","7.1.0","7.2.0","7.3.0","7.3.1","7.3.2","7.3.3","7.3.4","7.4.0","7.4.1","8.0.0","8.0.1","8.0.2","8.0.3","8.0.4","8.0.5","8.0.6","8.0.7","8.1.0","8.1.1","8.1.2","8.1.3","8.1.4","8.1.5","8.1.6","8.2.0","8.2.1","8.2.2","8.2.3","8.2.4","8.2.5","8.2.6","8.3.0","8.3.1","8.3.2","8.4.0","8.4.1","8.4.2","8.4.3","8.4.4","8.4.5","8.4.6","8.4.7","9.0.0-beta.1","9.0.0-beta.2","9.0.0-beta.3","9.0.0-beta.4","9.0.0-beta.5","9.0.0-beta.6","9.0.0-beta.7","9.0.0-beta.8","9.0.0-beta.9","9.0.0-beta.10","9.0.0-beta.11","9.0.0-beta.16","9.0.0-beta.17","9.0.0-beta.18","9.0.0-beta.19","9.0.0-beta.20","9.0.0-beta.21","9.0.0-beta.22","9.0.0-beta.23","9.0.0-beta.24","9.0.0-beta.25","9.0.0-beta.26","9.0.0-beta.28","9.0.0-beta.29","9.0.0-beta.30","9.0.0-beta.31","9.0.0-beta.32","9.0.0-beta.33","9.0.0-beta.34","9.0.0-beta.35","9.0.0-beta.36","9.0.0-beta.38","9.0.0-beta.40","9.0.0-beta.41","9.0.0-beta.42","9.0.0","9.0.1","9.0.2","9.0.3","9.0.4","9.0.5","9.1.0","9.1.1","9.2.0","9.2.1","9.2.2","9.2.3","9.2.4","9.3.0-beta.1","9.3.0","9.3.1","9.3.2","9.4.0","9.4.1","10.0.0-beta.1","10.0.0-beta.2","10.0.0-beta.3","10.0.0-beta.4","10.0.0-beta.5","10.0.0-beta.6","10.0.0","10.0.1","10.0.2","10.0.3","10.0.4","10.1.0","10.2.0","10.3.0","10.3.1","11.0.0-beta.1"],"filename":"/swiper-bundle.min.js","target":{"path":"/swiper-bundle.min.js","type":"file","details":{"contentType":"application/javascript","integrity":"sha384-+Q+2ske4VTJVY7Qnf9BRE5wrvrF4syiM+EOB6J74CMT26njdVRA+ByzNDOgZO6vV","language":"JavaScript","size":134991,"uri":null,"highlights":["<span class=\"code-comment\">/**\n</span>","<span class=\"code-comment\"> * Swiper 7.0.5\n</span>","<span class=\"code-comment\"> * Most modern mobile touch slider and framework with hardware accelerated transitions\n</span>","<span class=\"code-comment\"> * https://swiperjs.com\n</span>","<span class=\"code-comment\"> *\n</span>","<span class=\"code-comment\"> * Copyright 2014-2021 Vladimir Kharlampidi\n</span>","<span class=\"code-comment\"> *\n</span>","<span class=\"code-comment\"> * Released under the MIT License\n</span>","<span class=\"code-comment\"> *\n</span>","<span class=\"code-comment\"> * Released on: September 9, 2021\n</span>","<span class=\"code-comment\"> */</span>\n","\n","!function(e,t){\"object\"==typeof exports&amp;&amp;\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&amp;&amp;define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).Swiper=t()}(this,(function(){\"use strict\";function e(e){return null!==e&amp;&amp;\"object\"==typeof e&amp;&amp;\"constructor\"in e&amp;&amp;e.constructor===Object}function t(s={},a={}){Object.keys(a).forEach((i=&gt;{void 0===s[i]?s[i]=a[i]:e(a[i])&amp;&amp;e(s[i])&amp;&amp;Object.keys(a[i]).length&gt;0&amp;&amp;t(s[i],a[i])}))}const s={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:\"\"},querySelector:()=&gt;null,querySelectorAll:()=&gt;[],getElementById:()=&gt;null,createEvent:()=&gt;({initEvent(){}}),createElement:()=&gt;({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=&gt;[]}),createElementNS:()=&gt;({}),importNode:()=&gt;null,location:{hash:\"\",host:\"\",hostname:\"\",href:\"\",origin:\"\",pathname:\"\",protocol:\"\",search:\"\"}};function a(){const e=\"undefined\"!=typeof document?document:{};return t(e,s),e}const i={document:s,navigator:{userAgent:\"\"},location:{hash:\"\",host:\"\",hostname:\"\",href:\"\",origin:\"\",pathname:\"\",protocol:\"\",search:\"\"},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=&gt;({getPropertyValue:()=&gt;\"\"}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=&gt;({}),requestAnimationFrame:e=&gt;\"undefined\"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){\"undefined\"!=typeof setTimeout&amp;&amp;clearTimeout(e)}};function r(){const e=\"undefined\"!=typeof window?window:{};return t(e,i),e}class n extends Array{constructor(e){super(...e||[]),function(e){const t=e.__proto__;Object.defineProperty(e,\"__proto__\",{get:()=&gt;t,set(e){t.__proto__=e}})}(this)}}function l(e=[]){const t=[];return e.forEach((e=&gt;{Array.isArray(e)?t.push(...l(e)):t.push(e)})),t}function o(e,t){return Array.prototype.filter.call(e,t)}function d(e,t){const s=r(),i=a();let l=[];if(!t&amp;&amp;e instanceof n)return e;if(!e)return new n(l);if(\"string\"==typeof e){const s=e.trim();if(s.indexOf(\"&lt;\")&gt;=0&amp;&amp;s.indexOf(\"&gt;\")&gt;=0){let e=\"div\";0===s.indexOf(\"&lt;li\")&amp;&amp;(e=\"ul\"),0===s.indexOf(\"&lt;tr\")&amp;&amp;(e=\"tbody\"),0!==s.indexOf(\"&lt;td\")&amp;&amp;0!==s.indexOf(\"&lt;th\")||(e=\"tr\"),0===s.indexOf(\"&lt;tbody\")&amp;&amp;(e=\"table\"),0===s.indexOf(\"&lt;option\")&amp;&amp;(e=\"select\");const t=i.createElement(e);t.innerHTML=s;for(let e=0;e&lt;t.childNodes.length;e+=1)l.push(t.childNodes[e])}else l=function(e,t){if(\"string\"!=typeof e)return[e];const s=[],a=t.querySelectorAll(e);for(let e=0;e&lt;a.length;e+=1)s.push(a[e]);return s}(e.trim(),t||i)}else if(e.nodeType||e===s||e===i)l.push(e);else if(Array.isArray(e)){if(e instanceof n)return e;l=e}return new n(function(e){const t=[];for(let s=0;s&lt;e.length;s+=1)-1===t.indexOf(e[s])&amp;&amp;t.push(e[s]);return t}(l))}d.fn=n.prototype;const c={addClass:function(...e){const t=l(e.map((e=&gt;e.split(\" \"))));return this.forEach((e=&gt;{e.classList.add(...t)})),this},removeClass:function(...e){const t=l(e.map((e=&gt;e.split(\" \"))));return this.forEach((e=&gt;{e.classList.remove(...t)})),this},hasClass:function(...e){const t=l(e.map((e=&gt;e.split(\" \"))));return o(this,(e=&gt;t.filter((t=&gt;e.classList.contains(t))).length&gt;0)).length&gt;0},toggleClass:function(...e){const t=l(e.map((e=&gt;e.split(\" \"))));this.forEach((e=&gt;{t.forEach((t=&gt;{e.classList.toggle(t)}))}))},attr:function(e,t){if(1===arguments.length&amp;&amp;\"string\"==typeof e)return this[0]?this[0].getAttribute(e):void 0;for(let s=0;s&lt;this.length;s+=1)if(2===arguments.length)this[s].setAttribute(e,t);else for(const t in e)this[s][t]=e[t],this[s].setAttribute(t,e[t]);return this},removeAttr:function(e){for(let t=0;t&lt;this.length;t+=1)this[t].removeAttribute(e);return this},transform:function(e){for(let t=0;t&lt;this.length;t+=1)this[t].style.transform=e;return this},transition:function(e){for(let t=0;t&lt;this.length;t+=1)this[t].style.transitionDuration=\"string\"!=typeof e?`${e}ms`:e;return this},on:function(...e){let[t,s,a,i]=e;function r(e){const t=e.target;if(!t)return;const i=e.target.dom7EventData||[];if(i.indexOf(e)&lt;0&amp;&amp;i.unshift(e),d(t).is(s))a.apply(t,i);else{const e=d(t).parents();for(let t=0;t&lt;e.length;t+=1)d(e[t]).is(s)&amp;&amp;a.apply(e[t],i)}}function n(e){const t=e&amp;&amp;e.target&amp;&amp;e.target.dom7EventData||[];t.indexOf(e)&lt;0&amp;&amp;t.unshift(e),a.apply(this,t)}\"function\"==typeof e[1]&amp;&amp;([t,a,i]=e,s=void 0),i||(i=!1);const l=t.split(\" \");let o;for(let e=0;e&lt;this.length;e+=1){const t=this[e];if(s)for(o=0;o&lt;l.length;o+=1){const e=l[o];t.dom7LiveListeners||(t.dom7LiveListeners={}),t.dom7LiveListeners[e]||(t.dom7LiveListeners[e]=[]),t.dom7LiveListeners[e].push({listener:a,proxyListener:r}),t.addEventListener(e,r,i)}else for(o=0;o&lt;l.length;o+=1){const e=l[o];t.dom7Listeners||(t.dom7Listeners={}),t.dom7Listeners[e]||(t.dom7Listeners[e]=[]),t.dom7Listeners[e].push({listener:a,proxyListener:n}),t.addEventListener(e,n,i)}}return this},off:function(...e){let[t,s,a,i]=e;\"function\"==typeof e[1]&amp;&amp;([t,a,i]=e,s=void 0),i||(i=!1);const r=t.split(\" \");for(let e=0;e&lt;r.length;e+=1){const t=r[e];for(let e=0;e&lt;this.length;e+=1){const r=this[e];let n;if(!s&amp;&amp;r.dom7Listeners?n=r.dom7Listeners[t]:s&amp;&amp;r.dom7LiveListeners&amp;&amp;(n=r.dom7LiveListeners[t]),n&amp;&amp;n.length)for(let e=n.length-1;e&gt;=0;e-=1){const s=n[e];a&amp;&amp;s.listener===a||a&amp;&amp;s.listener&amp;&amp;s.listener.dom7proxy&amp;&amp;s.listener.dom7proxy===a?(r.removeEventListener(t,s.proxyListener,i),n.splice(e,1)):a||(r.removeEventListener(t,s.proxyListener,i),n.splice(e,1))}}}return this},trigger:function(...e){const t=r(),s=e[0].split(\" \"),a=e[1];for(let i=0;i&lt;s.length;i+=1){const r=s[i];for(let s=0;s&lt;this.length;s+=1){const i=this[s];if(t.CustomEvent){const s=new t.CustomEvent(r,{detail:a,bubbles:!0,cancelable:!0});i.dom7EventData=e.filter(((e,t)=&gt;t&gt;0)),i.dispatchEvent(s),i.dom7EventData=[],delete i.dom7EventData}}}return this},transitionEnd:function(e){const t=this;return e&amp;&amp;t.on(\"transitionend\",(function s(a){a.target===this&amp;&amp;(e.call(this,a),t.off(\"transitionend\",s))})),this},outerWidth:function(e){if(this.length&gt;0){if(e){const e=this.styles();return this[0].offsetWidth+parseFloat(e.getPropertyValue(\"margin-right\"))+parseFloat(e.getPropertyValue(\"margin-left\"))}return this[0].offsetWidth}return null},outerHeight:function(e){if(this.length&gt;0){if(e){const e=this.styles();return this[0].offsetHeight+parseFloat(e.getPropertyValue(\"margin-top\"))+parseFloat(e.getPropertyValue(\"margin-bottom\"))}return this[0].offsetHeight}return null},styles:function(){const e=r();return this[0]?e.getComputedStyle(this[0],null):{}},offset:function(){if(this.length&gt;0){const e=r(),t=a(),s=this[0],i=s.getBoundingClientRect(),n=t.body,l=s.clientTop||n.clientTop||0,o=s.clientLeft||n.clientLeft||0,d=s===e?e.scrollY:s.scrollTop,c=s===e?e.scrollX:s.scrollLeft;return{top:i.top+d-l,left:i.left+c-o}}return null},css:function(e,t){const s=r();let a;if(1===arguments.length){if(\"string\"!=typeof e){for(a=0;a&lt;this.length;a+=1)for(const t in e)this[a].style[t]=e[t];return this}if(this[0])return s.getComputedStyle(this[0],null).getPropertyValue(e)}if(2===arguments.length&amp;&amp;\"string\"==typeof e){for(a=0;a&lt;this.length;a+=1)this[a].style[e]=t;return this}return this},each:function(e){return e?(this.forEach(((t,s)=&gt;{e.apply(t,[t,s])})),this):this},html:function(e){if(void 0===e)return this[0]?this[0].innerHTML:null;for(let t=0;t&lt;this.length;t+=1)this[t].innerHTML=e;return this},text:function(e){if(void 0===e)return this[0]?this[0].textContent.trim():null;for(let t=0;t&lt;this.length;t+=1)this[t].textContent=e;return this},is:function(e){const t=r(),s=a(),i=this[0];let l,o;if(!i||void 0===e)return!1;if(\"string\"==typeof e){if(i.matches)return i.matches(e);if(i.webkitMatchesSelector)return i.webkitMatchesSelector(e);if(i.msMatchesSelector)return i.msMatchesSelector(e);for(l=d(e),o=0;o&lt;l.length;o+=1)if(l[o]===i)return!0;return!1}if(e===s)return i===s;if(e===t)return i===t;if(e.nodeType||e instanceof n){for(l=e.nodeType?[e]:e,o=0;o&lt;l.length;o+=1)if(l[o]===i)return!0;return!1}return!1},index:function(){let e,t=this[0];if(t){for(e=0;null!==(t=t.previousSibling);)1===t.nodeType&amp;&amp;(e+=1);return e}},eq:function(e){if(void 0===e)return this;const t=this.length;if(e&gt;t-1)return d([]);if(e&lt;0){const s=t+e;return d(s&lt;0?[]:[this[s]])}return d([this[e]])},append:function(...e){let t;const s=a();for(let a=0;a&lt;e.length;a+=1){t=e[a];for(let e=0;e&lt;this.length;e+=1)if(\"string\"==typeof t){const a=s.createElement(\"div\");for(a.innerHTML=t;a.firstChild;)this[e].appendChild(a.firstChild)}else if(t instanceof n)for(let s=0;s&lt;t.length;s+=1)this[e].appendChild(t[s]);else this[e].appendChild(t)}return this},prepend:function(e){const t=a();let s,i;for(s=0;s&lt;this.length;s+=1)if(\"string\"==typeof e){const a=t.createElement(\"div\");for(a.innerHTML=e,i=a.childNodes.length-1;i&gt;=0;i-=1)this[s].insertBefore(a.childNodes[i],this[s].childNodes[0])}else if(e instanceof n)for(i=0;i&lt;e.length;i+=1)this[s].insertBefore(e[i],this[s].childNodes[0]);else this[s].insertBefore(e,this[s].childNodes[0]);return this},next:function(e){return this.length&gt;0?e?this[0].nextElementSibling&amp;&amp;d(this[0].nextElementSibling).is(e)?d([this[0].nextElementSibling]):d([]):this[0].nextElementSibling?d([this[0].nextElementSibling]):d([]):d([])},nextAll:function(e){const t=[];let s=this[0];if(!s)return d([]);for(;s.nextElementSibling;){const a=s.nextElementSibling;e?d(a).is(e)&amp;&amp;t.push(a):t.push(a),s=a}return d(t)},prev:function(e){if(this.length&gt;0){const t=this[0];return e?t.previousElementSibling&amp;&amp;d(t.previousElementSibling).is(e)?d([t.previousElementSibling]):d([]):t.previousElementSibling?d([t.previousElementSibling]):d([])}return d([])},prevAll:function(e){const t=[];let s=this[0];if(!s)return d([]);for(;s.previousElementSibling;){const a=s.previousElementSibling;e?d(a).is(e)&amp;&amp;t.push(a):t.push(a),s=a}return d(t)},parent:function(e){const t=[];for(let s=0;s&lt;this.length;s+=1)null!==this[s].parentNode&amp;&amp;(e?d(this[s].parentNode).is(e)&amp;&amp;t.push(this[s].parentNode):t.push(this[s].parentNode));return d(t)},parents:function(e){const t=[];for(let s=0;s&lt;this.length;s+=1){let a=this[s].parentNode;for(;a;)e?d(a).is(e)&amp;&amp;t.push(a):t.push(a),a=a.parentNode}return d(t)},closest:function(e){let t=this;return void 0===e?d([]):(t.is(e)||(t=t.parents(e).eq(0)),t)},find:function(e){const t=[];for(let s=0;s&lt;this.length;s+=1){const a=this[s].querySelectorAll(e);for(let e=0;e&lt;a.length;e+=1)t.push(a[e])}return d(t)},children:function(e){const t=[];for(let s=0;s&lt;this.length;s+=1){const a=this[s].children;for(let s=0;s&lt;a.length;s+=1)e&amp;&amp;!d(a[s]).is(e)||t.push(a[s])}return d(t)},filter:function(e){return d(o(this,e))},remove:function(){for(let e=0;e&lt;this.length;e+=1)this[e].parentNode&amp;&amp;this[e].parentNode.removeChild(this[e]);return this}};function p(e,t=0){return setTimeout(e,t)}function u(){return Date.now()}function h(e,t=\"x\"){const s=r();let a,i,n;const l=function(e){const t=r();let s;return t.getComputedStyle&amp;&amp;(s=t.getComputedStyle(e,null)),!s&amp;&amp;e.currentStyle&amp;&amp;(s=e.currentStyle),s||(s=e.style),s}(e);return s.WebKitCSSMatrix?(i=l.transform||l.webkitTransform,i.split(\",\").length&gt;6&amp;&amp;(i=i.split(\", \").map((e=&gt;e.replace(\",\",\".\"))).join(\", \")),n=new s.WebKitCSSMatrix(\"none\"===i?\"\":i)):(n=l.MozTransform||l.OTransform||l.MsTransform||l.msTransform||l.transform||l.getPropertyValue(\"transform\").replace(\"translate(\",\"matrix(1, 0, 0, 1,\"),a=n.toString().split(\",\")),\"x\"===t&amp;&amp;(i=s.WebKitCSSMatrix?n.m41:16===a.length?parseFloat(a[12]):parseFloat(a[4])),\"y\"===t&amp;&amp;(i=s.WebKitCSSMatrix?n.m42:16===a.length?parseFloat(a[13]):parseFloat(a[5])),i||0}function m(e){return\"object\"==typeof e&amp;&amp;null!==e&amp;&amp;e.constructor&amp;&amp;\"Object\"===Object.prototype.toString.call(e).slice(8,-1)}function f(...e){const t=Object(e[0]),s=[\"__proto__\",\"constructor\",\"prototype\"];for(let i=1;i&lt;e.length;i+=1){const r=e[i];if(null!=r&amp;&amp;(a=r,!(\"undefined\"!=typeof window&amp;&amp;void 0!==window.HTMLElement?a instanceof HTMLElement:a&amp;&amp;(1===a.nodeType||11===a.nodeType)))){const e=Object.keys(Object(r)).filter((e=&gt;s.indexOf(e)&lt;0));for(let s=0,a=e.length;s&lt;a;s+=1){const a=e[s],i=Object.getOwnPropertyDescriptor(r,a);void 0!==i&amp;&amp;i.enumerable&amp;&amp;(m(t[a])&amp;&amp;m(r[a])?r[a].__swiper__?t[a]=r[a]:f(t[a],r[a]):!m(t[a])&amp;&amp;m(r[a])?(t[a]={},r[a].__swiper__?t[a]=r[a]:f(t[a],r[a])):t[a]=r[a])}}}var a;return t}function g(e,t,s){e.style.setProperty(t,s)}function v({swiper:e,targetPosition:t,side:s}){const a=r(),i=-e.translate;let n,l=null;const o=e.params.speed;e.wrapperEl.style.scrollSnapType=\"none\",a.cancelAnimationFrame(e.cssModeFrameID);const d=t&gt;i?\"next\":\"prev\",c=(e,t)=&gt;\"next\"===d&amp;&amp;e&gt;=t||\"prev\"===d&amp;&amp;e&lt;=t,p=()=&gt;{n=(new Date).getTime(),null===l&amp;&amp;(l=n);const r=Math.max(Math.min((n-l)/o,1),0),d=.5-Math.cos(r*Math.PI)/2;let u=i+d*(t-i);if(c(u,t)&amp;&amp;(u=t),e.wrapperEl.scrollTo({[s]:u}),c(u,t))return e.wrapperEl.style.overflow=\"hidden\",e.wrapperEl.style.scrollSnapType=\"\",setTimeout((()=&gt;{e.wrapperEl.style.overflow=\"\",e.wrapperEl.scrollTo({[s]:u})})),void a.cancelAnimationFrame(e.cssModeFrameID);e.cssModeFrameID=a.requestAnimationFrame(p)};p()}let w,b,x;function y(){return w||(w=function(){const e=r(),t=a();return{smoothScroll:t.documentElement&amp;&amp;\"scrollBehavior\"in t.documentElement.style,touch:!!(\"ontouchstart\"in e||e.DocumentTouch&amp;&amp;t instanceof e.DocumentTouch),passiveListener:function(){let t=!1;try{const s=Object.defineProperty({},\"passive\",{get(){t=!0}});e.addEventListener(\"testPassiveListener\",null,s)}catch(e){}return t}(),gestures:\"ongesturestart\"in e}}()),w}function E(e={}){return b||(b=function({userAgent:e}={}){const t=y(),s=r(),a=s.navigator.platform,i=e||s.navigator.userAgent,n={ios:!1,android:!1},l=s.screen.width,o=s.screen.height,d=i.match(/(Android);?[\\s\\/]+([\\d.]+)?/);let c=i.match(/(iPad).*OS\\s([\\d_]+)/);const p=i.match(/(iPod)(.*OS\\s([\\d_]+))?/),u=!c&amp;&amp;i.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/),h=\"Win32\"===a;let m=\"MacIntel\"===a;return!c&amp;&amp;m&amp;&amp;t.touch&amp;&amp;[\"1024x1366\",\"1366x1024\",\"834x1194\",\"1194x834\",\"834x1112\",\"1112x834\",\"768x1024\",\"1024x768\",\"820x1180\",\"1180x820\",\"810x1080\",\"1080x810\"].indexOf(`${l}x${o}`)&gt;=0&amp;&amp;(c=i.match(/(Version)\\/([\\d.]+)/),c||(c=[0,1,\"13_0_0\"]),m=!1),d&amp;&amp;!h&amp;&amp;(n.os=\"android\",n.android=!0),(c||u||p)&amp;&amp;(n.os=\"ios\",n.ios=!0),n}(e)),b}function T(){return x||(x=function(){const e=r();return{isSafari:function(){const t=e.navigator.userAgent.toLowerCase();return t.indexOf(\"safari\")&gt;=0&amp;&amp;t.indexOf(\"chrome\")&lt;0&amp;&amp;t.indexOf(\"android\")&lt;0}(),isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent)}}()),x}Object.keys(c).forEach((e=&gt;{Object.defineProperty(d.fn,e,{value:c[e],writable:!0})}));var C={on(e,t,s){const a=this;if(\"function\"!=typeof t)return a;const i=s?\"unshift\":\"push\";return e.split(\" \").forEach((e=&gt;{a.eventsListeners[e]||(a.eventsListeners[e]=[]),a.eventsListeners[e][i](t)})),a},once(e,t,s){const a=this;if(\"function\"!=typeof t)return a;function i(...s){a.off(e,i),i.__emitterProxy&amp;&amp;delete i.__emitterProxy,t.apply(a,s)}return i.__emitterProxy=t,a.on(e,i,s)},onAny(e,t){const s=this;if(\"function\"!=typeof e)return s;const a=t?\"unshift\":\"push\";return s.eventsAnyListeners.indexOf(e)&lt;0&amp;&amp;s.eventsAnyListeners[a](e),s},offAny(e){const t=this;if(!t.eventsAnyListeners)return t;const s=t.eventsAnyListeners.indexOf(e);return s&gt;=0&amp;&amp;t.eventsAnyListeners.splice(s,1),t},off(e,t){const s=this;return s.eventsListeners?(e.split(\" \").forEach((e=&gt;{void 0===t?s.eventsListeners[e]=[]:s.eventsListeners[e]&amp;&amp;s.eventsListeners[e].forEach(((a,i)=&gt;{(a===t||a.__emitterProxy&amp;&amp;a.__emitterProxy===t)&amp;&amp;s.eventsListeners[e].splice(i,1)}))})),s):s},emit(...e){const t=this;if(!t.eventsListeners)return t;let s,a,i;\"string\"==typeof e[0]||Array.isArray(e[0])?(s=e[0],a=e.slice(1,e.length),i=t):(s=e[0].events,a=e[0].data,i=e[0].context||t),a.unshift(i);return(Array.isArray(s)?s:s.split(\" \")).forEach((e=&gt;{t.eventsAnyListeners&amp;&amp;t.eventsAnyListeners.length&amp;&amp;t.eventsAnyListeners.forEach((t=&gt;{t.apply(i,[e,...a])})),t.eventsListeners&amp;&amp;t.eventsListeners[e]&amp;&amp;t.eventsListeners[e].forEach((e=&gt;{e.apply(i,a)}))})),t}};function $({swiper:e,runCallbacks:t,direction:s,step:a}){const{activeIndex:i,previousIndex:r}=e;let n=s;if(n||(n=i&gt;r?\"next\":i&lt;r?\"prev\":\"reset\"),e.emit(`transition${a}`),t&amp;&amp;i!==r){if(\"reset\"===n)return void e.emit(`slideResetTransition${a}`);e.emit(`slideChangeTransition${a}`),\"next\"===n?e.emit(`slideNextTransition${a}`):e.emit(`slidePrevTransition${a}`)}}function S(e){const t=this,s=a(),i=r(),n=t.touchEventsData,{params:l,touches:o,enabled:c}=t;if(!c)return;if(t.animating&amp;&amp;l.preventInteractionOnTransition)return;!t.animating&amp;&amp;l.cssMode&amp;&amp;l.loop&amp;&amp;t.loopFix();let p=e;p.originalEvent&amp;&amp;(p=p.originalEvent);let h=d(p.target);if(\"wrapper\"===l.touchEventsTarget&amp;&amp;!h.closest(t.wrapperEl).length)return;if(n.isTouchEvent=\"touchstart\"===p.type,!n.isTouchEvent&amp;&amp;\"which\"in p&amp;&amp;3===p.which)return;if(!n.isTouchEvent&amp;&amp;\"button\"in p&amp;&amp;p.button&gt;0)return;if(n.isTouched&amp;&amp;n.isMoved)return;!!l.noSwipingClass&amp;&amp;\"\"!==l.noSwipingClass&amp;&amp;p.target&amp;&amp;p.target.shadowRoot&amp;&amp;e.path&amp;&amp;e.path[0]&amp;&amp;(h=d(e.path[0]));const m=l.noSwipingSelector?l.noSwipingSelector:`.${l.noSwipingClass}`,f=!(!p.target||!p.target.shadowRoot);if(l.noSwiping&amp;&amp;(f?function(e,t=this){return function t(s){return s&amp;&amp;s!==a()&amp;&amp;s!==r()?(s.assignedSlot&amp;&amp;(s=s.assignedSlot),s.closest(e)||t(s.getRootNode().host)):null}(t)}(m,p.target):h.closest(m)[0]))return void(t.allowClick=!0);if(l.swipeHandler&amp;&amp;!h.closest(l.swipeHandler)[0])return;o.currentX=\"touchstart\"===p.type?p.targetTouches[0].pageX:p.pageX,o.currentY=\"touchstart\"===p.type?p.targetTouches[0].pageY:p.pageY;const g=o.currentX,v=o.currentY,w=l.edgeSwipeDetection||l.iOSEdgeSwipeDetection,b=l.edgeSwipeThreshold||l.iOSEdgeSwipeThreshold;if(w&amp;&amp;(g&lt;=b||g&gt;=i.innerWidth-b)){if(\"prevent\"!==w)return;e.preventDefault()}if(Object.assign(n,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),o.startX=g,o.startY=v,n.touchStartTime=u(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,l.threshold&gt;0&amp;&amp;(n.allowThresholdMove=!1),\"touchstart\"!==p.type){let e=!0;h.is(n.focusableElements)&amp;&amp;(e=!1),s.activeElement&amp;&amp;d(s.activeElement).is(n.focusableElements)&amp;&amp;s.activeElement!==h[0]&amp;&amp;s.activeElement.blur();const a=e&amp;&amp;t.allowTouchMove&amp;&amp;l.touchStartPreventDefault;!l.touchStartForcePreventDefault&amp;&amp;!a||h[0].isContentEditable||p.preventDefault()}t.emit(\"touchStart\",p)}function M(e){const t=a(),s=this,i=s.touchEventsData,{params:r,touches:n,rtlTranslate:l,enabled:o}=s;if(!o)return;let c=e;if(c.originalEvent&amp;&amp;(c=c.originalEvent),!i.isTouched)return void(i.startMoving&amp;&amp;i.isScrolling&amp;&amp;s.emit(\"touchMoveOpposite\",c));if(i.isTouchEvent&amp;&amp;\"touchmove\"!==c.type)return;const p=\"touchmove\"===c.type&amp;&amp;c.targetTouches&amp;&amp;(c.targetTouches[0]||c.changedTouches[0]),h=\"touchmove\"===c.type?p.pageX:c.pageX,m=\"touchmove\"===c.type?p.pageY:c.pageY;if(c.preventedByNestedSwiper)return n.startX=h,void(n.startY=m);if(!s.allowTouchMove)return s.allowClick=!1,void(i.isTouched&amp;&amp;(Object.assign(n,{startX:h,startY:m,currentX:h,currentY:m}),i.touchStartTime=u()));if(i.isTouchEvent&amp;&amp;r.touchReleaseOnEdges&amp;&amp;!r.loop)if(s.isVertical()){if(m&lt;n.startY&amp;&amp;s.translate&lt;=s.maxTranslate()||m&gt;n.startY&amp;&amp;s.translate&gt;=s.minTranslate())return i.isTouched=!1,void(i.isMoved=!1)}else if(h&lt;n.startX&amp;&amp;s.translate&lt;=s.maxTranslate()||h&gt;n.startX&amp;&amp;s.translate&gt;=s.minTranslate())return;if(i.isTouchEvent&amp;&amp;t.activeElement&amp;&amp;c.target===t.activeElement&amp;&amp;d(c.target).is(i.focusableElements))return i.isMoved=!0,void(s.allowClick=!1);if(i.allowTouchCallbacks&amp;&amp;s.emit(\"touchMove\",c),c.targetTouches&amp;&amp;c.targetTouches.length&gt;1)return;n.currentX=h,n.currentY=m;const f=n.currentX-n.startX,g=n.currentY-n.startY;if(s.params.threshold&amp;&amp;Math.sqrt(f**2+g**2)&lt;s.params.threshold)return;if(void 0===i.isScrolling){let e;s.isHorizontal()&amp;&amp;n.currentY===n.startY||s.isVertical()&amp;&amp;n.currentX===n.startX?i.isScrolling=!1:f*f+g*g&gt;=25&amp;&amp;(e=180*Math.atan2(Math.abs(g),Math.abs(f))/Math.PI,i.isScrolling=s.isHorizontal()?e&gt;r.touchAngle:90-e&gt;r.touchAngle)}if(i.isScrolling&amp;&amp;s.emit(\"touchMoveOpposite\",c),void 0===i.startMoving&amp;&amp;(n.currentX===n.startX&amp;&amp;n.currentY===n.startY||(i.startMoving=!0)),i.isScrolling)return void(i.isTouched=!1);if(!i.startMoving)return;s.allowClick=!1,!r.cssMode&amp;&amp;c.cancelable&amp;&amp;c.preventDefault(),r.touchMoveStopPropagation&amp;&amp;!r.nested&amp;&amp;c.stopPropagation(),i.isMoved||(r.loop&amp;&amp;!r.cssMode&amp;&amp;s.loopFix(),i.startTranslate=s.getTranslate(),s.setTransition(0),s.animating&amp;&amp;s.$wrapperEl.trigger(\"webkitTransitionEnd transitionend\"),i.allowMomentumBounce=!1,!r.grabCursor||!0!==s.allowSlideNext&amp;&amp;!0!==s.allowSlidePrev||s.setGrabCursor(!0),s.emit(\"sliderFirstMove\",c)),s.emit(\"sliderMove\",c),i.isMoved=!0;let v=s.isHorizontal()?f:g;n.diff=v,v*=r.touchRatio,l&amp;&amp;(v=-v),s.swipeDirection=v&gt;0?\"prev\":\"next\",i.currentTranslate=v+i.startTranslate;let w=!0,b=r.resistanceRatio;if(r.touchReleaseOnEdges&amp;&amp;(b=0),v&gt;0&amp;&amp;i.currentTranslate&gt;s.minTranslate()?(w=!1,r.resistance&amp;&amp;(i.currentTranslate=s.minTranslate()-1+(-s.minTranslate()+i.startTranslate+v)**b)):v&lt;0&amp;&amp;i.currentTranslate&lt;s.maxTranslate()&amp;&amp;(w=!1,r.resistance&amp;&amp;(i.currentTranslate=s.maxTranslate()+1-(s.maxTranslate()-i.startTranslate-v)**b)),w&amp;&amp;(c.preventedByNestedSwiper=!0),!s.allowSlideNext&amp;&amp;\"next\"===s.swipeDirection&amp;&amp;i.currentTranslate&lt;i.startTranslate&amp;&amp;(i.currentTranslate=i.startTranslate),!s.allowSlidePrev&amp;&amp;\"prev\"===s.swipeDirection&amp;&amp;i.currentTranslate&gt;i.startTranslate&amp;&amp;(i.currentTranslate=i.startTranslate),s.allowSlidePrev||s.allowSlideNext||(i.currentTranslate=i.startTranslate),r.threshold&gt;0){if(!(Math.abs(v)&gt;r.threshold||i.allowThresholdMove))return void(i.currentTranslate=i.startTranslate);if(!i.allowThresholdMove)return i.allowThresholdMove=!0,n.startX=n.currentX,n.startY=n.currentY,i.currentTranslate=i.startTranslate,void(n.diff=s.isHorizontal()?n.currentX-n.startX:n.currentY-n.startY)}r.followFinger&amp;&amp;!r.cssMode&amp;&amp;((r.freeMode&amp;&amp;r.freeMode.enabled&amp;&amp;s.freeMode||r.watchSlidesProgress)&amp;&amp;(s.updateActiveIndex(),s.updateSlidesClasses()),s.params.freeMode&amp;&amp;r.freeMode.enabled&amp;&amp;s.freeMode&amp;&amp;s.freeMode.onTouchMove(),s.updateProgress(i.currentTranslate),s.setTranslate(i.currentTranslate))}function P(e){const t=this,s=t.touchEventsData,{params:a,touches:i,rtlTranslate:r,slidesGrid:n,enabled:l}=t;if(!l)return;let o=e;if(o.originalEvent&amp;&amp;(o=o.originalEvent),s.allowTouchCallbacks&amp;&amp;t.emit(\"touchEnd\",o),s.allowTouchCallbacks=!1,!s.isTouched)return s.isMoved&amp;&amp;a.grabCursor&amp;&amp;t.setGrabCursor(!1),s.isMoved=!1,void(s.startMoving=!1);a.grabCursor&amp;&amp;s.isMoved&amp;&amp;s.isTouched&amp;&amp;(!0===t.allowSlideNext||!0===t.allowSlidePrev)&amp;&amp;t.setGrabCursor(!1);const d=u(),c=d-s.touchStartTime;if(t.allowClick&amp;&amp;(t.updateClickedSlide(o),t.emit(\"tap click\",o),c&lt;300&amp;&amp;d-s.lastClickTime&lt;300&amp;&amp;t.emit(\"doubleTap doubleClick\",o)),s.lastClickTime=u(),p((()=&gt;{t.destroyed||(t.allowClick=!0)})),!s.isTouched||!s.isMoved||!t.swipeDirection||0===i.diff||s.currentTranslate===s.startTranslate)return s.isTouched=!1,s.isMoved=!1,void(s.startMoving=!1);let h;if(s.isTouched=!1,s.isMoved=!1,s.startMoving=!1,h=a.followFinger?r?t.translate:-t.translate:-s.currentTranslate,a.cssMode)return;if(t.params.freeMode&amp;&amp;a.freeMode.enabled)return void t.freeMode.onTouchEnd({currentPos:h});let m=0,f=t.slidesSizesGrid[0];for(let e=0;e&lt;n.length;e+=e&lt;a.slidesPerGroupSkip?1:a.slidesPerGroup){const t=e&lt;a.slidesPerGroupSkip-1?1:a.slidesPerGroup;void 0!==n[e+t]?h&gt;=n[e]&amp;&amp;h&lt;n[e+t]&amp;&amp;(m=e,f=n[e+t]-n[e]):h&gt;=n[e]&amp;&amp;(m=e,f=n[n.length-1]-n[n.length-2])}const g=(h-n[m])/f,v=m&lt;a.slidesPerGroupSkip-1?1:a.slidesPerGroup;if(c&gt;a.longSwipesMs){if(!a.longSwipes)return void t.slideTo(t.activeIndex);\"next\"===t.swipeDirection&amp;&amp;(g&gt;=a.longSwipesRatio?t.slideTo(m+v):t.slideTo(m)),\"prev\"===t.swipeDirection&amp;&amp;(g&gt;1-a.longSwipesRatio?t.slideTo(m+v):t.slideTo(m))}else{if(!a.shortSwipes)return void t.slideTo(t.activeIndex);t.navigation&amp;&amp;(o.target===t.navigation.nextEl||o.target===t.navigation.prevEl)?o.target===t.navigation.nextEl?t.slideTo(m+v):t.slideTo(m):(\"next\"===t.swipeDirection&amp;&amp;t.slideTo(m+v),\"prev\"===t.swipeDirection&amp;&amp;t.slideTo(m))}}function k(){const e=this,{params:t,el:s}=e;if(s&amp;&amp;0===s.offsetWidth)return;t.breakpoints&amp;&amp;e.setBreakpoint();const{allowSlideNext:a,allowSlidePrev:i,snapGrid:r}=e;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses(),(\"auto\"===t.slidesPerView||t.slidesPerView&gt;1)&amp;&amp;e.isEnd&amp;&amp;!e.isBeginning&amp;&amp;!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),e.autoplay&amp;&amp;e.autoplay.running&amp;&amp;e.autoplay.paused&amp;&amp;e.autoplay.run(),e.allowSlidePrev=i,e.allowSlideNext=a,e.params.watchOverflow&amp;&amp;r!==e.snapGrid&amp;&amp;e.checkOverflow()}function z(e){const t=this;t.enabled&amp;&amp;(t.allowClick||(t.params.preventClicks&amp;&amp;e.preventDefault(),t.params.preventClicksPropagation&amp;&amp;t.animating&amp;&amp;(e.stopPropagation(),e.stopImmediatePropagation())))}function O(){const e=this,{wrapperEl:t,rtlTranslate:s,enabled:a}=e;if(!a)return;let i;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,-0===e.translate&amp;&amp;(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();const r=e.maxTranslate()-e.minTranslate();i=0===r?0:(e.translate-e.minTranslate())/r,i!==e.progress&amp;&amp;e.updateProgress(s?-e.translate:e.translate),e.emit(\"setTranslate\",e.translate,!1)}let I=!1;function L(){}const A=(e,t)=&gt;{const s=a(),{params:i,touchEvents:r,el:n,wrapperEl:l,device:o,support:d}=e,c=!!i.nested,p=\"on\"===t?\"addEventListener\":\"removeEventListener\",u=t;if(d.touch){const t=!(\"touchstart\"!==r.start||!d.passiveListener||!i.passiveListeners)&amp;&amp;{passive:!0,capture:!1};n[p](r.start,e.onTouchStart,t),n[p](r.move,e.onTouchMove,d.passiveListener?{passive:!1,capture:c}:c),n[p](r.end,e.onTouchEnd,t),r.cancel&amp;&amp;n[p](r.cancel,e.onTouchEnd,t)}else n[p](r.start,e.onTouchStart,!1),s[p](r.move,e.onTouchMove,c),s[p](r.end,e.onTouchEnd,!1);(i.preventClicks||i.preventClicksPropagation)&amp;&amp;n[p](\"click\",e.onClick,!0),i.cssMode&amp;&amp;l[p](\"scroll\",e.onScroll),i.updateOnWindowResize?e[u](o.ios||o.android?\"resize orientationchange observerUpdate\":\"resize observerUpdate\",k,!0):e[u](\"observerUpdate\",k,!0)};const D=(e,t)=&gt;e.grid&amp;&amp;t.grid&amp;&amp;t.grid.rows&gt;1;var G={init:!0,direction:\"horizontal\",touchEventsTarget:\"wrapper\",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,enabled:!0,focusableElements:\"input, select, option, textarea, button, video, label\",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:\"slide\",breakpoints:void 0,breakpointsBase:\"window\",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:0,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,preloadImages:!0,updateOnImagesReady:!0,loop:!1,loopAdditionalSlides:0,loopedSlides:null,loopFillGroupWithBlank:!1,loopPreventsSlide:!0,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:\"swiper-no-swiping\",noSwipingSelector:null,passiveListeners:!0,containerModifierClass:\"swiper-\",slideClass:\"swiper-slide\",slideBlankClass:\"swiper-slide-invisible-blank\",slideActiveClass:\"swiper-slide-active\",slideDuplicateActiveClass:\"swiper-slide-duplicate-active\",slideVisibleClass:\"swiper-slide-visible\",slideDuplicateClass:\"swiper-slide-duplicate\",slideNextClass:\"swiper-slide-next\",slideDuplicateNextClass:\"swiper-slide-duplicate-next\",slidePrevClass:\"swiper-slide-prev\",slideDuplicatePrevClass:\"swiper-slide-duplicate-prev\",wrapperClass:\"swiper-wrapper\",runCallbacksOnInit:!0,_emitClasses:!1};function N(e,t){return function(s={}){const a=Object.keys(s)[0],i=s[a];\"object\"==typeof i&amp;&amp;null!==i?([\"navigation\",\"pagination\",\"scrollbar\"].indexOf(a)&gt;=0&amp;&amp;!0===e[a]&amp;&amp;(e[a]={auto:!0}),a in e&amp;&amp;\"enabled\"in i?(!0===e[a]&amp;&amp;(e[a]={enabled:!0}),\"object\"!=typeof e[a]||\"enabled\"in e[a]||(e[a].enabled=!0),e[a]||(e[a]={enabled:!1}),f(t,s)):f(t,s)):f(t,s)}}const B={eventsEmitter:C,update:{updateSize:function(){const e=this;let t,s;const a=e.$el;t=void 0!==e.params.width&amp;&amp;null!==e.params.width?e.params.width:a[0].clientWidth,s=void 0!==e.params.height&amp;&amp;null!==e.params.height?e.params.height:a[0].clientHeight,0===t&amp;&amp;e.isHorizontal()||0===s&amp;&amp;e.isVertical()||(t=t-parseInt(a.css(\"padding-left\")||0,10)-parseInt(a.css(\"padding-right\")||0,10),s=s-parseInt(a.css(\"padding-top\")||0,10)-parseInt(a.css(\"padding-bottom\")||0,10),Number.isNaN(t)&amp;&amp;(t=0),Number.isNaN(s)&amp;&amp;(s=0),Object.assign(e,{width:t,height:s,size:e.isHorizontal()?t:s}))},updateSlides:function(){const e=this;function t(t){return e.isHorizontal()?t:{width:\"height\",\"margin-top\":\"margin-left\",\"margin-bottom \":\"margin-right\",\"margin-left\":\"margin-top\",\"margin-right\":\"margin-bottom\",\"padding-left\":\"padding-top\",\"padding-right\":\"padding-bottom\",marginRight:\"marginBottom\"}[t]}function s(e,s){return parseFloat(e.getPropertyValue(t(s))||0)}const a=e.params,{$wrapperEl:i,size:r,rtlTranslate:n,wrongRTL:l}=e,o=e.virtual&amp;&amp;a.virtual.enabled,d=o?e.virtual.slides.length:e.slides.length,c=i.children(`.${e.params.slideClass}`),p=o?e.virtual.slides.length:c.length;let u=[];const h=[],m=[];let f=a.slidesOffsetBefore;\"function\"==typeof f&amp;&amp;(f=a.slidesOffsetBefore.call(e));let v=a.slidesOffsetAfter;\"function\"==typeof v&amp;&amp;(v=a.slidesOffsetAfter.call(e));const w=e.snapGrid.length,b=e.slidesGrid.length;let x=a.spaceBetween,y=-f,E=0,T=0;if(void 0===r)return;\"string\"==typeof x&amp;&amp;x.indexOf(\"%\")&gt;=0&amp;&amp;(x=parseFloat(x.replace(\"%\",\"\"))/100*r),e.virtualSize=-x,n?c.css({marginLeft:\"\",marginBottom:\"\",marginTop:\"\"}):c.css({marginRight:\"\",marginBottom:\"\",marginTop:\"\"}),a.centeredSlides&amp;&amp;a.cssMode&amp;&amp;(g(e.wrapperEl,\"--swiper-centered-offset-before\",\"\"),g(e.wrapperEl,\"--swiper-centered-offset-after\",\"\"));const C=a.grid&amp;&amp;a.grid.rows&gt;1&amp;&amp;e.grid;let $;C&amp;&amp;e.grid.initSlides(p);const S=\"auto\"===a.slidesPerView&amp;&amp;a.breakpoints&amp;&amp;Object.keys(a.breakpoints).filter((e=&gt;void 0!==a.breakpoints[e].slidesPerView)).length&gt;0;for(let i=0;i&lt;p;i+=1){$=0;const n=c.eq(i);if(C&amp;&amp;e.grid.updateSlide(i,n,p,t),\"none\"!==n.css(\"display\")){if(\"auto\"===a.slidesPerView){S&amp;&amp;(c[i].style[t(\"width\")]=\"\");const r=getComputedStyle(n[0]),l=n[0].style.transform,o=n[0].style.webkitTransform;if(l&amp;&amp;(n[0].style.transform=\"none\"),o&amp;&amp;(n[0].style.webkitTransform=\"none\"),a.roundLengths)$=e.isHorizontal()?n.outerWidth(!0):n.outerHeight(!0);else{const e=s(r,\"width\"),t=s(r,\"padding-left\"),a=s(r,\"padding-right\"),i=s(r,\"margin-left\"),l=s(r,\"margin-right\"),o=r.getPropertyValue(\"box-sizing\");if(o&amp;&amp;\"border-box\"===o)$=e+i+l;else{const{clientWidth:s,offsetWidth:r}=n[0];$=e+t+a+i+l+(r-s)}}l&amp;&amp;(n[0].style.transform=l),o&amp;&amp;(n[0].style.webkitTransform=o),a.roundLengths&amp;&amp;($=Math.floor($))}else $=(r-(a.slidesPerView-1)*x)/a.slidesPerView,a.roundLengths&amp;&amp;($=Math.floor($)),c[i]&amp;&amp;(c[i].style[t(\"width\")]=`${$}px`);c[i]&amp;&amp;(c[i].swiperSlideSize=$),m.push($),a.centeredSlides?(y=y+$/2+E/2+x,0===E&amp;&amp;0!==i&amp;&amp;(y=y-r/2-x),0===i&amp;&amp;(y=y-r/2-x),Math.abs(y)&lt;.001&amp;&amp;(y=0),a.roundLengths&amp;&amp;(y=Math.floor(y)),T%a.slidesPerGroup==0&amp;&amp;u.push(y),h.push(y)):(a.roundLengths&amp;&amp;(y=Math.floor(y)),(T-Math.min(e.params.slidesPerGroupSkip,T))%e.params.slidesPerGroup==0&amp;&amp;u.push(y),h.push(y),y=y+$+x),e.virtualSize+=$+x,E=$,T+=1}}if(e.virtualSize=Math.max(e.virtualSize,r)+v,n&amp;&amp;l&amp;&amp;(\"slide\"===a.effect||\"coverflow\"===a.effect)&amp;&amp;i.css({width:`${e.virtualSize+a.spaceBetween}px`}),a.setWrapperSize&amp;&amp;i.css({[t(\"width\")]:`${e.virtualSize+a.spaceBetween}px`}),C&amp;&amp;e.grid.updateWrapperSize($,u,t),!a.centeredSlides){const t=[];for(let s=0;s&lt;u.length;s+=1){let i=u[s];a.roundLengths&amp;&amp;(i=Math.floor(i)),u[s]&lt;=e.virtualSize-r&amp;&amp;t.push(i)}u=t,Math.floor(e.virtualSize-r)-Math.floor(u[u.length-1])&gt;1&amp;&amp;u.push(e.virtualSize-r)}if(0===u.length&amp;&amp;(u=[0]),0!==a.spaceBetween){const s=e.isHorizontal()&amp;&amp;n?\"marginLeft\":t(\"marginRight\");c.filter(((e,t)=&gt;!a.cssMode||t!==c.length-1)).css({[s]:`${x}px`})}if(a.centeredSlides&amp;&amp;a.centeredSlidesBounds){let e=0;m.forEach((t=&gt;{e+=t+(a.spaceBetween?a.spaceBetween:0)})),e-=a.spaceBetween;const t=e-r;u=u.map((e=&gt;e&lt;0?-f:e&gt;t?t+v:e))}if(a.centerInsufficientSlides){let e=0;if(m.forEach((t=&gt;{e+=t+(a.spaceBetween?a.spaceBetween:0)})),e-=a.spaceBetween,e&lt;r){const t=(r-e)/2;u.forEach(((e,s)=&gt;{u[s]=e-t})),h.forEach(((e,s)=&gt;{h[s]=e+t}))}}if(Object.assign(e,{slides:c,snapGrid:u,slidesGrid:h,slidesSizesGrid:m}),a.centeredSlides&amp;&amp;a.cssMode&amp;&amp;!a.centeredSlidesBounds){g(e.wrapperEl,\"--swiper-centered-offset-before\",-u[0]+\"px\"),g(e.wrapperEl,\"--swiper-centered-offset-after\",e.size/2-m[m.length-1]/2+\"px\");const t=-e.snapGrid[0],s=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map((e=&gt;e+t)),e.slidesGrid=e.slidesGrid.map((e=&gt;e+s))}p!==d&amp;&amp;e.emit(\"slidesLengthChange\"),u.length!==w&amp;&amp;(e.params.watchOverflow&amp;&amp;e.checkOverflow(),e.emit(\"snapGridLengthChange\")),h.length!==b&amp;&amp;e.emit(\"slidesGridLengthChange\"),a.watchSlidesProgress&amp;&amp;e.updateSlidesOffset()},updateAutoHeight:function(e){const t=this,s=[],a=t.virtual&amp;&amp;t.params.virtual.enabled;let i,r=0;\"number\"==typeof e?t.setTransition(e):!0===e&amp;&amp;t.setTransition(t.params.speed);const n=e=&gt;a?t.slides.filter((t=&gt;parseInt(t.getAttribute(\"data-swiper-slide-index\"),10)===e))[0]:t.slides.eq(e)[0];if(\"auto\"!==t.params.slidesPerView&amp;&amp;t.params.slidesPerView&gt;1)if(t.params.centeredSlides)t.visibleSlides.each((e=&gt;{s.push(e)}));else for(i=0;i&lt;Math.ceil(t.params.slidesPerView);i+=1){const e=t.activeIndex+i;if(e&gt;t.slides.length&amp;&amp;!a)break;s.push(n(e))}else s.push(n(t.activeIndex));for(i=0;i&lt;s.length;i+=1)if(void 0!==s[i]){const e=s[i].offsetHeight;r=e&gt;r?e:r}r&amp;&amp;t.$wrapperEl.css(\"height\",`${r}px`)},updateSlidesOffset:function(){const e=this,t=e.slides;for(let s=0;s&lt;t.length;s+=1)t[s].swiperSlideOffset=e.isHorizontal()?t[s].offsetLeft:t[s].offsetTop},updateSlidesProgress:function(e=this&amp;&amp;this.translate||0){const t=this,s=t.params,{slides:a,rtlTranslate:i}=t;if(0===a.length)return;void 0===a[0].swiperSlideOffset&amp;&amp;t.updateSlidesOffset();let r=-e;i&amp;&amp;(r=e),a.removeClass(s.slideVisibleClass),t.visibleSlidesIndexes=[],t.visibleSlides=[];for(let e=0;e&lt;a.length;e+=1){const n=a[e];let l=n.swiperSlideOffset;s.cssMode&amp;&amp;s.centeredSlides&amp;&amp;(l-=a[0].swiperSlideOffset);const o=(r+(s.centeredSlides?t.minTranslate():0)-l)/(n.swiperSlideSize+s.spaceBetween),d=-(r-l),c=d+t.slidesSizesGrid[e];(d&gt;=0&amp;&amp;d&lt;t.size-1||c&gt;1&amp;&amp;c&lt;=t.size||d&lt;=0&amp;&amp;c&gt;=t.size)&amp;&amp;(t.visibleSlides.push(n),t.visibleSlidesIndexes.push(e),a.eq(e).addClass(s.slideVisibleClass)),n.progress=i?-o:o}t.visibleSlides=d(t.visibleSlides)},updateProgress:function(e){const t=this;if(void 0===e){const s=t.rtlTranslate?-1:1;e=t&amp;&amp;t.translate&amp;&amp;t.translate*s||0}const s=t.params,a=t.maxTranslate()-t.minTranslate();let{progress:i,isBeginning:r,isEnd:n}=t;const l=r,o=n;0===a?(i=0,r=!0,n=!0):(i=(e-t.minTranslate())/a,r=i&lt;=0,n=i&gt;=1),Object.assign(t,{progress:i,isBeginning:r,isEnd:n}),(s.watchSlidesProgress||s.centeredSlides&amp;&amp;s.autoHeight)&amp;&amp;t.updateSlidesProgress(e),r&amp;&amp;!l&amp;&amp;t.emit(\"reachBeginning toEdge\"),n&amp;&amp;!o&amp;&amp;t.emit(\"reachEnd toEdge\"),(l&amp;&amp;!r||o&amp;&amp;!n)&amp;&amp;t.emit(\"fromEdge\"),t.emit(\"progress\",i)},updateSlidesClasses:function(){const e=this,{slides:t,params:s,$wrapperEl:a,activeIndex:i,realIndex:r}=e,n=e.virtual&amp;&amp;s.virtual.enabled;let l;t.removeClass(`${s.slideActiveClass} ${s.slideNextClass} ${s.slidePrevClass} ${s.slideDuplicateActiveClass} ${s.slideDuplicateNextClass} ${s.slideDuplicatePrevClass}`),l=n?e.$wrapperEl.find(`.${s.slideClass}[data-swiper-slide-index=\"${i}\"]`):t.eq(i),l.addClass(s.slideActiveClass),s.loop&amp;&amp;(l.hasClass(s.slideDuplicateClass)?a.children(`.${s.slideClass}:not(.${s.slideDuplicateClass})[data-swiper-slide-index=\"${r}\"]`).addClass(s.slideDuplicateActiveClass):a.children(`.${s.slideClass}.${s.slideDuplicateClass}[data-swiper-slide-index=\"${r}\"]`).addClass(s.slideDuplicateActiveClass));let o=l.nextAll(`.${s.slideClass}`).eq(0).addClass(s.slideNextClass);s.loop&amp;&amp;0===o.length&amp;&amp;(o=t.eq(0),o.addClass(s.slideNextClass));let d=l.prevAll(`.${s.slideClass}`).eq(0).addClass(s.slidePrevClass);s.loop&amp;&amp;0===d.length&amp;&amp;(d=t.eq(-1),d.addClass(s.slidePrevClass)),s.loop&amp;&amp;(o.hasClass(s.slideDuplicateClass)?a.children(`.${s.slideClass}:not(.${s.slideDuplicateClass})[data-swiper-slide-index=\"${o.attr(\"data-swiper-slide-index\")}\"]`).addClass(s.slideDuplicateNextClass):a.children(`.${s.slideClass}.${s.slideDuplicateClass}[data-swiper-slide-index=\"${o.attr(\"data-swiper-slide-index\")}\"]`).addClass(s.slideDuplicateNextClass),d.hasClass(s.slideDuplicateClass)?a.children(`.${s.slideClass}:not(.${s.slideDuplicateClass})[data-swiper-slide-index=\"${d.attr(\"data-swiper-slide-index\")}\"]`).addClass(s.slideDuplicatePrevClass):a.children(`.${s.slideClass}.${s.slideDuplicateClass}[data-swiper-slide-index=\"${d.attr(\"data-swiper-slide-index\")}\"]`).addClass(s.slideDuplicatePrevClass)),e.emitSlidesClasses()},updateActiveIndex:function(e){const t=this,s=t.rtlTranslate?t.translate:-t.translate,{slidesGrid:a,snapGrid:i,params:r,activeIndex:n,realIndex:l,snapIndex:o}=t;let d,c=e;if(void 0===c){for(let e=0;e&lt;a.length;e+=1)void 0!==a[e+1]?s&gt;=a[e]&amp;&amp;s&lt;a[e+1]-(a[e+1]-a[e])/2?c=e:s&gt;=a[e]&amp;&amp;s&lt;a[e+1]&amp;&amp;(c=e+1):s&gt;=a[e]&amp;&amp;(c=e);r.normalizeSlideIndex&amp;&amp;(c&lt;0||void 0===c)&amp;&amp;(c=0)}if(i.indexOf(s)&gt;=0)d=i.indexOf(s);else{const e=Math.min(r.slidesPerGroupSkip,c);d=e+Math.floor((c-e)/r.slidesPerGroup)}if(d&gt;=i.length&amp;&amp;(d=i.length-1),c===n)return void(d!==o&amp;&amp;(t.snapIndex=d,t.emit(\"snapIndexChange\")));const p=parseInt(t.slides.eq(c).attr(\"data-swiper-slide-index\")||c,10);Object.assign(t,{snapIndex:d,realIndex:p,previousIndex:n,activeIndex:c}),t.emit(\"activeIndexChange\"),t.emit(\"snapIndexChange\"),l!==p&amp;&amp;t.emit(\"realIndexChange\"),(t.initialized||t.params.runCallbacksOnInit)&amp;&amp;t.emit(\"slideChange\")},updateClickedSlide:function(e){const t=this,s=t.params,a=d(e.target).closest(`.${s.slideClass}`)[0];let i,r=!1;if(a)for(let e=0;e&lt;t.slides.length;e+=1)if(t.slides[e]===a){r=!0,i=e;break}if(!a||!r)return t.clickedSlide=void 0,void(t.clickedIndex=void 0);t.clickedSlide=a,t.virtual&amp;&amp;t.params.virtual.enabled?t.clickedIndex=parseInt(d(a).attr(\"data-swiper-slide-index\"),10):t.clickedIndex=i,s.slideToClickedSlide&amp;&amp;void 0!==t.clickedIndex&amp;&amp;t.clickedIndex!==t.activeIndex&amp;&amp;t.slideToClickedSlide()}},translate:{getTranslate:function(e=(this.isHorizontal()?\"x\":\"y\")){const{params:t,rtlTranslate:s,translate:a,$wrapperEl:i}=this;if(t.virtualTranslate)return s?-a:a;if(t.cssMode)return a;let r=h(i[0],e);return s&amp;&amp;(r=-r),r||0},setTranslate:function(e,t){const s=this,{rtlTranslate:a,params:i,$wrapperEl:r,wrapperEl:n,progress:l}=s;let o,d=0,c=0;s.isHorizontal()?d=a?-e:e:c=e,i.roundLengths&amp;&amp;(d=Math.floor(d),c=Math.floor(c)),i.cssMode?n[s.isHorizontal()?\"scrollLeft\":\"scrollTop\"]=s.isHorizontal()?-d:-c:i.virtualTranslate||r.transform(`translate3d(${d}px, ${c}px, 0px)`),s.previousTranslate=s.translate,s.translate=s.isHorizontal()?d:c;const p=s.maxTranslate()-s.minTranslate();o=0===p?0:(e-s.minTranslate())/p,o!==l&amp;&amp;s.updateProgress(e),s.emit(\"setTranslate\",s.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e=0,t=this.params.speed,s=!0,a=!0,i){const r=this,{params:n,wrapperEl:l}=r;if(r.animating&amp;&amp;n.preventInteractionOnTransition)return!1;const o=r.minTranslate(),d=r.maxTranslate();let c;if(c=a&amp;&amp;e&gt;o?o:a&amp;&amp;e&lt;d?d:e,r.updateProgress(c),n.cssMode){const e=r.isHorizontal();if(0===t)l[e?\"scrollLeft\":\"scrollTop\"]=-c;else{if(!r.support.smoothScroll)return v({swiper:r,targetPosition:-c,side:e?\"left\":\"top\"}),!0;l.scrollTo({[e?\"left\":\"top\"]:-c,behavior:\"smooth\"})}return!0}return 0===t?(r.setTransition(0),r.setTranslate(c),s&amp;&amp;(r.emit(\"beforeTransitionStart\",t,i),r.emit(\"transitionEnd\"))):(r.setTransition(t),r.setTranslate(c),s&amp;&amp;(r.emit(\"beforeTransitionStart\",t,i),r.emit(\"transitionStart\")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(e){r&amp;&amp;!r.destroyed&amp;&amp;e.target===this&amp;&amp;(r.$wrapperEl[0].removeEventListener(\"transitionend\",r.onTranslateToWrapperTransitionEnd),r.$wrapperEl[0].removeEventListener(\"webkitTransitionEnd\",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,s&amp;&amp;r.emit(\"transitionEnd\"))}),r.$wrapperEl[0].addEventListener(\"transitionend\",r.onTranslateToWrapperTransitionEnd),r.$wrapperEl[0].addEventListener(\"webkitTransitionEnd\",r.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){const s=this;s.params.cssMode||s.$wrapperEl.transition(e),s.emit(\"setTransition\",e,t)},transitionStart:function(e=!0,t){const s=this,{params:a}=s;a.cssMode||(a.autoHeight&amp;&amp;s.updateAutoHeight(),$({swiper:s,runCallbacks:e,direction:t,step:\"Start\"}))},transitionEnd:function(e=!0,t){const s=this,{params:a}=s;s.animating=!1,a.cssMode||(s.setTransition(0),$({swiper:s,runCallbacks:e,direction:t,step:\"End\"}))}},slide:{slideTo:function(e=0,t=this.params.speed,s=!0,a,i){if(\"number\"!=typeof e&amp;&amp;\"string\"!=typeof e)throw new Error(`The 'index' argument cannot have type other than 'number' or 'string'. [${typeof e}] given.`);if(\"string\"==typeof e){const t=parseInt(e,10);if(!isFinite(t))throw new Error(`The passed-in 'index' (string) couldn't be converted to 'number'. [${e}] given.`);e=t}const r=this;let n=e;n&lt;0&amp;&amp;(n=0);const{params:l,snapGrid:o,slidesGrid:d,previousIndex:c,activeIndex:p,rtlTranslate:u,wrapperEl:h,enabled:m}=r;if(r.animating&amp;&amp;l.preventInteractionOnTransition||!m&amp;&amp;!a&amp;&amp;!i)return!1;const f=Math.min(r.params.slidesPerGroupSkip,n);let g=f+Math.floor((n-f)/r.params.slidesPerGroup);g&gt;=o.length&amp;&amp;(g=o.length-1),(p||l.initialSlide||0)===(c||0)&amp;&amp;s&amp;&amp;r.emit(\"beforeSlideChangeStart\");const w=-o[g];if(r.updateProgress(w),l.normalizeSlideIndex)for(let e=0;e&lt;d.length;e+=1){const t=-Math.floor(100*w),s=Math.floor(100*d[e]),a=Math.floor(100*d[e+1]);void 0!==d[e+1]?t&gt;=s&amp;&amp;t&lt;a-(a-s)/2?n=e:t&gt;=s&amp;&amp;t&lt;a&amp;&amp;(n=e+1):t&gt;=s&amp;&amp;(n=e)}if(r.initialized&amp;&amp;n!==p){if(!r.allowSlideNext&amp;&amp;w&lt;r.translate&amp;&amp;w&lt;r.minTranslate())return!1;if(!r.allowSlidePrev&amp;&amp;w&gt;r.translate&amp;&amp;w&gt;r.maxTranslate()&amp;&amp;(p||0)!==n)return!1}let b;if(b=n&gt;p?\"next\":n&lt;p?\"prev\":\"reset\",u&amp;&amp;-w===r.translate||!u&amp;&amp;w===r.translate)return r.updateActiveIndex(n),l.autoHeight&amp;&amp;r.updateAutoHeight(),r.updateSlidesClasses(),\"slide\"!==l.effect&amp;&amp;r.setTranslate(w),\"reset\"!==b&amp;&amp;(r.transitionStart(s,b),r.transitionEnd(s,b)),!1;if(l.cssMode){const e=r.isHorizontal(),s=u?w:-w;if(0===t){const t=r.virtual&amp;&amp;r.params.virtual.enabled;t&amp;&amp;(r.wrapperEl.style.scrollSnapType=\"none\"),h[e?\"scrollLeft\":\"scrollTop\"]=s,t&amp;&amp;requestAnimationFrame((()=&gt;{r.wrapperEl.style.scrollSnapType=\"\"}))}else{if(!r.support.smoothScroll)return v({swiper:r,targetPosition:s,side:e?\"left\":\"top\"}),!0;h.scrollTo({[e?\"left\":\"top\"]:s,behavior:\"smooth\"})}return!0}return 0===t?(r.setTransition(0),r.setTranslate(w),r.updateActiveIndex(n),r.updateSlidesClasses(),r.emit(\"beforeTransitionStart\",t,a),r.transitionStart(s,b),r.transitionEnd(s,b)):(r.setTransition(t),r.setTranslate(w),r.updateActiveIndex(n),r.updateSlidesClasses(),r.emit(\"beforeTransitionStart\",t,a),r.transitionStart(s,b),r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(e){r&amp;&amp;!r.destroyed&amp;&amp;e.target===this&amp;&amp;(r.$wrapperEl[0].removeEventListener(\"transitionend\",r.onSlideToWrapperTransitionEnd),r.$wrapperEl[0].removeEventListener(\"webkitTransitionEnd\",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(s,b))}),r.$wrapperEl[0].addEventListener(\"transitionend\",r.onSlideToWrapperTransitionEnd),r.$wrapperEl[0].addEventListener(\"webkitTransitionEnd\",r.onSlideToWrapperTransitionEnd))),!0},slideToLoop:function(e=0,t=this.params.speed,s=!0,a){const i=this;let r=e;return i.params.loop&amp;&amp;(r+=i.loopedSlides),i.slideTo(r,t,s,a)},slideNext:function(e=this.params.speed,t=!0,s){const a=this,{animating:i,enabled:r,params:n}=a;if(!r)return a;let l=n.slidesPerGroup;\"auto\"===n.slidesPerView&amp;&amp;1===n.slidesPerGroup&amp;&amp;n.slidesPerGroupAuto&amp;&amp;(l=Math.max(a.slidesPerViewDynamic(\"current\",!0),1));const o=a.activeIndex&lt;n.slidesPerGroupSkip?1:l;if(n.loop){if(i&amp;&amp;n.loopPreventsSlide)return!1;a.loopFix(),a._clientLeft=a.$wrapperEl[0].clientLeft}return a.slideTo(a.activeIndex+o,e,t,s)},slidePrev:function(e=this.params.speed,t=!0,s){const a=this,{params:i,animating:r,snapGrid:n,slidesGrid:l,rtlTranslate:o,enabled:d}=a;if(!d)return a;if(i.loop){if(r&amp;&amp;i.loopPreventsSlide)return!1;a.loopFix(),a._clientLeft=a.$wrapperEl[0].clientLeft}function c(e){return e&lt;0?-Math.floor(Math.abs(e)):Math.floor(e)}const p=c(o?a.translate:-a.translate),u=n.map((e=&gt;c(e)));let h=n[u.indexOf(p)-1];if(void 0===h&amp;&amp;i.cssMode){let e;n.forEach(((t,s)=&gt;{p&gt;=t&amp;&amp;(e=s)})),void 0!==e&amp;&amp;(h=n[e&gt;0?e-1:e])}let m=0;return void 0!==h&amp;&amp;(m=l.indexOf(h),m&lt;0&amp;&amp;(m=a.activeIndex-1),\"auto\"===i.slidesPerView&amp;&amp;1===i.slidesPerGroup&amp;&amp;i.slidesPerGroupAuto&amp;&amp;(m=m-a.slidesPerViewDynamic(\"previous\",!0)+1,m=Math.max(m,0))),a.slideTo(m,e,t,s)},slideReset:function(e=this.params.speed,t=!0,s){return this.slideTo(this.activeIndex,e,t,s)},slideToClosest:function(e=this.params.speed,t=!0,s,a=.5){const i=this;let r=i.activeIndex;const n=Math.min(i.params.slidesPerGroupSkip,r),l=n+Math.floor((r-n)/i.params.slidesPerGroup),o=i.rtlTranslate?i.translate:-i.translate;if(o&gt;=i.snapGrid[l]){const e=i.snapGrid[l];o-e&gt;(i.snapGrid[l+1]-e)*a&amp;&amp;(r+=i.params.slidesPerGroup)}else{const e=i.snapGrid[l-1];o-e&lt;=(i.snapGrid[l]-e)*a&amp;&amp;(r-=i.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,i.slidesGrid.length-1),i.slideTo(r,e,t,s)},slideToClickedSlide:function(){const e=this,{params:t,$wrapperEl:s}=e,a=\"auto\"===t.slidesPerView?e.slidesPerViewDynamic():t.slidesPerView;let i,r=e.clickedIndex;if(t.loop){if(e.animating)return;i=parseInt(d(e.clickedSlide).attr(\"data-swiper-slide-index\"),10),t.centeredSlides?r&lt;e.loopedSlides-a/2||r&gt;e.slides.length-e.loopedSlides+a/2?(e.loopFix(),r=s.children(`.${t.slideClass}[data-swiper-slide-index=\"${i}\"]:not(.${t.slideDuplicateClass})`).eq(0).index(),p((()=&gt;{e.slideTo(r)}))):e.slideTo(r):r&gt;e.slides.length-a?(e.loopFix(),r=s.children(`.${t.slideClass}[data-swiper-slide-index=\"${i}\"]:not(.${t.slideDuplicateClass})`).eq(0).index(),p((()=&gt;{e.slideTo(r)}))):e.slideTo(r)}else e.slideTo(r)}},loop:{loopCreate:function(){const e=this,t=a(),{params:s,$wrapperEl:i}=e;i.children(`.${s.slideClass}.${s.slideDuplicateClass}`).remove();let r=i.children(`.${s.slideClass}`);if(s.loopFillGroupWithBlank){const e=s.slidesPerGroup-r.length%s.slidesPerGroup;if(e!==s.slidesPerGroup){for(let a=0;a&lt;e;a+=1){const e=d(t.createElement(\"div\")).addClass(`${s.slideClass} ${s.slideBlankClass}`);i.append(e)}r=i.children(`.${s.slideClass}`)}}\"auto\"!==s.slidesPerView||s.loopedSlides||(s.loopedSlides=r.length),e.loopedSlides=Math.ceil(parseFloat(s.loopedSlides||s.slidesPerView,10)),e.loopedSlides+=s.loopAdditionalSlides,e.loopedSlides&gt;r.length&amp;&amp;(e.loopedSlides=r.length);const n=[],l=[];r.each(((t,s)=&gt;{const a=d(t);s&lt;e.loopedSlides&amp;&amp;l.push(t),s&lt;r.length&amp;&amp;s&gt;=r.length-e.loopedSlides&amp;&amp;n.push(t),a.attr(\"data-swiper-slide-index\",s)}));for(let e=0;e&lt;l.length;e+=1)i.append(d(l[e].cloneNode(!0)).addClass(s.slideDuplicateClass));for(let e=n.length-1;e&gt;=0;e-=1)i.prepend(d(n[e].cloneNode(!0)).addClass(s.slideDuplicateClass))},loopFix:function(){const e=this;e.emit(\"beforeLoopFix\");const{activeIndex:t,slides:s,loopedSlides:a,allowSlidePrev:i,allowSlideNext:r,snapGrid:n,rtlTranslate:l}=e;let o;e.allowSlidePrev=!0,e.allowSlideNext=!0;const d=-n[t]-e.getTranslate();if(t&lt;a){o=s.length-3*a+t,o+=a;e.slideTo(o,0,!1,!0)&amp;&amp;0!==d&amp;&amp;e.setTranslate((l?-e.translate:e.translate)-d)}else if(t&gt;=s.length-a){o=-s.length+t+a,o+=a;e.slideTo(o,0,!1,!0)&amp;&amp;0!==d&amp;&amp;e.setTranslate((l?-e.translate:e.translate)-d)}e.allowSlidePrev=i,e.allowSlideNext=r,e.emit(\"loopFix\")},loopDestroy:function(){const{$wrapperEl:e,params:t,slides:s}=this;e.children(`.${t.slideClass}.${t.slideDuplicateClass},.${t.slideClass}.${t.slideBlankClass}`).remove(),s.removeAttr(\"data-swiper-slide-index\")}},grabCursor:{setGrabCursor:function(e){const t=this;if(t.support.touch||!t.params.simulateTouch||t.params.watchOverflow&amp;&amp;t.isLocked||t.params.cssMode)return;const s=\"container\"===t.params.touchEventsTarget?t.el:t.wrapperEl;s.style.cursor=\"move\",s.style.cursor=e?\"-webkit-grabbing\":\"-webkit-grab\",s.style.cursor=e?\"-moz-grabbin\":\"-moz-grab\",s.style.cursor=e?\"grabbing\":\"grab\"},unsetGrabCursor:function(){const e=this;e.support.touch||e.params.watchOverflow&amp;&amp;e.isLocked||e.params.cssMode||(e[\"container\"===e.params.touchEventsTarget?\"el\":\"wrapperEl\"].style.cursor=\"\")}},events:{attachEvents:function(){const e=this,t=a(),{params:s,support:i}=e;e.onTouchStart=S.bind(e),e.onTouchMove=M.bind(e),e.onTouchEnd=P.bind(e),s.cssMode&amp;&amp;(e.onScroll=O.bind(e)),e.onClick=z.bind(e),i.touch&amp;&amp;!I&amp;&amp;(t.addEventListener(\"touchstart\",L),I=!0),A(e,\"on\")},detachEvents:function(){A(this,\"off\")}},breakpoints:{setBreakpoint:function(){const e=this,{activeIndex:t,initialized:s,loopedSlides:a=0,params:i,$el:r}=e,n=i.breakpoints;if(!n||n&amp;&amp;0===Object.keys(n).length)return;const l=e.getBreakpoint(n,e.params.breakpointsBase,e.el);if(!l||e.currentBreakpoint===l)return;const o=(l in n?n[l]:void 0)||e.originalParams,d=D(e,i),c=D(e,o),p=i.enabled;d&amp;&amp;!c?(r.removeClass(`${i.containerModifierClass}grid ${i.containerModifierClass}grid-column`),e.emitContainerClasses()):!d&amp;&amp;c&amp;&amp;(r.addClass(`${i.containerModifierClass}grid`),(o.grid.fill&amp;&amp;\"column\"===o.grid.fill||!o.grid.fill&amp;&amp;\"column\"===i.grid.fill)&amp;&amp;r.addClass(`${i.containerModifierClass}grid-column`),e.emitContainerClasses());const u=o.direction&amp;&amp;o.direction!==i.direction,h=i.loop&amp;&amp;(o.slidesPerView!==i.slidesPerView||u);u&amp;&amp;s&amp;&amp;e.changeDirection(),f(e.params,o);const m=e.params.enabled;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),p&amp;&amp;!m?e.disable():!p&amp;&amp;m&amp;&amp;e.enable(),e.currentBreakpoint=l,e.emit(\"_beforeBreakpoint\",o),h&amp;&amp;s&amp;&amp;(e.loopDestroy(),e.loopCreate(),e.updateSlides(),e.slideTo(t-a+e.loopedSlides,0,!1)),e.emit(\"breakpoint\",o)},getBreakpoint:function(e,t=\"window\",s){if(!e||\"container\"===t&amp;&amp;!s)return;let a=!1;const i=r(),n=\"window\"===t?i.innerHeight:s.clientHeight,l=Object.keys(e).map((e=&gt;{if(\"string\"==typeof e&amp;&amp;0===e.indexOf(\"@\")){const t=parseFloat(e.substr(1));return{value:n*t,point:e}}return{value:e,point:e}}));l.sort(((e,t)=&gt;parseInt(e.value,10)-parseInt(t.value,10)));for(let e=0;e&lt;l.length;e+=1){const{point:r,value:n}=l[e];\"window\"===t?i.matchMedia(`(min-width: ${n}px)`).matches&amp;&amp;(a=r):n&lt;=s.clientWidth&amp;&amp;(a=r)}return a||\"max\"}},checkOverflow:{checkOverflow:function(){const e=this,{isLocked:t,params:s}=e,{slidesOffsetBefore:a}=s;if(a){const t=e.slides.length-1,s=e.slidesGrid[t]+e.slidesSizesGrid[t]+2*a;e.isLocked=e.size&gt;s}else e.isLocked=1===e.snapGrid.length;!0===s.allowSlideNext&amp;&amp;(e.allowSlideNext=!e.isLocked),!0===s.allowSlidePrev&amp;&amp;(e.allowSlidePrev=!e.isLocked),t&amp;&amp;t!==e.isLocked&amp;&amp;(e.isEnd=!1),t!==e.isLocked&amp;&amp;e.emit(e.isLocked?\"lock\":\"unlock\")}},classes:{addClasses:function(){const e=this,{classNames:t,params:s,rtl:a,$el:i,device:r,support:n}=e,l=function(e,t){const s=[];return e.forEach((e=&gt;{\"object\"==typeof e?Object.keys(e).forEach((a=&gt;{e[a]&amp;&amp;s.push(t+a)})):\"string\"==typeof e&amp;&amp;s.push(t+e)})),s}([\"initialized\",s.direction,{\"pointer-events\":!n.touch},{\"free-mode\":e.params.freeMode&amp;&amp;s.freeMode.enabled},{autoheight:s.autoHeight},{rtl:a},{grid:s.grid&amp;&amp;s.grid.rows&gt;1},{\"grid-column\":s.grid&amp;&amp;s.grid.rows&gt;1&amp;&amp;\"column\"===s.grid.fill},{android:r.android},{ios:r.ios},{\"css-mode\":s.cssMode},{centered:s.cssMode&amp;&amp;s.centeredSlides}],s.containerModifierClass);t.push(...l),i.addClass([...t].join(\" \")),e.emitContainerClasses()},removeClasses:function(){const{$el:e,classNames:t}=this;e.removeClass(t.join(\" \")),this.emitContainerClasses()}},images:{loadImage:function(e,t,s,a,i,n){const l=r();let o;function c(){n&amp;&amp;n()}d(e).parent(\"picture\")[0]||e.complete&amp;&amp;i?c():t?(o=new l.Image,o.onload=c,o.onerror=c,a&amp;&amp;(o.sizes=a),s&amp;&amp;(o.srcset=s),t&amp;&amp;(o.src=t)):c()},preloadImages:function(){const e=this;function t(){null!=e&amp;&amp;e&amp;&amp;!e.destroyed&amp;&amp;(void 0!==e.imagesLoaded&amp;&amp;(e.imagesLoaded+=1),e.imagesLoaded===e.imagesToLoad.length&amp;&amp;(e.params.updateOnImagesReady&amp;&amp;e.update(),e.emit(\"imagesReady\")))}e.imagesToLoad=e.$el.find(\"img\");for(let s=0;s&lt;e.imagesToLoad.length;s+=1){const a=e.imagesToLoad[s];e.loadImage(a,a.currentSrc||a.getAttribute(\"src\"),a.srcset||a.getAttribute(\"srcset\"),a.sizes||a.getAttribute(\"sizes\"),!0,t)}}}},X={};class H{constructor(...e){let t,s;if(1===e.length&amp;&amp;e[0].constructor&amp;&amp;\"Object\"===Object.prototype.toString.call(e[0]).slice(8,-1)?s=e[0]:[t,s]=e,s||(s={}),s=f({},s),t&amp;&amp;!s.el&amp;&amp;(s.el=t),s.el&amp;&amp;d(s.el).length&gt;1){const e=[];return d(s.el).each((t=&gt;{const a=f({},s,{el:t});e.push(new H(a))})),e}const a=this;a.__swiper__=!0,a.support=y(),a.device=E({userAgent:s.userAgent}),a.browser=T(),a.eventsListeners={},a.eventsAnyListeners=[],a.modules=[...a.__modules__],s.modules&amp;&amp;Array.isArray(s.modules)&amp;&amp;a.modules.push(...s.modules);const i={};a.modules.forEach((e=&gt;{e({swiper:a,extendParams:N(s,i),on:a.on.bind(a),once:a.once.bind(a),off:a.off.bind(a),emit:a.emit.bind(a)})}));const r=f({},G,i);return a.params=f({},r,X,s),a.originalParams=f({},a.params),a.passedParams=f({},s),a.params&amp;&amp;a.params.on&amp;&amp;Object.keys(a.params.on).forEach((e=&gt;{a.on(e,a.params.on[e])})),a.params&amp;&amp;a.params.onAny&amp;&amp;a.onAny(a.params.onAny),a.$=d,Object.assign(a,{enabled:a.params.enabled,el:t,classNames:[],slides:d(),slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=&gt;\"horizontal\"===a.params.direction,isVertical:()=&gt;\"vertical\"===a.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev,touchEvents:function(){const e=[\"touchstart\",\"touchmove\",\"touchend\",\"touchcancel\"],t=[\"pointerdown\",\"pointermove\",\"pointerup\"];return a.touchEventsTouch={start:e[0],move:e[1],end:e[2],cancel:e[3]},a.touchEventsDesktop={start:t[0],move:t[1],end:t[2]},a.support.touch||!a.params.simulateTouch?a.touchEventsTouch:a.touchEventsDesktop}(),touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:a.params.focusableElements,lastClickTime:u(),clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,isTouchEvent:void 0,startMoving:void 0},allowClick:!0,allowTouchMove:a.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),a.emit(\"_swiper\"),a.params.init&amp;&amp;a.init(),a}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&amp;&amp;e.setGrabCursor(),e.emit(\"enable\"))}disable(){const e=this;e.enabled&amp;&amp;(e.enabled=!1,e.params.grabCursor&amp;&amp;e.unsetGrabCursor(),e.emit(\"disable\"))}setProgress(e,t){const s=this;e=Math.min(Math.max(e,0),1);const a=s.minTranslate(),i=(s.maxTranslate()-a)*e+a;s.translateTo(i,void 0===t?0:t),s.updateActiveIndex(),s.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(\" \").filter((t=&gt;0===t.indexOf(\"swiper\")||0===t.indexOf(e.params.containerModifierClass)));e.emit(\"_containerClasses\",t.join(\" \"))}getSlideClasses(e){const t=this;return e.className.split(\" \").filter((e=&gt;0===e.indexOf(\"swiper-slide\")||0===e.indexOf(t.params.slideClass))).join(\" \")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.each((s=&gt;{const a=e.getSlideClasses(s);t.push({slideEl:s,classNames:a}),e.emit(\"_slideClass\",s,a)})),e.emit(\"_slideClasses\",t)}slidesPerViewDynamic(e=\"current\",t=!1){const{params:s,slides:a,slidesGrid:i,slidesSizesGrid:r,size:n,activeIndex:l}=this;let o=1;if(s.centeredSlides){let e,t=a[l].swiperSlideSize;for(let s=l+1;s&lt;a.length;s+=1)a[s]&amp;&amp;!e&amp;&amp;(t+=a[s].swiperSlideSize,o+=1,t&gt;n&amp;&amp;(e=!0));for(let s=l-1;s&gt;=0;s-=1)a[s]&amp;&amp;!e&amp;&amp;(t+=a[s].swiperSlideSize,o+=1,t&gt;n&amp;&amp;(e=!0))}else if(\"current\"===e)for(let e=l+1;e&lt;a.length;e+=1){(t?i[e]+r[e]-i[l]&lt;n:i[e]-i[l]&lt;n)&amp;&amp;(o+=1)}else for(let e=l-1;e&gt;=0;e-=1){i[l]-i[e]&lt;n&amp;&amp;(o+=1)}return o}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:s}=e;function a(){const t=e.rtlTranslate?-1*e.translate:e.translate,s=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(s),e.updateActiveIndex(),e.updateSlidesClasses()}let i;s.breakpoints&amp;&amp;e.setBreakpoint(),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.params.freeMode&amp;&amp;e.params.freeMode.enabled?(a(),e.params.autoHeight&amp;&amp;e.updateAutoHeight()):(i=(\"auto\"===e.params.slidesPerView||e.params.slidesPerView&gt;1)&amp;&amp;e.isEnd&amp;&amp;!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),i||a()),s.watchOverflow&amp;&amp;t!==e.snapGrid&amp;&amp;e.checkOverflow(),e.emit(\"update\")}changeDirection(e,t=!0){const s=this,a=s.params.direction;return e||(e=\"horizontal\"===a?\"vertical\":\"horizontal\"),e===a||\"horizontal\"!==e&amp;&amp;\"vertical\"!==e||(s.$el.removeClass(`${s.params.containerModifierClass}${a}`).addClass(`${s.params.containerModifierClass}${e}`),s.emitContainerClasses(),s.params.direction=e,s.slides.each((t=&gt;{\"vertical\"===e?t.style.width=\"\":t.style.height=\"\"})),s.emit(\"changeDirection\"),t&amp;&amp;s.update()),s}mount(e){const t=this;if(t.mounted)return!0;const s=d(e||t.params.el);if(!(e=s[0]))return!1;e.swiper=t;const i=()=&gt;`.${(t.params.wrapperClass||\"\").trim().split(\" \").join(\".\")}`;let r=(()=&gt;{if(e&amp;&amp;e.shadowRoot&amp;&amp;e.shadowRoot.querySelector){const t=d(e.shadowRoot.querySelector(i()));return t.children=e=&gt;s.children(e),t}return s.children(i())})();if(0===r.length&amp;&amp;t.params.createElements){const e=a().createElement(\"div\");r=d(e),e.className=t.params.wrapperClass,s.append(e),s.children(`.${t.params.slideClass}`).each((e=&gt;{r.append(e)}))}return Object.assign(t,{$el:s,el:e,$wrapperEl:r,wrapperEl:r[0],mounted:!0,rtl:\"rtl\"===e.dir.toLowerCase()||\"rtl\"===s.css(\"direction\"),rtlTranslate:\"horizontal\"===t.params.direction&amp;&amp;(\"rtl\"===e.dir.toLowerCase()||\"rtl\"===s.css(\"direction\")),wrongRTL:\"-webkit-box\"===r.css(\"display\")}),!0}init(e){const t=this;if(t.initialized)return t;return!1===t.mount(e)||(t.emit(\"beforeInit\"),t.params.breakpoints&amp;&amp;t.setBreakpoint(),t.addClasses(),t.params.loop&amp;&amp;t.loopCreate(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&amp;&amp;t.checkOverflow(),t.params.grabCursor&amp;&amp;t.enabled&amp;&amp;t.setGrabCursor(),t.params.preloadImages&amp;&amp;t.preloadImages(),t.params.loop?t.slideTo(t.params.initialSlide+t.loopedSlides,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.attachEvents(),t.initialized=!0,t.emit(\"init\"),t.emit(\"afterInit\")),t}destroy(e=!0,t=!0){const s=this,{params:a,$el:i,$wrapperEl:r,slides:n}=s;return void 0===s.params||s.destroyed||(s.emit(\"beforeDestroy\"),s.initialized=!1,s.detachEvents(),a.loop&amp;&amp;s.loopDestroy(),t&amp;&amp;(s.removeClasses(),i.removeAttr(\"style\"),r.removeAttr(\"style\"),n&amp;&amp;n.length&amp;&amp;n.removeClass([a.slideVisibleClass,a.slideActiveClass,a.slideNextClass,a.slidePrevClass].join(\" \")).removeAttr(\"style\").removeAttr(\"data-swiper-slide-index\")),s.emit(\"destroy\"),Object.keys(s.eventsListeners).forEach((e=&gt;{s.off(e)})),!1!==e&amp;&amp;(s.$el[0].swiper=null,function(e){const t=e;Object.keys(t).forEach((e=&gt;{try{t[e]=null}catch(e){}try{delete t[e]}catch(e){}}))}(s)),s.destroyed=!0),null}static extendDefaults(e){f(X,e)}static get extendedDefaults(){return X}static get defaults(){return G}static installModule(e){H.prototype.__modules__||(H.prototype.__modules__=[]);const t=H.prototype.__modules__;\"function\"==typeof e&amp;&amp;t.indexOf(e)&lt;0&amp;&amp;t.push(e)}static use(e){return Array.isArray(e)?(e.forEach((e=&gt;H.installModule(e))),H):(H.installModule(e),H)}}function Y(e,t,s,i){const r=a();return e.params.createElements&amp;&amp;Object.keys(i).forEach((a=&gt;{if(!s[a]&amp;&amp;!0===s.auto){let n=e.$el.children(`.${i[a]}`)[0];n||(n=r.createElement(\"div\"),n.className=i[a],e.$el.append(n)),s[a]=n,t[a]=n}})),s}function W(e=\"\"){return`.${e.trim().replace(/([\\.:!\\/])/g,\"\\\\$1\").replace(/ /g,\".\")}`}function R(e){const t=this,{$wrapperEl:s,params:a}=t;if(a.loop&amp;&amp;t.loopDestroy(),\"object\"==typeof e&amp;&amp;\"length\"in e)for(let t=0;t&lt;e.length;t+=1)e[t]&amp;&amp;s.append(e[t]);else s.append(e);a.loop&amp;&amp;t.loopCreate(),a.observer||t.update()}function j(e){const t=this,{params:s,$wrapperEl:a,activeIndex:i}=t;s.loop&amp;&amp;t.loopDestroy();let r=i+1;if(\"object\"==typeof e&amp;&amp;\"length\"in e){for(let t=0;t&lt;e.length;t+=1)e[t]&amp;&amp;a.prepend(e[t]);r=i+e.length}else a.prepend(e);s.loop&amp;&amp;t.loopCreate(),s.observer||t.update(),t.slideTo(r,0,!1)}function _(e,t){const s=this,{$wrapperEl:a,params:i,activeIndex:r}=s;let n=r;i.loop&amp;&amp;(n-=s.loopedSlides,s.loopDestroy(),s.slides=a.children(`.${i.slideClass}`));const l=s.slides.length;if(e&lt;=0)return void s.prependSlide(t);if(e&gt;=l)return void s.appendSlide(t);let o=n&gt;e?n+1:n;const d=[];for(let t=l-1;t&gt;=e;t-=1){const e=s.slides.eq(t);e.remove(),d.unshift(e)}if(\"object\"==typeof t&amp;&amp;\"length\"in t){for(let e=0;e&lt;t.length;e+=1)t[e]&amp;&amp;a.append(t[e]);o=n&gt;e?n+t.length:n}else a.append(t);for(let e=0;e&lt;d.length;e+=1)a.append(d[e]);i.loop&amp;&amp;s.loopCreate(),i.observer||s.update(),i.loop?s.slideTo(o+s.loopedSlides,0,!1):s.slideTo(o,0,!1)}function V(e){const t=this,{params:s,$wrapperEl:a,activeIndex:i}=t;let r=i;s.loop&amp;&amp;(r-=t.loopedSlides,t.loopDestroy(),t.slides=a.children(`.${s.slideClass}`));let n,l=r;if(\"object\"==typeof e&amp;&amp;\"length\"in e){for(let s=0;s&lt;e.length;s+=1)n=e[s],t.slides[n]&amp;&amp;t.slides.eq(n).remove(),n&lt;l&amp;&amp;(l-=1);l=Math.max(l,0)}else n=e,t.slides[n]&amp;&amp;t.slides.eq(n).remove(),n&lt;l&amp;&amp;(l-=1),l=Math.max(l,0);s.loop&amp;&amp;t.loopCreate(),s.observer||t.update(),s.loop?t.slideTo(l+t.loopedSlides,0,!1):t.slideTo(l,0,!1)}function q(){const e=this,t=[];for(let s=0;s&lt;e.slides.length;s+=1)t.push(s);e.removeSlide(t)}function F(e){const{effect:t,swiper:s,on:a,setTranslate:i,setTransition:r,overwriteParams:n,perspective:l}=e;a(\"beforeInit\",(()=&gt;{if(s.params.effect!==t)return;s.classNames.push(`${s.params.containerModifierClass}${t}`),l&amp;&amp;l()&amp;&amp;s.classNames.push(`${s.params.containerModifierClass}3d`);const e=n?n():{};Object.assign(s.params,e),Object.assign(s.originalParams,e)})),a(\"setTranslate\",(()=&gt;{s.params.effect===t&amp;&amp;i()})),a(\"setTransition\",((e,a)=&gt;{s.params.effect===t&amp;&amp;r(a)}))}function U(e,t){return e.transformEl?t.find(e.transformEl).css({\"backface-visibility\":\"hidden\",\"-webkit-backface-visibility\":\"hidden\"}):t}function K({swiper:e,duration:t,transformEl:s,allSlides:a}){const{slides:i,activeIndex:r,$wrapperEl:n}=e;if(e.params.virtualTranslate&amp;&amp;0!==t){let t,l=!1;t=a?s?i.find(s):i:s?i.eq(r).find(s):i.eq(r),t.transitionEnd((()=&gt;{if(l)return;if(!e||e.destroyed)return;l=!0,e.animating=!1;const t=[\"webkitTransitionEnd\",\"transitionend\"];for(let e=0;e&lt;t.length;e+=1)n.trigger(t[e])}))}}function Z(e,t,s){const a=\"swiper-slide-shadow\"+(s?`-${s}`:\"\"),i=e.transformEl?t.find(e.transformEl):t;let r=i.children(`.${a}`);return r.length||(r=d(`&lt;div class=\"swiper-slide-shadow${s?`-${s}`:\"\"}\"&gt;&lt;/div&gt;`),i.append(r)),r}Object.keys(B).forEach((e=&gt;{Object.keys(B[e]).forEach((t=&gt;{H.prototype[t]=B[e][t]}))})),H.use([function({swiper:e,on:t,emit:s}){const a=r();let i=null;const n=()=&gt;{e&amp;&amp;!e.destroyed&amp;&amp;e.initialized&amp;&amp;(s(\"beforeResize\"),s(\"resize\"))},l=()=&gt;{e&amp;&amp;!e.destroyed&amp;&amp;e.initialized&amp;&amp;s(\"orientationchange\")};t(\"init\",(()=&gt;{e.params.resizeObserver&amp;&amp;void 0!==a.ResizeObserver?e&amp;&amp;!e.destroyed&amp;&amp;e.initialized&amp;&amp;(i=new ResizeObserver((t=&gt;{const{width:s,height:a}=e;let i=s,r=a;t.forEach((({contentBoxSize:t,contentRect:s,target:a})=&gt;{a&amp;&amp;a!==e.el||(i=s?s.width:(t[0]||t).inlineSize,r=s?s.height:(t[0]||t).blockSize)})),i===s&amp;&amp;r===a||n()})),i.observe(e.el)):(a.addEventListener(\"resize\",n),a.addEventListener(\"orientationchange\",l))})),t(\"destroy\",(()=&gt;{i&amp;&amp;i.unobserve&amp;&amp;e.el&amp;&amp;(i.unobserve(e.el),i=null),a.removeEventListener(\"resize\",n),a.removeEventListener(\"orientationchange\",l)}))},function({swiper:e,extendParams:t,on:s,emit:a}){const i=[],n=r(),l=(e,t={})=&gt;{const s=new(n.MutationObserver||n.WebkitMutationObserver)((e=&gt;{if(1===e.length)return void a(\"observerUpdate\",e[0]);const t=function(){a(\"observerUpdate\",e[0])};n.requestAnimationFrame?n.requestAnimationFrame(t):n.setTimeout(t,0)}));s.observe(e,{attributes:void 0===t.attributes||t.attributes,childList:void 0===t.childList||t.childList,characterData:void 0===t.characterData||t.characterData}),i.push(s)};t({observer:!1,observeParents:!1,observeSlideChildren:!1}),s(\"init\",(()=&gt;{if(e.params.observer){if(e.params.observeParents){const t=e.$el.parents();for(let e=0;e&lt;t.length;e+=1)l(t[e])}l(e.$el[0],{childList:e.params.observeSlideChildren}),l(e.$wrapperEl[0],{attributes:!1})}})),s(\"destroy\",(()=&gt;{i.forEach((e=&gt;{e.disconnect()})),i.splice(0,i.length)}))}]);const J=[function({swiper:e,extendParams:t,on:s}){function a(t,s){const a=e.params.virtual;if(a.cache&amp;&amp;e.virtual.cache[s])return e.virtual.cache[s];const i=a.renderSlide?d(a.renderSlide.call(e,t,s)):d(`&lt;div class=\"${e.params.slideClass}\" data-swiper-slide-index=\"${s}\"&gt;${t}&lt;/div&gt;`);return i.attr(\"data-swiper-slide-index\")||i.attr(\"data-swiper-slide-index\",s),a.cache&amp;&amp;(e.virtual.cache[s]=i),i}function i(t){const{slidesPerView:s,slidesPerGroup:i,centeredSlides:r}=e.params,{addSlidesBefore:n,addSlidesAfter:l}=e.params.virtual,{from:o,to:d,slides:c,slidesGrid:p,offset:u}=e.virtual;e.updateActiveIndex();const h=e.activeIndex||0;let m,f,g;m=e.rtlTranslate?\"right\":e.isHorizontal()?\"left\":\"top\",r?(f=Math.floor(s/2)+i+l,g=Math.floor(s/2)+i+n):(f=s+(i-1)+l,g=i+n);const v=Math.max((h||0)-g,0),w=Math.min((h||0)+f,c.length-1),b=(e.slidesGrid[v]||0)-(e.slidesGrid[0]||0);function x(){e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.lazy&amp;&amp;e.params.lazy.enabled&amp;&amp;e.lazy.load()}if(Object.assign(e.virtual,{from:v,to:w,offset:b,slidesGrid:e.slidesGrid}),o===v&amp;&amp;d===w&amp;&amp;!t)return e.slidesGrid!==p&amp;&amp;b!==u&amp;&amp;e.slides.css(m,`${b}px`),void e.updateProgress();if(e.params.virtual.renderExternal)return e.params.virtual.renderExternal.call(e,{offset:b,from:v,to:w,slides:function(){const e=[];for(let t=v;t&lt;=w;t+=1)e.push(c[t]);return e}()}),void(e.params.virtual.renderExternalUpdate&amp;&amp;x());const y=[],E=[];if(t)e.$wrapperEl.find(`.${e.params.slideClass}`).remove();else for(let t=o;t&lt;=d;t+=1)(t&lt;v||t&gt;w)&amp;&amp;e.$wrapperEl.find(`.${e.params.slideClass}[data-swiper-slide-index=\"${t}\"]`).remove();for(let e=0;e&lt;c.length;e+=1)e&gt;=v&amp;&amp;e&lt;=w&amp;&amp;(void 0===d||t?E.push(e):(e&gt;d&amp;&amp;E.push(e),e&lt;o&amp;&amp;y.push(e)));E.forEach((t=&gt;{e.$wrapperEl.append(a(c[t],t))})),y.sort(((e,t)=&gt;t-e)).forEach((t=&gt;{e.$wrapperEl.prepend(a(c[t],t))})),e.$wrapperEl.children(\".swiper-slide\").css(m,`${b}px`),x()}t({virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,renderExternalUpdate:!0,addSlidesBefore:0,addSlidesAfter:0}}),e.virtual={cache:{},from:void 0,to:void 0,slides:[],offset:0,slidesGrid:[]},s(\"beforeInit\",(()=&gt;{e.params.virtual.enabled&amp;&amp;(e.virtual.slides=e.params.virtual.slides,e.classNames.push(`${e.params.containerModifierClass}virtual`),e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0,e.params.initialSlide||i())})),s(\"setTranslate\",(()=&gt;{e.params.virtual.enabled&amp;&amp;i()})),s(\"init update resize\",(()=&gt;{e.params.virtual.enabled&amp;&amp;e.params.cssMode&amp;&amp;g(e.wrapperEl,\"--swiper-virtual-size\",`${e.virtualSize}px`)})),Object.assign(e.virtual,{appendSlide:function(t){if(\"object\"==typeof t&amp;&amp;\"length\"in t)for(let s=0;s&lt;t.length;s+=1)t[s]&amp;&amp;e.virtual.slides.push(t[s]);else e.virtual.slides.push(t);i(!0)},prependSlide:function(t){const s=e.activeIndex;let a=s+1,r=1;if(Array.isArray(t)){for(let s=0;s&lt;t.length;s+=1)t[s]&amp;&amp;e.virtual.slides.unshift(t[s]);a=s+t.length,r=t.length}else e.virtual.slides.unshift(t);if(e.params.virtual.cache){const t=e.virtual.cache,s={};Object.keys(t).forEach((e=&gt;{const a=t[e],i=a.attr(\"data-swiper-slide-index\");i&amp;&amp;a.attr(\"data-swiper-slide-index\",parseInt(i,10)+1),s[parseInt(e,10)+r]=a})),e.virtual.cache=s}i(!0),e.slideTo(a,0)},removeSlide:function(t){if(null==t)return;let s=e.activeIndex;if(Array.isArray(t))for(let a=t.length-1;a&gt;=0;a-=1)e.virtual.slides.splice(t[a],1),e.params.virtual.cache&amp;&amp;delete e.virtual.cache[t[a]],t[a]&lt;s&amp;&amp;(s-=1),s=Math.max(s,0);else e.virtual.slides.splice(t,1),e.params.virtual.cache&amp;&amp;delete e.virtual.cache[t],t&lt;s&amp;&amp;(s-=1),s=Math.max(s,0);i(!0),e.slideTo(s,0)},removeAllSlides:function(){e.virtual.slides=[],e.params.virtual.cache&amp;&amp;(e.virtual.cache={}),i(!0),e.slideTo(0,0)},update:i})},function({swiper:e,extendParams:t,on:s,emit:i}){const n=a(),l=r();function o(t){if(!e.enabled)return;const{rtlTranslate:s}=e;let a=t;a.originalEvent&amp;&amp;(a=a.originalEvent);const r=a.keyCode||a.charCode,o=e.params.keyboard.pageUpDown,d=o&amp;&amp;33===r,c=o&amp;&amp;34===r,p=37===r,u=39===r,h=38===r,m=40===r;if(!e.allowSlideNext&amp;&amp;(e.isHorizontal()&amp;&amp;u||e.isVertical()&amp;&amp;m||c))return!1;if(!e.allowSlidePrev&amp;&amp;(e.isHorizontal()&amp;&amp;p||e.isVertical()&amp;&amp;h||d))return!1;if(!(a.shiftKey||a.altKey||a.ctrlKey||a.metaKey||n.activeElement&amp;&amp;n.activeElement.nodeName&amp;&amp;(\"input\"===n.activeElement.nodeName.toLowerCase()||\"textarea\"===n.activeElement.nodeName.toLowerCase()))){if(e.params.keyboard.onlyInViewport&amp;&amp;(d||c||p||u||h||m)){let t=!1;if(e.$el.parents(`.${e.params.slideClass}`).length&gt;0&amp;&amp;0===e.$el.parents(`.${e.params.slideActiveClass}`).length)return;const a=e.$el,i=a[0].clientWidth,r=a[0].clientHeight,n=l.innerWidth,o=l.innerHeight,d=e.$el.offset();s&amp;&amp;(d.left-=e.$el[0].scrollLeft);const c=[[d.left,d.top],[d.left+i,d.top],[d.left,d.top+r],[d.left+i,d.top+r]];for(let e=0;e&lt;c.length;e+=1){const s=c[e];if(s[0]&gt;=0&amp;&amp;s[0]&lt;=n&amp;&amp;s[1]&gt;=0&amp;&amp;s[1]&lt;=o){if(0===s[0]&amp;&amp;0===s[1])continue;t=!0}}if(!t)return}e.isHorizontal()?((d||c||p||u)&amp;&amp;(a.preventDefault?a.preventDefault():a.returnValue=!1),((c||u)&amp;&amp;!s||(d||p)&amp;&amp;s)&amp;&amp;e.slideNext(),((d||p)&amp;&amp;!s||(c||u)&amp;&amp;s)&amp;&amp;e.slidePrev()):((d||c||h||m)&amp;&amp;(a.preventDefault?a.preventDefault():a.returnValue=!1),(c||m)&amp;&amp;e.slideNext(),(d||h)&amp;&amp;e.slidePrev()),i(\"keyPress\",r)}}function c(){e.keyboard.enabled||(d(n).on(\"keydown\",o),e.keyboard.enabled=!0)}function p(){e.keyboard.enabled&amp;&amp;(d(n).off(\"keydown\",o),e.keyboard.enabled=!1)}e.keyboard={enabled:!1},t({keyboard:{enabled:!1,onlyInViewport:!0,pageUpDown:!0}}),s(\"init\",(()=&gt;{e.params.keyboard.enabled&amp;&amp;c()})),s(\"destroy\",(()=&gt;{e.keyboard.enabled&amp;&amp;p()})),Object.assign(e.keyboard,{enable:c,disable:p})},function({swiper:e,extendParams:t,on:s,emit:a}){const i=r();let n;t({mousewheel:{enabled:!1,releaseOnEdges:!1,invert:!1,forceToAxis:!1,sensitivity:1,eventsTarget:\"container\",thresholdDelta:null,thresholdTime:null}}),e.mousewheel={enabled:!1};let l,o=u();const c=[];function h(){e.enabled&amp;&amp;(e.mouseEntered=!0)}function m(){e.enabled&amp;&amp;(e.mouseEntered=!1)}function f(t){return!(e.params.mousewheel.thresholdDelta&amp;&amp;t.delta&lt;e.params.mousewheel.thresholdDelta)&amp;&amp;(!(e.params.mousewheel.thresholdTime&amp;&amp;u()-o&lt;e.params.mousewheel.thresholdTime)&amp;&amp;(t.delta&gt;=6&amp;&amp;u()-o&lt;60||(t.direction&lt;0?e.isEnd&amp;&amp;!e.params.loop||e.animating||(e.slideNext(),a(\"scroll\",t.raw)):e.isBeginning&amp;&amp;!e.params.loop||e.animating||(e.slidePrev(),a(\"scroll\",t.raw)),o=(new i.Date).getTime(),!1)))}function g(t){let s=t,i=!0;if(!e.enabled)return;const r=e.params.mousewheel;e.params.cssMode&amp;&amp;s.preventDefault();let o=e.$el;if(\"container\"!==e.params.mousewheel.eventsTarget&amp;&amp;(o=d(e.params.mousewheel.eventsTarget)),!e.mouseEntered&amp;&amp;!o[0].contains(s.target)&amp;&amp;!r.releaseOnEdges)return!0;s.originalEvent&amp;&amp;(s=s.originalEvent);let h=0;const m=e.rtlTranslate?-1:1,g=function(e){let t=0,s=0,a=0,i=0;return\"detail\"in e&amp;&amp;(s=e.detail),\"wheelDelta\"in e&amp;&amp;(s=-e.wheelDelta/120),\"wheelDeltaY\"in e&amp;&amp;(s=-e.wheelDeltaY/120),\"wheelDeltaX\"in e&amp;&amp;(t=-e.wheelDeltaX/120),\"axis\"in e&amp;&amp;e.axis===e.HORIZONTAL_AXIS&amp;&amp;(t=s,s=0),a=10*t,i=10*s,\"deltaY\"in e&amp;&amp;(i=e.deltaY),\"deltaX\"in e&amp;&amp;(a=e.deltaX),e.shiftKey&amp;&amp;!a&amp;&amp;(a=i,i=0),(a||i)&amp;&amp;e.deltaMode&amp;&amp;(1===e.deltaMode?(a*=40,i*=40):(a*=800,i*=800)),a&amp;&amp;!t&amp;&amp;(t=a&lt;1?-1:1),i&amp;&amp;!s&amp;&amp;(s=i&lt;1?-1:1),{spinX:t,spinY:s,pixelX:a,pixelY:i}}(s);if(r.forceToAxis)if(e.isHorizontal()){if(!(Math.abs(g.pixelX)&gt;Math.abs(g.pixelY)))return!0;h=-g.pixelX*m}else{if(!(Math.abs(g.pixelY)&gt;Math.abs(g.pixelX)))return!0;h=-g.pixelY}else h=Math.abs(g.pixelX)&gt;Math.abs(g.pixelY)?-g.pixelX*m:-g.pixelY;if(0===h)return!0;r.invert&amp;&amp;(h=-h);let v=e.getTranslate()+h*r.sensitivity;if(v&gt;=e.minTranslate()&amp;&amp;(v=e.minTranslate()),v&lt;=e.maxTranslate()&amp;&amp;(v=e.maxTranslate()),i=!!e.params.loop||!(v===e.minTranslate()||v===e.maxTranslate()),i&amp;&amp;e.params.nested&amp;&amp;s.stopPropagation(),e.params.freeMode&amp;&amp;e.params.freeMode.enabled){const t={time:u(),delta:Math.abs(h),direction:Math.sign(h)},i=l&amp;&amp;t.time&lt;l.time+500&amp;&amp;t.delta&lt;=l.delta&amp;&amp;t.direction===l.direction;if(!i){l=void 0,e.params.loop&amp;&amp;e.loopFix();let o=e.getTranslate()+h*r.sensitivity;const d=e.isBeginning,u=e.isEnd;if(o&gt;=e.minTranslate()&amp;&amp;(o=e.minTranslate()),o&lt;=e.maxTranslate()&amp;&amp;(o=e.maxTranslate()),e.setTransition(0),e.setTranslate(o),e.updateProgress(),e.updateActiveIndex(),e.updateSlidesClasses(),(!d&amp;&amp;e.isBeginning||!u&amp;&amp;e.isEnd)&amp;&amp;e.updateSlidesClasses(),e.params.freeMode.sticky){clearTimeout(n),n=void 0,c.length&gt;=15&amp;&amp;c.shift();const s=c.length?c[c.length-1]:void 0,a=c[0];if(c.push(t),s&amp;&amp;(t.delta&gt;s.delta||t.direction!==s.direction))c.splice(0);else if(c.length&gt;=15&amp;&amp;t.time-a.time&lt;500&amp;&amp;a.delta-t.delta&gt;=1&amp;&amp;t.delta&lt;=6){const s=h&gt;0?.8:.2;l=t,c.splice(0),n=p((()=&gt;{e.slideToClosest(e.params.speed,!0,void 0,s)}),0)}n||(n=p((()=&gt;{l=t,c.splice(0),e.slideToClosest(e.params.speed,!0,void 0,.5)}),500))}if(i||a(\"scroll\",s),e.params.autoplay&amp;&amp;e.params.autoplayDisableOnInteraction&amp;&amp;e.autoplay.stop(),o===e.minTranslate()||o===e.maxTranslate())return!0}}else{const s={time:u(),delta:Math.abs(h),direction:Math.sign(h),raw:t};c.length&gt;=2&amp;&amp;c.shift();const a=c.length?c[c.length-1]:void 0;if(c.push(s),a?(s.direction!==a.direction||s.delta&gt;a.delta||s.time&gt;a.time+150)&amp;&amp;f(s):f(s),function(t){const s=e.params.mousewheel;if(t.direction&lt;0){if(e.isEnd&amp;&amp;!e.params.loop&amp;&amp;s.releaseOnEdges)return!0}else if(e.isBeginning&amp;&amp;!e.params.loop&amp;&amp;s.releaseOnEdges)return!0;return!1}(s))return!0}return s.preventDefault?s.preventDefault():s.returnValue=!1,!1}function v(t){let s=e.$el;\"container\"!==e.params.mousewheel.eventsTarget&amp;&amp;(s=d(e.params.mousewheel.eventsTarget)),s[t](\"mouseenter\",h),s[t](\"mouseleave\",m),s[t](\"wheel\",g)}function w(){return e.params.cssMode?(e.wrapperEl.removeEventListener(\"wheel\",g),!0):!e.mousewheel.enabled&amp;&amp;(v(\"on\"),e.mousewheel.enabled=!0,!0)}function b(){return e.params.cssMode?(e.wrapperEl.addEventListener(event,g),!0):!!e.mousewheel.enabled&amp;&amp;(v(\"off\"),e.mousewheel.enabled=!1,!0)}s(\"init\",(()=&gt;{!e.params.mousewheel.enabled&amp;&amp;e.params.cssMode&amp;&amp;b(),e.params.mousewheel.enabled&amp;&amp;w()})),s(\"destroy\",(()=&gt;{e.params.cssMode&amp;&amp;w(),e.mousewheel.enabled&amp;&amp;b()})),Object.assign(e.mousewheel,{enable:w,disable:b})},function({swiper:e,extendParams:t,on:s,emit:a}){function i(t){let s;return t&amp;&amp;(s=d(t),e.params.uniqueNavElements&amp;&amp;\"string\"==typeof t&amp;&amp;s.length&gt;1&amp;&amp;1===e.$el.find(t).length&amp;&amp;(s=e.$el.find(t))),s}function r(t,s){const a=e.params.navigation;t&amp;&amp;t.length&gt;0&amp;&amp;(t[s?\"addClass\":\"removeClass\"](a.disabledClass),t[0]&amp;&amp;\"BUTTON\"===t[0].tagName&amp;&amp;(t[0].disabled=s),e.params.watchOverflow&amp;&amp;e.enabled&amp;&amp;t[e.isLocked?\"addClass\":\"removeClass\"](a.lockClass))}function n(){if(e.params.loop)return;const{$nextEl:t,$prevEl:s}=e.navigation;r(s,e.isBeginning),r(t,e.isEnd)}function l(t){t.preventDefault(),e.isBeginning&amp;&amp;!e.params.loop||e.slidePrev()}function o(t){t.preventDefault(),e.isEnd&amp;&amp;!e.params.loop||e.slideNext()}function c(){const t=e.params.navigation;if(e.params.navigation=Y(e,e.originalParams.navigation,e.params.navigation,{nextEl:\"swiper-button-next\",prevEl:\"swiper-button-prev\"}),!t.nextEl&amp;&amp;!t.prevEl)return;const s=i(t.nextEl),a=i(t.prevEl);s&amp;&amp;s.length&gt;0&amp;&amp;s.on(\"click\",o),a&amp;&amp;a.length&gt;0&amp;&amp;a.on(\"click\",l),Object.assign(e.navigation,{$nextEl:s,nextEl:s&amp;&amp;s[0],$prevEl:a,prevEl:a&amp;&amp;a[0]}),e.enabled||(s&amp;&amp;s.addClass(t.lockClass),a&amp;&amp;a.addClass(t.lockClass))}function p(){const{$nextEl:t,$prevEl:s}=e.navigation;t&amp;&amp;t.length&amp;&amp;(t.off(\"click\",o),t.removeClass(e.params.navigation.disabledClass)),s&amp;&amp;s.length&amp;&amp;(s.off(\"click\",l),s.removeClass(e.params.navigation.disabledClass))}t({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:\"swiper-button-disabled\",hiddenClass:\"swiper-button-hidden\",lockClass:\"swiper-button-lock\"}}),e.navigation={nextEl:null,$nextEl:null,prevEl:null,$prevEl:null},s(\"init\",(()=&gt;{c(),n()})),s(\"toEdge fromEdge lock unlock\",(()=&gt;{n()})),s(\"destroy\",(()=&gt;{p()})),s(\"enable disable\",(()=&gt;{const{$nextEl:t,$prevEl:s}=e.navigation;t&amp;&amp;t[e.enabled?\"removeClass\":\"addClass\"](e.params.navigation.lockClass),s&amp;&amp;s[e.enabled?\"removeClass\":\"addClass\"](e.params.navigation.lockClass)})),s(\"click\",((t,s)=&gt;{const{$nextEl:i,$prevEl:r}=e.navigation,n=s.target;if(e.params.navigation.hideOnClick&amp;&amp;!d(n).is(r)&amp;&amp;!d(n).is(i)){if(e.pagination&amp;&amp;e.params.pagination&amp;&amp;e.params.pagination.clickable&amp;&amp;(e.pagination.el===n||e.pagination.el.contains(n)))return;let t;i?t=i.hasClass(e.params.navigation.hiddenClass):r&amp;&amp;(t=r.hasClass(e.params.navigation.hiddenClass)),a(!0===t?\"navigationShow\":\"navigationHide\"),i&amp;&amp;i.toggleClass(e.params.navigation.hiddenClass),r&amp;&amp;r.toggleClass(e.params.navigation.hiddenClass)}})),Object.assign(e.navigation,{update:n,init:c,destroy:p})},function({swiper:e,extendParams:t,on:s,emit:a}){const i=\"swiper-pagination\";let r;t({pagination:{el:null,bulletElement:\"span\",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:\"bullets\",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=&gt;e,formatFractionTotal:e=&gt;e,bulletClass:`${i}-bullet`,bulletActiveClass:`${i}-bullet-active`,modifierClass:`${i}-`,currentClass:`${i}-current`,totalClass:`${i}-total`,hiddenClass:`${i}-hidden`,progressbarFillClass:`${i}-progressbar-fill`,progressbarOppositeClass:`${i}-progressbar-opposite`,clickableClass:`${i}-clickable`,lockClass:`${i}-lock`,horizontalClass:`${i}-horizontal`,verticalClass:`${i}-vertical`}}),e.pagination={el:null,$el:null,bullets:[]};let n=0;function l(){return!e.params.pagination.el||!e.pagination.el||!e.pagination.$el||0===e.pagination.$el.length}function o(t,s){const{bulletActiveClass:a}=e.params.pagination;t[s]().addClass(`${a}-${s}`)[s]().addClass(`${a}-${s}-${s}`)}function c(){const t=e.rtl,s=e.params.pagination;if(l())return;const i=e.virtual&amp;&amp;e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,c=e.pagination.$el;let p;const u=e.params.loop?Math.ceil((i-2*e.loopedSlides)/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?(p=Math.ceil((e.activeIndex-e.loopedSlides)/e.params.slidesPerGroup),p&gt;i-1-2*e.loopedSlides&amp;&amp;(p-=i-2*e.loopedSlides),p&gt;u-1&amp;&amp;(p-=u),p&lt;0&amp;&amp;\"bullets\"!==e.params.paginationType&amp;&amp;(p=u+p)):p=void 0!==e.snapIndex?e.snapIndex:e.activeIndex||0,\"bullets\"===s.type&amp;&amp;e.pagination.bullets&amp;&amp;e.pagination.bullets.length&gt;0){const a=e.pagination.bullets;let i,l,u;if(s.dynamicBullets&amp;&amp;(r=a.eq(0)[e.isHorizontal()?\"outerWidth\":\"outerHeight\"](!0),c.css(e.isHorizontal()?\"width\":\"height\",r*(s.dynamicMainBullets+4)+\"px\"),s.dynamicMainBullets&gt;1&amp;&amp;void 0!==e.previousIndex&amp;&amp;(n+=p-e.previousIndex,n&gt;s.dynamicMainBullets-1?n=s.dynamicMainBullets-1:n&lt;0&amp;&amp;(n=0)),i=p-n,l=i+(Math.min(a.length,s.dynamicMainBullets)-1),u=(l+i)/2),a.removeClass([\"\",\"-next\",\"-next-next\",\"-prev\",\"-prev-prev\",\"-main\"].map((e=&gt;`${s.bulletActiveClass}${e}`)).join(\" \")),c.length&gt;1)a.each((e=&gt;{const t=d(e),a=t.index();a===p&amp;&amp;t.addClass(s.bulletActiveClass),s.dynamicBullets&amp;&amp;(a&gt;=i&amp;&amp;a&lt;=l&amp;&amp;t.addClass(`${s.bulletActiveClass}-main`),a===i&amp;&amp;o(t,\"prev\"),a===l&amp;&amp;o(t,\"next\"))}));else{const t=a.eq(p),r=t.index();if(t.addClass(s.bulletActiveClass),s.dynamicBullets){const t=a.eq(i),n=a.eq(l);for(let e=i;e&lt;=l;e+=1)a.eq(e).addClass(`${s.bulletActiveClass}-main`);if(e.params.loop)if(r&gt;=a.length-s.dynamicMainBullets){for(let e=s.dynamicMainBullets;e&gt;=0;e-=1)a.eq(a.length-e).addClass(`${s.bulletActiveClass}-main`);a.eq(a.length-s.dynamicMainBullets-1).addClass(`${s.bulletActiveClass}-prev`)}else o(t,\"prev\"),o(n,\"next\");else o(t,\"prev\"),o(n,\"next\")}}if(s.dynamicBullets){const i=Math.min(a.length,s.dynamicMainBullets+4),n=(r*i-r)/2-u*r,l=t?\"right\":\"left\";a.css(e.isHorizontal()?l:\"top\",`${n}px`)}}if(\"fraction\"===s.type&amp;&amp;(c.find(W(s.currentClass)).text(s.formatFractionCurrent(p+1)),c.find(W(s.totalClass)).text(s.formatFractionTotal(u))),\"progressbar\"===s.type){let t;t=s.progressbarOpposite?e.isHorizontal()?\"vertical\":\"horizontal\":e.isHorizontal()?\"horizontal\":\"vertical\";const a=(p+1)/u;let i=1,r=1;\"horizontal\"===t?i=a:r=a,c.find(W(s.progressbarFillClass)).transform(`translate3d(0,0,0) scaleX(${i}) scaleY(${r})`).transition(e.params.speed)}\"custom\"===s.type&amp;&amp;s.renderCustom?(c.html(s.renderCustom(e,p+1,u)),a(\"paginationRender\",c[0])):a(\"paginationUpdate\",c[0]),e.params.watchOverflow&amp;&amp;e.enabled&amp;&amp;c[e.isLocked?\"addClass\":\"removeClass\"](s.lockClass)}function p(){const t=e.params.pagination;if(l())return;const s=e.virtual&amp;&amp;e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,i=e.pagination.$el;let r=\"\";if(\"bullets\"===t.type){let a=e.params.loop?Math.ceil((s-2*e.loopedSlides)/e.params.slidesPerGroup):e.snapGrid.length;e.params.freeMode&amp;&amp;e.params.freeMode.enabled&amp;&amp;!e.params.loop&amp;&amp;a&gt;s&amp;&amp;(a=s);for(let s=0;s&lt;a;s+=1)t.renderBullet?r+=t.renderBullet.call(e,s,t.bulletClass):r+=`&lt;${t.bulletElement} class=\"${t.bulletClass}\"&gt;&lt;/${t.bulletElement}&gt;`;i.html(r),e.pagination.bullets=i.find(W(t.bulletClass))}\"fraction\"===t.type&amp;&amp;(r=t.renderFraction?t.renderFraction.call(e,t.currentClass,t.totalClass):`&lt;span class=\"${t.currentClass}\"&gt;&lt;/span&gt; / &lt;span class=\"${t.totalClass}\"&gt;&lt;/span&gt;`,i.html(r)),\"progressbar\"===t.type&amp;&amp;(r=t.renderProgressbar?t.renderProgressbar.call(e,t.progressbarFillClass):`&lt;span class=\"${t.progressbarFillClass}\"&gt;&lt;/span&gt;`,i.html(r)),\"custom\"!==t.type&amp;&amp;a(\"paginationRender\",e.pagination.$el[0])}function u(){e.params.pagination=Y(e,e.originalParams.pagination,e.params.pagination,{el:\"swiper-pagination\"});const t=e.params.pagination;if(!t.el)return;let s=d(t.el);0!==s.length&amp;&amp;(e.params.uniqueNavElements&amp;&amp;\"string\"==typeof t.el&amp;&amp;s.length&gt;1&amp;&amp;(s=e.$el.find(t.el),s.length&gt;1&amp;&amp;(s=s.filter((t=&gt;d(t).parents(\".swiper\")[0]===e.el)))),\"bullets\"===t.type&amp;&amp;t.clickable&amp;&amp;s.addClass(t.clickableClass),s.addClass(t.modifierClass+t.type),s.addClass(t.modifierClass+e.params.direction),\"bullets\"===t.type&amp;&amp;t.dynamicBullets&amp;&amp;(s.addClass(`${t.modifierClass}${t.type}-dynamic`),n=0,t.dynamicMainBullets&lt;1&amp;&amp;(t.dynamicMainBullets=1)),\"progressbar\"===t.type&amp;&amp;t.progressbarOpposite&amp;&amp;s.addClass(t.progressbarOppositeClass),t.clickable&amp;&amp;s.on(\"click\",W(t.bulletClass),(function(t){t.preventDefault();let s=d(this).index()*e.params.slidesPerGroup;e.params.loop&amp;&amp;(s+=e.loopedSlides),e.slideTo(s)})),Object.assign(e.pagination,{$el:s,el:s[0]}),e.enabled||s.addClass(t.lockClass))}function h(){const t=e.params.pagination;if(l())return;const s=e.pagination.$el;s.removeClass(t.hiddenClass),s.removeClass(t.modifierClass+t.type),s.removeClass(t.modifierClass+e.params.direction),e.pagination.bullets&amp;&amp;e.pagination.bullets.removeClass&amp;&amp;e.pagination.bullets.removeClass(t.bulletActiveClass),t.clickable&amp;&amp;s.off(\"click\",W(t.bulletClass))}s(\"init\",(()=&gt;{u(),p(),c()})),s(\"activeIndexChange\",(()=&gt;{(e.params.loop||void 0===e.snapIndex)&amp;&amp;c()})),s(\"snapIndexChange\",(()=&gt;{e.params.loop||c()})),s(\"slidesLengthChange\",(()=&gt;{e.params.loop&amp;&amp;(p(),c())})),s(\"snapGridLengthChange\",(()=&gt;{e.params.loop||(p(),c())})),s(\"destroy\",(()=&gt;{h()})),s(\"enable disable\",(()=&gt;{const{$el:t}=e.pagination;t&amp;&amp;t[e.enabled?\"removeClass\":\"addClass\"](e.params.pagination.lockClass)})),s(\"lock unlock\",(()=&gt;{c()})),s(\"click\",((t,s)=&gt;{const i=s.target,{$el:r}=e.pagination;if(e.params.pagination.el&amp;&amp;e.params.pagination.hideOnClick&amp;&amp;r.length&gt;0&amp;&amp;!d(i).hasClass(e.params.pagination.bulletClass)){if(e.navigation&amp;&amp;(e.navigation.nextEl&amp;&amp;i===e.navigation.nextEl||e.navigation.prevEl&amp;&amp;i===e.navigation.prevEl))return;const t=r.hasClass(e.params.pagination.hiddenClass);a(!0===t?\"paginationShow\":\"paginationHide\"),r.toggleClass(e.params.pagination.hiddenClass)}})),Object.assign(e.pagination,{render:p,update:c,init:u,destroy:h})},function({swiper:e,extendParams:t,on:s,emit:i}){const r=a();let n,l,o,c,u=!1,h=null,m=null;function f(){if(!e.params.scrollbar.el||!e.scrollbar.el)return;const{scrollbar:t,rtlTranslate:s,progress:a}=e,{$dragEl:i,$el:r}=t,n=e.params.scrollbar;let d=l,c=(o-l)*a;s?(c=-c,c&gt;0?(d=l-c,c=0):-c+l&gt;o&amp;&amp;(d=o+c)):c&lt;0?(d=l+c,c=0):c+l&gt;o&amp;&amp;(d=o-c),e.isHorizontal()?(i.transform(`translate3d(${c}px, 0, 0)`),i[0].style.width=`${d}px`):(i.transform(`translate3d(0px, ${c}px, 0)`),i[0].style.height=`${d}px`),n.hide&amp;&amp;(clearTimeout(h),r[0].style.opacity=1,h=setTimeout((()=&gt;{r[0].style.opacity=0,r.transition(400)}),1e3))}function g(){if(!e.params.scrollbar.el||!e.scrollbar.el)return;const{scrollbar:t}=e,{$dragEl:s,$el:a}=t;s[0].style.width=\"\",s[0].style.height=\"\",o=e.isHorizontal()?a[0].offsetWidth:a[0].offsetHeight,c=e.size/(e.virtualSize+e.params.slidesOffsetBefore-(e.params.centeredSlides?e.snapGrid[0]:0)),l=\"auto\"===e.params.scrollbar.dragSize?o*c:parseInt(e.params.scrollbar.dragSize,10),e.isHorizontal()?s[0].style.width=`${l}px`:s[0].style.height=`${l}px`,a[0].style.display=c&gt;=1?\"none\":\"\",e.params.scrollbar.hide&amp;&amp;(a[0].style.opacity=0),e.params.watchOverflow&amp;&amp;e.enabled&amp;&amp;t.$el[e.isLocked?\"addClass\":\"removeClass\"](e.params.scrollbar.lockClass)}function v(t){return e.isHorizontal()?\"touchstart\"===t.type||\"touchmove\"===t.type?t.targetTouches[0].clientX:t.clientX:\"touchstart\"===t.type||\"touchmove\"===t.type?t.targetTouches[0].clientY:t.clientY}function w(t){const{scrollbar:s,rtlTranslate:a}=e,{$el:i}=s;let r;r=(v(t)-i.offset()[e.isHorizontal()?\"left\":\"top\"]-(null!==n?n:l/2))/(o-l),r=Math.max(Math.min(r,1),0),a&amp;&amp;(r=1-r);const d=e.minTranslate()+(e.maxTranslate()-e.minTranslate())*r;e.updateProgress(d),e.setTranslate(d),e.updateActiveIndex(),e.updateSlidesClasses()}function b(t){const s=e.params.scrollbar,{scrollbar:a,$wrapperEl:r}=e,{$el:l,$dragEl:o}=a;u=!0,n=t.target===o[0]||t.target===o?v(t)-t.target.getBoundingClientRect()[e.isHorizontal()?\"left\":\"top\"]:null,t.preventDefault(),t.stopPropagation(),r.transition(100),o.transition(100),w(t),clearTimeout(m),l.transition(0),s.hide&amp;&amp;l.css(\"opacity\",1),e.params.cssMode&amp;&amp;e.$wrapperEl.css(\"scroll-snap-type\",\"none\"),i(\"scrollbarDragStart\",t)}function x(t){const{scrollbar:s,$wrapperEl:a}=e,{$el:r,$dragEl:n}=s;u&amp;&amp;(t.preventDefault?t.preventDefault():t.returnValue=!1,w(t),a.transition(0),r.transition(0),n.transition(0),i(\"scrollbarDragMove\",t))}function y(t){const s=e.params.scrollbar,{scrollbar:a,$wrapperEl:r}=e,{$el:n}=a;u&amp;&amp;(u=!1,e.params.cssMode&amp;&amp;(e.$wrapperEl.css(\"scroll-snap-type\",\"\"),r.transition(\"\")),s.hide&amp;&amp;(clearTimeout(m),m=p((()=&gt;{n.css(\"opacity\",0),n.transition(400)}),1e3)),i(\"scrollbarDragEnd\",t),s.snapOnRelease&amp;&amp;e.slideToClosest())}function E(t){const{scrollbar:s,touchEventsTouch:a,touchEventsDesktop:i,params:n,support:l}=e,o=s.$el[0],d=!(!l.passiveListener||!n.passiveListeners)&amp;&amp;{passive:!1,capture:!1},c=!(!l.passiveListener||!n.passiveListeners)&amp;&amp;{passive:!0,capture:!1};if(!o)return;const p=\"on\"===t?\"addEventListener\":\"removeEventListener\";l.touch?(o[p](a.start,b,d),o[p](a.move,x,d),o[p](a.end,y,c)):(o[p](i.start,b,d),r[p](i.move,x,d),r[p](i.end,y,c))}function T(){const{scrollbar:t,$el:s}=e;e.params.scrollbar=Y(e,e.originalParams.scrollbar,e.params.scrollbar,{el:\"swiper-scrollbar\"});const a=e.params.scrollbar;if(!a.el)return;let i=d(a.el);e.params.uniqueNavElements&amp;&amp;\"string\"==typeof a.el&amp;&amp;i.length&gt;1&amp;&amp;1===s.find(a.el).length&amp;&amp;(i=s.find(a.el));let r=i.find(`.${e.params.scrollbar.dragClass}`);0===r.length&amp;&amp;(r=d(`&lt;div class=\"${e.params.scrollbar.dragClass}\"&gt;&lt;/div&gt;`),i.append(r)),Object.assign(t,{$el:i,el:i[0],$dragEl:r,dragEl:r[0]}),a.draggable&amp;&amp;e.params.scrollbar.el&amp;&amp;E(\"on\"),i&amp;&amp;i[e.enabled?\"removeClass\":\"addClass\"](e.params.scrollbar.lockClass)}function C(){e.params.scrollbar.el&amp;&amp;E(\"off\")}t({scrollbar:{el:null,dragSize:\"auto\",hide:!1,draggable:!1,snapOnRelease:!0,lockClass:\"swiper-scrollbar-lock\",dragClass:\"swiper-scrollbar-drag\"}}),e.scrollbar={el:null,dragEl:null,$el:null,$dragEl:null},s(\"init\",(()=&gt;{T(),g(),f()})),s(\"update resize observerUpdate lock unlock\",(()=&gt;{g()})),s(\"setTranslate\",(()=&gt;{f()})),s(\"setTransition\",((t,s)=&gt;{!function(t){e.params.scrollbar.el&amp;&amp;e.scrollbar.el&amp;&amp;e.scrollbar.$dragEl.transition(t)}(s)})),s(\"enable disable\",(()=&gt;{const{$el:t}=e.scrollbar;t&amp;&amp;t[e.enabled?\"removeClass\":\"addClass\"](e.params.scrollbar.lockClass)})),s(\"destroy\",(()=&gt;{C()})),Object.assign(e.scrollbar,{updateSize:g,setTranslate:f,init:T,destroy:C})},function({swiper:e,extendParams:t,on:s}){t({parallax:{enabled:!1}});const a=(t,s)=&gt;{const{rtl:a}=e,i=d(t),r=a?-1:1,n=i.attr(\"data-swiper-parallax\")||\"0\";let l=i.attr(\"data-swiper-parallax-x\"),o=i.attr(\"data-swiper-parallax-y\");const c=i.attr(\"data-swiper-parallax-scale\"),p=i.attr(\"data-swiper-parallax-opacity\");if(l||o?(l=l||\"0\",o=o||\"0\"):e.isHorizontal()?(l=n,o=\"0\"):(o=n,l=\"0\"),l=l.indexOf(\"%\")&gt;=0?parseInt(l,10)*s*r+\"%\":l*s*r+\"px\",o=o.indexOf(\"%\")&gt;=0?parseInt(o,10)*s+\"%\":o*s+\"px\",null!=p){const e=p-(p-1)*(1-Math.abs(s));i[0].style.opacity=e}if(null==c)i.transform(`translate3d(${l}, ${o}, 0px)`);else{const e=c-(c-1)*(1-Math.abs(s));i.transform(`translate3d(${l}, ${o}, 0px) scale(${e})`)}},i=()=&gt;{const{$el:t,slides:s,progress:i,snapGrid:r}=e;t.children(\"[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]\").each((e=&gt;{a(e,i)})),s.each(((t,s)=&gt;{let n=t.progress;e.params.slidesPerGroup&gt;1&amp;&amp;\"auto\"!==e.params.slidesPerView&amp;&amp;(n+=Math.ceil(s/2)-i*(r.length-1)),n=Math.min(Math.max(n,-1),1),d(t).find(\"[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]\").each((e=&gt;{a(e,n)}))}))};s(\"beforeInit\",(()=&gt;{e.params.parallax.enabled&amp;&amp;(e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0)})),s(\"init\",(()=&gt;{e.params.parallax.enabled&amp;&amp;i()})),s(\"setTranslate\",(()=&gt;{e.params.parallax.enabled&amp;&amp;i()})),s(\"setTransition\",((t,s)=&gt;{e.params.parallax.enabled&amp;&amp;((t=e.params.speed)=&gt;{const{$el:s}=e;s.find(\"[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]\").each((e=&gt;{const s=d(e);let a=parseInt(s.attr(\"data-swiper-parallax-duration\"),10)||t;0===t&amp;&amp;(a=0),s.transition(a)}))})(s)}))},function({swiper:e,extendParams:t,on:s,emit:a}){const i=r();t({zoom:{enabled:!1,maxRatio:3,minRatio:1,toggle:!0,containerClass:\"swiper-zoom-container\",zoomedSlideClass:\"swiper-slide-zoomed\"}}),e.zoom={enabled:!1};let n,l,o,c=1,p=!1;const u={$slideEl:void 0,slideWidth:void 0,slideHeight:void 0,$imageEl:void 0,$imageWrapEl:void 0,maxRatio:3},m={isTouched:void 0,isMoved:void 0,currentX:void 0,currentY:void 0,minX:void 0,minY:void 0,maxX:void 0,maxY:void 0,width:void 0,height:void 0,startX:void 0,startY:void 0,touchesStart:{},touchesCurrent:{}},f={x:void 0,y:void 0,prevPositionX:void 0,prevPositionY:void 0,prevTime:void 0};let g=1;function v(e){if(e.targetTouches.length&lt;2)return 1;const t=e.targetTouches[0].pageX,s=e.targetTouches[0].pageY,a=e.targetTouches[1].pageX,i=e.targetTouches[1].pageY;return Math.sqrt((a-t)**2+(i-s)**2)}function w(t){const s=e.support,a=e.params.zoom;if(l=!1,o=!1,!s.gestures){if(\"touchstart\"!==t.type||\"touchstart\"===t.type&amp;&amp;t.targetTouches.length&lt;2)return;l=!0,u.scaleStart=v(t)}u.$slideEl&amp;&amp;u.$slideEl.length||(u.$slideEl=d(t.target).closest(`.${e.params.slideClass}`),0===u.$slideEl.length&amp;&amp;(u.$slideEl=e.slides.eq(e.activeIndex)),u.$imageEl=u.$slideEl.find(`.${a.containerClass}`).eq(0).find(\"img, svg, canvas, picture, .swiper-zoom-target\"),u.$imageWrapEl=u.$imageEl.parent(`.${a.containerClass}`),u.maxRatio=u.$imageWrapEl.attr(\"data-swiper-zoom\")||a.maxRatio,0!==u.$imageWrapEl.length)?(u.$imageEl&amp;&amp;u.$imageEl.transition(0),p=!0):u.$imageEl=void 0}function b(t){const s=e.support,a=e.params.zoom,i=e.zoom;if(!s.gestures){if(\"touchmove\"!==t.type||\"touchmove\"===t.type&amp;&amp;t.targetTouches.length&lt;2)return;o=!0,u.scaleMove=v(t)}u.$imageEl&amp;&amp;0!==u.$imageEl.length?(s.gestures?i.scale=t.scale*c:i.scale=u.scaleMove/u.scaleStart*c,i.scale&gt;u.maxRatio&amp;&amp;(i.scale=u.maxRatio-1+(i.scale-u.maxRatio+1)**.5),i.scale&lt;a.minRatio&amp;&amp;(i.scale=a.minRatio+1-(a.minRatio-i.scale+1)**.5),u.$imageEl.transform(`translate3d(0,0,0) scale(${i.scale})`)):\"gesturechange\"===t.type&amp;&amp;w(t)}function x(t){const s=e.device,a=e.support,i=e.params.zoom,r=e.zoom;if(!a.gestures){if(!l||!o)return;if(\"touchend\"!==t.type||\"touchend\"===t.type&amp;&amp;t.changedTouches.length&lt;2&amp;&amp;!s.android)return;l=!1,o=!1}u.$imageEl&amp;&amp;0!==u.$imageEl.length&amp;&amp;(r.scale=Math.max(Math.min(r.scale,u.maxRatio),i.minRatio),u.$imageEl.transition(e.params.speed).transform(`translate3d(0,0,0) scale(${r.scale})`),c=r.scale,p=!1,1===r.scale&amp;&amp;(u.$slideEl=void 0))}function y(t){const s=e.zoom;if(!u.$imageEl||0===u.$imageEl.length)return;if(e.allowClick=!1,!m.isTouched||!u.$slideEl)return;m.isMoved||(m.width=u.$imageEl[0].offsetWidth,m.height=u.$imageEl[0].offsetHeight,m.startX=h(u.$imageWrapEl[0],\"x\")||0,m.startY=h(u.$imageWrapEl[0],\"y\")||0,u.slideWidth=u.$slideEl[0].offsetWidth,u.slideHeight=u.$slideEl[0].offsetHeight,u.$imageWrapEl.transition(0));const a=m.width*s.scale,i=m.height*s.scale;if(!(a&lt;u.slideWidth&amp;&amp;i&lt;u.slideHeight)){if(m.minX=Math.min(u.slideWidth/2-a/2,0),m.maxX=-m.minX,m.minY=Math.min(u.slideHeight/2-i/2,0),m.maxY=-m.minY,m.touchesCurrent.x=\"touchmove\"===t.type?t.targetTouches[0].pageX:t.pageX,m.touchesCurrent.y=\"touchmove\"===t.type?t.targetTouches[0].pageY:t.pageY,!m.isMoved&amp;&amp;!p){if(e.isHorizontal()&amp;&amp;(Math.floor(m.minX)===Math.floor(m.startX)&amp;&amp;m.touchesCurrent.x&lt;m.touchesStart.x||Math.floor(m.maxX)===Math.floor(m.startX)&amp;&amp;m.touchesCurrent.x&gt;m.touchesStart.x))return void(m.isTouched=!1);if(!e.isHorizontal()&amp;&amp;(Math.floor(m.minY)===Math.floor(m.startY)&amp;&amp;m.touchesCurrent.y&lt;m.touchesStart.y||Math.floor(m.maxY)===Math.floor(m.startY)&amp;&amp;m.touchesCurrent.y&gt;m.touchesStart.y))return void(m.isTouched=!1)}t.cancelable&amp;&amp;t.preventDefault(),t.stopPropagation(),m.isMoved=!0,m.currentX=m.touchesCurrent.x-m.touchesStart.x+m.startX,m.currentY=m.touchesCurrent.y-m.touchesStart.y+m.startY,m.currentX&lt;m.minX&amp;&amp;(m.currentX=m.minX+1-(m.minX-m.currentX+1)**.8),m.currentX&gt;m.maxX&amp;&amp;(m.currentX=m.maxX-1+(m.currentX-m.maxX+1)**.8),m.currentY&lt;m.minY&amp;&amp;(m.currentY=m.minY+1-(m.minY-m.currentY+1)**.8),m.currentY&gt;m.maxY&amp;&amp;(m.currentY=m.maxY-1+(m.currentY-m.maxY+1)**.8),f.prevPositionX||(f.prevPositionX=m.touchesCurrent.x),f.prevPositionY||(f.prevPositionY=m.touchesCurrent.y),f.prevTime||(f.prevTime=Date.now()),f.x=(m.touchesCurrent.x-f.prevPositionX)/(Date.now()-f.prevTime)/2,f.y=(m.touchesCurrent.y-f.prevPositionY)/(Date.now()-f.prevTime)/2,Math.abs(m.touchesCurrent.x-f.prevPositionX)&lt;2&amp;&amp;(f.x=0),Math.abs(m.touchesCurrent.y-f.prevPositionY)&lt;2&amp;&amp;(f.y=0),f.prevPositionX=m.touchesCurrent.x,f.prevPositionY=m.touchesCurrent.y,f.prevTime=Date.now(),u.$imageWrapEl.transform(`translate3d(${m.currentX}px, ${m.currentY}px,0)`)}}function E(){const t=e.zoom;u.$slideEl&amp;&amp;e.previousIndex!==e.activeIndex&amp;&amp;(u.$imageEl&amp;&amp;u.$imageEl.transform(\"translate3d(0,0,0) scale(1)\"),u.$imageWrapEl&amp;&amp;u.$imageWrapEl.transform(\"translate3d(0,0,0)\"),t.scale=1,c=1,u.$slideEl=void 0,u.$imageEl=void 0,u.$imageWrapEl=void 0)}function T(t){const s=e.zoom,a=e.params.zoom;if(u.$slideEl||(t&amp;&amp;t.target&amp;&amp;(u.$slideEl=d(t.target).closest(`.${e.params.slideClass}`)),u.$slideEl||(e.params.virtual&amp;&amp;e.params.virtual.enabled&amp;&amp;e.virtual?u.$slideEl=e.$wrapperEl.children(`.${e.params.slideActiveClass}`):u.$slideEl=e.slides.eq(e.activeIndex)),u.$imageEl=u.$slideEl.find(`.${a.containerClass}`).eq(0).find(\"img, svg, canvas, picture, .swiper-zoom-target\"),u.$imageWrapEl=u.$imageEl.parent(`.${a.containerClass}`)),!u.$imageEl||0===u.$imageEl.length||!u.$imageWrapEl||0===u.$imageWrapEl.length)return;let r,n,l,o,p,h,f,g,v,w,b,x,y,E,T,C,$,S;e.params.cssMode&amp;&amp;(e.wrapperEl.style.overflow=\"hidden\",e.wrapperEl.style.touchAction=\"none\"),u.$slideEl.addClass(`${a.zoomedSlideClass}`),void 0===m.touchesStart.x&amp;&amp;t?(r=\"touchend\"===t.type?t.changedTouches[0].pageX:t.pageX,n=\"touchend\"===t.type?t.changedTouches[0].pageY:t.pageY):(r=m.touchesStart.x,n=m.touchesStart.y),s.scale=u.$imageWrapEl.attr(\"data-swiper-zoom\")||a.maxRatio,c=u.$imageWrapEl.attr(\"data-swiper-zoom\")||a.maxRatio,t?($=u.$slideEl[0].offsetWidth,S=u.$slideEl[0].offsetHeight,l=u.$slideEl.offset().left+i.scrollX,o=u.$slideEl.offset().top+i.scrollY,p=l+$/2-r,h=o+S/2-n,v=u.$imageEl[0].offsetWidth,w=u.$imageEl[0].offsetHeight,b=v*s.scale,x=w*s.scale,y=Math.min($/2-b/2,0),E=Math.min(S/2-x/2,0),T=-y,C=-E,f=p*s.scale,g=h*s.scale,f&lt;y&amp;&amp;(f=y),f&gt;T&amp;&amp;(f=T),g&lt;E&amp;&amp;(g=E),g&gt;C&amp;&amp;(g=C)):(f=0,g=0),u.$imageWrapEl.transition(300).transform(`translate3d(${f}px, ${g}px,0)`),u.$imageEl.transition(300).transform(`translate3d(0,0,0) scale(${s.scale})`)}function C(){const t=e.zoom,s=e.params.zoom;u.$slideEl||(e.params.virtual&amp;&amp;e.params.virtual.enabled&amp;&amp;e.virtual?u.$slideEl=e.$wrapperEl.children(`.${e.params.slideActiveClass}`):u.$slideEl=e.slides.eq(e.activeIndex),u.$imageEl=u.$slideEl.find(`.${s.containerClass}`).eq(0).find(\"img, svg, canvas, picture, .swiper-zoom-target\"),u.$imageWrapEl=u.$imageEl.parent(`.${s.containerClass}`)),u.$imageEl&amp;&amp;0!==u.$imageEl.length&amp;&amp;u.$imageWrapEl&amp;&amp;0!==u.$imageWrapEl.length&amp;&amp;(e.params.cssMode&amp;&amp;(e.wrapperEl.style.overflow=\"\",e.wrapperEl.style.touchAction=\"\"),t.scale=1,c=1,u.$imageWrapEl.transition(300).transform(\"translate3d(0,0,0)\"),u.$imageEl.transition(300).transform(\"translate3d(0,0,0) scale(1)\"),u.$slideEl.removeClass(`${s.zoomedSlideClass}`),u.$slideEl=void 0)}function $(t){const s=e.zoom;s.scale&amp;&amp;1!==s.scale?C():T(t)}function S(){const t=e.support;return{passiveListener:!(\"touchstart\"!==e.touchEvents.start||!t.passiveListener||!e.params.passiveListeners)&amp;&amp;{passive:!0,capture:!1},activeListenerWithCapture:!t.passiveListener||{passive:!1,capture:!0}}}function M(){return`.${e.params.slideClass}`}function P(t){const{passiveListener:s}=S(),a=M();e.$wrapperEl[t](\"gesturestart\",a,w,s),e.$wrapperEl[t](\"gesturechange\",a,b,s),e.$wrapperEl[t](\"gestureend\",a,x,s)}function k(){n||(n=!0,P(\"on\"))}function z(){n&amp;&amp;(n=!1,P(\"off\"))}function O(){const t=e.zoom;if(t.enabled)return;t.enabled=!0;const s=e.support,{passiveListener:a,activeListenerWithCapture:i}=S(),r=M();s.gestures?(e.$wrapperEl.on(e.touchEvents.start,k,a),e.$wrapperEl.on(e.touchEvents.end,z,a)):\"touchstart\"===e.touchEvents.start&amp;&amp;(e.$wrapperEl.on(e.touchEvents.start,r,w,a),e.$wrapperEl.on(e.touchEvents.move,r,b,i),e.$wrapperEl.on(e.touchEvents.end,r,x,a),e.touchEvents.cancel&amp;&amp;e.$wrapperEl.on(e.touchEvents.cancel,r,x,a)),e.$wrapperEl.on(e.touchEvents.move,`.${e.params.zoom.containerClass}`,y,i)}function I(){const t=e.zoom;if(!t.enabled)return;const s=e.support;t.enabled=!1;const{passiveListener:a,activeListenerWithCapture:i}=S(),r=M();s.gestures?(e.$wrapperEl.off(e.touchEvents.start,k,a),e.$wrapperEl.off(e.touchEvents.end,z,a)):\"touchstart\"===e.touchEvents.start&amp;&amp;(e.$wrapperEl.off(e.touchEvents.start,r,w,a),e.$wrapperEl.off(e.touchEvents.move,r,b,i),e.$wrapperEl.off(e.touchEvents.end,r,x,a),e.touchEvents.cancel&amp;&amp;e.$wrapperEl.off(e.touchEvents.cancel,r,x,a)),e.$wrapperEl.off(e.touchEvents.move,`.${e.params.zoom.containerClass}`,y,i)}Object.defineProperty(e.zoom,\"scale\",{get:()=&gt;g,set(e){if(g!==e){const t=u.$imageEl?u.$imageEl[0]:void 0,s=u.$slideEl?u.$slideEl[0]:void 0;a(\"zoomChange\",e,t,s)}g=e}}),s(\"init\",(()=&gt;{e.params.zoom.enabled&amp;&amp;O()})),s(\"destroy\",(()=&gt;{I()})),s(\"touchStart\",((t,s)=&gt;{e.zoom.enabled&amp;&amp;function(t){const s=e.device;u.$imageEl&amp;&amp;0!==u.$imageEl.length&amp;&amp;(m.isTouched||(s.android&amp;&amp;t.cancelable&amp;&amp;t.preventDefault(),m.isTouched=!0,m.touchesStart.x=\"touchstart\"===t.type?t.targetTouches[0].pageX:t.pageX,m.touchesStart.y=\"touchstart\"===t.type?t.targetTouches[0].pageY:t.pageY))}(s)})),s(\"touchEnd\",((t,s)=&gt;{e.zoom.enabled&amp;&amp;function(){const t=e.zoom;if(!u.$imageEl||0===u.$imageEl.length)return;if(!m.isTouched||!m.isMoved)return m.isTouched=!1,void(m.isMoved=!1);m.isTouched=!1,m.isMoved=!1;let s=300,a=300;const i=f.x*s,r=m.currentX+i,n=f.y*a,l=m.currentY+n;0!==f.x&amp;&amp;(s=Math.abs((r-m.currentX)/f.x)),0!==f.y&amp;&amp;(a=Math.abs((l-m.currentY)/f.y));const o=Math.max(s,a);m.currentX=r,m.currentY=l;const d=m.width*t.scale,c=m.height*t.scale;m.minX=Math.min(u.slideWidth/2-d/2,0),m.maxX=-m.minX,m.minY=Math.min(u.slideHeight/2-c/2,0),m.maxY=-m.minY,m.currentX=Math.max(Math.min(m.currentX,m.maxX),m.minX),m.currentY=Math.max(Math.min(m.currentY,m.maxY),m.minY),u.$imageWrapEl.transition(o).transform(`translate3d(${m.currentX}px, ${m.currentY}px,0)`)}()})),s(\"doubleTap\",((t,s)=&gt;{!e.animating&amp;&amp;e.params.zoom.enabled&amp;&amp;e.zoom.enabled&amp;&amp;e.params.zoom.toggle&amp;&amp;$(s)})),s(\"transitionEnd\",(()=&gt;{e.zoom.enabled&amp;&amp;e.params.zoom.enabled&amp;&amp;E()})),s(\"slideChange\",(()=&gt;{e.zoom.enabled&amp;&amp;e.params.zoom.enabled&amp;&amp;e.params.cssMode&amp;&amp;E()})),Object.assign(e.zoom,{enable:O,disable:I,in:T,out:C,toggle:$})},function({swiper:e,extendParams:t,on:s,emit:a}){t({lazy:{checkInView:!1,enabled:!1,loadPrevNext:!1,loadPrevNextAmount:1,loadOnTransitionStart:!1,scrollingElement:\"\",elementClass:\"swiper-lazy\",loadingClass:\"swiper-lazy-loading\",loadedClass:\"swiper-lazy-loaded\",preloaderClass:\"swiper-lazy-preloader\"}}),e.lazy={};let i=!1,n=!1;function l(t,s=!0){const i=e.params.lazy;if(void 0===t)return;if(0===e.slides.length)return;const r=e.virtual&amp;&amp;e.params.virtual.enabled?e.$wrapperEl.children(`.${e.params.slideClass}[data-swiper-slide-index=\"${t}\"]`):e.slides.eq(t),n=r.find(`.${i.elementClass}:not(.${i.loadedClass}):not(.${i.loadingClass})`);!r.hasClass(i.elementClass)||r.hasClass(i.loadedClass)||r.hasClass(i.loadingClass)||n.push(r[0]),0!==n.length&amp;&amp;n.each((t=&gt;{const n=d(t);n.addClass(i.loadingClass);const o=n.attr(\"data-background\"),c=n.attr(\"data-src\"),p=n.attr(\"data-srcset\"),u=n.attr(\"data-sizes\"),h=n.parent(\"picture\");e.loadImage(n[0],c||o,p,u,!1,(()=&gt;{if(null!=e&amp;&amp;e&amp;&amp;(!e||e.params)&amp;&amp;!e.destroyed){if(o?(n.css(\"background-image\",`url(\"${o}\")`),n.removeAttr(\"data-background\")):(p&amp;&amp;(n.attr(\"srcset\",p),n.removeAttr(\"data-srcset\")),u&amp;&amp;(n.attr(\"sizes\",u),n.removeAttr(\"data-sizes\")),h.length&amp;&amp;h.children(\"source\").each((e=&gt;{const t=d(e);t.attr(\"data-srcset\")&amp;&amp;(t.attr(\"srcset\",t.attr(\"data-srcset\")),t.removeAttr(\"data-srcset\"))})),c&amp;&amp;(n.attr(\"src\",c),n.removeAttr(\"data-src\"))),n.addClass(i.loadedClass).removeClass(i.loadingClass),r.find(`.${i.preloaderClass}`).remove(),e.params.loop&amp;&amp;s){const t=r.attr(\"data-swiper-slide-index\");if(r.hasClass(e.params.slideDuplicateClass)){l(e.$wrapperEl.children(`[data-swiper-slide-index=\"${t}\"]:not(.${e.params.slideDuplicateClass})`).index(),!1)}else{l(e.$wrapperEl.children(`.${e.params.slideDuplicateClass}[data-swiper-slide-index=\"${t}\"]`).index(),!1)}}a(\"lazyImageReady\",r[0],n[0]),e.params.autoHeight&amp;&amp;e.updateAutoHeight()}})),a(\"lazyImageLoad\",r[0],n[0])}))}function o(){const{$wrapperEl:t,params:s,slides:a,activeIndex:i}=e,r=e.virtual&amp;&amp;s.virtual.enabled,o=s.lazy;let c=s.slidesPerView;function p(e){if(r){if(t.children(`.${s.slideClass}[data-swiper-slide-index=\"${e}\"]`).length)return!0}else if(a[e])return!0;return!1}function u(e){return r?d(e).attr(\"data-swiper-slide-index\"):d(e).index()}if(\"auto\"===c&amp;&amp;(c=0),n||(n=!0),e.params.watchSlidesProgress)t.children(`.${s.slideVisibleClass}`).each((e=&gt;{l(r?d(e).attr(\"data-swiper-slide-index\"):d(e).index())}));else if(c&gt;1)for(let e=i;e&lt;i+c;e+=1)p(e)&amp;&amp;l(e);else l(i);if(o.loadPrevNext)if(c&gt;1||o.loadPrevNextAmount&amp;&amp;o.loadPrevNextAmount&gt;1){const e=o.loadPrevNextAmount,t=c,s=Math.min(i+t+Math.max(e,t),a.length),r=Math.max(i-Math.max(t,e),0);for(let e=i+c;e&lt;s;e+=1)p(e)&amp;&amp;l(e);for(let e=r;e&lt;i;e+=1)p(e)&amp;&amp;l(e)}else{const e=t.children(`.${s.slideNextClass}`);e.length&gt;0&amp;&amp;l(u(e));const a=t.children(`.${s.slidePrevClass}`);a.length&gt;0&amp;&amp;l(u(a))}}function c(){const t=r();if(!e||e.destroyed)return;const s=e.params.lazy.scrollingElement?d(e.params.lazy.scrollingElement):d(t),a=s[0]===t,n=a?t.innerWidth:s[0].offsetWidth,l=a?t.innerHeight:s[0].offsetHeight,p=e.$el.offset(),{rtlTranslate:u}=e;let h=!1;u&amp;&amp;(p.left-=e.$el[0].scrollLeft);const m=[[p.left,p.top],[p.left+e.width,p.top],[p.left,p.top+e.height],[p.left+e.width,p.top+e.height]];for(let e=0;e&lt;m.length;e+=1){const t=m[e];if(t[0]&gt;=0&amp;&amp;t[0]&lt;=n&amp;&amp;t[1]&gt;=0&amp;&amp;t[1]&lt;=l){if(0===t[0]&amp;&amp;0===t[1])continue;h=!0}}const f=!(\"touchstart\"!==e.touchEvents.start||!e.support.passiveListener||!e.params.passiveListeners)&amp;&amp;{passive:!0,capture:!1};h?(o(),s.off(\"scroll\",c,f)):i||(i=!0,s.on(\"scroll\",c,f))}s(\"beforeInit\",(()=&gt;{e.params.lazy.enabled&amp;&amp;e.params.preloadImages&amp;&amp;(e.params.preloadImages=!1)})),s(\"init\",(()=&gt;{e.params.lazy.enabled&amp;&amp;(e.params.lazy.checkInView?c():o())})),s(\"scroll\",(()=&gt;{e.params.freeMode&amp;&amp;e.params.freeMode.enabled&amp;&amp;!e.params.freeMode.sticky&amp;&amp;o()})),s(\"scrollbarDragMove resize _freeModeNoMomentumRelease\",(()=&gt;{e.params.lazy.enabled&amp;&amp;(e.params.lazy.checkInView?c():o())})),s(\"transitionStart\",(()=&gt;{e.params.lazy.enabled&amp;&amp;(e.params.lazy.loadOnTransitionStart||!e.params.lazy.loadOnTransitionStart&amp;&amp;!n)&amp;&amp;(e.params.lazy.checkInView?c():o())})),s(\"transitionEnd\",(()=&gt;{e.params.lazy.enabled&amp;&amp;!e.params.lazy.loadOnTransitionStart&amp;&amp;(e.params.lazy.checkInView?c():o())})),s(\"slideChange\",(()=&gt;{const{lazy:t,cssMode:s,watchSlidesProgress:a,touchReleaseOnEdges:i,resistanceRatio:r}=e.params;t.enabled&amp;&amp;(s||a&amp;&amp;(i||0===r))&amp;&amp;o()})),Object.assign(e.lazy,{load:o,loadInSlide:l})},function({swiper:e,extendParams:t,on:s}){function a(e,t){const s=function(){let e,t,s;return(a,i)=&gt;{for(t=-1,e=a.length;e-t&gt;1;)s=e+t&gt;&gt;1,a[s]&lt;=i?t=s:e=s;return e}}();let a,i;return this.x=e,this.y=t,this.lastIndex=e.length-1,this.interpolate=function(e){return e?(i=s(this.x,e),a=i-1,(e-this.x[a])*(this.y[i]-this.y[a])/(this.x[i]-this.x[a])+this.y[a]):0},this}function i(){e.controller.control&amp;&amp;e.controller.spline&amp;&amp;(e.controller.spline=void 0,delete e.controller.spline)}t({controller:{control:void 0,inverse:!1,by:\"slide\"}}),e.controller={control:void 0},s(\"beforeInit\",(()=&gt;{e.controller.control=e.params.controller.control})),s(\"update\",(()=&gt;{i()})),s(\"resize\",(()=&gt;{i()})),s(\"observerUpdate\",(()=&gt;{i()})),s(\"setTranslate\",((t,s,a)=&gt;{e.controller.control&amp;&amp;e.controller.setTranslate(s,a)})),s(\"setTransition\",((t,s,a)=&gt;{e.controller.control&amp;&amp;e.controller.setTransition(s,a)})),Object.assign(e.controller,{setTranslate:function(t,s){const i=e.controller.control;let r,n;const l=e.constructor;function o(t){const s=e.rtlTranslate?-e.translate:e.translate;\"slide\"===e.params.controller.by&amp;&amp;(!function(t){e.controller.spline||(e.controller.spline=e.params.loop?new a(e.slidesGrid,t.slidesGrid):new a(e.snapGrid,t.snapGrid))}(t),n=-e.controller.spline.interpolate(-s)),n&amp;&amp;\"container\"!==e.params.controller.by||(r=(t.maxTranslate()-t.minTranslate())/(e.maxTranslate()-e.minTranslate()),n=(s-e.minTranslate())*r+t.minTranslate()),e.params.controller.inverse&amp;&amp;(n=t.maxTranslate()-n),t.updateProgress(n),t.setTranslate(n,e),t.updateActiveIndex(),t.updateSlidesClasses()}if(Array.isArray(i))for(let e=0;e&lt;i.length;e+=1)i[e]!==s&amp;&amp;i[e]instanceof l&amp;&amp;o(i[e]);else i instanceof l&amp;&amp;s!==i&amp;&amp;o(i)},setTransition:function(t,s){const a=e.constructor,i=e.controller.control;let r;function n(s){s.setTransition(t,e),0!==t&amp;&amp;(s.transitionStart(),s.params.autoHeight&amp;&amp;p((()=&gt;{s.updateAutoHeight()})),s.$wrapperEl.transitionEnd((()=&gt;{i&amp;&amp;(s.params.loop&amp;&amp;\"slide\"===e.params.controller.by&amp;&amp;s.loopFix(),s.transitionEnd())})))}if(Array.isArray(i))for(r=0;r&lt;i.length;r+=1)i[r]!==s&amp;&amp;i[r]instanceof a&amp;&amp;n(i[r]);else i instanceof a&amp;&amp;s!==i&amp;&amp;n(i)}})},function({swiper:e,extendParams:t,on:s}){t({a11y:{enabled:!0,notificationClass:\"swiper-notification\",prevSlideMessage:\"Previous slide\",nextSlideMessage:\"Next slide\",firstSlideMessage:\"This is the first slide\",lastSlideMessage:\"This is the last slide\",paginationBulletMessage:\"Go to slide {{index}}\",slideLabelMessage:\"{{index}} / {{slidesLength}}\",containerMessage:null,containerRoleDescriptionMessage:null,itemRoleDescriptionMessage:null,slideRole:\"group\"}});let a=null;function i(e){const t=a;0!==t.length&amp;&amp;(t.html(\"\"),t.html(e))}function r(e){e.attr(\"tabIndex\",\"0\")}function n(e){e.attr(\"tabIndex\",\"-1\")}function l(e,t){e.attr(\"role\",t)}function o(e,t){e.attr(\"aria-roledescription\",t)}function c(e,t){e.attr(\"aria-label\",t)}function p(e){e.attr(\"aria-disabled\",!0)}function u(e){e.attr(\"aria-disabled\",!1)}function h(t){if(13!==t.keyCode&amp;&amp;32!==t.keyCode)return;const s=e.params.a11y,a=d(t.target);e.navigation&amp;&amp;e.navigation.$nextEl&amp;&amp;a.is(e.navigation.$nextEl)&amp;&amp;(e.isEnd&amp;&amp;!e.params.loop||e.slideNext(),e.isEnd?i(s.lastSlideMessage):i(s.nextSlideMessage)),e.navigation&amp;&amp;e.navigation.$prevEl&amp;&amp;a.is(e.navigation.$prevEl)&amp;&amp;(e.isBeginning&amp;&amp;!e.params.loop||e.slidePrev(),e.isBeginning?i(s.firstSlideMessage):i(s.prevSlideMessage)),e.pagination&amp;&amp;a.is(W(e.params.pagination.bulletClass))&amp;&amp;a[0].click()}function m(){if(e.params.loop||!e.navigation)return;const{$nextEl:t,$prevEl:s}=e.navigation;s&amp;&amp;s.length&gt;0&amp;&amp;(e.isBeginning?(p(s),n(s)):(u(s),r(s))),t&amp;&amp;t.length&gt;0&amp;&amp;(e.isEnd?(p(t),n(t)):(u(t),r(t)))}function f(){return e.pagination&amp;&amp;e.params.pagination.clickable&amp;&amp;e.pagination.bullets&amp;&amp;e.pagination.bullets.length}const g=(e,t,s)=&gt;{r(e),\"BUTTON\"!==e[0].tagName&amp;&amp;(l(e,\"button\"),e.on(\"keydown\",h)),c(e,s),function(e,t){e.attr(\"aria-controls\",t)}(e,t)};function v(){const t=e.params.a11y;e.$el.append(a);const s=e.$el;t.containerRoleDescriptionMessage&amp;&amp;o(s,t.containerRoleDescriptionMessage),t.containerMessage&amp;&amp;c(s,t.containerMessage);const i=e.$wrapperEl,r=i.attr(\"id\")||`swiper-wrapper-${function(e=16){return\"x\".repeat(e).replace(/x/g,(()=&gt;Math.round(16*Math.random()).toString(16)))}(16)}`,n=e.params.autoplay&amp;&amp;e.params.autoplay.enabled?\"off\":\"polite\";var p;p=r,i.attr(\"id\",p),function(e,t){e.attr(\"aria-live\",t)}(i,n),t.itemRoleDescriptionMessage&amp;&amp;o(d(e.slides),t.itemRoleDescriptionMessage),l(d(e.slides),t.slideRole);const u=e.params.loop?e.slides.filter((t=&gt;!t.classList.contains(e.params.slideDuplicateClass))).length:e.slides.length;let m,v;e.slides.each(((s,a)=&gt;{const i=d(s),r=e.params.loop?parseInt(i.attr(\"data-swiper-slide-index\"),10):a;c(i,t.slideLabelMessage.replace(/\\{\\{index\\}\\}/,r+1).replace(/\\{\\{slidesLength\\}\\}/,u))})),e.navigation&amp;&amp;e.navigation.$nextEl&amp;&amp;(m=e.navigation.$nextEl),e.navigation&amp;&amp;e.navigation.$prevEl&amp;&amp;(v=e.navigation.$prevEl),m&amp;&amp;m.length&amp;&amp;g(m,r,t.nextSlideMessage),v&amp;&amp;v.length&amp;&amp;g(v,r,t.prevSlideMessage),f()&amp;&amp;e.pagination.$el.on(\"keydown\",W(e.params.pagination.bulletClass),h)}s(\"beforeInit\",(()=&gt;{a=d(`&lt;span class=\"${e.params.a11y.notificationClass}\" aria-live=\"assertive\" aria-atomic=\"true\"&gt;&lt;/span&gt;`)})),s(\"afterInit\",(()=&gt;{e.params.a11y.enabled&amp;&amp;(v(),m())})),s(\"toEdge\",(()=&gt;{e.params.a11y.enabled&amp;&amp;m()})),s(\"fromEdge\",(()=&gt;{e.params.a11y.enabled&amp;&amp;m()})),s(\"paginationUpdate\",(()=&gt;{e.params.a11y.enabled&amp;&amp;function(){const t=e.params.a11y;f()&amp;&amp;e.pagination.bullets.each((s=&gt;{const a=d(s);r(a),e.params.pagination.renderBullet||(l(a,\"button\"),c(a,t.paginationBulletMessage.replace(/\\{\\{index\\}\\}/,a.index()+1)))}))}()})),s(\"destroy\",(()=&gt;{e.params.a11y.enabled&amp;&amp;function(){let t,s;a&amp;&amp;a.length&gt;0&amp;&amp;a.remove(),e.navigation&amp;&amp;e.navigation.$nextEl&amp;&amp;(t=e.navigation.$nextEl),e.navigation&amp;&amp;e.navigation.$prevEl&amp;&amp;(s=e.navigation.$prevEl),t&amp;&amp;t.off(\"keydown\",h),s&amp;&amp;s.off(\"keydown\",h),f()&amp;&amp;e.pagination.$el.off(\"keydown\",W(e.params.pagination.bulletClass),h)}()}))},function({swiper:e,extendParams:t,on:s}){t({history:{enabled:!1,root:\"\",replaceState:!1,key:\"slides\"}});let a=!1,i={};const n=e=&gt;e.toString().replace(/\\s+/g,\"-\").replace(/[^\\w-]+/g,\"\").replace(/--+/g,\"-\").replace(/^-+/,\"\").replace(/-+$/,\"\"),l=e=&gt;{const t=r();let s;s=e?new URL(e):t.location;const a=s.pathname.slice(1).split(\"/\").filter((e=&gt;\"\"!==e)),i=a.length;return{key:a[i-2],value:a[i-1]}},o=(t,s)=&gt;{const i=r();if(!a||!e.params.history.enabled)return;let l;l=e.params.url?new URL(e.params.url):i.location;const o=e.slides.eq(s);let d=n(o.attr(\"data-history\"));if(e.params.history.root.length&gt;0){let s=e.params.history.root;\"/\"===s[s.length-1]&amp;&amp;(s=s.slice(0,s.length-1)),d=`${s}/${t}/${d}`}else l.pathname.includes(t)||(d=`${t}/${d}`);const c=i.history.state;c&amp;&amp;c.value===d||(e.params.history.replaceState?i.history.replaceState({value:d},null,d):i.history.pushState({value:d},null,d))},d=(t,s,a)=&gt;{if(s)for(let i=0,r=e.slides.length;i&lt;r;i+=1){const r=e.slides.eq(i);if(n(r.attr(\"data-history\"))===s&amp;&amp;!r.hasClass(e.params.slideDuplicateClass)){const s=r.index();e.slideTo(s,t,a)}}else e.slideTo(0,t,a)},c=()=&gt;{i=l(e.params.url),d(e.params.speed,e.paths.value,!1)};s(\"init\",(()=&gt;{e.params.history.enabled&amp;&amp;(()=&gt;{const t=r();if(e.params.history){if(!t.history||!t.history.pushState)return e.params.history.enabled=!1,void(e.params.hashNavigation.enabled=!0);a=!0,i=l(e.params.url),(i.key||i.value)&amp;&amp;(d(0,i.value,e.params.runCallbacksOnInit),e.params.history.replaceState||t.addEventListener(\"popstate\",c))}})()})),s(\"destroy\",(()=&gt;{e.params.history.enabled&amp;&amp;(()=&gt;{const t=r();e.params.history.replaceState||t.removeEventListener(\"popstate\",c)})()})),s(\"transitionEnd _freeModeNoMomentumRelease\",(()=&gt;{a&amp;&amp;o(e.params.history.key,e.activeIndex)})),s(\"slideChange\",(()=&gt;{a&amp;&amp;e.params.cssMode&amp;&amp;o(e.params.history.key,e.activeIndex)}))},function({swiper:e,extendParams:t,emit:s,on:i}){let n=!1;const l=a(),o=r();t({hashNavigation:{enabled:!1,replaceState:!1,watchState:!1}});const c=()=&gt;{s(\"hashChange\");const t=l.location.hash.replace(\"#\",\"\");if(t!==e.slides.eq(e.activeIndex).attr(\"data-hash\")){const s=e.$wrapperEl.children(`.${e.params.slideClass}[data-hash=\"${t}\"]`).index();if(void 0===s)return;e.slideTo(s)}},p=()=&gt;{if(n&amp;&amp;e.params.hashNavigation.enabled)if(e.params.hashNavigation.replaceState&amp;&amp;o.history&amp;&amp;o.history.replaceState)o.history.replaceState(null,null,`#${e.slides.eq(e.activeIndex).attr(\"data-hash\")}`||\"\"),s(\"hashSet\");else{const t=e.slides.eq(e.activeIndex),a=t.attr(\"data-hash\")||t.attr(\"data-history\");l.location.hash=a||\"\",s(\"hashSet\")}};i(\"init\",(()=&gt;{e.params.hashNavigation.enabled&amp;&amp;(()=&gt;{if(!e.params.hashNavigation.enabled||e.params.history&amp;&amp;e.params.history.enabled)return;n=!0;const t=l.location.hash.replace(\"#\",\"\");if(t){const s=0;for(let a=0,i=e.slides.length;a&lt;i;a+=1){const i=e.slides.eq(a);if((i.attr(\"data-hash\")||i.attr(\"data-history\"))===t&amp;&amp;!i.hasClass(e.params.slideDuplicateClass)){const t=i.index();e.slideTo(t,s,e.params.runCallbacksOnInit,!0)}}}e.params.hashNavigation.watchState&amp;&amp;d(o).on(\"hashchange\",c)})()})),i(\"destroy\",(()=&gt;{e.params.hashNavigation.enabled&amp;&amp;e.params.hashNavigation.watchState&amp;&amp;d(o).off(\"hashchange\",c)})),i(\"transitionEnd _freeModeNoMomentumRelease\",(()=&gt;{n&amp;&amp;p()})),i(\"slideChange\",(()=&gt;{n&amp;&amp;e.params.cssMode&amp;&amp;p()}))},function({swiper:e,extendParams:t,on:s,emit:i}){let r;function n(){const t=e.slides.eq(e.activeIndex);let s=e.params.autoplay.delay;t.attr(\"data-swiper-autoplay\")&amp;&amp;(s=t.attr(\"data-swiper-autoplay\")||e.params.autoplay.delay),clearTimeout(r),r=p((()=&gt;{let t;e.params.autoplay.reverseDirection?e.params.loop?(e.loopFix(),t=e.slidePrev(e.params.speed,!0,!0),i(\"autoplay\")):e.isBeginning?e.params.autoplay.stopOnLastSlide?o():(t=e.slideTo(e.slides.length-1,e.params.speed,!0,!0),i(\"autoplay\")):(t=e.slidePrev(e.params.speed,!0,!0),i(\"autoplay\")):e.params.loop?(e.loopFix(),t=e.slideNext(e.params.speed,!0,!0),i(\"autoplay\")):e.isEnd?e.params.autoplay.stopOnLastSlide?o():(t=e.slideTo(0,e.params.speed,!0,!0),i(\"autoplay\")):(t=e.slideNext(e.params.speed,!0,!0),i(\"autoplay\")),(e.params.cssMode&amp;&amp;e.autoplay.running||!1===t)&amp;&amp;n()}),s)}function l(){return void 0===r&amp;&amp;(!e.autoplay.running&amp;&amp;(e.autoplay.running=!0,i(\"autoplayStart\"),n(),!0))}function o(){return!!e.autoplay.running&amp;&amp;(void 0!==r&amp;&amp;(r&amp;&amp;(clearTimeout(r),r=void 0),e.autoplay.running=!1,i(\"autoplayStop\"),!0))}function d(t){e.autoplay.running&amp;&amp;(e.autoplay.paused||(r&amp;&amp;clearTimeout(r),e.autoplay.paused=!0,0!==t&amp;&amp;e.params.autoplay.waitForTransition?[\"transitionend\",\"webkitTransitionEnd\"].forEach((t=&gt;{e.$wrapperEl[0].addEventListener(t,u)})):(e.autoplay.paused=!1,n())))}function c(){const t=a();\"hidden\"===t.visibilityState&amp;&amp;e.autoplay.running&amp;&amp;d(),\"visible\"===t.visibilityState&amp;&amp;e.autoplay.paused&amp;&amp;(n(),e.autoplay.paused=!1)}function u(t){e&amp;&amp;!e.destroyed&amp;&amp;e.$wrapperEl&amp;&amp;t.target===e.$wrapperEl[0]&amp;&amp;([\"transitionend\",\"webkitTransitionEnd\"].forEach((t=&gt;{e.$wrapperEl[0].removeEventListener(t,u)})),e.autoplay.paused=!1,e.autoplay.running?n():o())}function h(){e.params.autoplay.disableOnInteraction?o():d(),[\"transitionend\",\"webkitTransitionEnd\"].forEach((t=&gt;{e.$wrapperEl[0].removeEventListener(t,u)}))}function m(){e.params.autoplay.disableOnInteraction||(e.autoplay.paused=!1,n())}e.autoplay={running:!1,paused:!1},t({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!0,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}}),s(\"init\",(()=&gt;{if(e.params.autoplay.enabled){l();a().addEventListener(\"visibilitychange\",c),e.params.autoplay.pauseOnMouseEnter&amp;&amp;(e.$el.on(\"mouseenter\",h),e.$el.on(\"mouseleave\",m))}})),s(\"beforeTransitionStart\",((t,s,a)=&gt;{e.autoplay.running&amp;&amp;(a||!e.params.autoplay.disableOnInteraction?e.autoplay.pause(s):o())})),s(\"sliderFirstMove\",(()=&gt;{e.autoplay.running&amp;&amp;(e.params.autoplay.disableOnInteraction?o():d())})),s(\"touchEnd\",(()=&gt;{e.params.cssMode&amp;&amp;e.autoplay.paused&amp;&amp;!e.params.autoplay.disableOnInteraction&amp;&amp;n()})),s(\"destroy\",(()=&gt;{e.$el.off(\"mouseenter\",h),e.$el.off(\"mouseleave\",m),e.autoplay.running&amp;&amp;o();a().removeEventListener(\"visibilitychange\",c)})),Object.assign(e.autoplay,{pause:d,run:n,start:l,stop:o})},function({swiper:e,extendParams:t,on:s}){t({thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:\"swiper-slide-thumb-active\",thumbsContainerClass:\"swiper-thumbs\"}});let a=!1,i=!1;function r(){const t=e.thumbs.swiper;if(!t)return;const s=t.clickedIndex,a=t.clickedSlide;if(a&amp;&amp;d(a).hasClass(e.params.thumbs.slideThumbActiveClass))return;if(null==s)return;let i;if(i=t.params.loop?parseInt(d(t.clickedSlide).attr(\"data-swiper-slide-index\"),10):s,e.params.loop){let t=e.activeIndex;e.slides.eq(t).hasClass(e.params.slideDuplicateClass)&amp;&amp;(e.loopFix(),e._clientLeft=e.$wrapperEl[0].clientLeft,t=e.activeIndex);const s=e.slides.eq(t).prevAll(`[data-swiper-slide-index=\"${i}\"]`).eq(0).index(),a=e.slides.eq(t).nextAll(`[data-swiper-slide-index=\"${i}\"]`).eq(0).index();i=void 0===s?a:void 0===a?s:a-t&lt;t-s?a:s}e.slideTo(i)}function n(){const{thumbs:t}=e.params;if(a)return!1;a=!0;const s=e.constructor;if(t.swiper instanceof s)e.thumbs.swiper=t.swiper,Object.assign(e.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),Object.assign(e.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1});else if(m(t.swiper)){const a=Object.assign({},t.swiper);Object.assign(a,{watchSlidesProgress:!0,slideToClickedSlide:!1}),e.thumbs.swiper=new s(a),i=!0}return e.thumbs.swiper.$el.addClass(e.params.thumbs.thumbsContainerClass),e.thumbs.swiper.on(\"tap\",r),!0}function l(t){const s=e.thumbs.swiper;if(!s)return;const a=\"auto\"===s.params.slidesPerView?s.slidesPerViewDynamic():s.params.slidesPerView,i=e.params.thumbs.autoScrollOffset,r=i&amp;&amp;!s.params.loop;if(e.realIndex!==s.realIndex||r){let n,l,o=s.activeIndex;if(s.params.loop){s.slides.eq(o).hasClass(s.params.slideDuplicateClass)&amp;&amp;(s.loopFix(),s._clientLeft=s.$wrapperEl[0].clientLeft,o=s.activeIndex);const t=s.slides.eq(o).prevAll(`[data-swiper-slide-index=\"${e.realIndex}\"]`).eq(0).index(),a=s.slides.eq(o).nextAll(`[data-swiper-slide-index=\"${e.realIndex}\"]`).eq(0).index();n=void 0===t?a:void 0===a?t:a-o==o-t?s.params.slidesPerGroup&gt;1?a:o:a-o&lt;o-t?a:t,l=e.activeIndex&gt;e.previousIndex?\"next\":\"prev\"}else n=e.realIndex,l=n&gt;e.previousIndex?\"next\":\"prev\";r&amp;&amp;(n+=\"next\"===l?i:-1*i),s.visibleSlidesIndexes&amp;&amp;s.visibleSlidesIndexes.indexOf(n)&lt;0&amp;&amp;(s.params.centeredSlides?n=n&gt;o?n-Math.floor(a/2)+1:n+Math.floor(a/2)-1:n&gt;o&amp;&amp;s.params.slidesPerGroup,s.slideTo(n,t?0:void 0))}let n=1;const l=e.params.thumbs.slideThumbActiveClass;if(e.params.slidesPerView&gt;1&amp;&amp;!e.params.centeredSlides&amp;&amp;(n=e.params.slidesPerView),e.params.thumbs.multipleActiveThumbs||(n=1),n=Math.floor(n),s.slides.removeClass(l),s.params.loop||s.params.virtual&amp;&amp;s.params.virtual.enabled)for(let t=0;t&lt;n;t+=1)s.$wrapperEl.children(`[data-swiper-slide-index=\"${e.realIndex+t}\"]`).addClass(l);else for(let t=0;t&lt;n;t+=1)s.slides.eq(e.realIndex+t).addClass(l)}e.thumbs={swiper:null},s(\"beforeInit\",(()=&gt;{const{thumbs:t}=e.params;t&amp;&amp;t.swiper&amp;&amp;(n(),l(!0))})),s(\"slideChange update resize observerUpdate\",(()=&gt;{e.thumbs.swiper&amp;&amp;l()})),s(\"setTransition\",((t,s)=&gt;{const a=e.thumbs.swiper;a&amp;&amp;a.setTransition(s)})),s(\"beforeDestroy\",(()=&gt;{const t=e.thumbs.swiper;t&amp;&amp;i&amp;&amp;t&amp;&amp;t.destroy()})),Object.assign(e.thumbs,{init:n,update:l})},function({swiper:e,extendParams:t,emit:s,once:a}){t({freeMode:{enabled:!1,momentum:!0,momentumRatio:1,momentumBounce:!0,momentumBounceRatio:1,momentumVelocityRatio:1,sticky:!1,minimumVelocity:.02}}),Object.assign(e,{freeMode:{onTouchMove:function(){const{touchEventsData:t,touches:s}=e;0===t.velocities.length&amp;&amp;t.velocities.push({position:s[e.isHorizontal()?\"startX\":\"startY\"],time:t.touchStartTime}),t.velocities.push({position:s[e.isHorizontal()?\"currentX\":\"currentY\"],time:u()})},onTouchEnd:function({currentPos:t}){const{params:i,$wrapperEl:r,rtlTranslate:n,snapGrid:l,touchEventsData:o}=e,d=u()-o.touchStartTime;if(t&lt;-e.minTranslate())e.slideTo(e.activeIndex);else if(t&gt;-e.maxTranslate())e.slides.length&lt;l.length?e.slideTo(l.length-1):e.slideTo(e.slides.length-1);else{if(i.freeMode.momentum){if(o.velocities.length&gt;1){const t=o.velocities.pop(),s=o.velocities.pop(),a=t.position-s.position,r=t.time-s.time;e.velocity=a/r,e.velocity/=2,Math.abs(e.velocity)&lt;i.freeMode.minimumVelocity&amp;&amp;(e.velocity=0),(r&gt;150||u()-t.time&gt;300)&amp;&amp;(e.velocity=0)}else e.velocity=0;e.velocity*=i.freeMode.momentumVelocityRatio,o.velocities.length=0;let t=1e3*i.freeMode.momentumRatio;const d=e.velocity*t;let c=e.translate+d;n&amp;&amp;(c=-c);let p,h=!1;const m=20*Math.abs(e.velocity)*i.freeMode.momentumBounceRatio;let f;if(c&lt;e.maxTranslate())i.freeMode.momentumBounce?(c+e.maxTranslate()&lt;-m&amp;&amp;(c=e.maxTranslate()-m),p=e.maxTranslate(),h=!0,o.allowMomentumBounce=!0):c=e.maxTranslate(),i.loop&amp;&amp;i.centeredSlides&amp;&amp;(f=!0);else if(c&gt;e.minTranslate())i.freeMode.momentumBounce?(c-e.minTranslate()&gt;m&amp;&amp;(c=e.minTranslate()+m),p=e.minTranslate(),h=!0,o.allowMomentumBounce=!0):c=e.minTranslate(),i.loop&amp;&amp;i.centeredSlides&amp;&amp;(f=!0);else if(i.freeMode.sticky){let t;for(let e=0;e&lt;l.length;e+=1)if(l[e]&gt;-c){t=e;break}c=Math.abs(l[t]-c)&lt;Math.abs(l[t-1]-c)||\"next\"===e.swipeDirection?l[t]:l[t-1],c=-c}if(f&amp;&amp;a(\"transitionEnd\",(()=&gt;{e.loopFix()})),0!==e.velocity){if(t=n?Math.abs((-c-e.translate)/e.velocity):Math.abs((c-e.translate)/e.velocity),i.freeMode.sticky){const s=Math.abs((n?-c:c)-e.translate),a=e.slidesSizesGrid[e.activeIndex];t=s&lt;a?i.speed:s&lt;2*a?1.5*i.speed:2.5*i.speed}}else if(i.freeMode.sticky)return void e.slideToClosest();i.freeMode.momentumBounce&amp;&amp;h?(e.updateProgress(p),e.setTransition(t),e.setTranslate(c),e.transitionStart(!0,e.swipeDirection),e.animating=!0,r.transitionEnd((()=&gt;{e&amp;&amp;!e.destroyed&amp;&amp;o.allowMomentumBounce&amp;&amp;(s(\"momentumBounce\"),e.setTransition(i.speed),setTimeout((()=&gt;{e.setTranslate(p),r.transitionEnd((()=&gt;{e&amp;&amp;!e.destroyed&amp;&amp;e.transitionEnd()}))}),0))}))):e.velocity?(s(\"_freeModeNoMomentumRelease\"),e.updateProgress(c),e.setTransition(t),e.setTranslate(c),e.transitionStart(!0,e.swipeDirection),e.animating||(e.animating=!0,r.transitionEnd((()=&gt;{e&amp;&amp;!e.destroyed&amp;&amp;e.transitionEnd()})))):e.updateProgress(c),e.updateActiveIndex(),e.updateSlidesClasses()}else{if(i.freeMode.sticky)return void e.slideToClosest();i.freeMode&amp;&amp;s(\"_freeModeNoMomentumRelease\")}(!i.freeMode.momentum||d&gt;=i.longSwipesMs)&amp;&amp;(e.updateProgress(),e.updateActiveIndex(),e.updateSlidesClasses())}}}})},function({swiper:e,extendParams:t}){let s,a,i;t({grid:{rows:1,fill:\"column\"}}),e.grid={initSlides:t=&gt;{const{slidesPerView:r}=e.params,{rows:n,fill:l}=e.params.grid;a=s/n,i=Math.floor(t/n),s=Math.floor(t/n)===t/n?t:Math.ceil(t/n)*n,\"auto\"!==r&amp;&amp;\"row\"===l&amp;&amp;(s=Math.max(s,r*n))},updateSlide:(t,r,n,l)=&gt;{const{slidesPerGroup:o,spaceBetween:d}=e.params,{rows:c,fill:p}=e.params.grid;let u,h,m;if(\"row\"===p&amp;&amp;o&gt;1){const e=Math.floor(t/(o*c)),a=t-c*o*e,i=0===e?o:Math.min(Math.ceil((n-e*c*o)/c),o);m=Math.floor(a/i),h=a-m*i+e*o,u=h+m*s/c,r.css({\"-webkit-order\":u,order:u})}else\"column\"===p?(h=Math.floor(t/c),m=t-h*c,(h&gt;i||h===i&amp;&amp;m===c-1)&amp;&amp;(m+=1,m&gt;=c&amp;&amp;(m=0,h+=1))):(m=Math.floor(t/a),h=t-m*a);r.css(l(\"margin-top\"),0!==m?d&amp;&amp;`${d}px`:\"\")},updateWrapperSize:(t,a,i)=&gt;{const{spaceBetween:r,centeredSlides:n,roundLengths:l}=e.params,{rows:o}=e.params.grid;if(e.virtualSize=(t+r)*s,e.virtualSize=Math.ceil(e.virtualSize/o)-r,e.$wrapperEl.css({[i(\"width\")]:`${e.virtualSize+r}px`}),n){a.splice(0,a.length);const t=[];for(let s=0;s&lt;a.length;s+=1){let i=a[s];l&amp;&amp;(i=Math.floor(i)),a[s]&lt;e.virtualSize+a[0]&amp;&amp;t.push(i)}a.push(...t)}}}},function({swiper:e}){Object.assign(e,{appendSlide:R.bind(e),prependSlide:j.bind(e),addSlide:_.bind(e),removeSlide:V.bind(e),removeAllSlides:q.bind(e)})},function({swiper:e,extendParams:t,on:s}){t({fadeEffect:{crossFade:!1,transformEl:null}}),F({effect:\"fade\",swiper:e,on:s,setTranslate:()=&gt;{const{slides:t}=e,s=e.params.fadeEffect;for(let a=0;a&lt;t.length;a+=1){const t=e.slides.eq(a);let i=-t[0].swiperSlideOffset;e.params.virtualTranslate||(i-=e.translate);let r=0;e.isHorizontal()||(r=i,i=0);const n=e.params.fadeEffect.crossFade?Math.max(1-Math.abs(t[0].progress),0):1+Math.min(Math.max(t[0].progress,-1),0);U(s,t).css({opacity:n}).transform(`translate3d(${i}px, ${r}px, 0px)`)}},setTransition:t=&gt;{const{transformEl:s}=e.params.fadeEffect;(s?e.slides.find(s):e.slides).transition(t),K({swiper:e,duration:t,transformEl:s,allSlides:!0})},overwriteParams:()=&gt;({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!e.params.cssMode})})},function({swiper:e,extendParams:t,on:s}){t({cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94}}),F({effect:\"cube\",swiper:e,on:s,setTranslate:()=&gt;{const{$el:t,$wrapperEl:s,slides:a,width:i,height:r,rtlTranslate:n,size:l,browser:o}=e,c=e.params.cubeEffect,p=e.isHorizontal(),u=e.virtual&amp;&amp;e.params.virtual.enabled;let h,m=0;c.shadow&amp;&amp;(p?(h=s.find(\".swiper-cube-shadow\"),0===h.length&amp;&amp;(h=d('&lt;div class=\"swiper-cube-shadow\"&gt;&lt;/div&gt;'),s.append(h)),h.css({height:`${i}px`})):(h=t.find(\".swiper-cube-shadow\"),0===h.length&amp;&amp;(h=d('&lt;div class=\"swiper-cube-shadow\"&gt;&lt;/div&gt;'),t.append(h))));for(let e=0;e&lt;a.length;e+=1){const t=a.eq(e);let s=e;u&amp;&amp;(s=parseInt(t.attr(\"data-swiper-slide-index\"),10));let i=90*s,r=Math.floor(i/360);n&amp;&amp;(i=-i,r=Math.floor(-i/360));const o=Math.max(Math.min(t[0].progress,1),-1);let h=0,f=0,g=0;s%4==0?(h=4*-r*l,g=0):(s-1)%4==0?(h=0,g=4*-r*l):(s-2)%4==0?(h=l+4*r*l,g=l):(s-3)%4==0&amp;&amp;(h=-l,g=3*l+4*l*r),n&amp;&amp;(h=-h),p||(f=h,h=0);const v=`rotateX(${p?0:-i}deg) rotateY(${p?i:0}deg) translate3d(${h}px, ${f}px, ${g}px)`;if(o&lt;=1&amp;&amp;o&gt;-1&amp;&amp;(m=90*s+90*o,n&amp;&amp;(m=90*-s-90*o)),t.transform(v),c.slideShadows){let e=p?t.find(\".swiper-slide-shadow-left\"):t.find(\".swiper-slide-shadow-top\"),s=p?t.find(\".swiper-slide-shadow-right\"):t.find(\".swiper-slide-shadow-bottom\");0===e.length&amp;&amp;(e=d(`&lt;div class=\"swiper-slide-shadow-${p?\"left\":\"top\"}\"&gt;&lt;/div&gt;`),t.append(e)),0===s.length&amp;&amp;(s=d(`&lt;div class=\"swiper-slide-shadow-${p?\"right\":\"bottom\"}\"&gt;&lt;/div&gt;`),t.append(s)),e.length&amp;&amp;(e[0].style.opacity=Math.max(-o,0)),s.length&amp;&amp;(s[0].style.opacity=Math.max(o,0))}}if(s.css({\"-webkit-transform-origin\":`50% 50% -${l/2}px`,\"transform-origin\":`50% 50% -${l/2}px`}),c.shadow)if(p)h.transform(`translate3d(0px, ${i/2+c.shadowOffset}px, ${-i/2}px) rotateX(90deg) rotateZ(0deg) scale(${c.shadowScale})`);else{const e=Math.abs(m)-90*Math.floor(Math.abs(m)/90),t=1.5-(Math.sin(2*e*Math.PI/360)/2+Math.cos(2*e*Math.PI/360)/2),s=c.shadowScale,a=c.shadowScale/t,i=c.shadowOffset;h.transform(`scale3d(${s}, 1, ${a}) translate3d(0px, ${r/2+i}px, ${-r/2/a}px) rotateX(-90deg)`)}const f=o.isSafari||o.isWebView?-l/2:0;s.transform(`translate3d(0px,0,${f}px) rotateX(${e.isHorizontal()?0:m}deg) rotateY(${e.isHorizontal()?-m:0}deg)`)},setTransition:t=&gt;{const{$el:s,slides:a}=e;a.transition(t).find(\".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left\").transition(t),e.params.cubeEffect.shadow&amp;&amp;!e.isHorizontal()&amp;&amp;s.find(\".swiper-cube-shadow\").transition(t)},perspective:()=&gt;!0,overwriteParams:()=&gt;({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,resistanceRatio:0,spaceBetween:0,centeredSlides:!1,virtualTranslate:!0})})},function({swiper:e,extendParams:t,on:s}){t({flipEffect:{slideShadows:!0,limitRotation:!0,transformEl:null}}),F({effect:\"flip\",swiper:e,on:s,setTranslate:()=&gt;{const{slides:t,rtlTranslate:s}=e,a=e.params.flipEffect;for(let i=0;i&lt;t.length;i+=1){const r=t.eq(i);let n=r[0].progress;e.params.flipEffect.limitRotation&amp;&amp;(n=Math.max(Math.min(r[0].progress,1),-1));const l=r[0].swiperSlideOffset;let o=-180*n,d=0,c=e.params.cssMode?-l-e.translate:-l,p=0;if(e.isHorizontal()?s&amp;&amp;(o=-o):(p=c,c=0,d=-o,o=0),r[0].style.zIndex=-Math.abs(Math.round(n))+t.length,a.slideShadows){let t=e.isHorizontal()?r.find(\".swiper-slide-shadow-left\"):r.find(\".swiper-slide-shadow-top\"),s=e.isHorizontal()?r.find(\".swiper-slide-shadow-right\"):r.find(\".swiper-slide-shadow-bottom\");0===t.length&amp;&amp;(t=Z(a,r,e.isHorizontal()?\"left\":\"top\")),0===s.length&amp;&amp;(s=Z(a,r,e.isHorizontal()?\"right\":\"bottom\")),t.length&amp;&amp;(t[0].style.opacity=Math.max(-n,0)),s.length&amp;&amp;(s[0].style.opacity=Math.max(n,0))}const u=`translate3d(${c}px, ${p}px, 0px) rotateX(${d}deg) rotateY(${o}deg)`;U(a,r).transform(u)}},setTransition:t=&gt;{const{transformEl:s}=e.params.flipEffect;(s?e.slides.find(s):e.slides).transition(t).find(\".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left\").transition(t),K({swiper:e,duration:t,transformEl:s})},perspective:()=&gt;!0,overwriteParams:()=&gt;({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!e.params.cssMode})})},function({swiper:e,extendParams:t,on:s}){t({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0,transformEl:null}}),F({effect:\"coverflow\",swiper:e,on:s,setTranslate:()=&gt;{const{width:t,height:s,slides:a,slidesSizesGrid:i}=e,r=e.params.coverflowEffect,n=e.isHorizontal(),l=e.translate,o=n?t/2-l:s/2-l,d=n?r.rotate:-r.rotate,c=r.depth;for(let e=0,t=a.length;e&lt;t;e+=1){const t=a.eq(e),s=i[e],l=(o-t[0].swiperSlideOffset-s/2)/s*r.modifier;let p=n?d*l:0,u=n?0:d*l,h=-c*Math.abs(l),m=r.stretch;\"string\"==typeof m&amp;&amp;-1!==m.indexOf(\"%\")&amp;&amp;(m=parseFloat(r.stretch)/100*s);let f=n?0:m*l,g=n?m*l:0,v=1-(1-r.scale)*Math.abs(l);Math.abs(g)&lt;.001&amp;&amp;(g=0),Math.abs(f)&lt;.001&amp;&amp;(f=0),Math.abs(h)&lt;.001&amp;&amp;(h=0),Math.abs(p)&lt;.001&amp;&amp;(p=0),Math.abs(u)&lt;.001&amp;&amp;(u=0),Math.abs(v)&lt;.001&amp;&amp;(v=0);const w=`translate3d(${g}px,${f}px,${h}px)  rotateX(${u}deg) rotateY(${p}deg) scale(${v})`;if(U(r,t).transform(w),t[0].style.zIndex=1-Math.abs(Math.round(l)),r.slideShadows){let e=n?t.find(\".swiper-slide-shadow-left\"):t.find(\".swiper-slide-shadow-top\"),s=n?t.find(\".swiper-slide-shadow-right\"):t.find(\".swiper-slide-shadow-bottom\");0===e.length&amp;&amp;(e=Z(r,t,n?\"left\":\"top\")),0===s.length&amp;&amp;(s=Z(r,t,n?\"right\":\"bottom\")),e.length&amp;&amp;(e[0].style.opacity=l&gt;0?l:0),s.length&amp;&amp;(s[0].style.opacity=-l&gt;0?-l:0)}}},setTransition:t=&gt;{const{transformEl:s}=e.params.coverflowEffect;(s?e.slides.find(s):e.slides).transition(t).find(\".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left\").transition(t)},perspective:()=&gt;!0,overwriteParams:()=&gt;({watchSlidesProgress:!0})})},function({swiper:e,extendParams:t,on:s}){t({creativeEffect:{transformEl:null,limitProgress:1,shadowPerProgress:!1,progressMultiplier:1,perspective:!0,prev:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1},next:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1}}});const a=e=&gt;\"string\"==typeof e?e:`${e}px`;F({effect:\"creative\",swiper:e,on:s,setTranslate:()=&gt;{const{slides:t}=e,s=e.params.creativeEffect,{progressMultiplier:i}=s;for(let r=0;r&lt;t.length;r+=1){const n=t.eq(r),l=n[0].progress,o=Math.min(Math.max(n[0].progress,-s.limitProgress),s.limitProgress),d=n[0].swiperSlideOffset,c=[e.params.cssMode?-d-e.translate:-d,0,0],p=[0,0,0];let u=!1;e.isHorizontal()||(c[1]=c[0],c[0]=0);let h={translate:[0,0,0],rotate:[0,0,0],scale:1,opacity:1};o&lt;0?(h=s.next,u=!0):o&gt;0&amp;&amp;(h=s.prev,u=!0),c.forEach(((e,t)=&gt;{c[t]=`calc(${e}px + (${a(h.translate[t])} * ${Math.abs(o*i)}))`})),p.forEach(((e,t)=&gt;{p[t]=h.rotate[t]*Math.abs(o*i)})),n[0].style.zIndex=-Math.abs(Math.round(l))+t.length;const m=c.join(\", \"),f=`rotateX(${p[0]}deg) rotateY(${p[1]}deg) rotateZ(${p[2]}deg)`,g=o&lt;0?`scale(${1+(1-h.scale)*o*i})`:`scale(${1-(1-h.scale)*o*i})`,v=o&lt;0?1+(1-h.opacity)*o*i:1-(1-h.opacity)*o*i,w=`translate3d(${m}) ${f} ${g}`;if(u&amp;&amp;h.shadow||!u){let e=n.children(\".swiper-slide-shadow\");if(0===e.length&amp;&amp;h.shadow&amp;&amp;(e=Z(s,n)),e.length){const t=s.shadowPerProgress?o*(1/s.limitProgress):o;e[0].style.opacity=Math.min(Math.max(Math.abs(t),0),1)}}const b=U(s,n);b.transform(w).css({opacity:v}),h.origin&amp;&amp;b.css(\"transform-origin\",h.origin)}},setTransition:t=&gt;{const{transformEl:s}=e.params.creativeEffect;(s?e.slides.find(s):e.slides).transition(t).find(\".swiper-slide-shadow\").transition(t),K({swiper:e,duration:t,transformEl:s})},perspective:()=&gt;e.params.creativeEffect.perspective,overwriteParams:()=&gt;({watchSlidesProgress:!0,virtualTranslate:!e.params.cssMode})})},function({swiper:e,extendParams:t,on:s}){t({cardsEffect:{slideShadows:!0,transformEl:null}}),F({effect:\"cards\",swiper:e,on:s,setTranslate:()=&gt;{const{slides:t,activeIndex:s}=e,a=e.params.cardsEffect,{startTranslate:i,isTouched:r}=e.touchEventsData,n=e.translate;for(let l=0;l&lt;t.length;l+=1){const o=t.eq(l),d=o[0].progress,c=Math.min(Math.max(d,-4),4);let p=o[0].swiperSlideOffset;e.params.centeredSlides&amp;&amp;!e.params.cssMode&amp;&amp;e.$wrapperEl.transform(`translateX(${e.minTranslate()}px)`),e.params.centeredSlides&amp;&amp;e.params.cssMode&amp;&amp;(p-=t[0].swiperSlideOffset);let u=e.params.cssMode?-p-e.translate:-p,h=0;const m=-100*Math.abs(c);let f=1,g=-2*c,v=8-.75*Math.abs(c);const w=(l===s||l===s-1)&amp;&amp;c&gt;0&amp;&amp;c&lt;1&amp;&amp;(r||e.params.cssMode)&amp;&amp;n&lt;i,b=(l===s||l===s+1)&amp;&amp;c&lt;0&amp;&amp;c&gt;-1&amp;&amp;(r||e.params.cssMode)&amp;&amp;n&gt;i;if(w||b){const e=(1-Math.abs((Math.abs(c)-.5)/.5))**.5;g+=-28*c*e,f+=-.5*e,v+=96*e,h=-25*e*Math.abs(c)+\"%\"}if(u=c&lt;0?`calc(${u}px + (${v*Math.abs(c)}%))`:c&gt;0?`calc(${u}px + (-${v*Math.abs(c)}%))`:`${u}px`,!e.isHorizontal()){const e=h;h=u,u=e}const x=`\\n        translate3d(${u}, ${h}, ${m}px)\\n        rotateZ(${g}deg)\\n        scale(${c&lt;0?\"\"+(1+(1-f)*c):\"\"+(1-(1-f)*c)})\\n      `;if(a.slideShadows){let e=o.find(\".swiper-slide-shadow\");0===e.length&amp;&amp;(e=Z(a,o)),e.length&amp;&amp;(e[0].style.opacity=Math.min(Math.max((Math.abs(c)-.5)/.5,0),1))}o[0].style.zIndex=-Math.abs(Math.round(d))+t.length;U(a,o).transform(x)}},setTransition:t=&gt;{const{transformEl:s}=e.params.cardsEffect;(s?e.slides.find(s):e.slides).transition(t).find(\".swiper-slide-shadow\").transition(t),K({swiper:e,duration:t,transformEl:s})},perspective:()=&gt;!0,overwriteParams:()=&gt;({watchSlidesProgress:!0,virtualTranslate:!e.params.cssMode})})}];return H.use(J),H}));\n","<span class=\"code-comment\">//# sourceMappingURL=swiper-bundle.min.js.map</span>"]}}}</script></head><body><div id="root"><style data-emotion-css="gtfibm">html{box-sizing:border-box;}*,*:before,*:after{box-sizing:inherit;}html,body,#root{height:100%;margin:0;}body{font-family:-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;font-size:16px;line-height:1.5;overflow-wrap:break-word;background:white;color:black;}code{font-family:Menlo, Monaco, Lucida Console, Liberation Mono, DejaVu Sans Mono, Bitstream Vera Sans Mono, Courier New, monospace;}th,td{padding:0;}select{font-size:inherit;}#root{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;}</style><style data-emotion-css="1r6h1r6">.code-listing{background:#fbfdff;color:#383a42;}.code-comment,.code-quote{color:#a0a1a7;font-style:italic;}.code-doctag,.code-keyword,.code-link,.code-formula{color:#a626a4;}.code-section,.code-name,.code-selector-tag,.code-deletion,.code-subst{color:#e45649;}.code-literal{color:#0184bb;}.code-string,.code-regexp,.code-addition,.code-attribute,.code-meta-string{color:#50a14f;}.code-built_in,.code-class .code-title{color:#c18401;}.code-attr,.code-variable,.code-template-variable,.code-type,.code-selector-class,.code-selector-attr,.code-selector-pseudo,.code-number{color:#986801;}.code-symbol,.code-bullet,.code-meta,.code-selector-id,.code-title{color:#4078f2;}.code-emphasis{font-style:italic;}.code-strong{font-weight:bold;}</style><style data-emotion-css="1c3h18e">.css-1c3h18e{-webkit-flex:1 0 auto;-ms-flex:1 0 auto;flex:1 0 auto;}</style><div class="css-1c3h18e"><style data-emotion-css="1cfuj1t">.css-1cfuj1t{max-width:940px;padding:0 20px;margin:0 auto;}</style><div class="css-1cfuj1t"><style data-emotion-css="i51og3">.css-i51og3{margin-top:2rem;}</style><header class="css-i51og3"><style data-emotion-css="1y7u1xh">.css-1y7u1xh{text-align:center;font-size:3rem;-webkit-letter-spacing:0.05em;-moz-letter-spacing:0.05em;-ms-letter-spacing:0.05em;letter-spacing:0.05em;}</style><h1 class="css-1y7u1xh"><style data-emotion-css="1ydg16i">.css-1ydg16i{color:#000;-webkit-text-decoration:none;text-decoration:none;}</style><a href="/" class="css-1ydg16i">UNPKG</a></h1></header></div><div class="css-1cfuj1t"><style data-emotion-css="93o42g">.css-93o42g{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}@media (max-width:700px){.css-93o42g{-webkit-flex-direction:column-reverse;-ms-flex-direction:column-reverse;flex-direction:column-reverse;-webkit-align-items:flex-start;-webkit-box-align:flex-start;-ms-flex-align:flex-start;align-items:flex-start;}}</style><header class="css-93o42g"><style data-emotion-css="1dlpvgi">.css-1dlpvgi{font-size:1.5rem;font-weight:normal;-webkit-flex:1;-ms-flex:1;flex:1;word-break:break-all;}</style><h1 class="css-1dlpvgi"><nav><style data-emotion-css="xt128v">.css-xt128v{color:#0076ff;-webkit-text-decoration:none;text-decoration:none;}.css-xt128v:hover{-webkit-text-decoration:underline;text-decoration:underline;}</style><a href="/browse/swiper@7.0.5/" class="css-xt128v">swiper</a><style data-emotion-css="lllnmq">.css-lllnmq{padding-left:5px;padding-right:5px;}</style><span class="css-lllnmq">/</span><strong>swiper-bundle.min.js</strong></nav></h1><style data-emotion-css="1nr3dab">.css-1nr3dab{margin-left:20px;}@media (max-width:700px){.css-1nr3dab{margin-left:0;margin-bottom:0;}}</style><p class="css-1nr3dab"><label>Version:<!-- --> <style data-emotion-css="un3bt6">.css-un3bt6{-webkit-appearance:none;-moz-appearance:none;appearance:none;cursor:pointer;padding:4px 24px 4px 8px;font-weight:600;font-size:0.9em;color:#24292e;border:1px solid rgba(27,31,35,.2);border-radius:3px;background-color:#eff3f6;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAKCAYAAAC9vt6cAAAAAXNSR0IArs4c6QAAARFJREFUKBVjZAACNS39RhBNKrh17WI9o4quoT3Dn78HSNUMUs/CzOTI/O7Vi4dCYpJ3/jP+92BkYGAlyiBGhm8MjIxJt65e3MQM0vDu9YvLYmISILYZELOBxHABRkaGr0yMzF23r12YDFIDNgDEePv65SEhEXENBkYGFSAXuyGMjF8Z/jOsvX3tYiFIDwgwQSgIaaijnvj/P8M5IO8HsjiY/f//D4b//88A1SQhywG9jQr09PS4v/1mPAeUUPzP8B8cJowMjL+Bqu6xMQmaXL164AuyDgwDQJLa2qYSP//9vARkCoMVMzK8YeVkNbh+9uxzMB+JwGoASF5Vx0jz/98/18BqmZi171w9D2EjaaYKEwAEK00XQLdJuwAAAABJRU5ErkJggg==);background-position:right 8px center;background-repeat:no-repeat;background-size:auto 25%;}.css-un3bt6:hover{background-color:#e6ebf1;border-color:rgba(27,31,35,.35);}.css-un3bt6:active{background-color:#e9ecef;border-color:rgba(27,31,35,.35);box-shadow:inset 0 0.15em 0.3em rgba(27,31,35,.15);}</style><select name="version" class="css-un3bt6"><option value="0.9.0-beta.12">0.9.0-beta.12</option><option value="0.9.0-beta.14">0.9.0-beta.14</option><option value="0.9.0-beta.15">0.9.0-beta.15</option><option value="0.9.0-beta.16">0.9.0-beta.16</option><option value="2.7.0">2.7.0</option><option value="2.7.5">2.7.5</option><option value="2.7.6">2.7.6</option><option value="3.0.0">3.0.0</option><option value="3.0.1">3.0.1</option><option value="3.0.2">3.0.2</option><option value="3.0.3">3.0.3</option><option value="3.0.4">3.0.4</option><option value="3.0.5">3.0.5</option><option value="3.0.6">3.0.6</option><option value="3.0.7">3.0.7</option><option value="3.0.8">3.0.8</option><option value="3.1.0">3.1.0</option><option value="3.1.2">3.1.2</option><option value="3.1.5">3.1.5</option><option value="3.1.7">3.1.7</option><option value="3.2.0">3.2.0</option><option value="3.2.5">3.2.5</option><option value="3.2.6">3.2.6</option><option value="3.2.7">3.2.7</option><option value="3.3.0">3.3.0</option><option value="3.3.1">3.3.1</option><option value="3.4.0">3.4.0</option><option value="3.4.1">3.4.1</option><option value="3.4.2">3.4.2</option><option value="4.0.0-beta.1">4.0.0-beta.1</option><option value="4.0.0-beta.2">4.0.0-beta.2</option><option value="4.0.0-beta.3">4.0.0-beta.3</option><option value="4.0.0-beta.4">4.0.0-beta.4</option><option value="4.0.0">4.0.0</option><option value="4.0.1">4.0.1</option><option value="4.0.2">4.0.2</option><option value="4.0.3">4.0.3</option><option value="4.0.5">4.0.5</option><option value="4.0.6">4.0.6</option><option value="4.0.7">4.0.7</option><option value="4.1.0">4.1.0</option><option value="4.1.5">4.1.5</option><option value="4.1.6">4.1.6</option><option value="4.2.0">4.2.0</option><option value="4.2.2">4.2.2</option><option value="4.2.5">4.2.5</option><option value="4.2.6">4.2.6</option><option value="4.3.0">4.3.0</option><option value="4.3.2">4.3.2</option><option value="4.3.3">4.3.3</option><option value="4.3.5">4.3.5</option><option value="4.4.0">4.4.0</option><option value="4.4.1">4.4.1</option><option value="4.4.2">4.4.2</option><option value="4.4.5">4.4.5</option><option value="4.4.6">4.4.6</option><option value="4.5.0">4.5.0</option><option value="4.5.1">4.5.1</option><option value="5.0.0">5.0.0</option><option value="5.0.1">5.0.1</option><option value="5.0.2">5.0.2</option><option value="5.0.3">5.0.3</option><option value="5.0.4">5.0.4</option><option value="5.1.0">5.1.0</option><option value="5.2.0">5.2.0</option><option value="5.2.1">5.2.1</option><option value="5.3.0">5.3.0</option><option value="5.3.1">5.3.1</option><option value="5.3.5">5.3.5</option><option value="5.3.6">5.3.6</option><option value="5.3.7">5.3.7</option><option value="5.3.8">5.3.8</option><option value="5.4.0">5.4.0</option><option value="5.4.1">5.4.1</option><option value="5.4.2">5.4.2</option><option value="5.4.3">5.4.3</option><option value="5.4.4">5.4.4</option><option value="5.4.5">5.4.5</option><option value="6.0.0-alpha.1">6.0.0-alpha.1</option><option value="6.0.0-alpha.2">6.0.0-alpha.2</option><option value="6.0.0-alpha.3">6.0.0-alpha.3</option><option value="6.0.0-alpha.4">6.0.0-alpha.4</option><option value="6.0.0-alpha.5">6.0.0-alpha.5</option><option value="6.0.0-alpha.6">6.0.0-alpha.6</option><option value="6.0.0-alpha.7">6.0.0-alpha.7</option><option value="6.0.0-alpha.8">6.0.0-alpha.8</option><option value="6.0.0-alpha.9">6.0.0-alpha.9</option><option value="6.0.0-alpha.10">6.0.0-alpha.10</option><option value="6.0.0-alpha.11">6.0.0-alpha.11</option><option value="6.0.0-alpha.12">6.0.0-alpha.12</option><option value="6.0.0-alpha.15">6.0.0-alpha.15</option><option value="6.0.0-alpha.16">6.0.0-alpha.16</option><option value="6.0.0-alpha.17">6.0.0-alpha.17</option><option value="6.0.0-alpha.18">6.0.0-alpha.18</option><option value="6.0.0">6.0.0</option><option value="6.0.1">6.0.1</option><option value="6.0.2">6.0.2</option><option value="6.0.3">6.0.3</option><option value="6.0.4">6.0.4</option><option value="6.1.0">6.1.0</option><option value="6.1.1">6.1.1</option><option value="6.1.2">6.1.2</option><option value="6.1.3">6.1.3</option><option value="6.2.0">6.2.0</option><option value="6.3.0">6.3.0</option><option value="6.3.1">6.3.1</option><option value="6.3.2">6.3.2</option><option value="6.3.3">6.3.3</option><option value="6.3.4">6.3.4</option><option value="6.3.5">6.3.5</option><option value="6.4.0">6.4.0</option><option value="6.4.1">6.4.1</option><option value="6.4.2">6.4.2</option><option value="6.4.3">6.4.3</option><option value="6.4.4">6.4.4</option><option value="6.4.5">6.4.5</option><option value="6.4.6">6.4.6</option><option value="6.4.7">6.4.7</option><option value="6.4.8">6.4.8</option><option value="6.4.9">6.4.9</option><option value="6.4.10">6.4.10</option><option value="6.4.11">6.4.11</option><option value="6.4.12">6.4.12</option><option value="6.4.14">6.4.14</option><option value="6.4.15">6.4.15</option><option value="6.5.0">6.5.0</option><option value="6.5.1">6.5.1</option><option value="6.5.2">6.5.2</option><option value="6.5.3">6.5.3</option><option value="6.5.4">6.5.4</option><option value="6.5.5-beta.1">6.5.5-beta.1</option><option value="6.5.5">6.5.5</option><option value="6.5.6">6.5.6</option><option value="6.5.7">6.5.7</option><option value="6.5.8">6.5.8</option><option value="6.5.9">6.5.9</option><option value="6.6.0">6.6.0</option><option value="6.6.1">6.6.1</option><option value="6.6.2">6.6.2</option><option value="6.7.0">6.7.0</option><option value="6.7.1">6.7.1</option><option value="6.7.5">6.7.5</option><option value="6.8.0-beta.1">6.8.0-beta.1</option><option value="6.8.0">6.8.0</option><option value="6.8.1">6.8.1</option><option value="6.8.2">6.8.2</option><option value="6.8.3">6.8.3</option><option value="6.8.4">6.8.4</option><option value="7.0.0-alpha.1">7.0.0-alpha.1</option><option value="7.0.0-alpha.2">7.0.0-alpha.2</option><option value="7.0.0-alpha.3">7.0.0-alpha.3</option><option value="7.0.0-alpha.4">7.0.0-alpha.4</option><option value="7.0.0-alpha.5">7.0.0-alpha.5</option><option value="7.0.0-alpha.6">7.0.0-alpha.6</option><option value="7.0.0-alpha.7">7.0.0-alpha.7</option><option value="7.0.0-alpha.8">7.0.0-alpha.8</option><option value="7.0.0-alpha.9">7.0.0-alpha.9</option><option value="7.0.0-alpha.10">7.0.0-alpha.10</option><option value="7.0.0-alpha.11">7.0.0-alpha.11</option><option value="7.0.0-alpha.12">7.0.0-alpha.12</option><option value="7.0.0-alpha.14">7.0.0-alpha.14</option><option value="7.0.0-alpha.15">7.0.0-alpha.15</option><option value="7.0.0-alpha.16">7.0.0-alpha.16</option><option value="7.0.0-alpha.17">7.0.0-alpha.17</option><option value="7.0.0-alpha.18">7.0.0-alpha.18</option><option value="7.0.0-alpha.19">7.0.0-alpha.19</option><option value="7.0.0-alpha.20">7.0.0-alpha.20</option><option value="7.0.0-alpha.21">7.0.0-alpha.21</option><option value="7.0.0-alpha.22">7.0.0-alpha.22</option><option value="7.0.0-alpha.23">7.0.0-alpha.23</option><option value="7.0.0-alpha.24">7.0.0-alpha.24</option><option value="7.0.0-alpha.25">7.0.0-alpha.25</option><option value="7.0.0-alpha.26">7.0.0-alpha.26</option><option value="7.0.0-alpha.27">7.0.0-alpha.27</option><option value="7.0.0-alpha.28">7.0.0-alpha.28</option><option value="7.0.0-alpha.29">7.0.0-alpha.29</option><option value="7.0.0-alpha.30">7.0.0-alpha.30</option><option value="7.0.0-alpha.31">7.0.0-alpha.31</option><option value="7.0.0-alpha.32">7.0.0-alpha.32</option><option value="7.0.0-alpha.33">7.0.0-alpha.33</option><option value="7.0.0-alpha.34">7.0.0-alpha.34</option><option value="7.0.0-alpha.35">7.0.0-alpha.35</option><option value="7.0.0-alpha.36">7.0.0-alpha.36</option><option value="7.0.0-alpha.37">7.0.0-alpha.37</option><option value="7.0.0-alpha.38">7.0.0-alpha.38</option><option value="7.0.0-alpha.39">7.0.0-alpha.39</option><option value="7.0.0-alpha.40">7.0.0-alpha.40</option><option value="7.0.0">7.0.0</option><option value="7.0.1">7.0.1</option><option value="7.0.2">7.0.2</option><option value="7.0.3">7.0.3</option><option value="7.0.4">7.0.4</option><option selected="" value="7.0.5">7.0.5</option><option value="7.0.6">7.0.6</option><option value="7.0.7">7.0.7</option><option value="7.0.8">7.0.8</option><option value="7.0.9">7.0.9</option><option value="7.1.0">7.1.0</option><option value="7.2.0">7.2.0</option><option value="7.3.0">7.3.0</option><option value="7.3.1">7.3.1</option><option value="7.3.2">7.3.2</option><option value="7.3.3">7.3.3</option><option value="7.3.4">7.3.4</option><option value="7.4.0">7.4.0</option><option value="7.4.1">7.4.1</option><option value="8.0.0">8.0.0</option><option value="8.0.1">8.0.1</option><option value="8.0.2">8.0.2</option><option value="8.0.3">8.0.3</option><option value="8.0.4">8.0.4</option><option value="8.0.5">8.0.5</option><option value="8.0.6">8.0.6</option><option value="8.0.7">8.0.7</option><option value="8.1.0">8.1.0</option><option value="8.1.1">8.1.1</option><option value="8.1.2">8.1.2</option><option value="8.1.3">8.1.3</option><option value="8.1.4">8.1.4</option><option value="8.1.5">8.1.5</option><option value="8.1.6">8.1.6</option><option value="8.2.0">8.2.0</option><option value="8.2.1">8.2.1</option><option value="8.2.2">8.2.2</option><option value="8.2.3">8.2.3</option><option value="8.2.4">8.2.4</option><option value="8.2.5">8.2.5</option><option value="8.2.6">8.2.6</option><option value="8.3.0">8.3.0</option><option value="8.3.1">8.3.1</option><option value="8.3.2">8.3.2</option><option value="8.4.0">8.4.0</option><option value="8.4.1">8.4.1</option><option value="8.4.2">8.4.2</option><option value="8.4.3">8.4.3</option><option value="8.4.4">8.4.4</option><option value="8.4.5">8.4.5</option><option value="8.4.6">8.4.6</option><option value="8.4.7">8.4.7</option><option value="9.0.0-beta.1">9.0.0-beta.1</option><option value="9.0.0-beta.2">9.0.0-beta.2</option><option value="9.0.0-beta.3">9.0.0-beta.3</option><option value="9.0.0-beta.4">9.0.0-beta.4</option><option value="9.0.0-beta.5">9.0.0-beta.5</option><option value="9.0.0-beta.6">9.0.0-beta.6</option><option value="9.0.0-beta.7">9.0.0-beta.7</option><option value="9.0.0-beta.8">9.0.0-beta.8</option><option value="9.0.0-beta.9">9.0.0-beta.9</option><option value="9.0.0-beta.10">9.0.0-beta.10</option><option value="9.0.0-beta.11">9.0.0-beta.11</option><option value="9.0.0-beta.16">9.0.0-beta.16</option><option value="9.0.0-beta.17">9.0.0-beta.17</option><option value="9.0.0-beta.18">9.0.0-beta.18</option><option value="9.0.0-beta.19">9.0.0-beta.19</option><option value="9.0.0-beta.20">9.0.0-beta.20</option><option value="9.0.0-beta.21">9.0.0-beta.21</option><option value="9.0.0-beta.22">9.0.0-beta.22</option><option value="9.0.0-beta.23">9.0.0-beta.23</option><option value="9.0.0-beta.24">9.0.0-beta.24</option><option value="9.0.0-beta.25">9.0.0-beta.25</option><option value="9.0.0-beta.26">9.0.0-beta.26</option><option value="9.0.0-beta.28">9.0.0-beta.28</option><option value="9.0.0-beta.29">9.0.0-beta.29</option><option value="9.0.0-beta.30">9.0.0-beta.30</option><option value="9.0.0-beta.31">9.0.0-beta.31</option><option value="9.0.0-beta.32">9.0.0-beta.32</option><option value="9.0.0-beta.33">9.0.0-beta.33</option><option value="9.0.0-beta.34">9.0.0-beta.34</option><option value="9.0.0-beta.35">9.0.0-beta.35</option><option value="9.0.0-beta.36">9.0.0-beta.36</option><option value="9.0.0-beta.38">9.0.0-beta.38</option><option value="9.0.0-beta.40">9.0.0-beta.40</option><option value="9.0.0-beta.41">9.0.0-beta.41</option><option value="9.0.0-beta.42">9.0.0-beta.42</option><option value="9.0.0">9.0.0</option><option value="9.0.1">9.0.1</option><option value="9.0.2">9.0.2</option><option value="9.0.3">9.0.3</option><option value="9.0.4">9.0.4</option><option value="9.0.5">9.0.5</option><option value="9.1.0">9.1.0</option><option value="9.1.1">9.1.1</option><option value="9.2.0">9.2.0</option><option value="9.2.1">9.2.1</option><option value="9.2.2">9.2.2</option><option value="9.2.3">9.2.3</option><option value="9.2.4">9.2.4</option><option value="9.3.0-beta.1">9.3.0-beta.1</option><option value="9.3.0">9.3.0</option><option value="9.3.1">9.3.1</option><option value="9.3.2">9.3.2</option><option value="9.4.0">9.4.0</option><option value="9.4.1">9.4.1</option><option value="10.0.0-beta.1">10.0.0-beta.1</option><option value="10.0.0-beta.2">10.0.0-beta.2</option><option value="10.0.0-beta.3">10.0.0-beta.3</option><option value="10.0.0-beta.4">10.0.0-beta.4</option><option value="10.0.0-beta.5">10.0.0-beta.5</option><option value="10.0.0-beta.6">10.0.0-beta.6</option><option value="10.0.0">10.0.0</option><option value="10.0.1">10.0.1</option><option value="10.0.2">10.0.2</option><option value="10.0.3">10.0.3</option><option value="10.0.4">10.0.4</option><option value="10.1.0">10.1.0</option><option value="10.2.0">10.2.0</option><option value="10.3.0">10.3.0</option><option value="10.3.1">10.3.1</option><option value="11.0.0-beta.1">11.0.0-beta.1</option></select></label></p></header></div><style data-emotion-css="107j3ms">.css-107j3ms{max-width:940px;padding:0 20px;margin:0 auto;}@media (max-width:700px){.css-107j3ms{padding:0;margin:0;}}</style><div class="css-107j3ms"><style data-emotion-css="q3frg4">.css-q3frg4{border:1px solid #dfe2e5;border-radius:3px;}@media (max-width:700px){.css-q3frg4{border-right-width:0;border-left-width:0;}}</style><div class="css-q3frg4"><style data-emotion-css="10o5omr">.css-10o5omr{padding:10px;background:#f6f8fa;color:#424242;border:1px solid #d1d5da;border-top-left-radius:3px;border-top-right-radius:3px;margin:-1px -1px 0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;}@media (max-width:700px){.css-10o5omr{padding-right:20px;padding-left:20px;}}</style><div class="css-10o5omr"><span>135 kB</span><span>JavaScript</span><span><style data-emotion-css="18x593j">.css-18x593j{display:inline-block;margin-left:8px;padding:2px 8px;-webkit-text-decoration:none;text-decoration:none;font-weight:600;font-size:0.9rem;color:#24292e;background-color:#eff3f6;border:1px solid rgba(27,31,35,.2);border-radius:3px;}.css-18x593j:hover{background-color:#e6ebf1;border-color:rgba(27,31,35,.35);}.css-18x593j:active{background-color:#e9ecef;border-color:rgba(27,31,35,.35);box-shadow:inset 0 0.15em 0.3em rgba(27,31,35,.15);}</style><a href="/swiper@7.0.5/swiper-bundle.min.js" class="css-18x593j">View Raw</a></span></div><style data-emotion-css="1i31ihw">.css-1i31ihw{overflow-x:auto;overflow-y:hidden;padding-top:5px;padding-bottom:5px;}</style><div class="code-listing css-1i31ihw"><style data-emotion-css="173nir8">.css-173nir8{border:none;border-collapse:collapse;border-spacing:0;}</style><table class="css-173nir8"><tbody><tr><style data-emotion-css="a4x74f">.css-a4x74f{padding-left:10px;padding-right:10px;color:rgba(27,31,35,.3);text-align:right;vertical-align:top;width:1%;min-width:50px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;}</style><td id="L1" class="css-a4x74f"><span>1</span></td><style data-emotion-css="1dcdqdg">.css-1dcdqdg{padding-left:10px;padding-right:10px;color:#24292e;white-space:pre;}</style><td id="LC1" class="css-1dcdqdg"><code><span class="code-comment">/**
</span></code></td></tr><tr><td id="L2" class="css-a4x74f"><span>2</span></td><td id="LC2" class="css-1dcdqdg"><code><span class="code-comment"> * Swiper 7.0.5
</span></code></td></tr><tr><td id="L3" class="css-a4x74f"><span>3</span></td><td id="LC3" class="css-1dcdqdg"><code><span class="code-comment"> * Most modern mobile touch slider and framework with hardware accelerated transitions
</span></code></td></tr><tr><td id="L4" class="css-a4x74f"><span>4</span></td><td id="LC4" class="css-1dcdqdg"><code><span class="code-comment"> * https://swiperjs.com
</span></code></td></tr><tr><td id="L5" class="css-a4x74f"><span>5</span></td><td id="LC5" class="css-1dcdqdg"><code><span class="code-comment"> *
</span></code></td></tr><tr><td id="L6" class="css-a4x74f"><span>6</span></td><td id="LC6" class="css-1dcdqdg"><code><span class="code-comment"> * Copyright 2014-2021 Vladimir Kharlampidi
</span></code></td></tr><tr><td id="L7" class="css-a4x74f"><span>7</span></td><td id="LC7" class="css-1dcdqdg"><code><span class="code-comment"> *
</span></code></td></tr><tr><td id="L8" class="css-a4x74f"><span>8</span></td><td id="LC8" class="css-1dcdqdg"><code><span class="code-comment"> * Released under the MIT License
</span></code></td></tr><tr><td id="L9" class="css-a4x74f"><span>9</span></td><td id="LC9" class="css-1dcdqdg"><code><span class="code-comment"> *
</span></code></td></tr><tr><td id="L10" class="css-a4x74f"><span>10</span></td><td id="LC10" class="css-1dcdqdg"><code><span class="code-comment"> * Released on: September 9, 2021
</span></code></td></tr><tr><td id="L11" class="css-a4x74f"><span>11</span></td><td id="LC11" class="css-1dcdqdg"><code><span class="code-comment"> */</span>
</code></td></tr><tr><td id="L12" class="css-a4x74f"><span>12</span></td><td id="LC12" class="css-1dcdqdg"><code>
</code></td></tr><tr><td id="L13" class="css-a4x74f"><span>13</span></td><td id="LC13" class="css-1dcdqdg"><code>!function(e,t){"object"==typeof exports&amp;&amp;"undefined"!=typeof module?module.exports=t():"function"==typeof define&amp;&amp;define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).Swiper=t()}(this,(function(){"use strict";function e(e){return null!==e&amp;&amp;"object"==typeof e&amp;&amp;"constructor"in e&amp;&amp;e.constructor===Object}function t(s={},a={}){Object.keys(a).forEach((i=&gt;{void 0===s[i]?s[i]=a[i]:e(a[i])&amp;&amp;e(s[i])&amp;&amp;Object.keys(a[i]).length&gt;0&amp;&amp;t(s[i],a[i])}))}const s={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=&gt;null,querySelectorAll:()=&gt;[],getElementById:()=&gt;null,createEvent:()=&gt;({initEvent(){}}),createElement:()=&gt;({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=&gt;[]}),createElementNS:()=&gt;({}),importNode:()=&gt;null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function a(){const e="undefined"!=typeof document?document:{};return t(e,s),e}const i={document:s,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=&gt;({getPropertyValue:()=&gt;""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=&gt;({}),requestAnimationFrame:e=&gt;"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&amp;&amp;clearTimeout(e)}};function r(){const e="undefined"!=typeof window?window:{};return t(e,i),e}class n extends Array{constructor(e){super(...e||[]),function(e){const t=e.__proto__;Object.defineProperty(e,"__proto__",{get:()=&gt;t,set(e){t.__proto__=e}})}(this)}}function l(e=[]){const t=[];return e.forEach((e=&gt;{Array.isArray(e)?t.push(...l(e)):t.push(e)})),t}function o(e,t){return Array.prototype.filter.call(e,t)}function d(e,t){const s=r(),i=a();let l=[];if(!t&amp;&amp;e instanceof n)return e;if(!e)return new n(l);if("string"==typeof e){const s=e.trim();if(s.indexOf("&lt;")&gt;=0&amp;&amp;s.indexOf("&gt;")&gt;=0){let e="div";0===s.indexOf("&lt;li")&amp;&amp;(e="ul"),0===s.indexOf("&lt;tr")&amp;&amp;(e="tbody"),0!==s.indexOf("&lt;td")&amp;&amp;0!==s.indexOf("&lt;th")||(e="tr"),0===s.indexOf("&lt;tbody")&amp;&amp;(e="table"),0===s.indexOf("&lt;option")&amp;&amp;(e="select");const t=i.createElement(e);t.innerHTML=s;for(let e=0;e&lt;t.childNodes.length;e+=1)l.push(t.childNodes[e])}else l=function(e,t){if("string"!=typeof e)return[e];const s=[],a=t.querySelectorAll(e);for(let e=0;e&lt;a.length;e+=1)s.push(a[e]);return s}(e.trim(),t||i)}else if(e.nodeType||e===s||e===i)l.push(e);else if(Array.isArray(e)){if(e instanceof n)return e;l=e}return new n(function(e){const t=[];for(let s=0;s&lt;e.length;s+=1)-1===t.indexOf(e[s])&amp;&amp;t.push(e[s]);return t}(l))}d.fn=n.prototype;const c={addClass:function(...e){const t=l(e.map((e=&gt;e.split(" "))));return this.forEach((e=&gt;{e.classList.add(...t)})),this},removeClass:function(...e){const t=l(e.map((e=&gt;e.split(" "))));return this.forEach((e=&gt;{e.classList.remove(...t)})),this},hasClass:function(...e){const t=l(e.map((e=&gt;e.split(" "))));return o(this,(e=&gt;t.filter((t=&gt;e.classList.contains(t))).length&gt;0)).length&gt;0},toggleClass:function(...e){const t=l(e.map((e=&gt;e.split(" "))));this.forEach((e=&gt;{t.forEach((t=&gt;{e.classList.toggle(t)}))}))},attr:function(e,t){if(1===arguments.length&amp;&amp;"string"==typeof e)return this[0]?this[0].getAttribute(e):void 0;for(let s=0;s&lt;this.length;s+=1)if(2===arguments.length)this[s].setAttribute(e,t);else for(const t in e)this[s][t]=e[t],this[s].setAttribute(t,e[t]);return this},removeAttr:function(e){for(let t=0;t&lt;this.length;t+=1)this[t].removeAttribute(e);return this},transform:function(e){for(let t=0;t&lt;this.length;t+=1)this[t].style.transform=e;return this},transition:function(e){for(let t=0;t&lt;this.length;t+=1)this[t].style.transitionDuration="string"!=typeof e?`${e}ms`:e;return this},on:function(...e){let[t,s,a,i]=e;function r(e){const t=e.target;if(!t)return;const i=e.target.dom7EventData||[];if(i.indexOf(e)&lt;0&amp;&amp;i.unshift(e),d(t).is(s))a.apply(t,i);else{const e=d(t).parents();for(let t=0;t&lt;e.length;t+=1)d(e[t]).is(s)&amp;&amp;a.apply(e[t],i)}}function n(e){const t=e&amp;&amp;e.target&amp;&amp;e.target.dom7EventData||[];t.indexOf(e)&lt;0&amp;&amp;t.unshift(e),a.apply(this,t)}"function"==typeof e[1]&amp;&amp;([t,a,i]=e,s=void 0),i||(i=!1);const l=t.split(" ");let o;for(let e=0;e&lt;this.length;e+=1){const t=this[e];if(s)for(o=0;o&lt;l.length;o+=1){const e=l[o];t.dom7LiveListeners||(t.dom7LiveListeners={}),t.dom7LiveListeners[e]||(t.dom7LiveListeners[e]=[]),t.dom7LiveListeners[e].push({listener:a,proxyListener:r}),t.addEventListener(e,r,i)}else for(o=0;o&lt;l.length;o+=1){const e=l[o];t.dom7Listeners||(t.dom7Listeners={}),t.dom7Listeners[e]||(t.dom7Listeners[e]=[]),t.dom7Listeners[e].push({listener:a,proxyListener:n}),t.addEventListener(e,n,i)}}return this},off:function(...e){let[t,s,a,i]=e;"function"==typeof e[1]&amp;&amp;([t,a,i]=e,s=void 0),i||(i=!1);const r=t.split(" ");for(let e=0;e&lt;r.length;e+=1){const t=r[e];for(let e=0;e&lt;this.length;e+=1){const r=this[e];let n;if(!s&amp;&amp;r.dom7Listeners?n=r.dom7Listeners[t]:s&amp;&amp;r.dom7LiveListeners&amp;&amp;(n=r.dom7LiveListeners[t]),n&amp;&amp;n.length)for(let e=n.length-1;e&gt;=0;e-=1){const s=n[e];a&amp;&amp;s.listener===a||a&amp;&amp;s.listener&amp;&amp;s.listener.dom7proxy&amp;&amp;s.listener.dom7proxy===a?(r.removeEventListener(t,s.proxyListener,i),n.splice(e,1)):a||(r.removeEventListener(t,s.proxyListener,i),n.splice(e,1))}}}return this},trigger:function(...e){const t=r(),s=e[0].split(" "),a=e[1];for(let i=0;i&lt;s.length;i+=1){const r=s[i];for(let s=0;s&lt;this.length;s+=1){const i=this[s];if(t.CustomEvent){const s=new t.CustomEvent(r,{detail:a,bubbles:!0,cancelable:!0});i.dom7EventData=e.filter(((e,t)=&gt;t&gt;0)),i.dispatchEvent(s),i.dom7EventData=[],delete i.dom7EventData}}}return this},transitionEnd:function(e){const t=this;return e&amp;&amp;t.on("transitionend",(function s(a){a.target===this&amp;&amp;(e.call(this,a),t.off("transitionend",s))})),this},outerWidth:function(e){if(this.length&gt;0){if(e){const e=this.styles();return this[0].offsetWidth+parseFloat(e.getPropertyValue("margin-right"))+parseFloat(e.getPropertyValue("margin-left"))}return this[0].offsetWidth}return null},outerHeight:function(e){if(this.length&gt;0){if(e){const e=this.styles();return this[0].offsetHeight+parseFloat(e.getPropertyValue("margin-top"))+parseFloat(e.getPropertyValue("margin-bottom"))}return this[0].offsetHeight}return null},styles:function(){const e=r();return this[0]?e.getComputedStyle(this[0],null):{}},offset:function(){if(this.length&gt;0){const e=r(),t=a(),s=this[0],i=s.getBoundingClientRect(),n=t.body,l=s.clientTop||n.clientTop||0,o=s.clientLeft||n.clientLeft||0,d=s===e?e.scrollY:s.scrollTop,c=s===e?e.scrollX:s.scrollLeft;return{top:i.top+d-l,left:i.left+c-o}}return null},css:function(e,t){const s=r();let a;if(1===arguments.length){if("string"!=typeof e){for(a=0;a&lt;this.length;a+=1)for(const t in e)this[a].style[t]=e[t];return this}if(this[0])return s.getComputedStyle(this[0],null).getPropertyValue(e)}if(2===arguments.length&amp;&amp;"string"==typeof e){for(a=0;a&lt;this.length;a+=1)this[a].style[e]=t;return this}return this},each:function(e){return e?(this.forEach(((t,s)=&gt;{e.apply(t,[t,s])})),this):this},html:function(e){if(void 0===e)return this[0]?this[0].innerHTML:null;for(let t=0;t&lt;this.length;t+=1)this[t].innerHTML=e;return this},text:function(e){if(void 0===e)return this[0]?this[0].textContent.trim():null;for(let t=0;t&lt;this.length;t+=1)this[t].textContent=e;return this},is:function(e){const t=r(),s=a(),i=this[0];let l,o;if(!i||void 0===e)return!1;if("string"==typeof e){if(i.matches)return i.matches(e);if(i.webkitMatchesSelector)return i.webkitMatchesSelector(e);if(i.msMatchesSelector)return i.msMatchesSelector(e);for(l=d(e),o=0;o&lt;l.length;o+=1)if(l[o]===i)return!0;return!1}if(e===s)return i===s;if(e===t)return i===t;if(e.nodeType||e instanceof n){for(l=e.nodeType?[e]:e,o=0;o&lt;l.length;o+=1)if(l[o]===i)return!0;return!1}return!1},index:function(){let e,t=this[0];if(t){for(e=0;null!==(t=t.previousSibling);)1===t.nodeType&amp;&amp;(e+=1);return e}},eq:function(e){if(void 0===e)return this;const t=this.length;if(e&gt;t-1)return d([]);if(e&lt;0){const s=t+e;return d(s&lt;0?[]:[this[s]])}return d([this[e]])},append:function(...e){let t;const s=a();for(let a=0;a&lt;e.length;a+=1){t=e[a];for(let e=0;e&lt;this.length;e+=1)if("string"==typeof t){const a=s.createElement("div");for(a.innerHTML=t;a.firstChild;)this[e].appendChild(a.firstChild)}else if(t instanceof n)for(let s=0;s&lt;t.length;s+=1)this[e].appendChild(t[s]);else this[e].appendChild(t)}return this},prepend:function(e){const t=a();let s,i;for(s=0;s&lt;this.length;s+=1)if("string"==typeof e){const a=t.createElement("div");for(a.innerHTML=e,i=a.childNodes.length-1;i&gt;=0;i-=1)this[s].insertBefore(a.childNodes[i],this[s].childNodes[0])}else if(e instanceof n)for(i=0;i&lt;e.length;i+=1)this[s].insertBefore(e[i],this[s].childNodes[0]);else this[s].insertBefore(e,this[s].childNodes[0]);return this},next:function(e){return this.length&gt;0?e?this[0].nextElementSibling&amp;&amp;d(this[0].nextElementSibling).is(e)?d([this[0].nextElementSibling]):d([]):this[0].nextElementSibling?d([this[0].nextElementSibling]):d([]):d([])},nextAll:function(e){const t=[];let s=this[0];if(!s)return d([]);for(;s.nextElementSibling;){const a=s.nextElementSibling;e?d(a).is(e)&amp;&amp;t.push(a):t.push(a),s=a}return d(t)},prev:function(e){if(this.length&gt;0){const t=this[0];return e?t.previousElementSibling&amp;&amp;d(t.previousElementSibling).is(e)?d([t.previousElementSibling]):d([]):t.previousElementSibling?d([t.previousElementSibling]):d([])}return d([])},prevAll:function(e){const t=[];let s=this[0];if(!s)return d([]);for(;s.previousElementSibling;){const a=s.previousElementSibling;e?d(a).is(e)&amp;&amp;t.push(a):t.push(a),s=a}return d(t)},parent:function(e){const t=[];for(let s=0;s&lt;this.length;s+=1)null!==this[s].parentNode&amp;&amp;(e?d(this[s].parentNode).is(e)&amp;&amp;t.push(this[s].parentNode):t.push(this[s].parentNode));return d(t)},parents:function(e){const t=[];for(let s=0;s&lt;this.length;s+=1){let a=this[s].parentNode;for(;a;)e?d(a).is(e)&amp;&amp;t.push(a):t.push(a),a=a.parentNode}return d(t)},closest:function(e){let t=this;return void 0===e?d([]):(t.is(e)||(t=t.parents(e).eq(0)),t)},find:function(e){const t=[];for(let s=0;s&lt;this.length;s+=1){const a=this[s].querySelectorAll(e);for(let e=0;e&lt;a.length;e+=1)t.push(a[e])}return d(t)},children:function(e){const t=[];for(let s=0;s&lt;this.length;s+=1){const a=this[s].children;for(let s=0;s&lt;a.length;s+=1)e&amp;&amp;!d(a[s]).is(e)||t.push(a[s])}return d(t)},filter:function(e){return d(o(this,e))},remove:function(){for(let e=0;e&lt;this.length;e+=1)this[e].parentNode&amp;&amp;this[e].parentNode.removeChild(this[e]);return this}};function p(e,t=0){return setTimeout(e,t)}function u(){return Date.now()}function h(e,t="x"){const s=r();let a,i,n;const l=function(e){const t=r();let s;return t.getComputedStyle&amp;&amp;(s=t.getComputedStyle(e,null)),!s&amp;&amp;e.currentStyle&amp;&amp;(s=e.currentStyle),s||(s=e.style),s}(e);return s.WebKitCSSMatrix?(i=l.transform||l.webkitTransform,i.split(",").length&gt;6&amp;&amp;(i=i.split(", ").map((e=&gt;e.replace(",","."))).join(", ")),n=new s.WebKitCSSMatrix("none"===i?"":i)):(n=l.MozTransform||l.OTransform||l.MsTransform||l.msTransform||l.transform||l.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),a=n.toString().split(",")),"x"===t&amp;&amp;(i=s.WebKitCSSMatrix?n.m41:16===a.length?parseFloat(a[12]):parseFloat(a[4])),"y"===t&amp;&amp;(i=s.WebKitCSSMatrix?n.m42:16===a.length?parseFloat(a[13]):parseFloat(a[5])),i||0}function m(e){return"object"==typeof e&amp;&amp;null!==e&amp;&amp;e.constructor&amp;&amp;"Object"===Object.prototype.toString.call(e).slice(8,-1)}function f(...e){const t=Object(e[0]),s=["__proto__","constructor","prototype"];for(let i=1;i&lt;e.length;i+=1){const r=e[i];if(null!=r&amp;&amp;(a=r,!("undefined"!=typeof window&amp;&amp;void 0!==window.HTMLElement?a instanceof HTMLElement:a&amp;&amp;(1===a.nodeType||11===a.nodeType)))){const e=Object.keys(Object(r)).filter((e=&gt;s.indexOf(e)&lt;0));for(let s=0,a=e.length;s&lt;a;s+=1){const a=e[s],i=Object.getOwnPropertyDescriptor(r,a);void 0!==i&amp;&amp;i.enumerable&amp;&amp;(m(t[a])&amp;&amp;m(r[a])?r[a].__swiper__?t[a]=r[a]:f(t[a],r[a]):!m(t[a])&amp;&amp;m(r[a])?(t[a]={},r[a].__swiper__?t[a]=r[a]:f(t[a],r[a])):t[a]=r[a])}}}var a;return t}function g(e,t,s){e.style.setProperty(t,s)}function v({swiper:e,targetPosition:t,side:s}){const a=r(),i=-e.translate;let n,l=null;const o=e.params.speed;e.wrapperEl.style.scrollSnapType="none",a.cancelAnimationFrame(e.cssModeFrameID);const d=t&gt;i?"next":"prev",c=(e,t)=&gt;"next"===d&amp;&amp;e&gt;=t||"prev"===d&amp;&amp;e&lt;=t,p=()=&gt;{n=(new Date).getTime(),null===l&amp;&amp;(l=n);const r=Math.max(Math.min((n-l)/o,1),0),d=.5-Math.cos(r*Math.PI)/2;let u=i+d*(t-i);if(c(u,t)&amp;&amp;(u=t),e.wrapperEl.scrollTo({[s]:u}),c(u,t))return e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout((()=&gt;{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[s]:u})})),void a.cancelAnimationFrame(e.cssModeFrameID);e.cssModeFrameID=a.requestAnimationFrame(p)};p()}let w,b,x;function y(){return w||(w=function(){const e=r(),t=a();return{smoothScroll:t.documentElement&amp;&amp;"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&amp;&amp;t instanceof e.DocumentTouch),passiveListener:function(){let t=!1;try{const s=Object.defineProperty({},"passive",{get(){t=!0}});e.addEventListener("testPassiveListener",null,s)}catch(e){}return t}(),gestures:"ongesturestart"in e}}()),w}function E(e={}){return b||(b=function({userAgent:e}={}){const t=y(),s=r(),a=s.navigator.platform,i=e||s.navigator.userAgent,n={ios:!1,android:!1},l=s.screen.width,o=s.screen.height,d=i.match(/(Android);?[\s\/]+([\d.]+)?/);let c=i.match(/(iPad).*OS\s([\d_]+)/);const p=i.match(/(iPod)(.*OS\s([\d_]+))?/),u=!c&amp;&amp;i.match(/(iPhone\sOS|iOS)\s([\d_]+)/),h="Win32"===a;let m="MacIntel"===a;return!c&amp;&amp;m&amp;&amp;t.touch&amp;&amp;["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${l}x${o}`)&gt;=0&amp;&amp;(c=i.match(/(Version)\/([\d.]+)/),c||(c=[0,1,"13_0_0"]),m=!1),d&amp;&amp;!h&amp;&amp;(n.os="android",n.android=!0),(c||u||p)&amp;&amp;(n.os="ios",n.ios=!0),n}(e)),b}function T(){return x||(x=function(){const e=r();return{isSafari:function(){const t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")&gt;=0&amp;&amp;t.indexOf("chrome")&lt;0&amp;&amp;t.indexOf("android")&lt;0}(),isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent)}}()),x}Object.keys(c).forEach((e=&gt;{Object.defineProperty(d.fn,e,{value:c[e],writable:!0})}));var C={on(e,t,s){const a=this;if("function"!=typeof t)return a;const i=s?"unshift":"push";return e.split(" ").forEach((e=&gt;{a.eventsListeners[e]||(a.eventsListeners[e]=[]),a.eventsListeners[e][i](t)})),a},once(e,t,s){const a=this;if("function"!=typeof t)return a;function i(...s){a.off(e,i),i.__emitterProxy&amp;&amp;delete i.__emitterProxy,t.apply(a,s)}return i.__emitterProxy=t,a.on(e,i,s)},onAny(e,t){const s=this;if("function"!=typeof e)return s;const a=t?"unshift":"push";return s.eventsAnyListeners.indexOf(e)&lt;0&amp;&amp;s.eventsAnyListeners[a](e),s},offAny(e){const t=this;if(!t.eventsAnyListeners)return t;const s=t.eventsAnyListeners.indexOf(e);return s&gt;=0&amp;&amp;t.eventsAnyListeners.splice(s,1),t},off(e,t){const s=this;return s.eventsListeners?(e.split(" ").forEach((e=&gt;{void 0===t?s.eventsListeners[e]=[]:s.eventsListeners[e]&amp;&amp;s.eventsListeners[e].forEach(((a,i)=&gt;{(a===t||a.__emitterProxy&amp;&amp;a.__emitterProxy===t)&amp;&amp;s.eventsListeners[e].splice(i,1)}))})),s):s},emit(...e){const t=this;if(!t.eventsListeners)return t;let s,a,i;"string"==typeof e[0]||Array.isArray(e[0])?(s=e[0],a=e.slice(1,e.length),i=t):(s=e[0].events,a=e[0].data,i=e[0].context||t),a.unshift(i);return(Array.isArray(s)?s:s.split(" ")).forEach((e=&gt;{t.eventsAnyListeners&amp;&amp;t.eventsAnyListeners.length&amp;&amp;t.eventsAnyListeners.forEach((t=&gt;{t.apply(i,[e,...a])})),t.eventsListeners&amp;&amp;t.eventsListeners[e]&amp;&amp;t.eventsListeners[e].forEach((e=&gt;{e.apply(i,a)}))})),t}};function $({swiper:e,runCallbacks:t,direction:s,step:a}){const{activeIndex:i,previousIndex:r}=e;let n=s;if(n||(n=i&gt;r?"next":i&lt;r?"prev":"reset"),e.emit(`transition${a}`),t&amp;&amp;i!==r){if("reset"===n)return void e.emit(`slideResetTransition${a}`);e.emit(`slideChangeTransition${a}`),"next"===n?e.emit(`slideNextTransition${a}`):e.emit(`slidePrevTransition${a}`)}}function S(e){const t=this,s=a(),i=r(),n=t.touchEventsData,{params:l,touches:o,enabled:c}=t;if(!c)return;if(t.animating&amp;&amp;l.preventInteractionOnTransition)return;!t.animating&amp;&amp;l.cssMode&amp;&amp;l.loop&amp;&amp;t.loopFix();let p=e;p.originalEvent&amp;&amp;(p=p.originalEvent);let h=d(p.target);if("wrapper"===l.touchEventsTarget&amp;&amp;!h.closest(t.wrapperEl).length)return;if(n.isTouchEvent="touchstart"===p.type,!n.isTouchEvent&amp;&amp;"which"in p&amp;&amp;3===p.which)return;if(!n.isTouchEvent&amp;&amp;"button"in p&amp;&amp;p.button&gt;0)return;if(n.isTouched&amp;&amp;n.isMoved)return;!!l.noSwipingClass&amp;&amp;""!==l.noSwipingClass&amp;&amp;p.target&amp;&amp;p.target.shadowRoot&amp;&amp;e.path&amp;&amp;e.path[0]&amp;&amp;(h=d(e.path[0]));const m=l.noSwipingSelector?l.noSwipingSelector:`.${l.noSwipingClass}`,f=!(!p.target||!p.target.shadowRoot);if(l.noSwiping&amp;&amp;(f?function(e,t=this){return function t(s){return s&amp;&amp;s!==a()&amp;&amp;s!==r()?(s.assignedSlot&amp;&amp;(s=s.assignedSlot),s.closest(e)||t(s.getRootNode().host)):null}(t)}(m,p.target):h.closest(m)[0]))return void(t.allowClick=!0);if(l.swipeHandler&amp;&amp;!h.closest(l.swipeHandler)[0])return;o.currentX="touchstart"===p.type?p.targetTouches[0].pageX:p.pageX,o.currentY="touchstart"===p.type?p.targetTouches[0].pageY:p.pageY;const g=o.currentX,v=o.currentY,w=l.edgeSwipeDetection||l.iOSEdgeSwipeDetection,b=l.edgeSwipeThreshold||l.iOSEdgeSwipeThreshold;if(w&amp;&amp;(g&lt;=b||g&gt;=i.innerWidth-b)){if("prevent"!==w)return;e.preventDefault()}if(Object.assign(n,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),o.startX=g,o.startY=v,n.touchStartTime=u(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,l.threshold&gt;0&amp;&amp;(n.allowThresholdMove=!1),"touchstart"!==p.type){let e=!0;h.is(n.focusableElements)&amp;&amp;(e=!1),s.activeElement&amp;&amp;d(s.activeElement).is(n.focusableElements)&amp;&amp;s.activeElement!==h[0]&amp;&amp;s.activeElement.blur();const a=e&amp;&amp;t.allowTouchMove&amp;&amp;l.touchStartPreventDefault;!l.touchStartForcePreventDefault&amp;&amp;!a||h[0].isContentEditable||p.preventDefault()}t.emit("touchStart",p)}function M(e){const t=a(),s=this,i=s.touchEventsData,{params:r,touches:n,rtlTranslate:l,enabled:o}=s;if(!o)return;let c=e;if(c.originalEvent&amp;&amp;(c=c.originalEvent),!i.isTouched)return void(i.startMoving&amp;&amp;i.isScrolling&amp;&amp;s.emit("touchMoveOpposite",c));if(i.isTouchEvent&amp;&amp;"touchmove"!==c.type)return;const p="touchmove"===c.type&amp;&amp;c.targetTouches&amp;&amp;(c.targetTouches[0]||c.changedTouches[0]),h="touchmove"===c.type?p.pageX:c.pageX,m="touchmove"===c.type?p.pageY:c.pageY;if(c.preventedByNestedSwiper)return n.startX=h,void(n.startY=m);if(!s.allowTouchMove)return s.allowClick=!1,void(i.isTouched&amp;&amp;(Object.assign(n,{startX:h,startY:m,currentX:h,currentY:m}),i.touchStartTime=u()));if(i.isTouchEvent&amp;&amp;r.touchReleaseOnEdges&amp;&amp;!r.loop)if(s.isVertical()){if(m&lt;n.startY&amp;&amp;s.translate&lt;=s.maxTranslate()||m&gt;n.startY&amp;&amp;s.translate&gt;=s.minTranslate())return i.isTouched=!1,void(i.isMoved=!1)}else if(h&lt;n.startX&amp;&amp;s.translate&lt;=s.maxTranslate()||h&gt;n.startX&amp;&amp;s.translate&gt;=s.minTranslate())return;if(i.isTouchEvent&amp;&amp;t.activeElement&amp;&amp;c.target===t.activeElement&amp;&amp;d(c.target).is(i.focusableElements))return i.isMoved=!0,void(s.allowClick=!1);if(i.allowTouchCallbacks&amp;&amp;s.emit("touchMove",c),c.targetTouches&amp;&amp;c.targetTouches.length&gt;1)return;n.currentX=h,n.currentY=m;const f=n.currentX-n.startX,g=n.currentY-n.startY;if(s.params.threshold&amp;&amp;Math.sqrt(f**2+g**2)&lt;s.params.threshold)return;if(void 0===i.isScrolling){let e;s.isHorizontal()&amp;&amp;n.currentY===n.startY||s.isVertical()&amp;&amp;n.currentX===n.startX?i.isScrolling=!1:f*f+g*g&gt;=25&amp;&amp;(e=180*Math.atan2(Math.abs(g),Math.abs(f))/Math.PI,i.isScrolling=s.isHorizontal()?e&gt;r.touchAngle:90-e&gt;r.touchAngle)}if(i.isScrolling&amp;&amp;s.emit("touchMoveOpposite",c),void 0===i.startMoving&amp;&amp;(n.currentX===n.startX&amp;&amp;n.currentY===n.startY||(i.startMoving=!0)),i.isScrolling)return void(i.isTouched=!1);if(!i.startMoving)return;s.allowClick=!1,!r.cssMode&amp;&amp;c.cancelable&amp;&amp;c.preventDefault(),r.touchMoveStopPropagation&amp;&amp;!r.nested&amp;&amp;c.stopPropagation(),i.isMoved||(r.loop&amp;&amp;!r.cssMode&amp;&amp;s.loopFix(),i.startTranslate=s.getTranslate(),s.setTransition(0),s.animating&amp;&amp;s.$wrapperEl.trigger("webkitTransitionEnd transitionend"),i.allowMomentumBounce=!1,!r.grabCursor||!0!==s.allowSlideNext&amp;&amp;!0!==s.allowSlidePrev||s.setGrabCursor(!0),s.emit("sliderFirstMove",c)),s.emit("sliderMove",c),i.isMoved=!0;let v=s.isHorizontal()?f:g;n.diff=v,v*=r.touchRatio,l&amp;&amp;(v=-v),s.swipeDirection=v&gt;0?"prev":"next",i.currentTranslate=v+i.startTranslate;let w=!0,b=r.resistanceRatio;if(r.touchReleaseOnEdges&amp;&amp;(b=0),v&gt;0&amp;&amp;i.currentTranslate&gt;s.minTranslate()?(w=!1,r.resistance&amp;&amp;(i.currentTranslate=s.minTranslate()-1+(-s.minTranslate()+i.startTranslate+v)**b)):v&lt;0&amp;&amp;i.currentTranslate&lt;s.maxTranslate()&amp;&amp;(w=!1,r.resistance&amp;&amp;(i.currentTranslate=s.maxTranslate()+1-(s.maxTranslate()-i.startTranslate-v)**b)),w&amp;&amp;(c.preventedByNestedSwiper=!0),!s.allowSlideNext&amp;&amp;"next"===s.swipeDirection&amp;&amp;i.currentTranslate&lt;i.startTranslate&amp;&amp;(i.currentTranslate=i.startTranslate),!s.allowSlidePrev&amp;&amp;"prev"===s.swipeDirection&amp;&amp;i.currentTranslate&gt;i.startTranslate&amp;&amp;(i.currentTranslate=i.startTranslate),s.allowSlidePrev||s.allowSlideNext||(i.currentTranslate=i.startTranslate),r.threshold&gt;0){if(!(Math.abs(v)&gt;r.threshold||i.allowThresholdMove))return void(i.currentTranslate=i.startTranslate);if(!i.allowThresholdMove)return i.allowThresholdMove=!0,n.startX=n.currentX,n.startY=n.currentY,i.currentTranslate=i.startTranslate,void(n.diff=s.isHorizontal()?n.currentX-n.startX:n.currentY-n.startY)}r.followFinger&amp;&amp;!r.cssMode&amp;&amp;((r.freeMode&amp;&amp;r.freeMode.enabled&amp;&amp;s.freeMode||r.watchSlidesProgress)&amp;&amp;(s.updateActiveIndex(),s.updateSlidesClasses()),s.params.freeMode&amp;&amp;r.freeMode.enabled&amp;&amp;s.freeMode&amp;&amp;s.freeMode.onTouchMove(),s.updateProgress(i.currentTranslate),s.setTranslate(i.currentTranslate))}function P(e){const t=this,s=t.touchEventsData,{params:a,touches:i,rtlTranslate:r,slidesGrid:n,enabled:l}=t;if(!l)return;let o=e;if(o.originalEvent&amp;&amp;(o=o.originalEvent),s.allowTouchCallbacks&amp;&amp;t.emit("touchEnd",o),s.allowTouchCallbacks=!1,!s.isTouched)return s.isMoved&amp;&amp;a.grabCursor&amp;&amp;t.setGrabCursor(!1),s.isMoved=!1,void(s.startMoving=!1);a.grabCursor&amp;&amp;s.isMoved&amp;&amp;s.isTouched&amp;&amp;(!0===t.allowSlideNext||!0===t.allowSlidePrev)&amp;&amp;t.setGrabCursor(!1);const d=u(),c=d-s.touchStartTime;if(t.allowClick&amp;&amp;(t.updateClickedSlide(o),t.emit("tap click",o),c&lt;300&amp;&amp;d-s.lastClickTime&lt;300&amp;&amp;t.emit("doubleTap doubleClick",o)),s.lastClickTime=u(),p((()=&gt;{t.destroyed||(t.allowClick=!0)})),!s.isTouched||!s.isMoved||!t.swipeDirection||0===i.diff||s.currentTranslate===s.startTranslate)return s.isTouched=!1,s.isMoved=!1,void(s.startMoving=!1);let h;if(s.isTouched=!1,s.isMoved=!1,s.startMoving=!1,h=a.followFinger?r?t.translate:-t.translate:-s.currentTranslate,a.cssMode)return;if(t.params.freeMode&amp;&amp;a.freeMode.enabled)return void t.freeMode.onTouchEnd({currentPos:h});let m=0,f=t.slidesSizesGrid[0];for(let e=0;e&lt;n.length;e+=e&lt;a.slidesPerGroupSkip?1:a.slidesPerGroup){const t=e&lt;a.slidesPerGroupSkip-1?1:a.slidesPerGroup;void 0!==n[e+t]?h&gt;=n[e]&amp;&amp;h&lt;n[e+t]&amp;&amp;(m=e,f=n[e+t]-n[e]):h&gt;=n[e]&amp;&amp;(m=e,f=n[n.length-1]-n[n.length-2])}const g=(h-n[m])/f,v=m&lt;a.slidesPerGroupSkip-1?1:a.slidesPerGroup;if(c&gt;a.longSwipesMs){if(!a.longSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&amp;&amp;(g&gt;=a.longSwipesRatio?t.slideTo(m+v):t.slideTo(m)),"prev"===t.swipeDirection&amp;&amp;(g&gt;1-a.longSwipesRatio?t.slideTo(m+v):t.slideTo(m))}else{if(!a.shortSwipes)return void t.slideTo(t.activeIndex);t.navigation&amp;&amp;(o.target===t.navigation.nextEl||o.target===t.navigation.prevEl)?o.target===t.navigation.nextEl?t.slideTo(m+v):t.slideTo(m):("next"===t.swipeDirection&amp;&amp;t.slideTo(m+v),"prev"===t.swipeDirection&amp;&amp;t.slideTo(m))}}function k(){const e=this,{params:t,el:s}=e;if(s&amp;&amp;0===s.offsetWidth)return;t.breakpoints&amp;&amp;e.setBreakpoint();const{allowSlideNext:a,allowSlidePrev:i,snapGrid:r}=e;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses(),("auto"===t.slidesPerView||t.slidesPerView&gt;1)&amp;&amp;e.isEnd&amp;&amp;!e.isBeginning&amp;&amp;!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),e.autoplay&amp;&amp;e.autoplay.running&amp;&amp;e.autoplay.paused&amp;&amp;e.autoplay.run(),e.allowSlidePrev=i,e.allowSlideNext=a,e.params.watchOverflow&amp;&amp;r!==e.snapGrid&amp;&amp;e.checkOverflow()}function z(e){const t=this;t.enabled&amp;&amp;(t.allowClick||(t.params.preventClicks&amp;&amp;e.preventDefault(),t.params.preventClicksPropagation&amp;&amp;t.animating&amp;&amp;(e.stopPropagation(),e.stopImmediatePropagation())))}function O(){const e=this,{wrapperEl:t,rtlTranslate:s,enabled:a}=e;if(!a)return;let i;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,-0===e.translate&amp;&amp;(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();const r=e.maxTranslate()-e.minTranslate();i=0===r?0:(e.translate-e.minTranslate())/r,i!==e.progress&amp;&amp;e.updateProgress(s?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}let I=!1;function L(){}const A=(e,t)=&gt;{const s=a(),{params:i,touchEvents:r,el:n,wrapperEl:l,device:o,support:d}=e,c=!!i.nested,p="on"===t?"addEventListener":"removeEventListener",u=t;if(d.touch){const t=!("touchstart"!==r.start||!d.passiveListener||!i.passiveListeners)&amp;&amp;{passive:!0,capture:!1};n[p](r.start,e.onTouchStart,t),n[p](r.move,e.onTouchMove,d.passiveListener?{passive:!1,capture:c}:c),n[p](r.end,e.onTouchEnd,t),r.cancel&amp;&amp;n[p](r.cancel,e.onTouchEnd,t)}else n[p](r.start,e.onTouchStart,!1),s[p](r.move,e.onTouchMove,c),s[p](r.end,e.onTouchEnd,!1);(i.preventClicks||i.preventClicksPropagation)&amp;&amp;n[p]("click",e.onClick,!0),i.cssMode&amp;&amp;l[p]("scroll",e.onScroll),i.updateOnWindowResize?e[u](o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",k,!0):e[u]("observerUpdate",k,!0)};const D=(e,t)=&gt;e.grid&amp;&amp;t.grid&amp;&amp;t.grid.rows&gt;1;var G={init:!0,direction:"horizontal",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:0,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,preloadImages:!0,updateOnImagesReady:!0,loop:!1,loopAdditionalSlides:0,loopedSlides:null,loopFillGroupWithBlank:!1,loopPreventsSlide:!0,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-invisible-blank",slideActiveClass:"swiper-slide-active",slideDuplicateActiveClass:"swiper-slide-duplicate-active",slideVisibleClass:"swiper-slide-visible",slideDuplicateClass:"swiper-slide-duplicate",slideNextClass:"swiper-slide-next",slideDuplicateNextClass:"swiper-slide-duplicate-next",slidePrevClass:"swiper-slide-prev",slideDuplicatePrevClass:"swiper-slide-duplicate-prev",wrapperClass:"swiper-wrapper",runCallbacksOnInit:!0,_emitClasses:!1};function N(e,t){return function(s={}){const a=Object.keys(s)[0],i=s[a];"object"==typeof i&amp;&amp;null!==i?(["navigation","pagination","scrollbar"].indexOf(a)&gt;=0&amp;&amp;!0===e[a]&amp;&amp;(e[a]={auto:!0}),a in e&amp;&amp;"enabled"in i?(!0===e[a]&amp;&amp;(e[a]={enabled:!0}),"object"!=typeof e[a]||"enabled"in e[a]||(e[a].enabled=!0),e[a]||(e[a]={enabled:!1}),f(t,s)):f(t,s)):f(t,s)}}const B={eventsEmitter:C,update:{updateSize:function(){const e=this;let t,s;const a=e.$el;t=void 0!==e.params.width&amp;&amp;null!==e.params.width?e.params.width:a[0].clientWidth,s=void 0!==e.params.height&amp;&amp;null!==e.params.height?e.params.height:a[0].clientHeight,0===t&amp;&amp;e.isHorizontal()||0===s&amp;&amp;e.isVertical()||(t=t-parseInt(a.css("padding-left")||0,10)-parseInt(a.css("padding-right")||0,10),s=s-parseInt(a.css("padding-top")||0,10)-parseInt(a.css("padding-bottom")||0,10),Number.isNaN(t)&amp;&amp;(t=0),Number.isNaN(s)&amp;&amp;(s=0),Object.assign(e,{width:t,height:s,size:e.isHorizontal()?t:s}))},updateSlides:function(){const e=this;function t(t){return e.isHorizontal()?t:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[t]}function s(e,s){return parseFloat(e.getPropertyValue(t(s))||0)}const a=e.params,{$wrapperEl:i,size:r,rtlTranslate:n,wrongRTL:l}=e,o=e.virtual&amp;&amp;a.virtual.enabled,d=o?e.virtual.slides.length:e.slides.length,c=i.children(`.${e.params.slideClass}`),p=o?e.virtual.slides.length:c.length;let u=[];const h=[],m=[];let f=a.slidesOffsetBefore;"function"==typeof f&amp;&amp;(f=a.slidesOffsetBefore.call(e));let v=a.slidesOffsetAfter;"function"==typeof v&amp;&amp;(v=a.slidesOffsetAfter.call(e));const w=e.snapGrid.length,b=e.slidesGrid.length;let x=a.spaceBetween,y=-f,E=0,T=0;if(void 0===r)return;"string"==typeof x&amp;&amp;x.indexOf("%")&gt;=0&amp;&amp;(x=parseFloat(x.replace("%",""))/100*r),e.virtualSize=-x,n?c.css({marginLeft:"",marginBottom:"",marginTop:""}):c.css({marginRight:"",marginBottom:"",marginTop:""}),a.centeredSlides&amp;&amp;a.cssMode&amp;&amp;(g(e.wrapperEl,"--swiper-centered-offset-before",""),g(e.wrapperEl,"--swiper-centered-offset-after",""));const C=a.grid&amp;&amp;a.grid.rows&gt;1&amp;&amp;e.grid;let $;C&amp;&amp;e.grid.initSlides(p);const S="auto"===a.slidesPerView&amp;&amp;a.breakpoints&amp;&amp;Object.keys(a.breakpoints).filter((e=&gt;void 0!==a.breakpoints[e].slidesPerView)).length&gt;0;for(let i=0;i&lt;p;i+=1){$=0;const n=c.eq(i);if(C&amp;&amp;e.grid.updateSlide(i,n,p,t),"none"!==n.css("display")){if("auto"===a.slidesPerView){S&amp;&amp;(c[i].style[t("width")]="");const r=getComputedStyle(n[0]),l=n[0].style.transform,o=n[0].style.webkitTransform;if(l&amp;&amp;(n[0].style.transform="none"),o&amp;&amp;(n[0].style.webkitTransform="none"),a.roundLengths)$=e.isHorizontal()?n.outerWidth(!0):n.outerHeight(!0);else{const e=s(r,"width"),t=s(r,"padding-left"),a=s(r,"padding-right"),i=s(r,"margin-left"),l=s(r,"margin-right"),o=r.getPropertyValue("box-sizing");if(o&amp;&amp;"border-box"===o)$=e+i+l;else{const{clientWidth:s,offsetWidth:r}=n[0];$=e+t+a+i+l+(r-s)}}l&amp;&amp;(n[0].style.transform=l),o&amp;&amp;(n[0].style.webkitTransform=o),a.roundLengths&amp;&amp;($=Math.floor($))}else $=(r-(a.slidesPerView-1)*x)/a.slidesPerView,a.roundLengths&amp;&amp;($=Math.floor($)),c[i]&amp;&amp;(c[i].style[t("width")]=`${$}px`);c[i]&amp;&amp;(c[i].swiperSlideSize=$),m.push($),a.centeredSlides?(y=y+$/2+E/2+x,0===E&amp;&amp;0!==i&amp;&amp;(y=y-r/2-x),0===i&amp;&amp;(y=y-r/2-x),Math.abs(y)&lt;.001&amp;&amp;(y=0),a.roundLengths&amp;&amp;(y=Math.floor(y)),T%a.slidesPerGroup==0&amp;&amp;u.push(y),h.push(y)):(a.roundLengths&amp;&amp;(y=Math.floor(y)),(T-Math.min(e.params.slidesPerGroupSkip,T))%e.params.slidesPerGroup==0&amp;&amp;u.push(y),h.push(y),y=y+$+x),e.virtualSize+=$+x,E=$,T+=1}}if(e.virtualSize=Math.max(e.virtualSize,r)+v,n&amp;&amp;l&amp;&amp;("slide"===a.effect||"coverflow"===a.effect)&amp;&amp;i.css({width:`${e.virtualSize+a.spaceBetween}px`}),a.setWrapperSize&amp;&amp;i.css({[t("width")]:`${e.virtualSize+a.spaceBetween}px`}),C&amp;&amp;e.grid.updateWrapperSize($,u,t),!a.centeredSlides){const t=[];for(let s=0;s&lt;u.length;s+=1){let i=u[s];a.roundLengths&amp;&amp;(i=Math.floor(i)),u[s]&lt;=e.virtualSize-r&amp;&amp;t.push(i)}u=t,Math.floor(e.virtualSize-r)-Math.floor(u[u.length-1])&gt;1&amp;&amp;u.push(e.virtualSize-r)}if(0===u.length&amp;&amp;(u=[0]),0!==a.spaceBetween){const s=e.isHorizontal()&amp;&amp;n?"marginLeft":t("marginRight");c.filter(((e,t)=&gt;!a.cssMode||t!==c.length-1)).css({[s]:`${x}px`})}if(a.centeredSlides&amp;&amp;a.centeredSlidesBounds){let e=0;m.forEach((t=&gt;{e+=t+(a.spaceBetween?a.spaceBetween:0)})),e-=a.spaceBetween;const t=e-r;u=u.map((e=&gt;e&lt;0?-f:e&gt;t?t+v:e))}if(a.centerInsufficientSlides){let e=0;if(m.forEach((t=&gt;{e+=t+(a.spaceBetween?a.spaceBetween:0)})),e-=a.spaceBetween,e&lt;r){const t=(r-e)/2;u.forEach(((e,s)=&gt;{u[s]=e-t})),h.forEach(((e,s)=&gt;{h[s]=e+t}))}}if(Object.assign(e,{slides:c,snapGrid:u,slidesGrid:h,slidesSizesGrid:m}),a.centeredSlides&amp;&amp;a.cssMode&amp;&amp;!a.centeredSlidesBounds){g(e.wrapperEl,"--swiper-centered-offset-before",-u[0]+"px"),g(e.wrapperEl,"--swiper-centered-offset-after",e.size/2-m[m.length-1]/2+"px");const t=-e.snapGrid[0],s=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map((e=&gt;e+t)),e.slidesGrid=e.slidesGrid.map((e=&gt;e+s))}p!==d&amp;&amp;e.emit("slidesLengthChange"),u.length!==w&amp;&amp;(e.params.watchOverflow&amp;&amp;e.checkOverflow(),e.emit("snapGridLengthChange")),h.length!==b&amp;&amp;e.emit("slidesGridLengthChange"),a.watchSlidesProgress&amp;&amp;e.updateSlidesOffset()},updateAutoHeight:function(e){const t=this,s=[],a=t.virtual&amp;&amp;t.params.virtual.enabled;let i,r=0;"number"==typeof e?t.setTransition(e):!0===e&amp;&amp;t.setTransition(t.params.speed);const n=e=&gt;a?t.slides.filter((t=&gt;parseInt(t.getAttribute("data-swiper-slide-index"),10)===e))[0]:t.slides.eq(e)[0];if("auto"!==t.params.slidesPerView&amp;&amp;t.params.slidesPerView&gt;1)if(t.params.centeredSlides)t.visibleSlides.each((e=&gt;{s.push(e)}));else for(i=0;i&lt;Math.ceil(t.params.slidesPerView);i+=1){const e=t.activeIndex+i;if(e&gt;t.slides.length&amp;&amp;!a)break;s.push(n(e))}else s.push(n(t.activeIndex));for(i=0;i&lt;s.length;i+=1)if(void 0!==s[i]){const e=s[i].offsetHeight;r=e&gt;r?e:r}r&amp;&amp;t.$wrapperEl.css("height",`${r}px`)},updateSlidesOffset:function(){const e=this,t=e.slides;for(let s=0;s&lt;t.length;s+=1)t[s].swiperSlideOffset=e.isHorizontal()?t[s].offsetLeft:t[s].offsetTop},updateSlidesProgress:function(e=this&amp;&amp;this.translate||0){const t=this,s=t.params,{slides:a,rtlTranslate:i}=t;if(0===a.length)return;void 0===a[0].swiperSlideOffset&amp;&amp;t.updateSlidesOffset();let r=-e;i&amp;&amp;(r=e),a.removeClass(s.slideVisibleClass),t.visibleSlidesIndexes=[],t.visibleSlides=[];for(let e=0;e&lt;a.length;e+=1){const n=a[e];let l=n.swiperSlideOffset;s.cssMode&amp;&amp;s.centeredSlides&amp;&amp;(l-=a[0].swiperSlideOffset);const o=(r+(s.centeredSlides?t.minTranslate():0)-l)/(n.swiperSlideSize+s.spaceBetween),d=-(r-l),c=d+t.slidesSizesGrid[e];(d&gt;=0&amp;&amp;d&lt;t.size-1||c&gt;1&amp;&amp;c&lt;=t.size||d&lt;=0&amp;&amp;c&gt;=t.size)&amp;&amp;(t.visibleSlides.push(n),t.visibleSlidesIndexes.push(e),a.eq(e).addClass(s.slideVisibleClass)),n.progress=i?-o:o}t.visibleSlides=d(t.visibleSlides)},updateProgress:function(e){const t=this;if(void 0===e){const s=t.rtlTranslate?-1:1;e=t&amp;&amp;t.translate&amp;&amp;t.translate*s||0}const s=t.params,a=t.maxTranslate()-t.minTranslate();let{progress:i,isBeginning:r,isEnd:n}=t;const l=r,o=n;0===a?(i=0,r=!0,n=!0):(i=(e-t.minTranslate())/a,r=i&lt;=0,n=i&gt;=1),Object.assign(t,{progress:i,isBeginning:r,isEnd:n}),(s.watchSlidesProgress||s.centeredSlides&amp;&amp;s.autoHeight)&amp;&amp;t.updateSlidesProgress(e),r&amp;&amp;!l&amp;&amp;t.emit("reachBeginning toEdge"),n&amp;&amp;!o&amp;&amp;t.emit("reachEnd toEdge"),(l&amp;&amp;!r||o&amp;&amp;!n)&amp;&amp;t.emit("fromEdge"),t.emit("progress",i)},updateSlidesClasses:function(){const e=this,{slides:t,params:s,$wrapperEl:a,activeIndex:i,realIndex:r}=e,n=e.virtual&amp;&amp;s.virtual.enabled;let l;t.removeClass(`${s.slideActiveClass} ${s.slideNextClass} ${s.slidePrevClass} ${s.slideDuplicateActiveClass} ${s.slideDuplicateNextClass} ${s.slideDuplicatePrevClass}`),l=n?e.$wrapperEl.find(`.${s.slideClass}[data-swiper-slide-index="${i}"]`):t.eq(i),l.addClass(s.slideActiveClass),s.loop&amp;&amp;(l.hasClass(s.slideDuplicateClass)?a.children(`.${s.slideClass}:not(.${s.slideDuplicateClass})[data-swiper-slide-index="${r}"]`).addClass(s.slideDuplicateActiveClass):a.children(`.${s.slideClass}.${s.slideDuplicateClass}[data-swiper-slide-index="${r}"]`).addClass(s.slideDuplicateActiveClass));let o=l.nextAll(`.${s.slideClass}`).eq(0).addClass(s.slideNextClass);s.loop&amp;&amp;0===o.length&amp;&amp;(o=t.eq(0),o.addClass(s.slideNextClass));let d=l.prevAll(`.${s.slideClass}`).eq(0).addClass(s.slidePrevClass);s.loop&amp;&amp;0===d.length&amp;&amp;(d=t.eq(-1),d.addClass(s.slidePrevClass)),s.loop&amp;&amp;(o.hasClass(s.slideDuplicateClass)?a.children(`.${s.slideClass}:not(.${s.slideDuplicateClass})[data-swiper-slide-index="${o.attr("data-swiper-slide-index")}"]`).addClass(s.slideDuplicateNextClass):a.children(`.${s.slideClass}.${s.slideDuplicateClass}[data-swiper-slide-index="${o.attr("data-swiper-slide-index")}"]`).addClass(s.slideDuplicateNextClass),d.hasClass(s.slideDuplicateClass)?a.children(`.${s.slideClass}:not(.${s.slideDuplicateClass})[data-swiper-slide-index="${d.attr("data-swiper-slide-index")}"]`).addClass(s.slideDuplicatePrevClass):a.children(`.${s.slideClass}.${s.slideDuplicateClass}[data-swiper-slide-index="${d.attr("data-swiper-slide-index")}"]`).addClass(s.slideDuplicatePrevClass)),e.emitSlidesClasses()},updateActiveIndex:function(e){const t=this,s=t.rtlTranslate?t.translate:-t.translate,{slidesGrid:a,snapGrid:i,params:r,activeIndex:n,realIndex:l,snapIndex:o}=t;let d,c=e;if(void 0===c){for(let e=0;e&lt;a.length;e+=1)void 0!==a[e+1]?s&gt;=a[e]&amp;&amp;s&lt;a[e+1]-(a[e+1]-a[e])/2?c=e:s&gt;=a[e]&amp;&amp;s&lt;a[e+1]&amp;&amp;(c=e+1):s&gt;=a[e]&amp;&amp;(c=e);r.normalizeSlideIndex&amp;&amp;(c&lt;0||void 0===c)&amp;&amp;(c=0)}if(i.indexOf(s)&gt;=0)d=i.indexOf(s);else{const e=Math.min(r.slidesPerGroupSkip,c);d=e+Math.floor((c-e)/r.slidesPerGroup)}if(d&gt;=i.length&amp;&amp;(d=i.length-1),c===n)return void(d!==o&amp;&amp;(t.snapIndex=d,t.emit("snapIndexChange")));const p=parseInt(t.slides.eq(c).attr("data-swiper-slide-index")||c,10);Object.assign(t,{snapIndex:d,realIndex:p,previousIndex:n,activeIndex:c}),t.emit("activeIndexChange"),t.emit("snapIndexChange"),l!==p&amp;&amp;t.emit("realIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&amp;&amp;t.emit("slideChange")},updateClickedSlide:function(e){const t=this,s=t.params,a=d(e.target).closest(`.${s.slideClass}`)[0];let i,r=!1;if(a)for(let e=0;e&lt;t.slides.length;e+=1)if(t.slides[e]===a){r=!0,i=e;break}if(!a||!r)return t.clickedSlide=void 0,void(t.clickedIndex=void 0);t.clickedSlide=a,t.virtual&amp;&amp;t.params.virtual.enabled?t.clickedIndex=parseInt(d(a).attr("data-swiper-slide-index"),10):t.clickedIndex=i,s.slideToClickedSlide&amp;&amp;void 0!==t.clickedIndex&amp;&amp;t.clickedIndex!==t.activeIndex&amp;&amp;t.slideToClickedSlide()}},translate:{getTranslate:function(e=(this.isHorizontal()?"x":"y")){const{params:t,rtlTranslate:s,translate:a,$wrapperEl:i}=this;if(t.virtualTranslate)return s?-a:a;if(t.cssMode)return a;let r=h(i[0],e);return s&amp;&amp;(r=-r),r||0},setTranslate:function(e,t){const s=this,{rtlTranslate:a,params:i,$wrapperEl:r,wrapperEl:n,progress:l}=s;let o,d=0,c=0;s.isHorizontal()?d=a?-e:e:c=e,i.roundLengths&amp;&amp;(d=Math.floor(d),c=Math.floor(c)),i.cssMode?n[s.isHorizontal()?"scrollLeft":"scrollTop"]=s.isHorizontal()?-d:-c:i.virtualTranslate||r.transform(`translate3d(${d}px, ${c}px, 0px)`),s.previousTranslate=s.translate,s.translate=s.isHorizontal()?d:c;const p=s.maxTranslate()-s.minTranslate();o=0===p?0:(e-s.minTranslate())/p,o!==l&amp;&amp;s.updateProgress(e),s.emit("setTranslate",s.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e=0,t=this.params.speed,s=!0,a=!0,i){const r=this,{params:n,wrapperEl:l}=r;if(r.animating&amp;&amp;n.preventInteractionOnTransition)return!1;const o=r.minTranslate(),d=r.maxTranslate();let c;if(c=a&amp;&amp;e&gt;o?o:a&amp;&amp;e&lt;d?d:e,r.updateProgress(c),n.cssMode){const e=r.isHorizontal();if(0===t)l[e?"scrollLeft":"scrollTop"]=-c;else{if(!r.support.smoothScroll)return v({swiper:r,targetPosition:-c,side:e?"left":"top"}),!0;l.scrollTo({[e?"left":"top"]:-c,behavior:"smooth"})}return!0}return 0===t?(r.setTransition(0),r.setTranslate(c),s&amp;&amp;(r.emit("beforeTransitionStart",t,i),r.emit("transitionEnd"))):(r.setTransition(t),r.setTranslate(c),s&amp;&amp;(r.emit("beforeTransitionStart",t,i),r.emit("transitionStart")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(e){r&amp;&amp;!r.destroyed&amp;&amp;e.target===this&amp;&amp;(r.$wrapperEl[0].removeEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.$wrapperEl[0].removeEventListener("webkitTransitionEnd",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,s&amp;&amp;r.emit("transitionEnd"))}),r.$wrapperEl[0].addEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.$wrapperEl[0].addEventListener("webkitTransitionEnd",r.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){const s=this;s.params.cssMode||s.$wrapperEl.transition(e),s.emit("setTransition",e,t)},transitionStart:function(e=!0,t){const s=this,{params:a}=s;a.cssMode||(a.autoHeight&amp;&amp;s.updateAutoHeight(),$({swiper:s,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e=!0,t){const s=this,{params:a}=s;s.animating=!1,a.cssMode||(s.setTransition(0),$({swiper:s,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e=0,t=this.params.speed,s=!0,a,i){if("number"!=typeof e&amp;&amp;"string"!=typeof e)throw new Error(`The 'index' argument cannot have type other than 'number' or 'string'. [${typeof e}] given.`);if("string"==typeof e){const t=parseInt(e,10);if(!isFinite(t))throw new Error(`The passed-in 'index' (string) couldn't be converted to 'number'. [${e}] given.`);e=t}const r=this;let n=e;n&lt;0&amp;&amp;(n=0);const{params:l,snapGrid:o,slidesGrid:d,previousIndex:c,activeIndex:p,rtlTranslate:u,wrapperEl:h,enabled:m}=r;if(r.animating&amp;&amp;l.preventInteractionOnTransition||!m&amp;&amp;!a&amp;&amp;!i)return!1;const f=Math.min(r.params.slidesPerGroupSkip,n);let g=f+Math.floor((n-f)/r.params.slidesPerGroup);g&gt;=o.length&amp;&amp;(g=o.length-1),(p||l.initialSlide||0)===(c||0)&amp;&amp;s&amp;&amp;r.emit("beforeSlideChangeStart");const w=-o[g];if(r.updateProgress(w),l.normalizeSlideIndex)for(let e=0;e&lt;d.length;e+=1){const t=-Math.floor(100*w),s=Math.floor(100*d[e]),a=Math.floor(100*d[e+1]);void 0!==d[e+1]?t&gt;=s&amp;&amp;t&lt;a-(a-s)/2?n=e:t&gt;=s&amp;&amp;t&lt;a&amp;&amp;(n=e+1):t&gt;=s&amp;&amp;(n=e)}if(r.initialized&amp;&amp;n!==p){if(!r.allowSlideNext&amp;&amp;w&lt;r.translate&amp;&amp;w&lt;r.minTranslate())return!1;if(!r.allowSlidePrev&amp;&amp;w&gt;r.translate&amp;&amp;w&gt;r.maxTranslate()&amp;&amp;(p||0)!==n)return!1}let b;if(b=n&gt;p?"next":n&lt;p?"prev":"reset",u&amp;&amp;-w===r.translate||!u&amp;&amp;w===r.translate)return r.updateActiveIndex(n),l.autoHeight&amp;&amp;r.updateAutoHeight(),r.updateSlidesClasses(),"slide"!==l.effect&amp;&amp;r.setTranslate(w),"reset"!==b&amp;&amp;(r.transitionStart(s,b),r.transitionEnd(s,b)),!1;if(l.cssMode){const e=r.isHorizontal(),s=u?w:-w;if(0===t){const t=r.virtual&amp;&amp;r.params.virtual.enabled;t&amp;&amp;(r.wrapperEl.style.scrollSnapType="none"),h[e?"scrollLeft":"scrollTop"]=s,t&amp;&amp;requestAnimationFrame((()=&gt;{r.wrapperEl.style.scrollSnapType=""}))}else{if(!r.support.smoothScroll)return v({swiper:r,targetPosition:s,side:e?"left":"top"}),!0;h.scrollTo({[e?"left":"top"]:s,behavior:"smooth"})}return!0}return 0===t?(r.setTransition(0),r.setTranslate(w),r.updateActiveIndex(n),r.updateSlidesClasses(),r.emit("beforeTransitionStart",t,a),r.transitionStart(s,b),r.transitionEnd(s,b)):(r.setTransition(t),r.setTranslate(w),r.updateActiveIndex(n),r.updateSlidesClasses(),r.emit("beforeTransitionStart",t,a),r.transitionStart(s,b),r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(e){r&amp;&amp;!r.destroyed&amp;&amp;e.target===this&amp;&amp;(r.$wrapperEl[0].removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.$wrapperEl[0].removeEventListener("webkitTransitionEnd",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(s,b))}),r.$wrapperEl[0].addEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.$wrapperEl[0].addEventListener("webkitTransitionEnd",r.onSlideToWrapperTransitionEnd))),!0},slideToLoop:function(e=0,t=this.params.speed,s=!0,a){const i=this;let r=e;return i.params.loop&amp;&amp;(r+=i.loopedSlides),i.slideTo(r,t,s,a)},slideNext:function(e=this.params.speed,t=!0,s){const a=this,{animating:i,enabled:r,params:n}=a;if(!r)return a;let l=n.slidesPerGroup;"auto"===n.slidesPerView&amp;&amp;1===n.slidesPerGroup&amp;&amp;n.slidesPerGroupAuto&amp;&amp;(l=Math.max(a.slidesPerViewDynamic("current",!0),1));const o=a.activeIndex&lt;n.slidesPerGroupSkip?1:l;if(n.loop){if(i&amp;&amp;n.loopPreventsSlide)return!1;a.loopFix(),a._clientLeft=a.$wrapperEl[0].clientLeft}return a.slideTo(a.activeIndex+o,e,t,s)},slidePrev:function(e=this.params.speed,t=!0,s){const a=this,{params:i,animating:r,snapGrid:n,slidesGrid:l,rtlTranslate:o,enabled:d}=a;if(!d)return a;if(i.loop){if(r&amp;&amp;i.loopPreventsSlide)return!1;a.loopFix(),a._clientLeft=a.$wrapperEl[0].clientLeft}function c(e){return e&lt;0?-Math.floor(Math.abs(e)):Math.floor(e)}const p=c(o?a.translate:-a.translate),u=n.map((e=&gt;c(e)));let h=n[u.indexOf(p)-1];if(void 0===h&amp;&amp;i.cssMode){let e;n.forEach(((t,s)=&gt;{p&gt;=t&amp;&amp;(e=s)})),void 0!==e&amp;&amp;(h=n[e&gt;0?e-1:e])}let m=0;return void 0!==h&amp;&amp;(m=l.indexOf(h),m&lt;0&amp;&amp;(m=a.activeIndex-1),"auto"===i.slidesPerView&amp;&amp;1===i.slidesPerGroup&amp;&amp;i.slidesPerGroupAuto&amp;&amp;(m=m-a.slidesPerViewDynamic("previous",!0)+1,m=Math.max(m,0))),a.slideTo(m,e,t,s)},slideReset:function(e=this.params.speed,t=!0,s){return this.slideTo(this.activeIndex,e,t,s)},slideToClosest:function(e=this.params.speed,t=!0,s,a=.5){const i=this;let r=i.activeIndex;const n=Math.min(i.params.slidesPerGroupSkip,r),l=n+Math.floor((r-n)/i.params.slidesPerGroup),o=i.rtlTranslate?i.translate:-i.translate;if(o&gt;=i.snapGrid[l]){const e=i.snapGrid[l];o-e&gt;(i.snapGrid[l+1]-e)*a&amp;&amp;(r+=i.params.slidesPerGroup)}else{const e=i.snapGrid[l-1];o-e&lt;=(i.snapGrid[l]-e)*a&amp;&amp;(r-=i.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,i.slidesGrid.length-1),i.slideTo(r,e,t,s)},slideToClickedSlide:function(){const e=this,{params:t,$wrapperEl:s}=e,a="auto"===t.slidesPerView?e.slidesPerViewDynamic():t.slidesPerView;let i,r=e.clickedIndex;if(t.loop){if(e.animating)return;i=parseInt(d(e.clickedSlide).attr("data-swiper-slide-index"),10),t.centeredSlides?r&lt;e.loopedSlides-a/2||r&gt;e.slides.length-e.loopedSlides+a/2?(e.loopFix(),r=s.children(`.${t.slideClass}[data-swiper-slide-index="${i}"]:not(.${t.slideDuplicateClass})`).eq(0).index(),p((()=&gt;{e.slideTo(r)}))):e.slideTo(r):r&gt;e.slides.length-a?(e.loopFix(),r=s.children(`.${t.slideClass}[data-swiper-slide-index="${i}"]:not(.${t.slideDuplicateClass})`).eq(0).index(),p((()=&gt;{e.slideTo(r)}))):e.slideTo(r)}else e.slideTo(r)}},loop:{loopCreate:function(){const e=this,t=a(),{params:s,$wrapperEl:i}=e;i.children(`.${s.slideClass}.${s.slideDuplicateClass}`).remove();let r=i.children(`.${s.slideClass}`);if(s.loopFillGroupWithBlank){const e=s.slidesPerGroup-r.length%s.slidesPerGroup;if(e!==s.slidesPerGroup){for(let a=0;a&lt;e;a+=1){const e=d(t.createElement("div")).addClass(`${s.slideClass} ${s.slideBlankClass}`);i.append(e)}r=i.children(`.${s.slideClass}`)}}"auto"!==s.slidesPerView||s.loopedSlides||(s.loopedSlides=r.length),e.loopedSlides=Math.ceil(parseFloat(s.loopedSlides||s.slidesPerView,10)),e.loopedSlides+=s.loopAdditionalSlides,e.loopedSlides&gt;r.length&amp;&amp;(e.loopedSlides=r.length);const n=[],l=[];r.each(((t,s)=&gt;{const a=d(t);s&lt;e.loopedSlides&amp;&amp;l.push(t),s&lt;r.length&amp;&amp;s&gt;=r.length-e.loopedSlides&amp;&amp;n.push(t),a.attr("data-swiper-slide-index",s)}));for(let e=0;e&lt;l.length;e+=1)i.append(d(l[e].cloneNode(!0)).addClass(s.slideDuplicateClass));for(let e=n.length-1;e&gt;=0;e-=1)i.prepend(d(n[e].cloneNode(!0)).addClass(s.slideDuplicateClass))},loopFix:function(){const e=this;e.emit("beforeLoopFix");const{activeIndex:t,slides:s,loopedSlides:a,allowSlidePrev:i,allowSlideNext:r,snapGrid:n,rtlTranslate:l}=e;let o;e.allowSlidePrev=!0,e.allowSlideNext=!0;const d=-n[t]-e.getTranslate();if(t&lt;a){o=s.length-3*a+t,o+=a;e.slideTo(o,0,!1,!0)&amp;&amp;0!==d&amp;&amp;e.setTranslate((l?-e.translate:e.translate)-d)}else if(t&gt;=s.length-a){o=-s.length+t+a,o+=a;e.slideTo(o,0,!1,!0)&amp;&amp;0!==d&amp;&amp;e.setTranslate((l?-e.translate:e.translate)-d)}e.allowSlidePrev=i,e.allowSlideNext=r,e.emit("loopFix")},loopDestroy:function(){const{$wrapperEl:e,params:t,slides:s}=this;e.children(`.${t.slideClass}.${t.slideDuplicateClass},.${t.slideClass}.${t.slideBlankClass}`).remove(),s.removeAttr("data-swiper-slide-index")}},grabCursor:{setGrabCursor:function(e){const t=this;if(t.support.touch||!t.params.simulateTouch||t.params.watchOverflow&amp;&amp;t.isLocked||t.params.cssMode)return;const s="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;s.style.cursor="move",s.style.cursor=e?"-webkit-grabbing":"-webkit-grab",s.style.cursor=e?"-moz-grabbin":"-moz-grab",s.style.cursor=e?"grabbing":"grab"},unsetGrabCursor:function(){const e=this;e.support.touch||e.params.watchOverflow&amp;&amp;e.isLocked||e.params.cssMode||(e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="")}},events:{attachEvents:function(){const e=this,t=a(),{params:s,support:i}=e;e.onTouchStart=S.bind(e),e.onTouchMove=M.bind(e),e.onTouchEnd=P.bind(e),s.cssMode&amp;&amp;(e.onScroll=O.bind(e)),e.onClick=z.bind(e),i.touch&amp;&amp;!I&amp;&amp;(t.addEventListener("touchstart",L),I=!0),A(e,"on")},detachEvents:function(){A(this,"off")}},breakpoints:{setBreakpoint:function(){const e=this,{activeIndex:t,initialized:s,loopedSlides:a=0,params:i,$el:r}=e,n=i.breakpoints;if(!n||n&amp;&amp;0===Object.keys(n).length)return;const l=e.getBreakpoint(n,e.params.breakpointsBase,e.el);if(!l||e.currentBreakpoint===l)return;const o=(l in n?n[l]:void 0)||e.originalParams,d=D(e,i),c=D(e,o),p=i.enabled;d&amp;&amp;!c?(r.removeClass(`${i.containerModifierClass}grid ${i.containerModifierClass}grid-column`),e.emitContainerClasses()):!d&amp;&amp;c&amp;&amp;(r.addClass(`${i.containerModifierClass}grid`),(o.grid.fill&amp;&amp;"column"===o.grid.fill||!o.grid.fill&amp;&amp;"column"===i.grid.fill)&amp;&amp;r.addClass(`${i.containerModifierClass}grid-column`),e.emitContainerClasses());const u=o.direction&amp;&amp;o.direction!==i.direction,h=i.loop&amp;&amp;(o.slidesPerView!==i.slidesPerView||u);u&amp;&amp;s&amp;&amp;e.changeDirection(),f(e.params,o);const m=e.params.enabled;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),p&amp;&amp;!m?e.disable():!p&amp;&amp;m&amp;&amp;e.enable(),e.currentBreakpoint=l,e.emit("_beforeBreakpoint",o),h&amp;&amp;s&amp;&amp;(e.loopDestroy(),e.loopCreate(),e.updateSlides(),e.slideTo(t-a+e.loopedSlides,0,!1)),e.emit("breakpoint",o)},getBreakpoint:function(e,t="window",s){if(!e||"container"===t&amp;&amp;!s)return;let a=!1;const i=r(),n="window"===t?i.innerHeight:s.clientHeight,l=Object.keys(e).map((e=&gt;{if("string"==typeof e&amp;&amp;0===e.indexOf("@")){const t=parseFloat(e.substr(1));return{value:n*t,point:e}}return{value:e,point:e}}));l.sort(((e,t)=&gt;parseInt(e.value,10)-parseInt(t.value,10)));for(let e=0;e&lt;l.length;e+=1){const{point:r,value:n}=l[e];"window"===t?i.matchMedia(`(min-width: ${n}px)`).matches&amp;&amp;(a=r):n&lt;=s.clientWidth&amp;&amp;(a=r)}return a||"max"}},checkOverflow:{checkOverflow:function(){const e=this,{isLocked:t,params:s}=e,{slidesOffsetBefore:a}=s;if(a){const t=e.slides.length-1,s=e.slidesGrid[t]+e.slidesSizesGrid[t]+2*a;e.isLocked=e.size&gt;s}else e.isLocked=1===e.snapGrid.length;!0===s.allowSlideNext&amp;&amp;(e.allowSlideNext=!e.isLocked),!0===s.allowSlidePrev&amp;&amp;(e.allowSlidePrev=!e.isLocked),t&amp;&amp;t!==e.isLocked&amp;&amp;(e.isEnd=!1),t!==e.isLocked&amp;&amp;e.emit(e.isLocked?"lock":"unlock")}},classes:{addClasses:function(){const e=this,{classNames:t,params:s,rtl:a,$el:i,device:r,support:n}=e,l=function(e,t){const s=[];return e.forEach((e=&gt;{"object"==typeof e?Object.keys(e).forEach((a=&gt;{e[a]&amp;&amp;s.push(t+a)})):"string"==typeof e&amp;&amp;s.push(t+e)})),s}(["initialized",s.direction,{"pointer-events":!n.touch},{"free-mode":e.params.freeMode&amp;&amp;s.freeMode.enabled},{autoheight:s.autoHeight},{rtl:a},{grid:s.grid&amp;&amp;s.grid.rows&gt;1},{"grid-column":s.grid&amp;&amp;s.grid.rows&gt;1&amp;&amp;"column"===s.grid.fill},{android:r.android},{ios:r.ios},{"css-mode":s.cssMode},{centered:s.cssMode&amp;&amp;s.centeredSlides}],s.containerModifierClass);t.push(...l),i.addClass([...t].join(" ")),e.emitContainerClasses()},removeClasses:function(){const{$el:e,classNames:t}=this;e.removeClass(t.join(" ")),this.emitContainerClasses()}},images:{loadImage:function(e,t,s,a,i,n){const l=r();let o;function c(){n&amp;&amp;n()}d(e).parent("picture")[0]||e.complete&amp;&amp;i?c():t?(o=new l.Image,o.onload=c,o.onerror=c,a&amp;&amp;(o.sizes=a),s&amp;&amp;(o.srcset=s),t&amp;&amp;(o.src=t)):c()},preloadImages:function(){const e=this;function t(){null!=e&amp;&amp;e&amp;&amp;!e.destroyed&amp;&amp;(void 0!==e.imagesLoaded&amp;&amp;(e.imagesLoaded+=1),e.imagesLoaded===e.imagesToLoad.length&amp;&amp;(e.params.updateOnImagesReady&amp;&amp;e.update(),e.emit("imagesReady")))}e.imagesToLoad=e.$el.find("img");for(let s=0;s&lt;e.imagesToLoad.length;s+=1){const a=e.imagesToLoad[s];e.loadImage(a,a.currentSrc||a.getAttribute("src"),a.srcset||a.getAttribute("srcset"),a.sizes||a.getAttribute("sizes"),!0,t)}}}},X={};class H{constructor(...e){let t,s;if(1===e.length&amp;&amp;e[0].constructor&amp;&amp;"Object"===Object.prototype.toString.call(e[0]).slice(8,-1)?s=e[0]:[t,s]=e,s||(s={}),s=f({},s),t&amp;&amp;!s.el&amp;&amp;(s.el=t),s.el&amp;&amp;d(s.el).length&gt;1){const e=[];return d(s.el).each((t=&gt;{const a=f({},s,{el:t});e.push(new H(a))})),e}const a=this;a.__swiper__=!0,a.support=y(),a.device=E({userAgent:s.userAgent}),a.browser=T(),a.eventsListeners={},a.eventsAnyListeners=[],a.modules=[...a.__modules__],s.modules&amp;&amp;Array.isArray(s.modules)&amp;&amp;a.modules.push(...s.modules);const i={};a.modules.forEach((e=&gt;{e({swiper:a,extendParams:N(s,i),on:a.on.bind(a),once:a.once.bind(a),off:a.off.bind(a),emit:a.emit.bind(a)})}));const r=f({},G,i);return a.params=f({},r,X,s),a.originalParams=f({},a.params),a.passedParams=f({},s),a.params&amp;&amp;a.params.on&amp;&amp;Object.keys(a.params.on).forEach((e=&gt;{a.on(e,a.params.on[e])})),a.params&amp;&amp;a.params.onAny&amp;&amp;a.onAny(a.params.onAny),a.$=d,Object.assign(a,{enabled:a.params.enabled,el:t,classNames:[],slides:d(),slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=&gt;"horizontal"===a.params.direction,isVertical:()=&gt;"vertical"===a.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev,touchEvents:function(){const e=["touchstart","touchmove","touchend","touchcancel"],t=["pointerdown","pointermove","pointerup"];return a.touchEventsTouch={start:e[0],move:e[1],end:e[2],cancel:e[3]},a.touchEventsDesktop={start:t[0],move:t[1],end:t[2]},a.support.touch||!a.params.simulateTouch?a.touchEventsTouch:a.touchEventsDesktop}(),touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:a.params.focusableElements,lastClickTime:u(),clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,isTouchEvent:void 0,startMoving:void 0},allowClick:!0,allowTouchMove:a.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),a.emit("_swiper"),a.params.init&amp;&amp;a.init(),a}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&amp;&amp;e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&amp;&amp;(e.enabled=!1,e.params.grabCursor&amp;&amp;e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const s=this;e=Math.min(Math.max(e,0),1);const a=s.minTranslate(),i=(s.maxTranslate()-a)*e+a;s.translateTo(i,void 0===t?0:t),s.updateActiveIndex(),s.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter((t=&gt;0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass)));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return e.className.split(" ").filter((e=&gt;0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass))).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.each((s=&gt;{const a=e.getSlideClasses(s);t.push({slideEl:s,classNames:a}),e.emit("_slideClass",s,a)})),e.emit("_slideClasses",t)}slidesPerViewDynamic(e="current",t=!1){const{params:s,slides:a,slidesGrid:i,slidesSizesGrid:r,size:n,activeIndex:l}=this;let o=1;if(s.centeredSlides){let e,t=a[l].swiperSlideSize;for(let s=l+1;s&lt;a.length;s+=1)a[s]&amp;&amp;!e&amp;&amp;(t+=a[s].swiperSlideSize,o+=1,t&gt;n&amp;&amp;(e=!0));for(let s=l-1;s&gt;=0;s-=1)a[s]&amp;&amp;!e&amp;&amp;(t+=a[s].swiperSlideSize,o+=1,t&gt;n&amp;&amp;(e=!0))}else if("current"===e)for(let e=l+1;e&lt;a.length;e+=1){(t?i[e]+r[e]-i[l]&lt;n:i[e]-i[l]&lt;n)&amp;&amp;(o+=1)}else for(let e=l-1;e&gt;=0;e-=1){i[l]-i[e]&lt;n&amp;&amp;(o+=1)}return o}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:s}=e;function a(){const t=e.rtlTranslate?-1*e.translate:e.translate,s=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(s),e.updateActiveIndex(),e.updateSlidesClasses()}let i;s.breakpoints&amp;&amp;e.setBreakpoint(),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.params.freeMode&amp;&amp;e.params.freeMode.enabled?(a(),e.params.autoHeight&amp;&amp;e.updateAutoHeight()):(i=("auto"===e.params.slidesPerView||e.params.slidesPerView&gt;1)&amp;&amp;e.isEnd&amp;&amp;!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),i||a()),s.watchOverflow&amp;&amp;t!==e.snapGrid&amp;&amp;e.checkOverflow(),e.emit("update")}changeDirection(e,t=!0){const s=this,a=s.params.direction;return e||(e="horizontal"===a?"vertical":"horizontal"),e===a||"horizontal"!==e&amp;&amp;"vertical"!==e||(s.$el.removeClass(`${s.params.containerModifierClass}${a}`).addClass(`${s.params.containerModifierClass}${e}`),s.emitContainerClasses(),s.params.direction=e,s.slides.each((t=&gt;{"vertical"===e?t.style.width="":t.style.height=""})),s.emit("changeDirection"),t&amp;&amp;s.update()),s}mount(e){const t=this;if(t.mounted)return!0;const s=d(e||t.params.el);if(!(e=s[0]))return!1;e.swiper=t;const i=()=&gt;`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let r=(()=&gt;{if(e&amp;&amp;e.shadowRoot&amp;&amp;e.shadowRoot.querySelector){const t=d(e.shadowRoot.querySelector(i()));return t.children=e=&gt;s.children(e),t}return s.children(i())})();if(0===r.length&amp;&amp;t.params.createElements){const e=a().createElement("div");r=d(e),e.className=t.params.wrapperClass,s.append(e),s.children(`.${t.params.slideClass}`).each((e=&gt;{r.append(e)}))}return Object.assign(t,{$el:s,el:e,$wrapperEl:r,wrapperEl:r[0],mounted:!0,rtl:"rtl"===e.dir.toLowerCase()||"rtl"===s.css("direction"),rtlTranslate:"horizontal"===t.params.direction&amp;&amp;("rtl"===e.dir.toLowerCase()||"rtl"===s.css("direction")),wrongRTL:"-webkit-box"===r.css("display")}),!0}init(e){const t=this;if(t.initialized)return t;return!1===t.mount(e)||(t.emit("beforeInit"),t.params.breakpoints&amp;&amp;t.setBreakpoint(),t.addClasses(),t.params.loop&amp;&amp;t.loopCreate(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&amp;&amp;t.checkOverflow(),t.params.grabCursor&amp;&amp;t.enabled&amp;&amp;t.setGrabCursor(),t.params.preloadImages&amp;&amp;t.preloadImages(),t.params.loop?t.slideTo(t.params.initialSlide+t.loopedSlides,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.attachEvents(),t.initialized=!0,t.emit("init"),t.emit("afterInit")),t}destroy(e=!0,t=!0){const s=this,{params:a,$el:i,$wrapperEl:r,slides:n}=s;return void 0===s.params||s.destroyed||(s.emit("beforeDestroy"),s.initialized=!1,s.detachEvents(),a.loop&amp;&amp;s.loopDestroy(),t&amp;&amp;(s.removeClasses(),i.removeAttr("style"),r.removeAttr("style"),n&amp;&amp;n.length&amp;&amp;n.removeClass([a.slideVisibleClass,a.slideActiveClass,a.slideNextClass,a.slidePrevClass].join(" ")).removeAttr("style").removeAttr("data-swiper-slide-index")),s.emit("destroy"),Object.keys(s.eventsListeners).forEach((e=&gt;{s.off(e)})),!1!==e&amp;&amp;(s.$el[0].swiper=null,function(e){const t=e;Object.keys(t).forEach((e=&gt;{try{t[e]=null}catch(e){}try{delete t[e]}catch(e){}}))}(s)),s.destroyed=!0),null}static extendDefaults(e){f(X,e)}static get extendedDefaults(){return X}static get defaults(){return G}static installModule(e){H.prototype.__modules__||(H.prototype.__modules__=[]);const t=H.prototype.__modules__;"function"==typeof e&amp;&amp;t.indexOf(e)&lt;0&amp;&amp;t.push(e)}static use(e){return Array.isArray(e)?(e.forEach((e=&gt;H.installModule(e))),H):(H.installModule(e),H)}}function Y(e,t,s,i){const r=a();return e.params.createElements&amp;&amp;Object.keys(i).forEach((a=&gt;{if(!s[a]&amp;&amp;!0===s.auto){let n=e.$el.children(`.${i[a]}`)[0];n||(n=r.createElement("div"),n.className=i[a],e.$el.append(n)),s[a]=n,t[a]=n}})),s}function W(e=""){return`.${e.trim().replace(/([\.:!\/])/g,"\\$1").replace(/ /g,".")}`}function R(e){const t=this,{$wrapperEl:s,params:a}=t;if(a.loop&amp;&amp;t.loopDestroy(),"object"==typeof e&amp;&amp;"length"in e)for(let t=0;t&lt;e.length;t+=1)e[t]&amp;&amp;s.append(e[t]);else s.append(e);a.loop&amp;&amp;t.loopCreate(),a.observer||t.update()}function j(e){const t=this,{params:s,$wrapperEl:a,activeIndex:i}=t;s.loop&amp;&amp;t.loopDestroy();let r=i+1;if("object"==typeof e&amp;&amp;"length"in e){for(let t=0;t&lt;e.length;t+=1)e[t]&amp;&amp;a.prepend(e[t]);r=i+e.length}else a.prepend(e);s.loop&amp;&amp;t.loopCreate(),s.observer||t.update(),t.slideTo(r,0,!1)}function _(e,t){const s=this,{$wrapperEl:a,params:i,activeIndex:r}=s;let n=r;i.loop&amp;&amp;(n-=s.loopedSlides,s.loopDestroy(),s.slides=a.children(`.${i.slideClass}`));const l=s.slides.length;if(e&lt;=0)return void s.prependSlide(t);if(e&gt;=l)return void s.appendSlide(t);let o=n&gt;e?n+1:n;const d=[];for(let t=l-1;t&gt;=e;t-=1){const e=s.slides.eq(t);e.remove(),d.unshift(e)}if("object"==typeof t&amp;&amp;"length"in t){for(let e=0;e&lt;t.length;e+=1)t[e]&amp;&amp;a.append(t[e]);o=n&gt;e?n+t.length:n}else a.append(t);for(let e=0;e&lt;d.length;e+=1)a.append(d[e]);i.loop&amp;&amp;s.loopCreate(),i.observer||s.update(),i.loop?s.slideTo(o+s.loopedSlides,0,!1):s.slideTo(o,0,!1)}function V(e){const t=this,{params:s,$wrapperEl:a,activeIndex:i}=t;let r=i;s.loop&amp;&amp;(r-=t.loopedSlides,t.loopDestroy(),t.slides=a.children(`.${s.slideClass}`));let n,l=r;if("object"==typeof e&amp;&amp;"length"in e){for(let s=0;s&lt;e.length;s+=1)n=e[s],t.slides[n]&amp;&amp;t.slides.eq(n).remove(),n&lt;l&amp;&amp;(l-=1);l=Math.max(l,0)}else n=e,t.slides[n]&amp;&amp;t.slides.eq(n).remove(),n&lt;l&amp;&amp;(l-=1),l=Math.max(l,0);s.loop&amp;&amp;t.loopCreate(),s.observer||t.update(),s.loop?t.slideTo(l+t.loopedSlides,0,!1):t.slideTo(l,0,!1)}function q(){const e=this,t=[];for(let s=0;s&lt;e.slides.length;s+=1)t.push(s);e.removeSlide(t)}function F(e){const{effect:t,swiper:s,on:a,setTranslate:i,setTransition:r,overwriteParams:n,perspective:l}=e;a("beforeInit",(()=&gt;{if(s.params.effect!==t)return;s.classNames.push(`${s.params.containerModifierClass}${t}`),l&amp;&amp;l()&amp;&amp;s.classNames.push(`${s.params.containerModifierClass}3d`);const e=n?n():{};Object.assign(s.params,e),Object.assign(s.originalParams,e)})),a("setTranslate",(()=&gt;{s.params.effect===t&amp;&amp;i()})),a("setTransition",((e,a)=&gt;{s.params.effect===t&amp;&amp;r(a)}))}function U(e,t){return e.transformEl?t.find(e.transformEl).css({"backface-visibility":"hidden","-webkit-backface-visibility":"hidden"}):t}function K({swiper:e,duration:t,transformEl:s,allSlides:a}){const{slides:i,activeIndex:r,$wrapperEl:n}=e;if(e.params.virtualTranslate&amp;&amp;0!==t){let t,l=!1;t=a?s?i.find(s):i:s?i.eq(r).find(s):i.eq(r),t.transitionEnd((()=&gt;{if(l)return;if(!e||e.destroyed)return;l=!0,e.animating=!1;const t=["webkitTransitionEnd","transitionend"];for(let e=0;e&lt;t.length;e+=1)n.trigger(t[e])}))}}function Z(e,t,s){const a="swiper-slide-shadow"+(s?`-${s}`:""),i=e.transformEl?t.find(e.transformEl):t;let r=i.children(`.${a}`);return r.length||(r=d(`&lt;div class="swiper-slide-shadow${s?`-${s}`:""}"&gt;&lt;/div&gt;`),i.append(r)),r}Object.keys(B).forEach((e=&gt;{Object.keys(B[e]).forEach((t=&gt;{H.prototype[t]=B[e][t]}))})),H.use([function({swiper:e,on:t,emit:s}){const a=r();let i=null;const n=()=&gt;{e&amp;&amp;!e.destroyed&amp;&amp;e.initialized&amp;&amp;(s("beforeResize"),s("resize"))},l=()=&gt;{e&amp;&amp;!e.destroyed&amp;&amp;e.initialized&amp;&amp;s("orientationchange")};t("init",(()=&gt;{e.params.resizeObserver&amp;&amp;void 0!==a.ResizeObserver?e&amp;&amp;!e.destroyed&amp;&amp;e.initialized&amp;&amp;(i=new ResizeObserver((t=&gt;{const{width:s,height:a}=e;let i=s,r=a;t.forEach((({contentBoxSize:t,contentRect:s,target:a})=&gt;{a&amp;&amp;a!==e.el||(i=s?s.width:(t[0]||t).inlineSize,r=s?s.height:(t[0]||t).blockSize)})),i===s&amp;&amp;r===a||n()})),i.observe(e.el)):(a.addEventListener("resize",n),a.addEventListener("orientationchange",l))})),t("destroy",(()=&gt;{i&amp;&amp;i.unobserve&amp;&amp;e.el&amp;&amp;(i.unobserve(e.el),i=null),a.removeEventListener("resize",n),a.removeEventListener("orientationchange",l)}))},function({swiper:e,extendParams:t,on:s,emit:a}){const i=[],n=r(),l=(e,t={})=&gt;{const s=new(n.MutationObserver||n.WebkitMutationObserver)((e=&gt;{if(1===e.length)return void a("observerUpdate",e[0]);const t=function(){a("observerUpdate",e[0])};n.requestAnimationFrame?n.requestAnimationFrame(t):n.setTimeout(t,0)}));s.observe(e,{attributes:void 0===t.attributes||t.attributes,childList:void 0===t.childList||t.childList,characterData:void 0===t.characterData||t.characterData}),i.push(s)};t({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",(()=&gt;{if(e.params.observer){if(e.params.observeParents){const t=e.$el.parents();for(let e=0;e&lt;t.length;e+=1)l(t[e])}l(e.$el[0],{childList:e.params.observeSlideChildren}),l(e.$wrapperEl[0],{attributes:!1})}})),s("destroy",(()=&gt;{i.forEach((e=&gt;{e.disconnect()})),i.splice(0,i.length)}))}]);const J=[function({swiper:e,extendParams:t,on:s}){function a(t,s){const a=e.params.virtual;if(a.cache&amp;&amp;e.virtual.cache[s])return e.virtual.cache[s];const i=a.renderSlide?d(a.renderSlide.call(e,t,s)):d(`&lt;div class="${e.params.slideClass}" data-swiper-slide-index="${s}"&gt;${t}&lt;/div&gt;`);return i.attr("data-swiper-slide-index")||i.attr("data-swiper-slide-index",s),a.cache&amp;&amp;(e.virtual.cache[s]=i),i}function i(t){const{slidesPerView:s,slidesPerGroup:i,centeredSlides:r}=e.params,{addSlidesBefore:n,addSlidesAfter:l}=e.params.virtual,{from:o,to:d,slides:c,slidesGrid:p,offset:u}=e.virtual;e.updateActiveIndex();const h=e.activeIndex||0;let m,f,g;m=e.rtlTranslate?"right":e.isHorizontal()?"left":"top",r?(f=Math.floor(s/2)+i+l,g=Math.floor(s/2)+i+n):(f=s+(i-1)+l,g=i+n);const v=Math.max((h||0)-g,0),w=Math.min((h||0)+f,c.length-1),b=(e.slidesGrid[v]||0)-(e.slidesGrid[0]||0);function x(){e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.lazy&amp;&amp;e.params.lazy.enabled&amp;&amp;e.lazy.load()}if(Object.assign(e.virtual,{from:v,to:w,offset:b,slidesGrid:e.slidesGrid}),o===v&amp;&amp;d===w&amp;&amp;!t)return e.slidesGrid!==p&amp;&amp;b!==u&amp;&amp;e.slides.css(m,`${b}px`),void e.updateProgress();if(e.params.virtual.renderExternal)return e.params.virtual.renderExternal.call(e,{offset:b,from:v,to:w,slides:function(){const e=[];for(let t=v;t&lt;=w;t+=1)e.push(c[t]);return e}()}),void(e.params.virtual.renderExternalUpdate&amp;&amp;x());const y=[],E=[];if(t)e.$wrapperEl.find(`.${e.params.slideClass}`).remove();else for(let t=o;t&lt;=d;t+=1)(t&lt;v||t&gt;w)&amp;&amp;e.$wrapperEl.find(`.${e.params.slideClass}[data-swiper-slide-index="${t}"]`).remove();for(let e=0;e&lt;c.length;e+=1)e&gt;=v&amp;&amp;e&lt;=w&amp;&amp;(void 0===d||t?E.push(e):(e&gt;d&amp;&amp;E.push(e),e&lt;o&amp;&amp;y.push(e)));E.forEach((t=&gt;{e.$wrapperEl.append(a(c[t],t))})),y.sort(((e,t)=&gt;t-e)).forEach((t=&gt;{e.$wrapperEl.prepend(a(c[t],t))})),e.$wrapperEl.children(".swiper-slide").css(m,`${b}px`),x()}t({virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,renderExternalUpdate:!0,addSlidesBefore:0,addSlidesAfter:0}}),e.virtual={cache:{},from:void 0,to:void 0,slides:[],offset:0,slidesGrid:[]},s("beforeInit",(()=&gt;{e.params.virtual.enabled&amp;&amp;(e.virtual.slides=e.params.virtual.slides,e.classNames.push(`${e.params.containerModifierClass}virtual`),e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0,e.params.initialSlide||i())})),s("setTranslate",(()=&gt;{e.params.virtual.enabled&amp;&amp;i()})),s("init update resize",(()=&gt;{e.params.virtual.enabled&amp;&amp;e.params.cssMode&amp;&amp;g(e.wrapperEl,"--swiper-virtual-size",`${e.virtualSize}px`)})),Object.assign(e.virtual,{appendSlide:function(t){if("object"==typeof t&amp;&amp;"length"in t)for(let s=0;s&lt;t.length;s+=1)t[s]&amp;&amp;e.virtual.slides.push(t[s]);else e.virtual.slides.push(t);i(!0)},prependSlide:function(t){const s=e.activeIndex;let a=s+1,r=1;if(Array.isArray(t)){for(let s=0;s&lt;t.length;s+=1)t[s]&amp;&amp;e.virtual.slides.unshift(t[s]);a=s+t.length,r=t.length}else e.virtual.slides.unshift(t);if(e.params.virtual.cache){const t=e.virtual.cache,s={};Object.keys(t).forEach((e=&gt;{const a=t[e],i=a.attr("data-swiper-slide-index");i&amp;&amp;a.attr("data-swiper-slide-index",parseInt(i,10)+1),s[parseInt(e,10)+r]=a})),e.virtual.cache=s}i(!0),e.slideTo(a,0)},removeSlide:function(t){if(null==t)return;let s=e.activeIndex;if(Array.isArray(t))for(let a=t.length-1;a&gt;=0;a-=1)e.virtual.slides.splice(t[a],1),e.params.virtual.cache&amp;&amp;delete e.virtual.cache[t[a]],t[a]&lt;s&amp;&amp;(s-=1),s=Math.max(s,0);else e.virtual.slides.splice(t,1),e.params.virtual.cache&amp;&amp;delete e.virtual.cache[t],t&lt;s&amp;&amp;(s-=1),s=Math.max(s,0);i(!0),e.slideTo(s,0)},removeAllSlides:function(){e.virtual.slides=[],e.params.virtual.cache&amp;&amp;(e.virtual.cache={}),i(!0),e.slideTo(0,0)},update:i})},function({swiper:e,extendParams:t,on:s,emit:i}){const n=a(),l=r();function o(t){if(!e.enabled)return;const{rtlTranslate:s}=e;let a=t;a.originalEvent&amp;&amp;(a=a.originalEvent);const r=a.keyCode||a.charCode,o=e.params.keyboard.pageUpDown,d=o&amp;&amp;33===r,c=o&amp;&amp;34===r,p=37===r,u=39===r,h=38===r,m=40===r;if(!e.allowSlideNext&amp;&amp;(e.isHorizontal()&amp;&amp;u||e.isVertical()&amp;&amp;m||c))return!1;if(!e.allowSlidePrev&amp;&amp;(e.isHorizontal()&amp;&amp;p||e.isVertical()&amp;&amp;h||d))return!1;if(!(a.shiftKey||a.altKey||a.ctrlKey||a.metaKey||n.activeElement&amp;&amp;n.activeElement.nodeName&amp;&amp;("input"===n.activeElement.nodeName.toLowerCase()||"textarea"===n.activeElement.nodeName.toLowerCase()))){if(e.params.keyboard.onlyInViewport&amp;&amp;(d||c||p||u||h||m)){let t=!1;if(e.$el.parents(`.${e.params.slideClass}`).length&gt;0&amp;&amp;0===e.$el.parents(`.${e.params.slideActiveClass}`).length)return;const a=e.$el,i=a[0].clientWidth,r=a[0].clientHeight,n=l.innerWidth,o=l.innerHeight,d=e.$el.offset();s&amp;&amp;(d.left-=e.$el[0].scrollLeft);const c=[[d.left,d.top],[d.left+i,d.top],[d.left,d.top+r],[d.left+i,d.top+r]];for(let e=0;e&lt;c.length;e+=1){const s=c[e];if(s[0]&gt;=0&amp;&amp;s[0]&lt;=n&amp;&amp;s[1]&gt;=0&amp;&amp;s[1]&lt;=o){if(0===s[0]&amp;&amp;0===s[1])continue;t=!0}}if(!t)return}e.isHorizontal()?((d||c||p||u)&amp;&amp;(a.preventDefault?a.preventDefault():a.returnValue=!1),((c||u)&amp;&amp;!s||(d||p)&amp;&amp;s)&amp;&amp;e.slideNext(),((d||p)&amp;&amp;!s||(c||u)&amp;&amp;s)&amp;&amp;e.slidePrev()):((d||c||h||m)&amp;&amp;(a.preventDefault?a.preventDefault():a.returnValue=!1),(c||m)&amp;&amp;e.slideNext(),(d||h)&amp;&amp;e.slidePrev()),i("keyPress",r)}}function c(){e.keyboard.enabled||(d(n).on("keydown",o),e.keyboard.enabled=!0)}function p(){e.keyboard.enabled&amp;&amp;(d(n).off("keydown",o),e.keyboard.enabled=!1)}e.keyboard={enabled:!1},t({keyboard:{enabled:!1,onlyInViewport:!0,pageUpDown:!0}}),s("init",(()=&gt;{e.params.keyboard.enabled&amp;&amp;c()})),s("destroy",(()=&gt;{e.keyboard.enabled&amp;&amp;p()})),Object.assign(e.keyboard,{enable:c,disable:p})},function({swiper:e,extendParams:t,on:s,emit:a}){const i=r();let n;t({mousewheel:{enabled:!1,releaseOnEdges:!1,invert:!1,forceToAxis:!1,sensitivity:1,eventsTarget:"container",thresholdDelta:null,thresholdTime:null}}),e.mousewheel={enabled:!1};let l,o=u();const c=[];function h(){e.enabled&amp;&amp;(e.mouseEntered=!0)}function m(){e.enabled&amp;&amp;(e.mouseEntered=!1)}function f(t){return!(e.params.mousewheel.thresholdDelta&amp;&amp;t.delta&lt;e.params.mousewheel.thresholdDelta)&amp;&amp;(!(e.params.mousewheel.thresholdTime&amp;&amp;u()-o&lt;e.params.mousewheel.thresholdTime)&amp;&amp;(t.delta&gt;=6&amp;&amp;u()-o&lt;60||(t.direction&lt;0?e.isEnd&amp;&amp;!e.params.loop||e.animating||(e.slideNext(),a("scroll",t.raw)):e.isBeginning&amp;&amp;!e.params.loop||e.animating||(e.slidePrev(),a("scroll",t.raw)),o=(new i.Date).getTime(),!1)))}function g(t){let s=t,i=!0;if(!e.enabled)return;const r=e.params.mousewheel;e.params.cssMode&amp;&amp;s.preventDefault();let o=e.$el;if("container"!==e.params.mousewheel.eventsTarget&amp;&amp;(o=d(e.params.mousewheel.eventsTarget)),!e.mouseEntered&amp;&amp;!o[0].contains(s.target)&amp;&amp;!r.releaseOnEdges)return!0;s.originalEvent&amp;&amp;(s=s.originalEvent);let h=0;const m=e.rtlTranslate?-1:1,g=function(e){let t=0,s=0,a=0,i=0;return"detail"in e&amp;&amp;(s=e.detail),"wheelDelta"in e&amp;&amp;(s=-e.wheelDelta/120),"wheelDeltaY"in e&amp;&amp;(s=-e.wheelDeltaY/120),"wheelDeltaX"in e&amp;&amp;(t=-e.wheelDeltaX/120),"axis"in e&amp;&amp;e.axis===e.HORIZONTAL_AXIS&amp;&amp;(t=s,s=0),a=10*t,i=10*s,"deltaY"in e&amp;&amp;(i=e.deltaY),"deltaX"in e&amp;&amp;(a=e.deltaX),e.shiftKey&amp;&amp;!a&amp;&amp;(a=i,i=0),(a||i)&amp;&amp;e.deltaMode&amp;&amp;(1===e.deltaMode?(a*=40,i*=40):(a*=800,i*=800)),a&amp;&amp;!t&amp;&amp;(t=a&lt;1?-1:1),i&amp;&amp;!s&amp;&amp;(s=i&lt;1?-1:1),{spinX:t,spinY:s,pixelX:a,pixelY:i}}(s);if(r.forceToAxis)if(e.isHorizontal()){if(!(Math.abs(g.pixelX)&gt;Math.abs(g.pixelY)))return!0;h=-g.pixelX*m}else{if(!(Math.abs(g.pixelY)&gt;Math.abs(g.pixelX)))return!0;h=-g.pixelY}else h=Math.abs(g.pixelX)&gt;Math.abs(g.pixelY)?-g.pixelX*m:-g.pixelY;if(0===h)return!0;r.invert&amp;&amp;(h=-h);let v=e.getTranslate()+h*r.sensitivity;if(v&gt;=e.minTranslate()&amp;&amp;(v=e.minTranslate()),v&lt;=e.maxTranslate()&amp;&amp;(v=e.maxTranslate()),i=!!e.params.loop||!(v===e.minTranslate()||v===e.maxTranslate()),i&amp;&amp;e.params.nested&amp;&amp;s.stopPropagation(),e.params.freeMode&amp;&amp;e.params.freeMode.enabled){const t={time:u(),delta:Math.abs(h),direction:Math.sign(h)},i=l&amp;&amp;t.time&lt;l.time+500&amp;&amp;t.delta&lt;=l.delta&amp;&amp;t.direction===l.direction;if(!i){l=void 0,e.params.loop&amp;&amp;e.loopFix();let o=e.getTranslate()+h*r.sensitivity;const d=e.isBeginning,u=e.isEnd;if(o&gt;=e.minTranslate()&amp;&amp;(o=e.minTranslate()),o&lt;=e.maxTranslate()&amp;&amp;(o=e.maxTranslate()),e.setTransition(0),e.setTranslate(o),e.updateProgress(),e.updateActiveIndex(),e.updateSlidesClasses(),(!d&amp;&amp;e.isBeginning||!u&amp;&amp;e.isEnd)&amp;&amp;e.updateSlidesClasses(),e.params.freeMode.sticky){clearTimeout(n),n=void 0,c.length&gt;=15&amp;&amp;c.shift();const s=c.length?c[c.length-1]:void 0,a=c[0];if(c.push(t),s&amp;&amp;(t.delta&gt;s.delta||t.direction!==s.direction))c.splice(0);else if(c.length&gt;=15&amp;&amp;t.time-a.time&lt;500&amp;&amp;a.delta-t.delta&gt;=1&amp;&amp;t.delta&lt;=6){const s=h&gt;0?.8:.2;l=t,c.splice(0),n=p((()=&gt;{e.slideToClosest(e.params.speed,!0,void 0,s)}),0)}n||(n=p((()=&gt;{l=t,c.splice(0),e.slideToClosest(e.params.speed,!0,void 0,.5)}),500))}if(i||a("scroll",s),e.params.autoplay&amp;&amp;e.params.autoplayDisableOnInteraction&amp;&amp;e.autoplay.stop(),o===e.minTranslate()||o===e.maxTranslate())return!0}}else{const s={time:u(),delta:Math.abs(h),direction:Math.sign(h),raw:t};c.length&gt;=2&amp;&amp;c.shift();const a=c.length?c[c.length-1]:void 0;if(c.push(s),a?(s.direction!==a.direction||s.delta&gt;a.delta||s.time&gt;a.time+150)&amp;&amp;f(s):f(s),function(t){const s=e.params.mousewheel;if(t.direction&lt;0){if(e.isEnd&amp;&amp;!e.params.loop&amp;&amp;s.releaseOnEdges)return!0}else if(e.isBeginning&amp;&amp;!e.params.loop&amp;&amp;s.releaseOnEdges)return!0;return!1}(s))return!0}return s.preventDefault?s.preventDefault():s.returnValue=!1,!1}function v(t){let s=e.$el;"container"!==e.params.mousewheel.eventsTarget&amp;&amp;(s=d(e.params.mousewheel.eventsTarget)),s[t]("mouseenter",h),s[t]("mouseleave",m),s[t]("wheel",g)}function w(){return e.params.cssMode?(e.wrapperEl.removeEventListener("wheel",g),!0):!e.mousewheel.enabled&amp;&amp;(v("on"),e.mousewheel.enabled=!0,!0)}function b(){return e.params.cssMode?(e.wrapperEl.addEventListener(event,g),!0):!!e.mousewheel.enabled&amp;&amp;(v("off"),e.mousewheel.enabled=!1,!0)}s("init",(()=&gt;{!e.params.mousewheel.enabled&amp;&amp;e.params.cssMode&amp;&amp;b(),e.params.mousewheel.enabled&amp;&amp;w()})),s("destroy",(()=&gt;{e.params.cssMode&amp;&amp;w(),e.mousewheel.enabled&amp;&amp;b()})),Object.assign(e.mousewheel,{enable:w,disable:b})},function({swiper:e,extendParams:t,on:s,emit:a}){function i(t){let s;return t&amp;&amp;(s=d(t),e.params.uniqueNavElements&amp;&amp;"string"==typeof t&amp;&amp;s.length&gt;1&amp;&amp;1===e.$el.find(t).length&amp;&amp;(s=e.$el.find(t))),s}function r(t,s){const a=e.params.navigation;t&amp;&amp;t.length&gt;0&amp;&amp;(t[s?"addClass":"removeClass"](a.disabledClass),t[0]&amp;&amp;"BUTTON"===t[0].tagName&amp;&amp;(t[0].disabled=s),e.params.watchOverflow&amp;&amp;e.enabled&amp;&amp;t[e.isLocked?"addClass":"removeClass"](a.lockClass))}function n(){if(e.params.loop)return;const{$nextEl:t,$prevEl:s}=e.navigation;r(s,e.isBeginning),r(t,e.isEnd)}function l(t){t.preventDefault(),e.isBeginning&amp;&amp;!e.params.loop||e.slidePrev()}function o(t){t.preventDefault(),e.isEnd&amp;&amp;!e.params.loop||e.slideNext()}function c(){const t=e.params.navigation;if(e.params.navigation=Y(e,e.originalParams.navigation,e.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!t.nextEl&amp;&amp;!t.prevEl)return;const s=i(t.nextEl),a=i(t.prevEl);s&amp;&amp;s.length&gt;0&amp;&amp;s.on("click",o),a&amp;&amp;a.length&gt;0&amp;&amp;a.on("click",l),Object.assign(e.navigation,{$nextEl:s,nextEl:s&amp;&amp;s[0],$prevEl:a,prevEl:a&amp;&amp;a[0]}),e.enabled||(s&amp;&amp;s.addClass(t.lockClass),a&amp;&amp;a.addClass(t.lockClass))}function p(){const{$nextEl:t,$prevEl:s}=e.navigation;t&amp;&amp;t.length&amp;&amp;(t.off("click",o),t.removeClass(e.params.navigation.disabledClass)),s&amp;&amp;s.length&amp;&amp;(s.off("click",l),s.removeClass(e.params.navigation.disabledClass))}t({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock"}}),e.navigation={nextEl:null,$nextEl:null,prevEl:null,$prevEl:null},s("init",(()=&gt;{c(),n()})),s("toEdge fromEdge lock unlock",(()=&gt;{n()})),s("destroy",(()=&gt;{p()})),s("enable disable",(()=&gt;{const{$nextEl:t,$prevEl:s}=e.navigation;t&amp;&amp;t[e.enabled?"removeClass":"addClass"](e.params.navigation.lockClass),s&amp;&amp;s[e.enabled?"removeClass":"addClass"](e.params.navigation.lockClass)})),s("click",((t,s)=&gt;{const{$nextEl:i,$prevEl:r}=e.navigation,n=s.target;if(e.params.navigation.hideOnClick&amp;&amp;!d(n).is(r)&amp;&amp;!d(n).is(i)){if(e.pagination&amp;&amp;e.params.pagination&amp;&amp;e.params.pagination.clickable&amp;&amp;(e.pagination.el===n||e.pagination.el.contains(n)))return;let t;i?t=i.hasClass(e.params.navigation.hiddenClass):r&amp;&amp;(t=r.hasClass(e.params.navigation.hiddenClass)),a(!0===t?"navigationShow":"navigationHide"),i&amp;&amp;i.toggleClass(e.params.navigation.hiddenClass),r&amp;&amp;r.toggleClass(e.params.navigation.hiddenClass)}})),Object.assign(e.navigation,{update:n,init:c,destroy:p})},function({swiper:e,extendParams:t,on:s,emit:a}){const i="swiper-pagination";let r;t({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=&gt;e,formatFractionTotal:e=&gt;e,bulletClass:`${i}-bullet`,bulletActiveClass:`${i}-bullet-active`,modifierClass:`${i}-`,currentClass:`${i}-current`,totalClass:`${i}-total`,hiddenClass:`${i}-hidden`,progressbarFillClass:`${i}-progressbar-fill`,progressbarOppositeClass:`${i}-progressbar-opposite`,clickableClass:`${i}-clickable`,lockClass:`${i}-lock`,horizontalClass:`${i}-horizontal`,verticalClass:`${i}-vertical`}}),e.pagination={el:null,$el:null,bullets:[]};let n=0;function l(){return!e.params.pagination.el||!e.pagination.el||!e.pagination.$el||0===e.pagination.$el.length}function o(t,s){const{bulletActiveClass:a}=e.params.pagination;t[s]().addClass(`${a}-${s}`)[s]().addClass(`${a}-${s}-${s}`)}function c(){const t=e.rtl,s=e.params.pagination;if(l())return;const i=e.virtual&amp;&amp;e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,c=e.pagination.$el;let p;const u=e.params.loop?Math.ceil((i-2*e.loopedSlides)/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?(p=Math.ceil((e.activeIndex-e.loopedSlides)/e.params.slidesPerGroup),p&gt;i-1-2*e.loopedSlides&amp;&amp;(p-=i-2*e.loopedSlides),p&gt;u-1&amp;&amp;(p-=u),p&lt;0&amp;&amp;"bullets"!==e.params.paginationType&amp;&amp;(p=u+p)):p=void 0!==e.snapIndex?e.snapIndex:e.activeIndex||0,"bullets"===s.type&amp;&amp;e.pagination.bullets&amp;&amp;e.pagination.bullets.length&gt;0){const a=e.pagination.bullets;let i,l,u;if(s.dynamicBullets&amp;&amp;(r=a.eq(0)[e.isHorizontal()?"outerWidth":"outerHeight"](!0),c.css(e.isHorizontal()?"width":"height",r*(s.dynamicMainBullets+4)+"px"),s.dynamicMainBullets&gt;1&amp;&amp;void 0!==e.previousIndex&amp;&amp;(n+=p-e.previousIndex,n&gt;s.dynamicMainBullets-1?n=s.dynamicMainBullets-1:n&lt;0&amp;&amp;(n=0)),i=p-n,l=i+(Math.min(a.length,s.dynamicMainBullets)-1),u=(l+i)/2),a.removeClass(["","-next","-next-next","-prev","-prev-prev","-main"].map((e=&gt;`${s.bulletActiveClass}${e}`)).join(" ")),c.length&gt;1)a.each((e=&gt;{const t=d(e),a=t.index();a===p&amp;&amp;t.addClass(s.bulletActiveClass),s.dynamicBullets&amp;&amp;(a&gt;=i&amp;&amp;a&lt;=l&amp;&amp;t.addClass(`${s.bulletActiveClass}-main`),a===i&amp;&amp;o(t,"prev"),a===l&amp;&amp;o(t,"next"))}));else{const t=a.eq(p),r=t.index();if(t.addClass(s.bulletActiveClass),s.dynamicBullets){const t=a.eq(i),n=a.eq(l);for(let e=i;e&lt;=l;e+=1)a.eq(e).addClass(`${s.bulletActiveClass}-main`);if(e.params.loop)if(r&gt;=a.length-s.dynamicMainBullets){for(let e=s.dynamicMainBullets;e&gt;=0;e-=1)a.eq(a.length-e).addClass(`${s.bulletActiveClass}-main`);a.eq(a.length-s.dynamicMainBullets-1).addClass(`${s.bulletActiveClass}-prev`)}else o(t,"prev"),o(n,"next");else o(t,"prev"),o(n,"next")}}if(s.dynamicBullets){const i=Math.min(a.length,s.dynamicMainBullets+4),n=(r*i-r)/2-u*r,l=t?"right":"left";a.css(e.isHorizontal()?l:"top",`${n}px`)}}if("fraction"===s.type&amp;&amp;(c.find(W(s.currentClass)).text(s.formatFractionCurrent(p+1)),c.find(W(s.totalClass)).text(s.formatFractionTotal(u))),"progressbar"===s.type){let t;t=s.progressbarOpposite?e.isHorizontal()?"vertical":"horizontal":e.isHorizontal()?"horizontal":"vertical";const a=(p+1)/u;let i=1,r=1;"horizontal"===t?i=a:r=a,c.find(W(s.progressbarFillClass)).transform(`translate3d(0,0,0) scaleX(${i}) scaleY(${r})`).transition(e.params.speed)}"custom"===s.type&amp;&amp;s.renderCustom?(c.html(s.renderCustom(e,p+1,u)),a("paginationRender",c[0])):a("paginationUpdate",c[0]),e.params.watchOverflow&amp;&amp;e.enabled&amp;&amp;c[e.isLocked?"addClass":"removeClass"](s.lockClass)}function p(){const t=e.params.pagination;if(l())return;const s=e.virtual&amp;&amp;e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,i=e.pagination.$el;let r="";if("bullets"===t.type){let a=e.params.loop?Math.ceil((s-2*e.loopedSlides)/e.params.slidesPerGroup):e.snapGrid.length;e.params.freeMode&amp;&amp;e.params.freeMode.enabled&amp;&amp;!e.params.loop&amp;&amp;a&gt;s&amp;&amp;(a=s);for(let s=0;s&lt;a;s+=1)t.renderBullet?r+=t.renderBullet.call(e,s,t.bulletClass):r+=`&lt;${t.bulletElement} class="${t.bulletClass}"&gt;&lt;/${t.bulletElement}&gt;`;i.html(r),e.pagination.bullets=i.find(W(t.bulletClass))}"fraction"===t.type&amp;&amp;(r=t.renderFraction?t.renderFraction.call(e,t.currentClass,t.totalClass):`&lt;span class="${t.currentClass}"&gt;&lt;/span&gt; / &lt;span class="${t.totalClass}"&gt;&lt;/span&gt;`,i.html(r)),"progressbar"===t.type&amp;&amp;(r=t.renderProgressbar?t.renderProgressbar.call(e,t.progressbarFillClass):`&lt;span class="${t.progressbarFillClass}"&gt;&lt;/span&gt;`,i.html(r)),"custom"!==t.type&amp;&amp;a("paginationRender",e.pagination.$el[0])}function u(){e.params.pagination=Y(e,e.originalParams.pagination,e.params.pagination,{el:"swiper-pagination"});const t=e.params.pagination;if(!t.el)return;let s=d(t.el);0!==s.length&amp;&amp;(e.params.uniqueNavElements&amp;&amp;"string"==typeof t.el&amp;&amp;s.length&gt;1&amp;&amp;(s=e.$el.find(t.el),s.length&gt;1&amp;&amp;(s=s.filter((t=&gt;d(t).parents(".swiper")[0]===e.el)))),"bullets"===t.type&amp;&amp;t.clickable&amp;&amp;s.addClass(t.clickableClass),s.addClass(t.modifierClass+t.type),s.addClass(t.modifierClass+e.params.direction),"bullets"===t.type&amp;&amp;t.dynamicBullets&amp;&amp;(s.addClass(`${t.modifierClass}${t.type}-dynamic`),n=0,t.dynamicMainBullets&lt;1&amp;&amp;(t.dynamicMainBullets=1)),"progressbar"===t.type&amp;&amp;t.progressbarOpposite&amp;&amp;s.addClass(t.progressbarOppositeClass),t.clickable&amp;&amp;s.on("click",W(t.bulletClass),(function(t){t.preventDefault();let s=d(this).index()*e.params.slidesPerGroup;e.params.loop&amp;&amp;(s+=e.loopedSlides),e.slideTo(s)})),Object.assign(e.pagination,{$el:s,el:s[0]}),e.enabled||s.addClass(t.lockClass))}function h(){const t=e.params.pagination;if(l())return;const s=e.pagination.$el;s.removeClass(t.hiddenClass),s.removeClass(t.modifierClass+t.type),s.removeClass(t.modifierClass+e.params.direction),e.pagination.bullets&amp;&amp;e.pagination.bullets.removeClass&amp;&amp;e.pagination.bullets.removeClass(t.bulletActiveClass),t.clickable&amp;&amp;s.off("click",W(t.bulletClass))}s("init",(()=&gt;{u(),p(),c()})),s("activeIndexChange",(()=&gt;{(e.params.loop||void 0===e.snapIndex)&amp;&amp;c()})),s("snapIndexChange",(()=&gt;{e.params.loop||c()})),s("slidesLengthChange",(()=&gt;{e.params.loop&amp;&amp;(p(),c())})),s("snapGridLengthChange",(()=&gt;{e.params.loop||(p(),c())})),s("destroy",(()=&gt;{h()})),s("enable disable",(()=&gt;{const{$el:t}=e.pagination;t&amp;&amp;t[e.enabled?"removeClass":"addClass"](e.params.pagination.lockClass)})),s("lock unlock",(()=&gt;{c()})),s("click",((t,s)=&gt;{const i=s.target,{$el:r}=e.pagination;if(e.params.pagination.el&amp;&amp;e.params.pagination.hideOnClick&amp;&amp;r.length&gt;0&amp;&amp;!d(i).hasClass(e.params.pagination.bulletClass)){if(e.navigation&amp;&amp;(e.navigation.nextEl&amp;&amp;i===e.navigation.nextEl||e.navigation.prevEl&amp;&amp;i===e.navigation.prevEl))return;const t=r.hasClass(e.params.pagination.hiddenClass);a(!0===t?"paginationShow":"paginationHide"),r.toggleClass(e.params.pagination.hiddenClass)}})),Object.assign(e.pagination,{render:p,update:c,init:u,destroy:h})},function({swiper:e,extendParams:t,on:s,emit:i}){const r=a();let n,l,o,c,u=!1,h=null,m=null;function f(){if(!e.params.scrollbar.el||!e.scrollbar.el)return;const{scrollbar:t,rtlTranslate:s,progress:a}=e,{$dragEl:i,$el:r}=t,n=e.params.scrollbar;let d=l,c=(o-l)*a;s?(c=-c,c&gt;0?(d=l-c,c=0):-c+l&gt;o&amp;&amp;(d=o+c)):c&lt;0?(d=l+c,c=0):c+l&gt;o&amp;&amp;(d=o-c),e.isHorizontal()?(i.transform(`translate3d(${c}px, 0, 0)`),i[0].style.width=`${d}px`):(i.transform(`translate3d(0px, ${c}px, 0)`),i[0].style.height=`${d}px`),n.hide&amp;&amp;(clearTimeout(h),r[0].style.opacity=1,h=setTimeout((()=&gt;{r[0].style.opacity=0,r.transition(400)}),1e3))}function g(){if(!e.params.scrollbar.el||!e.scrollbar.el)return;const{scrollbar:t}=e,{$dragEl:s,$el:a}=t;s[0].style.width="",s[0].style.height="",o=e.isHorizontal()?a[0].offsetWidth:a[0].offsetHeight,c=e.size/(e.virtualSize+e.params.slidesOffsetBefore-(e.params.centeredSlides?e.snapGrid[0]:0)),l="auto"===e.params.scrollbar.dragSize?o*c:parseInt(e.params.scrollbar.dragSize,10),e.isHorizontal()?s[0].style.width=`${l}px`:s[0].style.height=`${l}px`,a[0].style.display=c&gt;=1?"none":"",e.params.scrollbar.hide&amp;&amp;(a[0].style.opacity=0),e.params.watchOverflow&amp;&amp;e.enabled&amp;&amp;t.$el[e.isLocked?"addClass":"removeClass"](e.params.scrollbar.lockClass)}function v(t){return e.isHorizontal()?"touchstart"===t.type||"touchmove"===t.type?t.targetTouches[0].clientX:t.clientX:"touchstart"===t.type||"touchmove"===t.type?t.targetTouches[0].clientY:t.clientY}function w(t){const{scrollbar:s,rtlTranslate:a}=e,{$el:i}=s;let r;r=(v(t)-i.offset()[e.isHorizontal()?"left":"top"]-(null!==n?n:l/2))/(o-l),r=Math.max(Math.min(r,1),0),a&amp;&amp;(r=1-r);const d=e.minTranslate()+(e.maxTranslate()-e.minTranslate())*r;e.updateProgress(d),e.setTranslate(d),e.updateActiveIndex(),e.updateSlidesClasses()}function b(t){const s=e.params.scrollbar,{scrollbar:a,$wrapperEl:r}=e,{$el:l,$dragEl:o}=a;u=!0,n=t.target===o[0]||t.target===o?v(t)-t.target.getBoundingClientRect()[e.isHorizontal()?"left":"top"]:null,t.preventDefault(),t.stopPropagation(),r.transition(100),o.transition(100),w(t),clearTimeout(m),l.transition(0),s.hide&amp;&amp;l.css("opacity",1),e.params.cssMode&amp;&amp;e.$wrapperEl.css("scroll-snap-type","none"),i("scrollbarDragStart",t)}function x(t){const{scrollbar:s,$wrapperEl:a}=e,{$el:r,$dragEl:n}=s;u&amp;&amp;(t.preventDefault?t.preventDefault():t.returnValue=!1,w(t),a.transition(0),r.transition(0),n.transition(0),i("scrollbarDragMove",t))}function y(t){const s=e.params.scrollbar,{scrollbar:a,$wrapperEl:r}=e,{$el:n}=a;u&amp;&amp;(u=!1,e.params.cssMode&amp;&amp;(e.$wrapperEl.css("scroll-snap-type",""),r.transition("")),s.hide&amp;&amp;(clearTimeout(m),m=p((()=&gt;{n.css("opacity",0),n.transition(400)}),1e3)),i("scrollbarDragEnd",t),s.snapOnRelease&amp;&amp;e.slideToClosest())}function E(t){const{scrollbar:s,touchEventsTouch:a,touchEventsDesktop:i,params:n,support:l}=e,o=s.$el[0],d=!(!l.passiveListener||!n.passiveListeners)&amp;&amp;{passive:!1,capture:!1},c=!(!l.passiveListener||!n.passiveListeners)&amp;&amp;{passive:!0,capture:!1};if(!o)return;const p="on"===t?"addEventListener":"removeEventListener";l.touch?(o[p](a.start,b,d),o[p](a.move,x,d),o[p](a.end,y,c)):(o[p](i.start,b,d),r[p](i.move,x,d),r[p](i.end,y,c))}function T(){const{scrollbar:t,$el:s}=e;e.params.scrollbar=Y(e,e.originalParams.scrollbar,e.params.scrollbar,{el:"swiper-scrollbar"});const a=e.params.scrollbar;if(!a.el)return;let i=d(a.el);e.params.uniqueNavElements&amp;&amp;"string"==typeof a.el&amp;&amp;i.length&gt;1&amp;&amp;1===s.find(a.el).length&amp;&amp;(i=s.find(a.el));let r=i.find(`.${e.params.scrollbar.dragClass}`);0===r.length&amp;&amp;(r=d(`&lt;div class="${e.params.scrollbar.dragClass}"&gt;&lt;/div&gt;`),i.append(r)),Object.assign(t,{$el:i,el:i[0],$dragEl:r,dragEl:r[0]}),a.draggable&amp;&amp;e.params.scrollbar.el&amp;&amp;E("on"),i&amp;&amp;i[e.enabled?"removeClass":"addClass"](e.params.scrollbar.lockClass)}function C(){e.params.scrollbar.el&amp;&amp;E("off")}t({scrollbar:{el:null,dragSize:"auto",hide:!1,draggable:!1,snapOnRelease:!0,lockClass:"swiper-scrollbar-lock",dragClass:"swiper-scrollbar-drag"}}),e.scrollbar={el:null,dragEl:null,$el:null,$dragEl:null},s("init",(()=&gt;{T(),g(),f()})),s("update resize observerUpdate lock unlock",(()=&gt;{g()})),s("setTranslate",(()=&gt;{f()})),s("setTransition",((t,s)=&gt;{!function(t){e.params.scrollbar.el&amp;&amp;e.scrollbar.el&amp;&amp;e.scrollbar.$dragEl.transition(t)}(s)})),s("enable disable",(()=&gt;{const{$el:t}=e.scrollbar;t&amp;&amp;t[e.enabled?"removeClass":"addClass"](e.params.scrollbar.lockClass)})),s("destroy",(()=&gt;{C()})),Object.assign(e.scrollbar,{updateSize:g,setTranslate:f,init:T,destroy:C})},function({swiper:e,extendParams:t,on:s}){t({parallax:{enabled:!1}});const a=(t,s)=&gt;{const{rtl:a}=e,i=d(t),r=a?-1:1,n=i.attr("data-swiper-parallax")||"0";let l=i.attr("data-swiper-parallax-x"),o=i.attr("data-swiper-parallax-y");const c=i.attr("data-swiper-parallax-scale"),p=i.attr("data-swiper-parallax-opacity");if(l||o?(l=l||"0",o=o||"0"):e.isHorizontal()?(l=n,o="0"):(o=n,l="0"),l=l.indexOf("%")&gt;=0?parseInt(l,10)*s*r+"%":l*s*r+"px",o=o.indexOf("%")&gt;=0?parseInt(o,10)*s+"%":o*s+"px",null!=p){const e=p-(p-1)*(1-Math.abs(s));i[0].style.opacity=e}if(null==c)i.transform(`translate3d(${l}, ${o}, 0px)`);else{const e=c-(c-1)*(1-Math.abs(s));i.transform(`translate3d(${l}, ${o}, 0px) scale(${e})`)}},i=()=&gt;{const{$el:t,slides:s,progress:i,snapGrid:r}=e;t.children("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each((e=&gt;{a(e,i)})),s.each(((t,s)=&gt;{let n=t.progress;e.params.slidesPerGroup&gt;1&amp;&amp;"auto"!==e.params.slidesPerView&amp;&amp;(n+=Math.ceil(s/2)-i*(r.length-1)),n=Math.min(Math.max(n,-1),1),d(t).find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each((e=&gt;{a(e,n)}))}))};s("beforeInit",(()=&gt;{e.params.parallax.enabled&amp;&amp;(e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0)})),s("init",(()=&gt;{e.params.parallax.enabled&amp;&amp;i()})),s("setTranslate",(()=&gt;{e.params.parallax.enabled&amp;&amp;i()})),s("setTransition",((t,s)=&gt;{e.params.parallax.enabled&amp;&amp;((t=e.params.speed)=&gt;{const{$el:s}=e;s.find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each((e=&gt;{const s=d(e);let a=parseInt(s.attr("data-swiper-parallax-duration"),10)||t;0===t&amp;&amp;(a=0),s.transition(a)}))})(s)}))},function({swiper:e,extendParams:t,on:s,emit:a}){const i=r();t({zoom:{enabled:!1,maxRatio:3,minRatio:1,toggle:!0,containerClass:"swiper-zoom-container",zoomedSlideClass:"swiper-slide-zoomed"}}),e.zoom={enabled:!1};let n,l,o,c=1,p=!1;const u={$slideEl:void 0,slideWidth:void 0,slideHeight:void 0,$imageEl:void 0,$imageWrapEl:void 0,maxRatio:3},m={isTouched:void 0,isMoved:void 0,currentX:void 0,currentY:void 0,minX:void 0,minY:void 0,maxX:void 0,maxY:void 0,width:void 0,height:void 0,startX:void 0,startY:void 0,touchesStart:{},touchesCurrent:{}},f={x:void 0,y:void 0,prevPositionX:void 0,prevPositionY:void 0,prevTime:void 0};let g=1;function v(e){if(e.targetTouches.length&lt;2)return 1;const t=e.targetTouches[0].pageX,s=e.targetTouches[0].pageY,a=e.targetTouches[1].pageX,i=e.targetTouches[1].pageY;return Math.sqrt((a-t)**2+(i-s)**2)}function w(t){const s=e.support,a=e.params.zoom;if(l=!1,o=!1,!s.gestures){if("touchstart"!==t.type||"touchstart"===t.type&amp;&amp;t.targetTouches.length&lt;2)return;l=!0,u.scaleStart=v(t)}u.$slideEl&amp;&amp;u.$slideEl.length||(u.$slideEl=d(t.target).closest(`.${e.params.slideClass}`),0===u.$slideEl.length&amp;&amp;(u.$slideEl=e.slides.eq(e.activeIndex)),u.$imageEl=u.$slideEl.find(`.${a.containerClass}`).eq(0).find("img, svg, canvas, picture, .swiper-zoom-target"),u.$imageWrapEl=u.$imageEl.parent(`.${a.containerClass}`),u.maxRatio=u.$imageWrapEl.attr("data-swiper-zoom")||a.maxRatio,0!==u.$imageWrapEl.length)?(u.$imageEl&amp;&amp;u.$imageEl.transition(0),p=!0):u.$imageEl=void 0}function b(t){const s=e.support,a=e.params.zoom,i=e.zoom;if(!s.gestures){if("touchmove"!==t.type||"touchmove"===t.type&amp;&amp;t.targetTouches.length&lt;2)return;o=!0,u.scaleMove=v(t)}u.$imageEl&amp;&amp;0!==u.$imageEl.length?(s.gestures?i.scale=t.scale*c:i.scale=u.scaleMove/u.scaleStart*c,i.scale&gt;u.maxRatio&amp;&amp;(i.scale=u.maxRatio-1+(i.scale-u.maxRatio+1)**.5),i.scale&lt;a.minRatio&amp;&amp;(i.scale=a.minRatio+1-(a.minRatio-i.scale+1)**.5),u.$imageEl.transform(`translate3d(0,0,0) scale(${i.scale})`)):"gesturechange"===t.type&amp;&amp;w(t)}function x(t){const s=e.device,a=e.support,i=e.params.zoom,r=e.zoom;if(!a.gestures){if(!l||!o)return;if("touchend"!==t.type||"touchend"===t.type&amp;&amp;t.changedTouches.length&lt;2&amp;&amp;!s.android)return;l=!1,o=!1}u.$imageEl&amp;&amp;0!==u.$imageEl.length&amp;&amp;(r.scale=Math.max(Math.min(r.scale,u.maxRatio),i.minRatio),u.$imageEl.transition(e.params.speed).transform(`translate3d(0,0,0) scale(${r.scale})`),c=r.scale,p=!1,1===r.scale&amp;&amp;(u.$slideEl=void 0))}function y(t){const s=e.zoom;if(!u.$imageEl||0===u.$imageEl.length)return;if(e.allowClick=!1,!m.isTouched||!u.$slideEl)return;m.isMoved||(m.width=u.$imageEl[0].offsetWidth,m.height=u.$imageEl[0].offsetHeight,m.startX=h(u.$imageWrapEl[0],"x")||0,m.startY=h(u.$imageWrapEl[0],"y")||0,u.slideWidth=u.$slideEl[0].offsetWidth,u.slideHeight=u.$slideEl[0].offsetHeight,u.$imageWrapEl.transition(0));const a=m.width*s.scale,i=m.height*s.scale;if(!(a&lt;u.slideWidth&amp;&amp;i&lt;u.slideHeight)){if(m.minX=Math.min(u.slideWidth/2-a/2,0),m.maxX=-m.minX,m.minY=Math.min(u.slideHeight/2-i/2,0),m.maxY=-m.minY,m.touchesCurrent.x="touchmove"===t.type?t.targetTouches[0].pageX:t.pageX,m.touchesCurrent.y="touchmove"===t.type?t.targetTouches[0].pageY:t.pageY,!m.isMoved&amp;&amp;!p){if(e.isHorizontal()&amp;&amp;(Math.floor(m.minX)===Math.floor(m.startX)&amp;&amp;m.touchesCurrent.x&lt;m.touchesStart.x||Math.floor(m.maxX)===Math.floor(m.startX)&amp;&amp;m.touchesCurrent.x&gt;m.touchesStart.x))return void(m.isTouched=!1);if(!e.isHorizontal()&amp;&amp;(Math.floor(m.minY)===Math.floor(m.startY)&amp;&amp;m.touchesCurrent.y&lt;m.touchesStart.y||Math.floor(m.maxY)===Math.floor(m.startY)&amp;&amp;m.touchesCurrent.y&gt;m.touchesStart.y))return void(m.isTouched=!1)}t.cancelable&amp;&amp;t.preventDefault(),t.stopPropagation(),m.isMoved=!0,m.currentX=m.touchesCurrent.x-m.touchesStart.x+m.startX,m.currentY=m.touchesCurrent.y-m.touchesStart.y+m.startY,m.currentX&lt;m.minX&amp;&amp;(m.currentX=m.minX+1-(m.minX-m.currentX+1)**.8),m.currentX&gt;m.maxX&amp;&amp;(m.currentX=m.maxX-1+(m.currentX-m.maxX+1)**.8),m.currentY&lt;m.minY&amp;&amp;(m.currentY=m.minY+1-(m.minY-m.currentY+1)**.8),m.currentY&gt;m.maxY&amp;&amp;(m.currentY=m.maxY-1+(m.currentY-m.maxY+1)**.8),f.prevPositionX||(f.prevPositionX=m.touchesCurrent.x),f.prevPositionY||(f.prevPositionY=m.touchesCurrent.y),f.prevTime||(f.prevTime=Date.now()),f.x=(m.touchesCurrent.x-f.prevPositionX)/(Date.now()-f.prevTime)/2,f.y=(m.touchesCurrent.y-f.prevPositionY)/(Date.now()-f.prevTime)/2,Math.abs(m.touchesCurrent.x-f.prevPositionX)&lt;2&amp;&amp;(f.x=0),Math.abs(m.touchesCurrent.y-f.prevPositionY)&lt;2&amp;&amp;(f.y=0),f.prevPositionX=m.touchesCurrent.x,f.prevPositionY=m.touchesCurrent.y,f.prevTime=Date.now(),u.$imageWrapEl.transform(`translate3d(${m.currentX}px, ${m.currentY}px,0)`)}}function E(){const t=e.zoom;u.$slideEl&amp;&amp;e.previousIndex!==e.activeIndex&amp;&amp;(u.$imageEl&amp;&amp;u.$imageEl.transform("translate3d(0,0,0) scale(1)"),u.$imageWrapEl&amp;&amp;u.$imageWrapEl.transform("translate3d(0,0,0)"),t.scale=1,c=1,u.$slideEl=void 0,u.$imageEl=void 0,u.$imageWrapEl=void 0)}function T(t){const s=e.zoom,a=e.params.zoom;if(u.$slideEl||(t&amp;&amp;t.target&amp;&amp;(u.$slideEl=d(t.target).closest(`.${e.params.slideClass}`)),u.$slideEl||(e.params.virtual&amp;&amp;e.params.virtual.enabled&amp;&amp;e.virtual?u.$slideEl=e.$wrapperEl.children(`.${e.params.slideActiveClass}`):u.$slideEl=e.slides.eq(e.activeIndex)),u.$imageEl=u.$slideEl.find(`.${a.containerClass}`).eq(0).find("img, svg, canvas, picture, .swiper-zoom-target"),u.$imageWrapEl=u.$imageEl.parent(`.${a.containerClass}`)),!u.$imageEl||0===u.$imageEl.length||!u.$imageWrapEl||0===u.$imageWrapEl.length)return;let r,n,l,o,p,h,f,g,v,w,b,x,y,E,T,C,$,S;e.params.cssMode&amp;&amp;(e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.touchAction="none"),u.$slideEl.addClass(`${a.zoomedSlideClass}`),void 0===m.touchesStart.x&amp;&amp;t?(r="touchend"===t.type?t.changedTouches[0].pageX:t.pageX,n="touchend"===t.type?t.changedTouches[0].pageY:t.pageY):(r=m.touchesStart.x,n=m.touchesStart.y),s.scale=u.$imageWrapEl.attr("data-swiper-zoom")||a.maxRatio,c=u.$imageWrapEl.attr("data-swiper-zoom")||a.maxRatio,t?($=u.$slideEl[0].offsetWidth,S=u.$slideEl[0].offsetHeight,l=u.$slideEl.offset().left+i.scrollX,o=u.$slideEl.offset().top+i.scrollY,p=l+$/2-r,h=o+S/2-n,v=u.$imageEl[0].offsetWidth,w=u.$imageEl[0].offsetHeight,b=v*s.scale,x=w*s.scale,y=Math.min($/2-b/2,0),E=Math.min(S/2-x/2,0),T=-y,C=-E,f=p*s.scale,g=h*s.scale,f&lt;y&amp;&amp;(f=y),f&gt;T&amp;&amp;(f=T),g&lt;E&amp;&amp;(g=E),g&gt;C&amp;&amp;(g=C)):(f=0,g=0),u.$imageWrapEl.transition(300).transform(`translate3d(${f}px, ${g}px,0)`),u.$imageEl.transition(300).transform(`translate3d(0,0,0) scale(${s.scale})`)}function C(){const t=e.zoom,s=e.params.zoom;u.$slideEl||(e.params.virtual&amp;&amp;e.params.virtual.enabled&amp;&amp;e.virtual?u.$slideEl=e.$wrapperEl.children(`.${e.params.slideActiveClass}`):u.$slideEl=e.slides.eq(e.activeIndex),u.$imageEl=u.$slideEl.find(`.${s.containerClass}`).eq(0).find("img, svg, canvas, picture, .swiper-zoom-target"),u.$imageWrapEl=u.$imageEl.parent(`.${s.containerClass}`)),u.$imageEl&amp;&amp;0!==u.$imageEl.length&amp;&amp;u.$imageWrapEl&amp;&amp;0!==u.$imageWrapEl.length&amp;&amp;(e.params.cssMode&amp;&amp;(e.wrapperEl.style.overflow="",e.wrapperEl.style.touchAction=""),t.scale=1,c=1,u.$imageWrapEl.transition(300).transform("translate3d(0,0,0)"),u.$imageEl.transition(300).transform("translate3d(0,0,0) scale(1)"),u.$slideEl.removeClass(`${s.zoomedSlideClass}`),u.$slideEl=void 0)}function $(t){const s=e.zoom;s.scale&amp;&amp;1!==s.scale?C():T(t)}function S(){const t=e.support;return{passiveListener:!("touchstart"!==e.touchEvents.start||!t.passiveListener||!e.params.passiveListeners)&amp;&amp;{passive:!0,capture:!1},activeListenerWithCapture:!t.passiveListener||{passive:!1,capture:!0}}}function M(){return`.${e.params.slideClass}`}function P(t){const{passiveListener:s}=S(),a=M();e.$wrapperEl[t]("gesturestart",a,w,s),e.$wrapperEl[t]("gesturechange",a,b,s),e.$wrapperEl[t]("gestureend",a,x,s)}function k(){n||(n=!0,P("on"))}function z(){n&amp;&amp;(n=!1,P("off"))}function O(){const t=e.zoom;if(t.enabled)return;t.enabled=!0;const s=e.support,{passiveListener:a,activeListenerWithCapture:i}=S(),r=M();s.gestures?(e.$wrapperEl.on(e.touchEvents.start,k,a),e.$wrapperEl.on(e.touchEvents.end,z,a)):"touchstart"===e.touchEvents.start&amp;&amp;(e.$wrapperEl.on(e.touchEvents.start,r,w,a),e.$wrapperEl.on(e.touchEvents.move,r,b,i),e.$wrapperEl.on(e.touchEvents.end,r,x,a),e.touchEvents.cancel&amp;&amp;e.$wrapperEl.on(e.touchEvents.cancel,r,x,a)),e.$wrapperEl.on(e.touchEvents.move,`.${e.params.zoom.containerClass}`,y,i)}function I(){const t=e.zoom;if(!t.enabled)return;const s=e.support;t.enabled=!1;const{passiveListener:a,activeListenerWithCapture:i}=S(),r=M();s.gestures?(e.$wrapperEl.off(e.touchEvents.start,k,a),e.$wrapperEl.off(e.touchEvents.end,z,a)):"touchstart"===e.touchEvents.start&amp;&amp;(e.$wrapperEl.off(e.touchEvents.start,r,w,a),e.$wrapperEl.off(e.touchEvents.move,r,b,i),e.$wrapperEl.off(e.touchEvents.end,r,x,a),e.touchEvents.cancel&amp;&amp;e.$wrapperEl.off(e.touchEvents.cancel,r,x,a)),e.$wrapperEl.off(e.touchEvents.move,`.${e.params.zoom.containerClass}`,y,i)}Object.defineProperty(e.zoom,"scale",{get:()=&gt;g,set(e){if(g!==e){const t=u.$imageEl?u.$imageEl[0]:void 0,s=u.$slideEl?u.$slideEl[0]:void 0;a("zoomChange",e,t,s)}g=e}}),s("init",(()=&gt;{e.params.zoom.enabled&amp;&amp;O()})),s("destroy",(()=&gt;{I()})),s("touchStart",((t,s)=&gt;{e.zoom.enabled&amp;&amp;function(t){const s=e.device;u.$imageEl&amp;&amp;0!==u.$imageEl.length&amp;&amp;(m.isTouched||(s.android&amp;&amp;t.cancelable&amp;&amp;t.preventDefault(),m.isTouched=!0,m.touchesStart.x="touchstart"===t.type?t.targetTouches[0].pageX:t.pageX,m.touchesStart.y="touchstart"===t.type?t.targetTouches[0].pageY:t.pageY))}(s)})),s("touchEnd",((t,s)=&gt;{e.zoom.enabled&amp;&amp;function(){const t=e.zoom;if(!u.$imageEl||0===u.$imageEl.length)return;if(!m.isTouched||!m.isMoved)return m.isTouched=!1,void(m.isMoved=!1);m.isTouched=!1,m.isMoved=!1;let s=300,a=300;const i=f.x*s,r=m.currentX+i,n=f.y*a,l=m.currentY+n;0!==f.x&amp;&amp;(s=Math.abs((r-m.currentX)/f.x)),0!==f.y&amp;&amp;(a=Math.abs((l-m.currentY)/f.y));const o=Math.max(s,a);m.currentX=r,m.currentY=l;const d=m.width*t.scale,c=m.height*t.scale;m.minX=Math.min(u.slideWidth/2-d/2,0),m.maxX=-m.minX,m.minY=Math.min(u.slideHeight/2-c/2,0),m.maxY=-m.minY,m.currentX=Math.max(Math.min(m.currentX,m.maxX),m.minX),m.currentY=Math.max(Math.min(m.currentY,m.maxY),m.minY),u.$imageWrapEl.transition(o).transform(`translate3d(${m.currentX}px, ${m.currentY}px,0)`)}()})),s("doubleTap",((t,s)=&gt;{!e.animating&amp;&amp;e.params.zoom.enabled&amp;&amp;e.zoom.enabled&amp;&amp;e.params.zoom.toggle&amp;&amp;$(s)})),s("transitionEnd",(()=&gt;{e.zoom.enabled&amp;&amp;e.params.zoom.enabled&amp;&amp;E()})),s("slideChange",(()=&gt;{e.zoom.enabled&amp;&amp;e.params.zoom.enabled&amp;&amp;e.params.cssMode&amp;&amp;E()})),Object.assign(e.zoom,{enable:O,disable:I,in:T,out:C,toggle:$})},function({swiper:e,extendParams:t,on:s,emit:a}){t({lazy:{checkInView:!1,enabled:!1,loadPrevNext:!1,loadPrevNextAmount:1,loadOnTransitionStart:!1,scrollingElement:"",elementClass:"swiper-lazy",loadingClass:"swiper-lazy-loading",loadedClass:"swiper-lazy-loaded",preloaderClass:"swiper-lazy-preloader"}}),e.lazy={};let i=!1,n=!1;function l(t,s=!0){const i=e.params.lazy;if(void 0===t)return;if(0===e.slides.length)return;const r=e.virtual&amp;&amp;e.params.virtual.enabled?e.$wrapperEl.children(`.${e.params.slideClass}[data-swiper-slide-index="${t}"]`):e.slides.eq(t),n=r.find(`.${i.elementClass}:not(.${i.loadedClass}):not(.${i.loadingClass})`);!r.hasClass(i.elementClass)||r.hasClass(i.loadedClass)||r.hasClass(i.loadingClass)||n.push(r[0]),0!==n.length&amp;&amp;n.each((t=&gt;{const n=d(t);n.addClass(i.loadingClass);const o=n.attr("data-background"),c=n.attr("data-src"),p=n.attr("data-srcset"),u=n.attr("data-sizes"),h=n.parent("picture");e.loadImage(n[0],c||o,p,u,!1,(()=&gt;{if(null!=e&amp;&amp;e&amp;&amp;(!e||e.params)&amp;&amp;!e.destroyed){if(o?(n.css("background-image",`url("${o}")`),n.removeAttr("data-background")):(p&amp;&amp;(n.attr("srcset",p),n.removeAttr("data-srcset")),u&amp;&amp;(n.attr("sizes",u),n.removeAttr("data-sizes")),h.length&amp;&amp;h.children("source").each((e=&gt;{const t=d(e);t.attr("data-srcset")&amp;&amp;(t.attr("srcset",t.attr("data-srcset")),t.removeAttr("data-srcset"))})),c&amp;&amp;(n.attr("src",c),n.removeAttr("data-src"))),n.addClass(i.loadedClass).removeClass(i.loadingClass),r.find(`.${i.preloaderClass}`).remove(),e.params.loop&amp;&amp;s){const t=r.attr("data-swiper-slide-index");if(r.hasClass(e.params.slideDuplicateClass)){l(e.$wrapperEl.children(`[data-swiper-slide-index="${t}"]:not(.${e.params.slideDuplicateClass})`).index(),!1)}else{l(e.$wrapperEl.children(`.${e.params.slideDuplicateClass}[data-swiper-slide-index="${t}"]`).index(),!1)}}a("lazyImageReady",r[0],n[0]),e.params.autoHeight&amp;&amp;e.updateAutoHeight()}})),a("lazyImageLoad",r[0],n[0])}))}function o(){const{$wrapperEl:t,params:s,slides:a,activeIndex:i}=e,r=e.virtual&amp;&amp;s.virtual.enabled,o=s.lazy;let c=s.slidesPerView;function p(e){if(r){if(t.children(`.${s.slideClass}[data-swiper-slide-index="${e}"]`).length)return!0}else if(a[e])return!0;return!1}function u(e){return r?d(e).attr("data-swiper-slide-index"):d(e).index()}if("auto"===c&amp;&amp;(c=0),n||(n=!0),e.params.watchSlidesProgress)t.children(`.${s.slideVisibleClass}`).each((e=&gt;{l(r?d(e).attr("data-swiper-slide-index"):d(e).index())}));else if(c&gt;1)for(let e=i;e&lt;i+c;e+=1)p(e)&amp;&amp;l(e);else l(i);if(o.loadPrevNext)if(c&gt;1||o.loadPrevNextAmount&amp;&amp;o.loadPrevNextAmount&gt;1){const e=o.loadPrevNextAmount,t=c,s=Math.min(i+t+Math.max(e,t),a.length),r=Math.max(i-Math.max(t,e),0);for(let e=i+c;e&lt;s;e+=1)p(e)&amp;&amp;l(e);for(let e=r;e&lt;i;e+=1)p(e)&amp;&amp;l(e)}else{const e=t.children(`.${s.slideNextClass}`);e.length&gt;0&amp;&amp;l(u(e));const a=t.children(`.${s.slidePrevClass}`);a.length&gt;0&amp;&amp;l(u(a))}}function c(){const t=r();if(!e||e.destroyed)return;const s=e.params.lazy.scrollingElement?d(e.params.lazy.scrollingElement):d(t),a=s[0]===t,n=a?t.innerWidth:s[0].offsetWidth,l=a?t.innerHeight:s[0].offsetHeight,p=e.$el.offset(),{rtlTranslate:u}=e;let h=!1;u&amp;&amp;(p.left-=e.$el[0].scrollLeft);const m=[[p.left,p.top],[p.left+e.width,p.top],[p.left,p.top+e.height],[p.left+e.width,p.top+e.height]];for(let e=0;e&lt;m.length;e+=1){const t=m[e];if(t[0]&gt;=0&amp;&amp;t[0]&lt;=n&amp;&amp;t[1]&gt;=0&amp;&amp;t[1]&lt;=l){if(0===t[0]&amp;&amp;0===t[1])continue;h=!0}}const f=!("touchstart"!==e.touchEvents.start||!e.support.passiveListener||!e.params.passiveListeners)&amp;&amp;{passive:!0,capture:!1};h?(o(),s.off("scroll",c,f)):i||(i=!0,s.on("scroll",c,f))}s("beforeInit",(()=&gt;{e.params.lazy.enabled&amp;&amp;e.params.preloadImages&amp;&amp;(e.params.preloadImages=!1)})),s("init",(()=&gt;{e.params.lazy.enabled&amp;&amp;(e.params.lazy.checkInView?c():o())})),s("scroll",(()=&gt;{e.params.freeMode&amp;&amp;e.params.freeMode.enabled&amp;&amp;!e.params.freeMode.sticky&amp;&amp;o()})),s("scrollbarDragMove resize _freeModeNoMomentumRelease",(()=&gt;{e.params.lazy.enabled&amp;&amp;(e.params.lazy.checkInView?c():o())})),s("transitionStart",(()=&gt;{e.params.lazy.enabled&amp;&amp;(e.params.lazy.loadOnTransitionStart||!e.params.lazy.loadOnTransitionStart&amp;&amp;!n)&amp;&amp;(e.params.lazy.checkInView?c():o())})),s("transitionEnd",(()=&gt;{e.params.lazy.enabled&amp;&amp;!e.params.lazy.loadOnTransitionStart&amp;&amp;(e.params.lazy.checkInView?c():o())})),s("slideChange",(()=&gt;{const{lazy:t,cssMode:s,watchSlidesProgress:a,touchReleaseOnEdges:i,resistanceRatio:r}=e.params;t.enabled&amp;&amp;(s||a&amp;&amp;(i||0===r))&amp;&amp;o()})),Object.assign(e.lazy,{load:o,loadInSlide:l})},function({swiper:e,extendParams:t,on:s}){function a(e,t){const s=function(){let e,t,s;return(a,i)=&gt;{for(t=-1,e=a.length;e-t&gt;1;)s=e+t&gt;&gt;1,a[s]&lt;=i?t=s:e=s;return e}}();let a,i;return this.x=e,this.y=t,this.lastIndex=e.length-1,this.interpolate=function(e){return e?(i=s(this.x,e),a=i-1,(e-this.x[a])*(this.y[i]-this.y[a])/(this.x[i]-this.x[a])+this.y[a]):0},this}function i(){e.controller.control&amp;&amp;e.controller.spline&amp;&amp;(e.controller.spline=void 0,delete e.controller.spline)}t({controller:{control:void 0,inverse:!1,by:"slide"}}),e.controller={control:void 0},s("beforeInit",(()=&gt;{e.controller.control=e.params.controller.control})),s("update",(()=&gt;{i()})),s("resize",(()=&gt;{i()})),s("observerUpdate",(()=&gt;{i()})),s("setTranslate",((t,s,a)=&gt;{e.controller.control&amp;&amp;e.controller.setTranslate(s,a)})),s("setTransition",((t,s,a)=&gt;{e.controller.control&amp;&amp;e.controller.setTransition(s,a)})),Object.assign(e.controller,{setTranslate:function(t,s){const i=e.controller.control;let r,n;const l=e.constructor;function o(t){const s=e.rtlTranslate?-e.translate:e.translate;"slide"===e.params.controller.by&amp;&amp;(!function(t){e.controller.spline||(e.controller.spline=e.params.loop?new a(e.slidesGrid,t.slidesGrid):new a(e.snapGrid,t.snapGrid))}(t),n=-e.controller.spline.interpolate(-s)),n&amp;&amp;"container"!==e.params.controller.by||(r=(t.maxTranslate()-t.minTranslate())/(e.maxTranslate()-e.minTranslate()),n=(s-e.minTranslate())*r+t.minTranslate()),e.params.controller.inverse&amp;&amp;(n=t.maxTranslate()-n),t.updateProgress(n),t.setTranslate(n,e),t.updateActiveIndex(),t.updateSlidesClasses()}if(Array.isArray(i))for(let e=0;e&lt;i.length;e+=1)i[e]!==s&amp;&amp;i[e]instanceof l&amp;&amp;o(i[e]);else i instanceof l&amp;&amp;s!==i&amp;&amp;o(i)},setTransition:function(t,s){const a=e.constructor,i=e.controller.control;let r;function n(s){s.setTransition(t,e),0!==t&amp;&amp;(s.transitionStart(),s.params.autoHeight&amp;&amp;p((()=&gt;{s.updateAutoHeight()})),s.$wrapperEl.transitionEnd((()=&gt;{i&amp;&amp;(s.params.loop&amp;&amp;"slide"===e.params.controller.by&amp;&amp;s.loopFix(),s.transitionEnd())})))}if(Array.isArray(i))for(r=0;r&lt;i.length;r+=1)i[r]!==s&amp;&amp;i[r]instanceof a&amp;&amp;n(i[r]);else i instanceof a&amp;&amp;s!==i&amp;&amp;n(i)}})},function({swiper:e,extendParams:t,on:s}){t({a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}",slideLabelMessage:"{{index}} / {{slidesLength}}",containerMessage:null,containerRoleDescriptionMessage:null,itemRoleDescriptionMessage:null,slideRole:"group"}});let a=null;function i(e){const t=a;0!==t.length&amp;&amp;(t.html(""),t.html(e))}function r(e){e.attr("tabIndex","0")}function n(e){e.attr("tabIndex","-1")}function l(e,t){e.attr("role",t)}function o(e,t){e.attr("aria-roledescription",t)}function c(e,t){e.attr("aria-label",t)}function p(e){e.attr("aria-disabled",!0)}function u(e){e.attr("aria-disabled",!1)}function h(t){if(13!==t.keyCode&amp;&amp;32!==t.keyCode)return;const s=e.params.a11y,a=d(t.target);e.navigation&amp;&amp;e.navigation.$nextEl&amp;&amp;a.is(e.navigation.$nextEl)&amp;&amp;(e.isEnd&amp;&amp;!e.params.loop||e.slideNext(),e.isEnd?i(s.lastSlideMessage):i(s.nextSlideMessage)),e.navigation&amp;&amp;e.navigation.$prevEl&amp;&amp;a.is(e.navigation.$prevEl)&amp;&amp;(e.isBeginning&amp;&amp;!e.params.loop||e.slidePrev(),e.isBeginning?i(s.firstSlideMessage):i(s.prevSlideMessage)),e.pagination&amp;&amp;a.is(W(e.params.pagination.bulletClass))&amp;&amp;a[0].click()}function m(){if(e.params.loop||!e.navigation)return;const{$nextEl:t,$prevEl:s}=e.navigation;s&amp;&amp;s.length&gt;0&amp;&amp;(e.isBeginning?(p(s),n(s)):(u(s),r(s))),t&amp;&amp;t.length&gt;0&amp;&amp;(e.isEnd?(p(t),n(t)):(u(t),r(t)))}function f(){return e.pagination&amp;&amp;e.params.pagination.clickable&amp;&amp;e.pagination.bullets&amp;&amp;e.pagination.bullets.length}const g=(e,t,s)=&gt;{r(e),"BUTTON"!==e[0].tagName&amp;&amp;(l(e,"button"),e.on("keydown",h)),c(e,s),function(e,t){e.attr("aria-controls",t)}(e,t)};function v(){const t=e.params.a11y;e.$el.append(a);const s=e.$el;t.containerRoleDescriptionMessage&amp;&amp;o(s,t.containerRoleDescriptionMessage),t.containerMessage&amp;&amp;c(s,t.containerMessage);const i=e.$wrapperEl,r=i.attr("id")||`swiper-wrapper-${function(e=16){return"x".repeat(e).replace(/x/g,(()=&gt;Math.round(16*Math.random()).toString(16)))}(16)}`,n=e.params.autoplay&amp;&amp;e.params.autoplay.enabled?"off":"polite";var p;p=r,i.attr("id",p),function(e,t){e.attr("aria-live",t)}(i,n),t.itemRoleDescriptionMessage&amp;&amp;o(d(e.slides),t.itemRoleDescriptionMessage),l(d(e.slides),t.slideRole);const u=e.params.loop?e.slides.filter((t=&gt;!t.classList.contains(e.params.slideDuplicateClass))).length:e.slides.length;let m,v;e.slides.each(((s,a)=&gt;{const i=d(s),r=e.params.loop?parseInt(i.attr("data-swiper-slide-index"),10):a;c(i,t.slideLabelMessage.replace(/\{\{index\}\}/,r+1).replace(/\{\{slidesLength\}\}/,u))})),e.navigation&amp;&amp;e.navigation.$nextEl&amp;&amp;(m=e.navigation.$nextEl),e.navigation&amp;&amp;e.navigation.$prevEl&amp;&amp;(v=e.navigation.$prevEl),m&amp;&amp;m.length&amp;&amp;g(m,r,t.nextSlideMessage),v&amp;&amp;v.length&amp;&amp;g(v,r,t.prevSlideMessage),f()&amp;&amp;e.pagination.$el.on("keydown",W(e.params.pagination.bulletClass),h)}s("beforeInit",(()=&gt;{a=d(`&lt;span class="${e.params.a11y.notificationClass}" aria-live="assertive" aria-atomic="true"&gt;&lt;/span&gt;`)})),s("afterInit",(()=&gt;{e.params.a11y.enabled&amp;&amp;(v(),m())})),s("toEdge",(()=&gt;{e.params.a11y.enabled&amp;&amp;m()})),s("fromEdge",(()=&gt;{e.params.a11y.enabled&amp;&amp;m()})),s("paginationUpdate",(()=&gt;{e.params.a11y.enabled&amp;&amp;function(){const t=e.params.a11y;f()&amp;&amp;e.pagination.bullets.each((s=&gt;{const a=d(s);r(a),e.params.pagination.renderBullet||(l(a,"button"),c(a,t.paginationBulletMessage.replace(/\{\{index\}\}/,a.index()+1)))}))}()})),s("destroy",(()=&gt;{e.params.a11y.enabled&amp;&amp;function(){let t,s;a&amp;&amp;a.length&gt;0&amp;&amp;a.remove(),e.navigation&amp;&amp;e.navigation.$nextEl&amp;&amp;(t=e.navigation.$nextEl),e.navigation&amp;&amp;e.navigation.$prevEl&amp;&amp;(s=e.navigation.$prevEl),t&amp;&amp;t.off("keydown",h),s&amp;&amp;s.off("keydown",h),f()&amp;&amp;e.pagination.$el.off("keydown",W(e.params.pagination.bulletClass),h)}()}))},function({swiper:e,extendParams:t,on:s}){t({history:{enabled:!1,root:"",replaceState:!1,key:"slides"}});let a=!1,i={};const n=e=&gt;e.toString().replace(/\s+/g,"-").replace(/[^\w-]+/g,"").replace(/--+/g,"-").replace(/^-+/,"").replace(/-+$/,""),l=e=&gt;{const t=r();let s;s=e?new URL(e):t.location;const a=s.pathname.slice(1).split("/").filter((e=&gt;""!==e)),i=a.length;return{key:a[i-2],value:a[i-1]}},o=(t,s)=&gt;{const i=r();if(!a||!e.params.history.enabled)return;let l;l=e.params.url?new URL(e.params.url):i.location;const o=e.slides.eq(s);let d=n(o.attr("data-history"));if(e.params.history.root.length&gt;0){let s=e.params.history.root;"/"===s[s.length-1]&amp;&amp;(s=s.slice(0,s.length-1)),d=`${s}/${t}/${d}`}else l.pathname.includes(t)||(d=`${t}/${d}`);const c=i.history.state;c&amp;&amp;c.value===d||(e.params.history.replaceState?i.history.replaceState({value:d},null,d):i.history.pushState({value:d},null,d))},d=(t,s,a)=&gt;{if(s)for(let i=0,r=e.slides.length;i&lt;r;i+=1){const r=e.slides.eq(i);if(n(r.attr("data-history"))===s&amp;&amp;!r.hasClass(e.params.slideDuplicateClass)){const s=r.index();e.slideTo(s,t,a)}}else e.slideTo(0,t,a)},c=()=&gt;{i=l(e.params.url),d(e.params.speed,e.paths.value,!1)};s("init",(()=&gt;{e.params.history.enabled&amp;&amp;(()=&gt;{const t=r();if(e.params.history){if(!t.history||!t.history.pushState)return e.params.history.enabled=!1,void(e.params.hashNavigation.enabled=!0);a=!0,i=l(e.params.url),(i.key||i.value)&amp;&amp;(d(0,i.value,e.params.runCallbacksOnInit),e.params.history.replaceState||t.addEventListener("popstate",c))}})()})),s("destroy",(()=&gt;{e.params.history.enabled&amp;&amp;(()=&gt;{const t=r();e.params.history.replaceState||t.removeEventListener("popstate",c)})()})),s("transitionEnd _freeModeNoMomentumRelease",(()=&gt;{a&amp;&amp;o(e.params.history.key,e.activeIndex)})),s("slideChange",(()=&gt;{a&amp;&amp;e.params.cssMode&amp;&amp;o(e.params.history.key,e.activeIndex)}))},function({swiper:e,extendParams:t,emit:s,on:i}){let n=!1;const l=a(),o=r();t({hashNavigation:{enabled:!1,replaceState:!1,watchState:!1}});const c=()=&gt;{s("hashChange");const t=l.location.hash.replace("#","");if(t!==e.slides.eq(e.activeIndex).attr("data-hash")){const s=e.$wrapperEl.children(`.${e.params.slideClass}[data-hash="${t}"]`).index();if(void 0===s)return;e.slideTo(s)}},p=()=&gt;{if(n&amp;&amp;e.params.hashNavigation.enabled)if(e.params.hashNavigation.replaceState&amp;&amp;o.history&amp;&amp;o.history.replaceState)o.history.replaceState(null,null,`#${e.slides.eq(e.activeIndex).attr("data-hash")}`||""),s("hashSet");else{const t=e.slides.eq(e.activeIndex),a=t.attr("data-hash")||t.attr("data-history");l.location.hash=a||"",s("hashSet")}};i("init",(()=&gt;{e.params.hashNavigation.enabled&amp;&amp;(()=&gt;{if(!e.params.hashNavigation.enabled||e.params.history&amp;&amp;e.params.history.enabled)return;n=!0;const t=l.location.hash.replace("#","");if(t){const s=0;for(let a=0,i=e.slides.length;a&lt;i;a+=1){const i=e.slides.eq(a);if((i.attr("data-hash")||i.attr("data-history"))===t&amp;&amp;!i.hasClass(e.params.slideDuplicateClass)){const t=i.index();e.slideTo(t,s,e.params.runCallbacksOnInit,!0)}}}e.params.hashNavigation.watchState&amp;&amp;d(o).on("hashchange",c)})()})),i("destroy",(()=&gt;{e.params.hashNavigation.enabled&amp;&amp;e.params.hashNavigation.watchState&amp;&amp;d(o).off("hashchange",c)})),i("transitionEnd _freeModeNoMomentumRelease",(()=&gt;{n&amp;&amp;p()})),i("slideChange",(()=&gt;{n&amp;&amp;e.params.cssMode&amp;&amp;p()}))},function({swiper:e,extendParams:t,on:s,emit:i}){let r;function n(){const t=e.slides.eq(e.activeIndex);let s=e.params.autoplay.delay;t.attr("data-swiper-autoplay")&amp;&amp;(s=t.attr("data-swiper-autoplay")||e.params.autoplay.delay),clearTimeout(r),r=p((()=&gt;{let t;e.params.autoplay.reverseDirection?e.params.loop?(e.loopFix(),t=e.slidePrev(e.params.speed,!0,!0),i("autoplay")):e.isBeginning?e.params.autoplay.stopOnLastSlide?o():(t=e.slideTo(e.slides.length-1,e.params.speed,!0,!0),i("autoplay")):(t=e.slidePrev(e.params.speed,!0,!0),i("autoplay")):e.params.loop?(e.loopFix(),t=e.slideNext(e.params.speed,!0,!0),i("autoplay")):e.isEnd?e.params.autoplay.stopOnLastSlide?o():(t=e.slideTo(0,e.params.speed,!0,!0),i("autoplay")):(t=e.slideNext(e.params.speed,!0,!0),i("autoplay")),(e.params.cssMode&amp;&amp;e.autoplay.running||!1===t)&amp;&amp;n()}),s)}function l(){return void 0===r&amp;&amp;(!e.autoplay.running&amp;&amp;(e.autoplay.running=!0,i("autoplayStart"),n(),!0))}function o(){return!!e.autoplay.running&amp;&amp;(void 0!==r&amp;&amp;(r&amp;&amp;(clearTimeout(r),r=void 0),e.autoplay.running=!1,i("autoplayStop"),!0))}function d(t){e.autoplay.running&amp;&amp;(e.autoplay.paused||(r&amp;&amp;clearTimeout(r),e.autoplay.paused=!0,0!==t&amp;&amp;e.params.autoplay.waitForTransition?["transitionend","webkitTransitionEnd"].forEach((t=&gt;{e.$wrapperEl[0].addEventListener(t,u)})):(e.autoplay.paused=!1,n())))}function c(){const t=a();"hidden"===t.visibilityState&amp;&amp;e.autoplay.running&amp;&amp;d(),"visible"===t.visibilityState&amp;&amp;e.autoplay.paused&amp;&amp;(n(),e.autoplay.paused=!1)}function u(t){e&amp;&amp;!e.destroyed&amp;&amp;e.$wrapperEl&amp;&amp;t.target===e.$wrapperEl[0]&amp;&amp;(["transitionend","webkitTransitionEnd"].forEach((t=&gt;{e.$wrapperEl[0].removeEventListener(t,u)})),e.autoplay.paused=!1,e.autoplay.running?n():o())}function h(){e.params.autoplay.disableOnInteraction?o():d(),["transitionend","webkitTransitionEnd"].forEach((t=&gt;{e.$wrapperEl[0].removeEventListener(t,u)}))}function m(){e.params.autoplay.disableOnInteraction||(e.autoplay.paused=!1,n())}e.autoplay={running:!1,paused:!1},t({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!0,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}}),s("init",(()=&gt;{if(e.params.autoplay.enabled){l();a().addEventListener("visibilitychange",c),e.params.autoplay.pauseOnMouseEnter&amp;&amp;(e.$el.on("mouseenter",h),e.$el.on("mouseleave",m))}})),s("beforeTransitionStart",((t,s,a)=&gt;{e.autoplay.running&amp;&amp;(a||!e.params.autoplay.disableOnInteraction?e.autoplay.pause(s):o())})),s("sliderFirstMove",(()=&gt;{e.autoplay.running&amp;&amp;(e.params.autoplay.disableOnInteraction?o():d())})),s("touchEnd",(()=&gt;{e.params.cssMode&amp;&amp;e.autoplay.paused&amp;&amp;!e.params.autoplay.disableOnInteraction&amp;&amp;n()})),s("destroy",(()=&gt;{e.$el.off("mouseenter",h),e.$el.off("mouseleave",m),e.autoplay.running&amp;&amp;o();a().removeEventListener("visibilitychange",c)})),Object.assign(e.autoplay,{pause:d,run:n,start:l,stop:o})},function({swiper:e,extendParams:t,on:s}){t({thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-thumbs"}});let a=!1,i=!1;function r(){const t=e.thumbs.swiper;if(!t)return;const s=t.clickedIndex,a=t.clickedSlide;if(a&amp;&amp;d(a).hasClass(e.params.thumbs.slideThumbActiveClass))return;if(null==s)return;let i;if(i=t.params.loop?parseInt(d(t.clickedSlide).attr("data-swiper-slide-index"),10):s,e.params.loop){let t=e.activeIndex;e.slides.eq(t).hasClass(e.params.slideDuplicateClass)&amp;&amp;(e.loopFix(),e._clientLeft=e.$wrapperEl[0].clientLeft,t=e.activeIndex);const s=e.slides.eq(t).prevAll(`[data-swiper-slide-index="${i}"]`).eq(0).index(),a=e.slides.eq(t).nextAll(`[data-swiper-slide-index="${i}"]`).eq(0).index();i=void 0===s?a:void 0===a?s:a-t&lt;t-s?a:s}e.slideTo(i)}function n(){const{thumbs:t}=e.params;if(a)return!1;a=!0;const s=e.constructor;if(t.swiper instanceof s)e.thumbs.swiper=t.swiper,Object.assign(e.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),Object.assign(e.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1});else if(m(t.swiper)){const a=Object.assign({},t.swiper);Object.assign(a,{watchSlidesProgress:!0,slideToClickedSlide:!1}),e.thumbs.swiper=new s(a),i=!0}return e.thumbs.swiper.$el.addClass(e.params.thumbs.thumbsContainerClass),e.thumbs.swiper.on("tap",r),!0}function l(t){const s=e.thumbs.swiper;if(!s)return;const a="auto"===s.params.slidesPerView?s.slidesPerViewDynamic():s.params.slidesPerView,i=e.params.thumbs.autoScrollOffset,r=i&amp;&amp;!s.params.loop;if(e.realIndex!==s.realIndex||r){let n,l,o=s.activeIndex;if(s.params.loop){s.slides.eq(o).hasClass(s.params.slideDuplicateClass)&amp;&amp;(s.loopFix(),s._clientLeft=s.$wrapperEl[0].clientLeft,o=s.activeIndex);const t=s.slides.eq(o).prevAll(`[data-swiper-slide-index="${e.realIndex}"]`).eq(0).index(),a=s.slides.eq(o).nextAll(`[data-swiper-slide-index="${e.realIndex}"]`).eq(0).index();n=void 0===t?a:void 0===a?t:a-o==o-t?s.params.slidesPerGroup&gt;1?a:o:a-o&lt;o-t?a:t,l=e.activeIndex&gt;e.previousIndex?"next":"prev"}else n=e.realIndex,l=n&gt;e.previousIndex?"next":"prev";r&amp;&amp;(n+="next"===l?i:-1*i),s.visibleSlidesIndexes&amp;&amp;s.visibleSlidesIndexes.indexOf(n)&lt;0&amp;&amp;(s.params.centeredSlides?n=n&gt;o?n-Math.floor(a/2)+1:n+Math.floor(a/2)-1:n&gt;o&amp;&amp;s.params.slidesPerGroup,s.slideTo(n,t?0:void 0))}let n=1;const l=e.params.thumbs.slideThumbActiveClass;if(e.params.slidesPerView&gt;1&amp;&amp;!e.params.centeredSlides&amp;&amp;(n=e.params.slidesPerView),e.params.thumbs.multipleActiveThumbs||(n=1),n=Math.floor(n),s.slides.removeClass(l),s.params.loop||s.params.virtual&amp;&amp;s.params.virtual.enabled)for(let t=0;t&lt;n;t+=1)s.$wrapperEl.children(`[data-swiper-slide-index="${e.realIndex+t}"]`).addClass(l);else for(let t=0;t&lt;n;t+=1)s.slides.eq(e.realIndex+t).addClass(l)}e.thumbs={swiper:null},s("beforeInit",(()=&gt;{const{thumbs:t}=e.params;t&amp;&amp;t.swiper&amp;&amp;(n(),l(!0))})),s("slideChange update resize observerUpdate",(()=&gt;{e.thumbs.swiper&amp;&amp;l()})),s("setTransition",((t,s)=&gt;{const a=e.thumbs.swiper;a&amp;&amp;a.setTransition(s)})),s("beforeDestroy",(()=&gt;{const t=e.thumbs.swiper;t&amp;&amp;i&amp;&amp;t&amp;&amp;t.destroy()})),Object.assign(e.thumbs,{init:n,update:l})},function({swiper:e,extendParams:t,emit:s,once:a}){t({freeMode:{enabled:!1,momentum:!0,momentumRatio:1,momentumBounce:!0,momentumBounceRatio:1,momentumVelocityRatio:1,sticky:!1,minimumVelocity:.02}}),Object.assign(e,{freeMode:{onTouchMove:function(){const{touchEventsData:t,touches:s}=e;0===t.velocities.length&amp;&amp;t.velocities.push({position:s[e.isHorizontal()?"startX":"startY"],time:t.touchStartTime}),t.velocities.push({position:s[e.isHorizontal()?"currentX":"currentY"],time:u()})},onTouchEnd:function({currentPos:t}){const{params:i,$wrapperEl:r,rtlTranslate:n,snapGrid:l,touchEventsData:o}=e,d=u()-o.touchStartTime;if(t&lt;-e.minTranslate())e.slideTo(e.activeIndex);else if(t&gt;-e.maxTranslate())e.slides.length&lt;l.length?e.slideTo(l.length-1):e.slideTo(e.slides.length-1);else{if(i.freeMode.momentum){if(o.velocities.length&gt;1){const t=o.velocities.pop(),s=o.velocities.pop(),a=t.position-s.position,r=t.time-s.time;e.velocity=a/r,e.velocity/=2,Math.abs(e.velocity)&lt;i.freeMode.minimumVelocity&amp;&amp;(e.velocity=0),(r&gt;150||u()-t.time&gt;300)&amp;&amp;(e.velocity=0)}else e.velocity=0;e.velocity*=i.freeMode.momentumVelocityRatio,o.velocities.length=0;let t=1e3*i.freeMode.momentumRatio;const d=e.velocity*t;let c=e.translate+d;n&amp;&amp;(c=-c);let p,h=!1;const m=20*Math.abs(e.velocity)*i.freeMode.momentumBounceRatio;let f;if(c&lt;e.maxTranslate())i.freeMode.momentumBounce?(c+e.maxTranslate()&lt;-m&amp;&amp;(c=e.maxTranslate()-m),p=e.maxTranslate(),h=!0,o.allowMomentumBounce=!0):c=e.maxTranslate(),i.loop&amp;&amp;i.centeredSlides&amp;&amp;(f=!0);else if(c&gt;e.minTranslate())i.freeMode.momentumBounce?(c-e.minTranslate()&gt;m&amp;&amp;(c=e.minTranslate()+m),p=e.minTranslate(),h=!0,o.allowMomentumBounce=!0):c=e.minTranslate(),i.loop&amp;&amp;i.centeredSlides&amp;&amp;(f=!0);else if(i.freeMode.sticky){let t;for(let e=0;e&lt;l.length;e+=1)if(l[e]&gt;-c){t=e;break}c=Math.abs(l[t]-c)&lt;Math.abs(l[t-1]-c)||"next"===e.swipeDirection?l[t]:l[t-1],c=-c}if(f&amp;&amp;a("transitionEnd",(()=&gt;{e.loopFix()})),0!==e.velocity){if(t=n?Math.abs((-c-e.translate)/e.velocity):Math.abs((c-e.translate)/e.velocity),i.freeMode.sticky){const s=Math.abs((n?-c:c)-e.translate),a=e.slidesSizesGrid[e.activeIndex];t=s&lt;a?i.speed:s&lt;2*a?1.5*i.speed:2.5*i.speed}}else if(i.freeMode.sticky)return void e.slideToClosest();i.freeMode.momentumBounce&amp;&amp;h?(e.updateProgress(p),e.setTransition(t),e.setTranslate(c),e.transitionStart(!0,e.swipeDirection),e.animating=!0,r.transitionEnd((()=&gt;{e&amp;&amp;!e.destroyed&amp;&amp;o.allowMomentumBounce&amp;&amp;(s("momentumBounce"),e.setTransition(i.speed),setTimeout((()=&gt;{e.setTranslate(p),r.transitionEnd((()=&gt;{e&amp;&amp;!e.destroyed&amp;&amp;e.transitionEnd()}))}),0))}))):e.velocity?(s("_freeModeNoMomentumRelease"),e.updateProgress(c),e.setTransition(t),e.setTranslate(c),e.transitionStart(!0,e.swipeDirection),e.animating||(e.animating=!0,r.transitionEnd((()=&gt;{e&amp;&amp;!e.destroyed&amp;&amp;e.transitionEnd()})))):e.updateProgress(c),e.updateActiveIndex(),e.updateSlidesClasses()}else{if(i.freeMode.sticky)return void e.slideToClosest();i.freeMode&amp;&amp;s("_freeModeNoMomentumRelease")}(!i.freeMode.momentum||d&gt;=i.longSwipesMs)&amp;&amp;(e.updateProgress(),e.updateActiveIndex(),e.updateSlidesClasses())}}}})},function({swiper:e,extendParams:t}){let s,a,i;t({grid:{rows:1,fill:"column"}}),e.grid={initSlides:t=&gt;{const{slidesPerView:r}=e.params,{rows:n,fill:l}=e.params.grid;a=s/n,i=Math.floor(t/n),s=Math.floor(t/n)===t/n?t:Math.ceil(t/n)*n,"auto"!==r&amp;&amp;"row"===l&amp;&amp;(s=Math.max(s,r*n))},updateSlide:(t,r,n,l)=&gt;{const{slidesPerGroup:o,spaceBetween:d}=e.params,{rows:c,fill:p}=e.params.grid;let u,h,m;if("row"===p&amp;&amp;o&gt;1){const e=Math.floor(t/(o*c)),a=t-c*o*e,i=0===e?o:Math.min(Math.ceil((n-e*c*o)/c),o);m=Math.floor(a/i),h=a-m*i+e*o,u=h+m*s/c,r.css({"-webkit-order":u,order:u})}else"column"===p?(h=Math.floor(t/c),m=t-h*c,(h&gt;i||h===i&amp;&amp;m===c-1)&amp;&amp;(m+=1,m&gt;=c&amp;&amp;(m=0,h+=1))):(m=Math.floor(t/a),h=t-m*a);r.css(l("margin-top"),0!==m?d&amp;&amp;`${d}px`:"")},updateWrapperSize:(t,a,i)=&gt;{const{spaceBetween:r,centeredSlides:n,roundLengths:l}=e.params,{rows:o}=e.params.grid;if(e.virtualSize=(t+r)*s,e.virtualSize=Math.ceil(e.virtualSize/o)-r,e.$wrapperEl.css({[i("width")]:`${e.virtualSize+r}px`}),n){a.splice(0,a.length);const t=[];for(let s=0;s&lt;a.length;s+=1){let i=a[s];l&amp;&amp;(i=Math.floor(i)),a[s]&lt;e.virtualSize+a[0]&amp;&amp;t.push(i)}a.push(...t)}}}},function({swiper:e}){Object.assign(e,{appendSlide:R.bind(e),prependSlide:j.bind(e),addSlide:_.bind(e),removeSlide:V.bind(e),removeAllSlides:q.bind(e)})},function({swiper:e,extendParams:t,on:s}){t({fadeEffect:{crossFade:!1,transformEl:null}}),F({effect:"fade",swiper:e,on:s,setTranslate:()=&gt;{const{slides:t}=e,s=e.params.fadeEffect;for(let a=0;a&lt;t.length;a+=1){const t=e.slides.eq(a);let i=-t[0].swiperSlideOffset;e.params.virtualTranslate||(i-=e.translate);let r=0;e.isHorizontal()||(r=i,i=0);const n=e.params.fadeEffect.crossFade?Math.max(1-Math.abs(t[0].progress),0):1+Math.min(Math.max(t[0].progress,-1),0);U(s,t).css({opacity:n}).transform(`translate3d(${i}px, ${r}px, 0px)`)}},setTransition:t=&gt;{const{transformEl:s}=e.params.fadeEffect;(s?e.slides.find(s):e.slides).transition(t),K({swiper:e,duration:t,transformEl:s,allSlides:!0})},overwriteParams:()=&gt;({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!e.params.cssMode})})},function({swiper:e,extendParams:t,on:s}){t({cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94}}),F({effect:"cube",swiper:e,on:s,setTranslate:()=&gt;{const{$el:t,$wrapperEl:s,slides:a,width:i,height:r,rtlTranslate:n,size:l,browser:o}=e,c=e.params.cubeEffect,p=e.isHorizontal(),u=e.virtual&amp;&amp;e.params.virtual.enabled;let h,m=0;c.shadow&amp;&amp;(p?(h=s.find(".swiper-cube-shadow"),0===h.length&amp;&amp;(h=d('&lt;div class="swiper-cube-shadow"&gt;&lt;/div&gt;'),s.append(h)),h.css({height:`${i}px`})):(h=t.find(".swiper-cube-shadow"),0===h.length&amp;&amp;(h=d('&lt;div class="swiper-cube-shadow"&gt;&lt;/div&gt;'),t.append(h))));for(let e=0;e&lt;a.length;e+=1){const t=a.eq(e);let s=e;u&amp;&amp;(s=parseInt(t.attr("data-swiper-slide-index"),10));let i=90*s,r=Math.floor(i/360);n&amp;&amp;(i=-i,r=Math.floor(-i/360));const o=Math.max(Math.min(t[0].progress,1),-1);let h=0,f=0,g=0;s%4==0?(h=4*-r*l,g=0):(s-1)%4==0?(h=0,g=4*-r*l):(s-2)%4==0?(h=l+4*r*l,g=l):(s-3)%4==0&amp;&amp;(h=-l,g=3*l+4*l*r),n&amp;&amp;(h=-h),p||(f=h,h=0);const v=`rotateX(${p?0:-i}deg) rotateY(${p?i:0}deg) translate3d(${h}px, ${f}px, ${g}px)`;if(o&lt;=1&amp;&amp;o&gt;-1&amp;&amp;(m=90*s+90*o,n&amp;&amp;(m=90*-s-90*o)),t.transform(v),c.slideShadows){let e=p?t.find(".swiper-slide-shadow-left"):t.find(".swiper-slide-shadow-top"),s=p?t.find(".swiper-slide-shadow-right"):t.find(".swiper-slide-shadow-bottom");0===e.length&amp;&amp;(e=d(`&lt;div class="swiper-slide-shadow-${p?"left":"top"}"&gt;&lt;/div&gt;`),t.append(e)),0===s.length&amp;&amp;(s=d(`&lt;div class="swiper-slide-shadow-${p?"right":"bottom"}"&gt;&lt;/div&gt;`),t.append(s)),e.length&amp;&amp;(e[0].style.opacity=Math.max(-o,0)),s.length&amp;&amp;(s[0].style.opacity=Math.max(o,0))}}if(s.css({"-webkit-transform-origin":`50% 50% -${l/2}px`,"transform-origin":`50% 50% -${l/2}px`}),c.shadow)if(p)h.transform(`translate3d(0px, ${i/2+c.shadowOffset}px, ${-i/2}px) rotateX(90deg) rotateZ(0deg) scale(${c.shadowScale})`);else{const e=Math.abs(m)-90*Math.floor(Math.abs(m)/90),t=1.5-(Math.sin(2*e*Math.PI/360)/2+Math.cos(2*e*Math.PI/360)/2),s=c.shadowScale,a=c.shadowScale/t,i=c.shadowOffset;h.transform(`scale3d(${s}, 1, ${a}) translate3d(0px, ${r/2+i}px, ${-r/2/a}px) rotateX(-90deg)`)}const f=o.isSafari||o.isWebView?-l/2:0;s.transform(`translate3d(0px,0,${f}px) rotateX(${e.isHorizontal()?0:m}deg) rotateY(${e.isHorizontal()?-m:0}deg)`)},setTransition:t=&gt;{const{$el:s,slides:a}=e;a.transition(t).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(t),e.params.cubeEffect.shadow&amp;&amp;!e.isHorizontal()&amp;&amp;s.find(".swiper-cube-shadow").transition(t)},perspective:()=&gt;!0,overwriteParams:()=&gt;({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,resistanceRatio:0,spaceBetween:0,centeredSlides:!1,virtualTranslate:!0})})},function({swiper:e,extendParams:t,on:s}){t({flipEffect:{slideShadows:!0,limitRotation:!0,transformEl:null}}),F({effect:"flip",swiper:e,on:s,setTranslate:()=&gt;{const{slides:t,rtlTranslate:s}=e,a=e.params.flipEffect;for(let i=0;i&lt;t.length;i+=1){const r=t.eq(i);let n=r[0].progress;e.params.flipEffect.limitRotation&amp;&amp;(n=Math.max(Math.min(r[0].progress,1),-1));const l=r[0].swiperSlideOffset;let o=-180*n,d=0,c=e.params.cssMode?-l-e.translate:-l,p=0;if(e.isHorizontal()?s&amp;&amp;(o=-o):(p=c,c=0,d=-o,o=0),r[0].style.zIndex=-Math.abs(Math.round(n))+t.length,a.slideShadows){let t=e.isHorizontal()?r.find(".swiper-slide-shadow-left"):r.find(".swiper-slide-shadow-top"),s=e.isHorizontal()?r.find(".swiper-slide-shadow-right"):r.find(".swiper-slide-shadow-bottom");0===t.length&amp;&amp;(t=Z(a,r,e.isHorizontal()?"left":"top")),0===s.length&amp;&amp;(s=Z(a,r,e.isHorizontal()?"right":"bottom")),t.length&amp;&amp;(t[0].style.opacity=Math.max(-n,0)),s.length&amp;&amp;(s[0].style.opacity=Math.max(n,0))}const u=`translate3d(${c}px, ${p}px, 0px) rotateX(${d}deg) rotateY(${o}deg)`;U(a,r).transform(u)}},setTransition:t=&gt;{const{transformEl:s}=e.params.flipEffect;(s?e.slides.find(s):e.slides).transition(t).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(t),K({swiper:e,duration:t,transformEl:s})},perspective:()=&gt;!0,overwriteParams:()=&gt;({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!e.params.cssMode})})},function({swiper:e,extendParams:t,on:s}){t({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0,transformEl:null}}),F({effect:"coverflow",swiper:e,on:s,setTranslate:()=&gt;{const{width:t,height:s,slides:a,slidesSizesGrid:i}=e,r=e.params.coverflowEffect,n=e.isHorizontal(),l=e.translate,o=n?t/2-l:s/2-l,d=n?r.rotate:-r.rotate,c=r.depth;for(let e=0,t=a.length;e&lt;t;e+=1){const t=a.eq(e),s=i[e],l=(o-t[0].swiperSlideOffset-s/2)/s*r.modifier;let p=n?d*l:0,u=n?0:d*l,h=-c*Math.abs(l),m=r.stretch;"string"==typeof m&amp;&amp;-1!==m.indexOf("%")&amp;&amp;(m=parseFloat(r.stretch)/100*s);let f=n?0:m*l,g=n?m*l:0,v=1-(1-r.scale)*Math.abs(l);Math.abs(g)&lt;.001&amp;&amp;(g=0),Math.abs(f)&lt;.001&amp;&amp;(f=0),Math.abs(h)&lt;.001&amp;&amp;(h=0),Math.abs(p)&lt;.001&amp;&amp;(p=0),Math.abs(u)&lt;.001&amp;&amp;(u=0),Math.abs(v)&lt;.001&amp;&amp;(v=0);const w=`translate3d(${g}px,${f}px,${h}px)  rotateX(${u}deg) rotateY(${p}deg) scale(${v})`;if(U(r,t).transform(w),t[0].style.zIndex=1-Math.abs(Math.round(l)),r.slideShadows){let e=n?t.find(".swiper-slide-shadow-left"):t.find(".swiper-slide-shadow-top"),s=n?t.find(".swiper-slide-shadow-right"):t.find(".swiper-slide-shadow-bottom");0===e.length&amp;&amp;(e=Z(r,t,n?"left":"top")),0===s.length&amp;&amp;(s=Z(r,t,n?"right":"bottom")),e.length&amp;&amp;(e[0].style.opacity=l&gt;0?l:0),s.length&amp;&amp;(s[0].style.opacity=-l&gt;0?-l:0)}}},setTransition:t=&gt;{const{transformEl:s}=e.params.coverflowEffect;(s?e.slides.find(s):e.slides).transition(t).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(t)},perspective:()=&gt;!0,overwriteParams:()=&gt;({watchSlidesProgress:!0})})},function({swiper:e,extendParams:t,on:s}){t({creativeEffect:{transformEl:null,limitProgress:1,shadowPerProgress:!1,progressMultiplier:1,perspective:!0,prev:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1},next:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1}}});const a=e=&gt;"string"==typeof e?e:`${e}px`;F({effect:"creative",swiper:e,on:s,setTranslate:()=&gt;{const{slides:t}=e,s=e.params.creativeEffect,{progressMultiplier:i}=s;for(let r=0;r&lt;t.length;r+=1){const n=t.eq(r),l=n[0].progress,o=Math.min(Math.max(n[0].progress,-s.limitProgress),s.limitProgress),d=n[0].swiperSlideOffset,c=[e.params.cssMode?-d-e.translate:-d,0,0],p=[0,0,0];let u=!1;e.isHorizontal()||(c[1]=c[0],c[0]=0);let h={translate:[0,0,0],rotate:[0,0,0],scale:1,opacity:1};o&lt;0?(h=s.next,u=!0):o&gt;0&amp;&amp;(h=s.prev,u=!0),c.forEach(((e,t)=&gt;{c[t]=`calc(${e}px + (${a(h.translate[t])} * ${Math.abs(o*i)}))`})),p.forEach(((e,t)=&gt;{p[t]=h.rotate[t]*Math.abs(o*i)})),n[0].style.zIndex=-Math.abs(Math.round(l))+t.length;const m=c.join(", "),f=`rotateX(${p[0]}deg) rotateY(${p[1]}deg) rotateZ(${p[2]}deg)`,g=o&lt;0?`scale(${1+(1-h.scale)*o*i})`:`scale(${1-(1-h.scale)*o*i})`,v=o&lt;0?1+(1-h.opacity)*o*i:1-(1-h.opacity)*o*i,w=`translate3d(${m}) ${f} ${g}`;if(u&amp;&amp;h.shadow||!u){let e=n.children(".swiper-slide-shadow");if(0===e.length&amp;&amp;h.shadow&amp;&amp;(e=Z(s,n)),e.length){const t=s.shadowPerProgress?o*(1/s.limitProgress):o;e[0].style.opacity=Math.min(Math.max(Math.abs(t),0),1)}}const b=U(s,n);b.transform(w).css({opacity:v}),h.origin&amp;&amp;b.css("transform-origin",h.origin)}},setTransition:t=&gt;{const{transformEl:s}=e.params.creativeEffect;(s?e.slides.find(s):e.slides).transition(t).find(".swiper-slide-shadow").transition(t),K({swiper:e,duration:t,transformEl:s})},perspective:()=&gt;e.params.creativeEffect.perspective,overwriteParams:()=&gt;({watchSlidesProgress:!0,virtualTranslate:!e.params.cssMode})})},function({swiper:e,extendParams:t,on:s}){t({cardsEffect:{slideShadows:!0,transformEl:null}}),F({effect:"cards",swiper:e,on:s,setTranslate:()=&gt;{const{slides:t,activeIndex:s}=e,a=e.params.cardsEffect,{startTranslate:i,isTouched:r}=e.touchEventsData,n=e.translate;for(let l=0;l&lt;t.length;l+=1){const o=t.eq(l),d=o[0].progress,c=Math.min(Math.max(d,-4),4);let p=o[0].swiperSlideOffset;e.params.centeredSlides&amp;&amp;!e.params.cssMode&amp;&amp;e.$wrapperEl.transform(`translateX(${e.minTranslate()}px)`),e.params.centeredSlides&amp;&amp;e.params.cssMode&amp;&amp;(p-=t[0].swiperSlideOffset);let u=e.params.cssMode?-p-e.translate:-p,h=0;const m=-100*Math.abs(c);let f=1,g=-2*c,v=8-.75*Math.abs(c);const w=(l===s||l===s-1)&amp;&amp;c&gt;0&amp;&amp;c&lt;1&amp;&amp;(r||e.params.cssMode)&amp;&amp;n&lt;i,b=(l===s||l===s+1)&amp;&amp;c&lt;0&amp;&amp;c&gt;-1&amp;&amp;(r||e.params.cssMode)&amp;&amp;n&gt;i;if(w||b){const e=(1-Math.abs((Math.abs(c)-.5)/.5))**.5;g+=-28*c*e,f+=-.5*e,v+=96*e,h=-25*e*Math.abs(c)+"%"}if(u=c&lt;0?`calc(${u}px + (${v*Math.abs(c)}%))`:c&gt;0?`calc(${u}px + (-${v*Math.abs(c)}%))`:`${u}px`,!e.isHorizontal()){const e=h;h=u,u=e}const x=`\n        translate3d(${u}, ${h}, ${m}px)\n        rotateZ(${g}deg)\n        scale(${c&lt;0?""+(1+(1-f)*c):""+(1-(1-f)*c)})\n      `;if(a.slideShadows){let e=o.find(".swiper-slide-shadow");0===e.length&amp;&amp;(e=Z(a,o)),e.length&amp;&amp;(e[0].style.opacity=Math.min(Math.max((Math.abs(c)-.5)/.5,0),1))}o[0].style.zIndex=-Math.abs(Math.round(d))+t.length;U(a,o).transform(x)}},setTransition:t=&gt;{const{transformEl:s}=e.params.cardsEffect;(s?e.slides.find(s):e.slides).transition(t).find(".swiper-slide-shadow").transition(t),K({swiper:e,duration:t,transformEl:s})},perspective:()=&gt;!0,overwriteParams:()=&gt;({watchSlidesProgress:!0,virtualTranslate:!e.params.cssMode})})}];return H.use(J),H}));
</code></td></tr><tr><td id="L14" class="css-a4x74f"><span>14</span></td><td id="LC14" class="css-1dcdqdg"><code><span class="code-comment">//# sourceMappingURL=swiper-bundle.min.js.map</span></code></td></tr><tr><td class="css-a4x74f">\</td><style data-emotion-css="jhhjkz">.css-jhhjkz{padding-left:10px;color:rgba(27,31,35,.3);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;}</style><td class="css-jhhjkz">No newline at end of file</td></tr></tbody></table></div></div></div></div><style data-emotion-css="1teho9j">.css-1teho9j{margin-top:5rem;background:black;color:#aaa;}</style><footer class="css-1teho9j"><style data-emotion-css="1ui8put">.css-1ui8put{max-width:940px;padding:10px 20px;margin:0 auto;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;}</style><div class="css-1ui8put"><p><span>Build: <!-- -->a7ebffa</span></p><p><span>© <!-- -->2023<!-- --> UNPKG</span></p><style data-emotion-css="la3nd4">.css-la3nd4{font-size:1.5rem;}</style><p class="css-la3nd4"><style data-emotion-css="bogekj">.css-bogekj{color:#aaa;display:inline-block;}.css-bogekj:hover{color:white;}</style><a href="https://twitter.com/unpkg" class="css-bogekj"><style data-emotion-css="i6dzq1">.css-i6dzq1{vertical-align:text-bottom;}</style><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" class="css-i6dzq1" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"></path></svg></a><style data-emotion-css="3czw03">.css-3czw03{color:#aaa;display:inline-block;margin-left:1rem;}.css-3czw03:hover{color:white;}</style><a href="https://github.com/mjackson/unpkg" class="css-3czw03"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 496 512" class="css-i6dzq1" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z"></path></svg></a></p></div></footer></div><script src="/react@16.8.6/umd/react.production.min.js"></script><script src="/react-dom@16.8.6/umd/react-dom.production.min.js"></script><script src="/@emotion/core@10.0.6/dist/core.umd.min.js"></script><script>'use strict';(function(t,A,c){function w(){w=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var e=arguments[b],c;for(c in e)Object.prototype.hasOwnProperty.call(e,c)&&(a[c]=e[c])}return a};return w.apply(this,arguments)}function P(a,b){if(null==a)return{};var e={},c=Object.keys(a),d;for(d=0;d<c.length;d++){var h=c[d];0<=b.indexOf(h)||(e[h]=a[h])}return e}function Q(a,b){b||(b=a.slice(0));a.raw=b;return a}function R(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,
"default")?a["default"]:a}function D(a,b){return b={exports:{}},a(b,b.exports),b.exports}function J(a,b,e,c,d){for(var g in a)if(ua(a,g)){try{if("function"!==typeof a[g]){var r=Error((c||"React class")+": "+e+" type `"+g+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof a[g]+"`.");r.name="Invariant Violation";throw r;}var k=a[g](b,g,c,e,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(q){k=q}!k||k instanceof Error||K((c||"React class")+": type specification of "+
e+" `"+g+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+typeof k+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).");if(k instanceof Error&&!(k.message in L)){L[k.message]=!0;var B=d?d():"";K("Failed "+e+" type: "+k.message+(null!=B?B:""))}}}function G(){return null}function S(a){var b,e=a.children;a=a.css;return c.jsx("div",{css:w((b={border:"1px solid #dfe2e5",
borderRadius:3},b["@media (max-width: 700px)"]={borderRightWidth:0,borderLeftWidth:0},b),a)},e)}function T(a){var b,e=a.children;a=a.css;return c.jsx("div",{css:w((b={padding:10,background:"#f6f8fa",color:"#424242",border:"1px solid #d1d5da",borderTopLeftRadius:3,borderTopRightRadius:3,margin:"-1px -1px 0",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between"},b["@media (max-width: 700px)"]={paddingRight:20,paddingLeft:20},b),a)},e)}function U(a){return a&&a.map(function(a,
c){return t.createElement(a.tag,z({key:c},a.attr),U(a.child))})}function E(a){return function(b){return t.createElement(va,z({attr:z({},a.attr)},b),U(a.child))}}function va(a){var b=function(b){var c=a.size||b.size||"1em";if(b.className)var e=b.className;a.className&&(e=(e?e+" ":"")+a.className);var h=a.attr,r=a.title,k=["attr","title"],B={},q;for(q in a)Object.prototype.hasOwnProperty.call(a,q)&&0>k.indexOf(q)&&(B[q]=a[q]);if(null!=a&&"function"===typeof Object.getOwnPropertySymbols){var p=0;for(q=
Object.getOwnPropertySymbols(a);p<q.length;p++)0>k.indexOf(q[p])&&(B[q[p]]=a[q[p]])}return t.createElement("svg",z({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},b.attr,h,B,{className:e,style:z({color:a.color||b.color},b.style,a.style),height:c,width:c,xmlns:"http://www.w3.org/2000/svg"}),r&&t.createElement("title",null,r),a.children)};return void 0!==V?t.createElement(V.Consumer,null,function(a){return b(a)}):b(W)}function F(a,b){var e=b.css;b=P(b,["css"]);return c.jsx(a,w({css:w({},
e,{verticalAlign:"text-bottom"})},b))}function wa(a){return F(X,a)}function xa(a){return F(Y,a)}function ya(a){return F(Z,a)}function za(a){return F(aa,a)}function Aa(a){return F(ba,a)}function ca(a){var b=a.path,e=a.details,g=Object.keys(e).reduce(function(a,b){var c=a.subdirs,g=a.files;b=e[b];"directory"===b.type?c.push(b):"file"===b.type&&g.push(b);return a},{subdirs:[],files:[]});a=g.subdirs;g=g.files;a.sort(da("path"));g.sort(da("path"));var d=[];"/"!==b&&d.push(c.jsx("tr",{key:".."},c.jsx("td",
{css:M}),c.jsx("td",{css:y},c.jsx("a",{title:"Parent directory",href:"../",css:N},"..")),c.jsx("td",{css:y}),c.jsx("td",{css:O})));a.forEach(function(a){a=a.path.substr(1<b.length?b.length+1:1);var e=a+"/";d.push(c.jsx("tr",{key:a},c.jsx("td",{css:M},c.jsx(ya,null)),c.jsx("td",{css:y},c.jsx("a",{title:a,href:e,css:N},a)),c.jsx("td",{css:y},"-"),c.jsx("td",{css:O},"-")))});g.forEach(function(a){var e=a.size,g=a.contentType;a=a.path.substr(1<b.length?b.length+1:1);d.push(c.jsx("tr",{key:a},c.jsx("td",
{css:M},"text/plain"===g||"text/markdown"===g?c.jsx(wa,null):c.jsx(xa,null)),c.jsx("td",{css:y},c.jsx("a",{title:a,href:a,css:N},a)),c.jsx("td",{css:y},ea(e)),c.jsx("td",{css:O},g)))});var h=[];0<g.length&&h.push(g.length+" file"+(1===g.length?"":"s"));0<a.length&&h.push(a.length+" folder"+(1===a.length?"":"s"));return c.jsx(S,null,c.jsx(T,null,c.jsx("span",null,h.join(", "))),c.jsx("table",{css:{width:"100%",borderCollapse:"collapse",borderRadius:2,background:"#fff","@media (max-width: 700px)":{"& th + th + th + th, & td + td + td + td":{display:"none"}},
"& tr:first-of-type td":{borderTop:0}}},c.jsx("thead",null,c.jsx("tr",null,c.jsx("th",null,c.jsx(H,null,"Icon")),c.jsx("th",null,c.jsx(H,null,"Name")),c.jsx("th",null,c.jsx(H,null,"Size")),c.jsx("th",null,c.jsx(H,null,"Content Type")))),c.jsx("tbody",null,d)))}function Ba(a){a=a.split("/");return a[a.length-1]}function Ca(a){var b=a.uri;return c.jsx("div",{css:{padding:20,textAlign:"center"}},c.jsx("img",{alt:Ba(a.path),src:b}))}function Da(a){a=a.highlights.slice(0);var b=a.length&&""===a[a.length-
1];b&&a.pop();return c.jsx("div",{className:"code-listing",css:{overflowX:"auto",overflowY:"hidden",paddingTop:5,paddingBottom:5}},c.jsx("table",{css:{border:"none",borderCollapse:"collapse",borderSpacing:0}},c.jsx("tbody",null,a.map(function(a,b){var e=b+1;return c.jsx("tr",{key:b},c.jsx("td",{id:"L"+e,css:{paddingLeft:10,paddingRight:10,color:"rgba(27,31,35,.3)",textAlign:"right",verticalAlign:"top",width:"1%",minWidth:50,userSelect:"none"}},c.jsx("span",null,e)),c.jsx("td",{id:"LC"+e,css:{paddingLeft:10,
paddingRight:10,color:"#24292e",whiteSpace:"pre"}},c.jsx("code",{dangerouslySetInnerHTML:{__html:a}})))}),!b&&c.jsx("tr",{key:"no-newline"},c.jsx("td",{css:{paddingLeft:10,paddingRight:10,color:"rgba(27,31,35,.3)",textAlign:"right",verticalAlign:"top",width:"1%",minWidth:50,userSelect:"none"}},"\\"),c.jsx("td",{css:{paddingLeft:10,color:"rgba(27,31,35,.3)",userSelect:"none"}},"No newline at end of file")))))}function Ea(){return c.jsx("div",{css:{padding:20}},c.jsx("p",{css:{textAlign:"center"}},
"No preview available."))}function fa(a){var b=a.packageName,e=a.packageVersion,g=a.path;a=a.details;var d=a.highlights,h=a.uri,r=a.language;return c.jsx(S,null,c.jsx(T,null,c.jsx("span",null,ea(a.size)),c.jsx("span",null,r),c.jsx("span",null,c.jsx("a",{href:"/"+b+"@"+e+g,css:{display:"inline-block",marginLeft:8,padding:"2px 8px",textDecoration:"none",fontWeight:600,fontSize:"0.9rem",color:"#24292e",backgroundColor:"#eff3f6",border:"1px solid rgba(27,31,35,.2)",borderRadius:3,":hover":{backgroundColor:"#e6ebf1",
borderColor:"rgba(27,31,35,.35)"},":active":{backgroundColor:"#e9ecef",borderColor:"rgba(27,31,35,.35)",boxShadow:"inset 0 0.15em 0.3em rgba(27,31,35,.15)"}}},"View Raw"))),d?c.jsx(Da,{highlights:d}):h?c.jsx(Ca,{path:g,uri:h}):c.jsx(Ea,null))}function ha(){var a=Q(["\n  .code-listing {\n    background: #fbfdff;\n    color: #383a42;\n  }\n  .code-comment,\n  .code-quote {\n    color: #a0a1a7;\n    font-style: italic;\n  }\n  .code-doctag,\n  .code-keyword,\n  .code-link,\n  .code-formula {\n    color: #a626a4;\n  }\n  .code-section,\n  .code-name,\n  .code-selector-tag,\n  .code-deletion,\n  .code-subst {\n    color: #e45649;\n  }\n  .code-literal {\n    color: #0184bb;\n  }\n  .code-string,\n  .code-regexp,\n  .code-addition,\n  .code-attribute,\n  .code-meta-string {\n    color: #50a14f;\n  }\n  .code-built_in,\n  .code-class .code-title {\n    color: #c18401;\n  }\n  .code-attr,\n  .code-variable,\n  .code-template-variable,\n  .code-type,\n  .code-selector-class,\n  .code-selector-attr,\n  .code-selector-pseudo,\n  .code-number {\n    color: #986801;\n  }\n  .code-symbol,\n  .code-bullet,\n  .code-meta,\n  .code-selector-id,\n  .code-title {\n    color: #4078f2;\n  }\n  .code-emphasis {\n    font-style: italic;\n  }\n  .code-strong {\n    font-weight: bold;\n  }\n"]);
ha=function(){return a};return a}function ia(){var a=Q(["\n  html {\n    box-sizing: border-box;\n  }\n  *,\n  *:before,\n  *:after {\n    box-sizing: inherit;\n  }\n\n  html,\n  body,\n  #root {\n    height: 100%;\n    margin: 0;\n  }\n\n  body {\n    ","\n    font-size: 16px;\n    line-height: 1.5;\n    overflow-wrap: break-word;\n    background: white;\n    color: black;\n  }\n\n  code {\n    ","\n  }\n\n  th,\n  td {\n    padding: 0;\n  }\n\n  select {\n    font-size: inherit;\n  }\n\n  #root {\n    display: flex;\n    flex-direction: column;\n  }\n"]);
ia=function(){return a};return a}function ja(a){var b=a.css;a=P(a,["css"]);return c.jsx("a",w({},a,{css:w({color:"#0076ff",textDecoration:"none",":hover":{textDecoration:"underline"}},b)}))}function Fa(){return c.jsx("header",{css:{marginTop:"2rem"}},c.jsx("h1",{css:{textAlign:"center",fontSize:"3rem",letterSpacing:"0.05em"}},c.jsx("a",{href:"/",css:{color:"#000",textDecoration:"none"}},"UNPKG")))}function Ga(a){var b=a.packageName,e=a.packageVersion,g=a.availableVersions;a=a.filename;var d=[];if("/"===
a)d.push(b);else{var h="/browse/"+b+"@"+e;d.push(c.jsx(ja,{href:h+"/"},b));b=a.replace(/^\/+/,"").replace(/\/+$/,"").split("/");a=b.pop();b.forEach(function(a){h+="/"+a;d.push(c.jsx(ja,{href:h+"/"},a))});d.push(a)}return c.jsx("header",{css:{display:"flex",flexDirection:"row",alignItems:"center","@media (max-width: 700px)":{flexDirection:"column-reverse",alignItems:"flex-start"}}},c.jsx("h1",{css:{fontSize:"1.5rem",fontWeight:"normal",flex:1,wordBreak:"break-all"}},c.jsx("nav",null,d.map(function(a,
b,e){return c.jsx(t.Fragment,{key:b},0!==b&&c.jsx("span",{css:{paddingLeft:5,paddingRight:5}},"/"),b===e.length-1?c.jsx("strong",null,a):a)}))),c.jsx(Ha,{packageVersion:e,availableVersions:g,onChange:function(a){window.location.href=window.location.href.replace("@"+e,"@"+a)}}))}function Ha(a){var b=a.onChange;return c.jsx("p",{css:{marginLeft:20,"@media (max-width: 700px)":{marginLeft:0,marginBottom:0}}},c.jsx("label",null,"Version:"," ",c.jsx("select",{name:"version",defaultValue:a.packageVersion,
onChange:function(a){b&&b(a.target.value)},css:{appearance:"none",cursor:"pointer",padding:"4px 24px 4px 8px",fontWeight:600,fontSize:"0.9em",color:"#24292e",border:"1px solid rgba(27,31,35,.2)",borderRadius:3,backgroundColor:"#eff3f6",backgroundImage:"url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAKCAYAAAC9vt6cAAAAAXNSR0IArs4c6QAAARFJREFUKBVjZAACNS39RhBNKrh17WI9o4quoT3Dn78HSNUMUs/CzOTI/O7Vi4dCYpJ3/jP+92BkYGAlyiBGhm8MjIxJt65e3MQM0vDu9YvLYmISILYZELOBxHABRkaGr0yMzF23r12YDFIDNgDEePv65SEhEXENBkYGFSAXuyGMjF8Z/jOsvX3tYiFIDwgwQSgIaaijnvj/P8M5IO8HsjiY/f//D4b//88A1SQhywG9jQr09PS4v/1mPAeUUPzP8B8cJowMjL+Bqu6xMQmaXL164AuyDgwDQJLa2qYSP//9vARkCoMVMzK8YeVkNbh+9uxzMB+JwGoASF5Vx0jz/98/18BqmZi171w9D2EjaaYKEwAEK00XQLdJuwAAAABJRU5ErkJggg==)",
backgroundPosition:"right 8px center",backgroundRepeat:"no-repeat",backgroundSize:"auto 25%",":hover":{backgroundColor:"#e6ebf1",borderColor:"rgba(27,31,35,.35)"},":active":{backgroundColor:"#e9ecef",borderColor:"rgba(27,31,35,.35)",boxShadow:"inset 0 0.15em 0.3em rgba(27,31,35,.15)"}}},a.availableVersions.map(function(a){return c.jsx("option",{key:a,value:a},a)}))))}function Ia(a){var b=a.packageName,e=a.packageVersion;a=a.target;return"directory"===a.type?c.jsx(ca,{path:a.path,details:a.details}):
"file"===a.type?c.jsx(fa,{packageName:b,packageVersion:e,path:a.path,details:a.details}):null}function ka(a){var b=a.packageName,e=a.packageVersion,g=a.availableVersions;g=void 0===g?[]:g;var d=a.filename;a=a.target;return c.jsx(t.Fragment,null,c.jsx(c.Global,{styles:Ja}),c.jsx(c.Global,{styles:Ka}),c.jsx("div",{css:{flex:"1 0 auto"}},c.jsx("div",{css:{maxWidth:940,padding:"0 20px",margin:"0 auto"}},c.jsx(Fa,null)),c.jsx("div",{css:{maxWidth:940,padding:"0 20px",margin:"0 auto"}},c.jsx(Ga,{packageName:b,
packageVersion:e,availableVersions:g,filename:d})),c.jsx("div",{css:{maxWidth:940,padding:"0 20px",margin:"0 auto","@media (max-width: 700px)":{padding:0,margin:0}}},c.jsx(Ia,{packageName:b,packageVersion:e,target:a}))),c.jsx("footer",{css:{marginTop:"5rem",background:"black",color:"#aaa"}},c.jsx("div",{css:{maxWidth:940,padding:"10px 20px",margin:"0 auto",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between"}},c.jsx("p",null,c.jsx("span",null,"Build: ","a7ebffa")),
c.jsx("p",null,c.jsx("span",null,"\u00a9 ",(new Date).getFullYear()," UNPKG")),c.jsx("p",{css:{fontSize:"1.5rem"}},c.jsx("a",{href:"https://twitter.com/unpkg",css:{color:"#aaa",display:"inline-block",":hover":{color:"white"}}},c.jsx(za,null)),c.jsx("a",{href:"https://github.com/mjackson/unpkg",css:{color:"#aaa",display:"inline-block",":hover":{color:"white"},marginLeft:"1rem"}},c.jsx(Aa,null))))))}var la="default"in t?t["default"]:t;A=A&&A.hasOwnProperty("default")?A["default"]:A;var La="undefined"!==
typeof globalThis?globalThis:"undefined"!==typeof window?window:"undefined"!==typeof global?global:"undefined"!==typeof self?self:{},m=D(function(a,b){function c(a){if("object"===typeof a&&null!==a){var b=a.$$typeof;switch(b){case d:switch(a=a.type,a){case l:case f:case r:case m:case k:case v:return a;default:switch(a=a&&a.$$typeof,a){case p:case n:case q:return a;default:return b}}case x:case u:case h:return b}}}function g(a){return c(a)===f}Object.defineProperty(b,"__esModule",{value:!0});var d=
(a="function"===typeof Symbol&&Symbol.for)?Symbol.for("react.element"):60103,h=a?Symbol.for("react.portal"):60106,r=a?Symbol.for("react.fragment"):60107,k=a?Symbol.for("react.strict_mode"):60108,m=a?Symbol.for("react.profiler"):60114,q=a?Symbol.for("react.provider"):60109,p=a?Symbol.for("react.context"):60110,l=a?Symbol.for("react.async_mode"):60111,f=a?Symbol.for("react.concurrent_mode"):60111,n=a?Symbol.for("react.forward_ref"):60112,v=a?Symbol.for("react.suspense"):60113,u=a?Symbol.for("react.memo"):
60115,x=a?Symbol.for("react.lazy"):60116;b.typeOf=c;b.AsyncMode=l;b.ConcurrentMode=f;b.ContextConsumer=p;b.ContextProvider=q;b.Element=d;b.ForwardRef=n;b.Fragment=r;b.Lazy=x;b.Memo=u;b.Portal=h;b.Profiler=m;b.StrictMode=k;b.Suspense=v;b.isValidElementType=function(a){return"string"===typeof a||"function"===typeof a||a===r||a===f||a===m||a===k||a===v||"object"===typeof a&&null!==a&&(a.$$typeof===x||a.$$typeof===u||a.$$typeof===q||a.$$typeof===p||a.$$typeof===n)};b.isAsyncMode=function(a){return g(a)||
c(a)===l};b.isConcurrentMode=g;b.isContextConsumer=function(a){return c(a)===p};b.isContextProvider=function(a){return c(a)===q};b.isElement=function(a){return"object"===typeof a&&null!==a&&a.$$typeof===d};b.isForwardRef=function(a){return c(a)===n};b.isFragment=function(a){return c(a)===r};b.isLazy=function(a){return c(a)===x};b.isMemo=function(a){return c(a)===u};b.isPortal=function(a){return c(a)===h};b.isProfiler=function(a){return c(a)===m};b.isStrictMode=function(a){return c(a)===k};b.isSuspense=
function(a){return c(a)===v}});R(m);var na=D(function(a,b){(function(){function a(a){if("object"===typeof a&&null!==a){var b=a.$$typeof;switch(b){case h:switch(a=a.type,a){case f:case n:case k:case q:case m:case u:return a;default:switch(a=a&&a.$$typeof,a){case l:case v:case p:return a;default:return b}}case I:case x:case r:return b}}}function c(b){return a(b)===n}Object.defineProperty(b,"__esModule",{value:!0});var d="function"===typeof Symbol&&Symbol.for,h=d?Symbol.for("react.element"):60103,r=
d?Symbol.for("react.portal"):60106,k=d?Symbol.for("react.fragment"):60107,m=d?Symbol.for("react.strict_mode"):60108,q=d?Symbol.for("react.profiler"):60114,p=d?Symbol.for("react.provider"):60109,l=d?Symbol.for("react.context"):60110,f=d?Symbol.for("react.async_mode"):60111,n=d?Symbol.for("react.concurrent_mode"):60111,v=d?Symbol.for("react.forward_ref"):60112,u=d?Symbol.for("react.suspense"):60113,x=d?Symbol.for("react.memo"):60115,I=d?Symbol.for("react.lazy"):60116;d=function(){};var Ma=function(a){for(var b=
arguments.length,f=Array(1<b?b-1:0),c=1;c<b;c++)f[c-1]=arguments[c];var n=0;b="Warning: "+a.replace(/%s/g,function(){return f[n++]});"undefined"!==typeof console&&console.warn(b);try{throw Error(b);}catch(Xa){}},Na=d=function(a,b){if(void 0===b)throw Error("`lowPriorityWarning(condition, format, ...args)` requires a warning message argument");if(!a){for(var f=arguments.length,c=Array(2<f?f-2:0),n=2;n<f;n++)c[n-2]=arguments[n];Ma.apply(void 0,[b].concat(c))}},ma=!1;b.typeOf=a;b.AsyncMode=f;b.ConcurrentMode=
n;b.ContextConsumer=l;b.ContextProvider=p;b.Element=h;b.ForwardRef=v;b.Fragment=k;b.Lazy=I;b.Memo=x;b.Portal=r;b.Profiler=q;b.StrictMode=m;b.Suspense=u;b.isValidElementType=function(a){return"string"===typeof a||"function"===typeof a||a===k||a===n||a===q||a===m||a===u||"object"===typeof a&&null!==a&&(a.$$typeof===I||a.$$typeof===x||a.$$typeof===p||a.$$typeof===l||a.$$typeof===v)};b.isAsyncMode=function(b){ma||(ma=!0,Na(!1,"The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API."));
return c(b)||a(b)===f};b.isConcurrentMode=c;b.isContextConsumer=function(b){return a(b)===l};b.isContextProvider=function(b){return a(b)===p};b.isElement=function(a){return"object"===typeof a&&null!==a&&a.$$typeof===h};b.isForwardRef=function(b){return a(b)===v};b.isFragment=function(b){return a(b)===k};b.isLazy=function(b){return a(b)===I};b.isMemo=function(b){return a(b)===x};b.isPortal=function(b){return a(b)===r};b.isProfiler=function(b){return a(b)===q};b.isStrictMode=function(b){return a(b)===
m};b.isSuspense=function(b){return a(b)===u}})()});R(na);var oa=D(function(a){a.exports=na}),pa=Object.getOwnPropertySymbols,Oa=Object.prototype.hasOwnProperty,Pa=Object.prototype.propertyIsEnumerable,Qa=function(){try{if(!Object.assign)return!1;var a=new String("abc");a[5]="de";if("5"===Object.getOwnPropertyNames(a)[0])return!1;var b={};for(a=0;10>a;a++)b["_"+String.fromCharCode(a)]=a;if("**********"!==Object.getOwnPropertyNames(b).map(function(a){return b[a]}).join(""))return!1;var c={};"abcdefghijklmnopqrst".split("").forEach(function(a){c[a]=
a});return"abcdefghijklmnopqrst"!==Object.keys(Object.assign({},c)).join("")?!1:!0}catch(g){return!1}}()?Object.assign:function(a,b){if(null===a||void 0===a)throw new TypeError("Object.assign cannot be called with null or undefined");var c=Object(a);for(var g,d=1;d<arguments.length;d++){var h=Object(arguments[d]);for(var r in h)Oa.call(h,r)&&(c[r]=h[r]);if(pa){g=pa(h);for(var k=0;k<g.length;k++)Pa.call(h,g[k])&&(c[g[k]]=h[g[k]])}}return c},K=function(){},L={},ua=Function.call.bind(Object.prototype.hasOwnProperty);
K=function(a){a="Warning: "+a;"undefined"!==typeof console&&console.error(a);try{throw Error(a);}catch(b){}};J.resetWarningCache=function(){L={}};var Ra=Function.call.bind(Object.prototype.hasOwnProperty),C=function(){};C=function(a){a="Warning: "+a;"undefined"!==typeof console&&console.error(a);try{throw Error(a);}catch(b){}};var Sa=function(a,b){function c(a,b){return a===b?0!==a||1/a===1/b:a!==a&&b!==b}function g(a){this.message=a;this.stack=""}function d(a){function c(c,n,v,d,e,u,h){d=d||"<<anonymous>>";
u=u||v;if("SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"!==h){if(b)throw c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types"),c.name="Invariant Violation",c;"undefined"!==typeof console&&(h=d+":"+v,!f[h]&&3>l&&(C("You are manually calling a React.PropTypes validation function for the `"+u+"` prop on `"+d+"`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details."),
f[h]=!0,l++))}return null==n[v]?c?null===n[v]?new g("The "+e+" `"+u+"` is marked as required "+("in `"+d+"`, but its value is `null`.")):new g("The "+e+" `"+u+"` is marked as required in "+("`"+d+"`, but its value is `undefined`.")):null:a(n,v,d,e,u)}var f={},l=0,d=c.bind(null,!1);d.isRequired=c.bind(null,!0);return d}function h(a){return d(function(b,c,f,d,l,e){b=b[c];return k(b)!==a?(b=m(b),new g("Invalid "+d+" `"+l+"` of type "+("`"+b+"` supplied to `"+f+"`, expected ")+("`"+a+"`."))):null})}function r(b){switch(typeof b){case "number":case "string":case "undefined":return!0;
case "boolean":return!b;case "object":if(Array.isArray(b))return b.every(r);if(null===b||a(b))return!0;var c=b&&(p&&b[p]||b["@@iterator"]);var f="function"===typeof c?c:void 0;if(f)if(c=f.call(b),f!==b.entries)for(;!(b=c.next()).done;){if(!r(b.value))return!1}else for(;!(b=c.next()).done;){if((b=b.value)&&!r(b[1]))return!1}else return!1;return!0;default:return!1}}function k(a){var b=typeof a;return Array.isArray(a)?"array":a instanceof RegExp?"object":"symbol"===b||a&&("Symbol"===a["@@toStringTag"]||
"function"===typeof Symbol&&a instanceof Symbol)?"symbol":b}function m(a){if("undefined"===typeof a||null===a)return""+a;var b=k(a);if("object"===b){if(a instanceof Date)return"date";if(a instanceof RegExp)return"regexp"}return b}function q(a){a=m(a);switch(a){case "array":case "object":return"an "+a;case "boolean":case "date":case "regexp":return"a "+a;default:return a}}var p="function"===typeof Symbol&&Symbol.iterator,l={array:h("array"),bool:h("boolean"),func:h("function"),number:h("number"),object:h("object"),
string:h("string"),symbol:h("symbol"),any:d(G),arrayOf:function(a){return d(function(b,c,f,d,l){if("function"!==typeof a)return new g("Property `"+l+"` of component `"+f+"` has invalid PropType notation inside arrayOf.");b=b[c];if(!Array.isArray(b))return b=k(b),new g("Invalid "+d+" `"+l+"` of type "+("`"+b+"` supplied to `"+f+"`, expected an array."));for(c=0;c<b.length;c++){var n=a(b,c,f,d,l+"["+c+"]","SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");if(n instanceof Error)return n}return null})},
element:function(){return d(function(b,c,d,l,e){b=b[c];return a(b)?null:(b=k(b),new g("Invalid "+l+" `"+e+"` of type "+("`"+b+"` supplied to `"+d+"`, expected a single ReactElement.")))})}(),elementType:function(){return d(function(a,b,c,d,l){a=a[b];return oa.isValidElementType(a)?null:(a=k(a),new g("Invalid "+d+" `"+l+"` of type "+("`"+a+"` supplied to `"+c+"`, expected a single ReactElement type.")))})}(),instanceOf:function(a){return d(function(b,c,f,d,l){if(!(b[c]instanceof a)){var n=a.name||
"<<anonymous>>";b=b[c];b=b.constructor&&b.constructor.name?b.constructor.name:"<<anonymous>>";return new g("Invalid "+d+" `"+l+"` of type "+("`"+b+"` supplied to `"+f+"`, expected ")+("instance of `"+n+"`."))}return null})},node:function(){return d(function(a,b,c,d,l){return r(a[b])?null:new g("Invalid "+d+" `"+l+"` supplied to "+("`"+c+"`, expected a ReactNode."))})}(),objectOf:function(a){return d(function(b,c,f,d,l){if("function"!==typeof a)return new g("Property `"+l+"` of component `"+f+"` has invalid PropType notation inside objectOf.");
b=b[c];c=k(b);if("object"!==c)return new g("Invalid "+d+" `"+l+"` of type "+("`"+c+"` supplied to `"+f+"`, expected an object."));for(var n in b)if(Ra(b,n)&&(c=a(b,n,f,d,l+"."+n,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"),c instanceof Error))return c;return null})},oneOf:function(a){return Array.isArray(a)?d(function(b,f,d,l,e){b=b[f];for(f=0;f<a.length;f++)if(c(b,a[f]))return null;f=JSON.stringify(a,function(a,b){return"symbol"===m(b)?String(b):b});return new g("Invalid "+l+" `"+e+"` of value `"+
String(b)+"` "+("supplied to `"+d+"`, expected one of "+f+"."))}):(1<arguments.length?C("Invalid arguments supplied to oneOf, expected an array, got "+arguments.length+" arguments. A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z])."):C("Invalid argument supplied to oneOf, expected an array."),G)},oneOfType:function(a){if(!Array.isArray(a))return C("Invalid argument supplied to oneOfType, expected an instance of array."),G;for(var b=0;b<a.length;b++){var c=a[b];if("function"!==
typeof c)return C("Invalid argument supplied to oneOfType. Expected an array of check functions, but received "+q(c)+" at index "+b+"."),G}return d(function(b,c,f,d,l){for(var e=0;e<a.length;e++)if(null==(0,a[e])(b,c,f,d,l,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"))return null;return new g("Invalid "+d+" `"+l+"` supplied to "+("`"+f+"`."))})},shape:function(a){return d(function(b,c,d,l,f){b=b[c];c=k(b);if("object"!==c)return new g("Invalid "+l+" `"+f+"` of type `"+c+"` "+("supplied to `"+d+"`, expected `object`."));
for(var e in a)if(c=a[e])if(c=c(b,e,d,l,f+"."+e,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"))return c;return null})},exact:function(a){return d(function(b,c,d,l,f){var e=b[c],n=k(e);if("object"!==n)return new g("Invalid "+l+" `"+f+"` of type `"+n+"` "+("supplied to `"+d+"`, expected `object`."));n=Qa({},b[c],a);for(var h in n){n=a[h];if(!n)return new g("Invalid "+l+" `"+f+"` key `"+h+"` supplied to `"+d+"`.\nBad object: "+JSON.stringify(b[c],null,"  ")+"\nValid keys: "+JSON.stringify(Object.keys(a),
null,"  "));if(n=n(e,h,d,l,f+"."+h,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"))return n}return null})}};g.prototype=Error.prototype;l.checkPropTypes=J;l.resetWarningCache=J.resetWarningCache;return l.PropTypes=l};m=D(function(a){a.exports=Sa(oa.isElement,!0)});var Ta=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b],g;for(g in c)Object.prototype.hasOwnProperty.call(c,g)&&(a[g]=c[g])}return a},Ua={border:0,clip:"rect(0 0 0 0)",height:"1px",width:"1px",margin:"-1px",
padding:0,overflow:"hidden",position:"absolute"},H=function(a){return la.createElement("div",Ta({style:Ua},a))},qa=D(function(a){(function(b,c){a.exports=c()})(La,function(){function a(a){if(!a)return!0;if(!d(a)||0!==a.length)for(var b in a)if(q.call(a,b))return!1;return!0}function c(a){return"number"===typeof a||"[object Number]"===t.call(a)}function g(a){return"string"===typeof a||"[object String]"===t.call(a)}function d(a){return"object"===typeof a&&"number"===typeof a.length&&"[object Array]"===
t.call(a)}function h(a){var b=parseInt(a);return b.toString()===a?b:a}function m(b,d,e,k){c(d)&&(d=[d]);if(a(d))return b;if(g(d))return m(b,d.split("."),e,k);var f=h(d[0]);if(1===d.length)return d=b[f],void 0!==d&&k||(b[f]=e),d;void 0===b[f]&&(c(f)?b[f]=[]:b[f]={});return m(b[f],d.slice(1),e,k)}function k(b,f){c(f)&&(f=[f]);if(!a(b)){if(a(f))return b;if(g(f))return k(b,f.split("."));var e=h(f[0]),l=b[e];if(1===f.length)void 0!==l&&(d(b)?b.splice(e,1):delete b[e]);else if(void 0!==b[e])return k(b[e],
f.slice(1));return b}}var t=Object.prototype.toString,q=Object.prototype.hasOwnProperty,p={ensureExists:function(a,b,c){return m(a,b,c,!0)},set:function(a,b,c,d){return m(a,b,c,d)},insert:function(a,b,c,e){var f=p.get(a,b);e=~~e;d(f)||(f=[],p.set(a,b,f));f.splice(e,0,c)},empty:function(b,f){if(a(f))return b;if(!a(b)){var e,h;if(!(e=p.get(b,f)))return b;if(g(e))return p.set(b,f,"");if("boolean"===typeof e||"[object Boolean]"===t.call(e))return p.set(b,f,!1);if(c(e))return p.set(b,f,0);if(d(e))e.length=
0;else if("object"===typeof e&&"[object Object]"===t.call(e))for(h in e)q.call(e,h)&&delete e[h];else return p.set(b,f,null)}},push:function(a,b){var c=p.get(a,b);d(c)||(c=[],p.set(a,b,c));c.push.apply(c,Array.prototype.slice.call(arguments,2))},coalesce:function(a,b,c){for(var d,e=0,f=b.length;e<f;e++)if(void 0!==(d=p.get(a,b[e])))return d;return c},get:function(b,d,e){c(d)&&(d=[d]);if(a(d))return b;if(a(b))return e;if(g(d))return p.get(b,d.split("."),e);var f=h(d[0]);return 1===d.length?void 0===
b[f]?e:b[f]:p.get(b[f],d.slice(1),e)},del:function(a,b){return k(a,b)}};return p})});var ra=function(a){return function(b){return typeof b===a}};var Va=function(a,b){var c=1,g=b||function(a,b){return b};"-"===a[0]&&(c=-1,a=a.substr(1));return function(b,e){var d;b=g(a,qa.get(b,a));e=g(a,qa.get(e,a));b<e&&(d=-1);b>e&&(d=1);b===e&&(d=0);return d*c}};var da=function(){var a=Array.prototype.slice.call(arguments),b=a.filter(ra("string")),c=a.filter(ra("function"))[0];return function(a,d){for(var e=b.length,
g=0,k=0;0===g&&k<e;)g=Va(b[k],c)(a,d),k++;return g}};let sa="B kB MB GB TB PB EB ZB YB".split(" "),ta=(a,b)=>{let c=a;"string"===typeof b?c=a.toLocaleString(b):!0===b&&(c=a.toLocaleString());return c};var ea=(a,b)=>{if(!Number.isFinite(a))throw new TypeError(`Expected a finite number, got ${typeof a}: ${a}`);b=Object.assign({},b);if(b.signed&&0===a)return" 0 B";var c=0>a;let g=c?"-":b.signed?"+":"";c&&(a=-a);if(1>a)return a=ta(a,b.locale),g+a+" B";c=Math.min(Math.floor(Math.log10(a)/3),sa.length-
1);a=Number((a/Math.pow(1E3,c)).toPrecision(3));a=ta(a,b.locale);return g+a+" "+sa[c]},W={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},V=t.createContext&&t.createContext(W),z=function(){z=Object.assign||function(a){for(var b,c=1,g=arguments.length;c<g;c++){b=arguments[c];for(var d in b)Object.prototype.hasOwnProperty.call(b,d)&&(a[d]=b[d])}return a};return z.apply(this,arguments)},Y=function(a){return E({tag:"svg",attr:{viewBox:"0 0 12 16"},child:[{tag:"path",attr:{fillRule:"evenodd",
d:"M8.5 1H1c-.55 0-1 .45-1 1v12c0 .55.45 1 1 1h10c.55 0 1-.45 1-1V4.5L8.5 1zM11 14H1V2h7l3 3v9zM5 6.98L3.5 8.5 5 10l-.5 1L2 8.5 4.5 6l.5.98zM7.5 6L10 8.5 7.5 11l-.5-.98L8.5 8.5 7 7l.5-1z"}}]})(a)};Y.displayName="GoFileCode";var Z=function(a){return E({tag:"svg",attr:{viewBox:"0 0 14 16"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M13 4H7V3c0-.66-.31-1-1-1H1c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1V5c0-.55-.45-1-1-1zM6 4H1V3h5v1z"}}]})(a)};Z.displayName="GoFileDirectory";var X=function(a){return E({tag:"svg",
attr:{viewBox:"0 0 12 16"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M6 5H2V4h4v1zM2 8h7V7H2v1zm0 2h7V9H2v1zm0 2h7v-1H2v1zm10-7.5V14c0 .55-.45 1-1 1H1c-.55 0-1-.45-1-1V2c0-.55.45-1 1-1h7.5L12 4.5zM11 5L8 2H1v12h10V5z"}}]})(a)};X.displayName="GoFile";var ba=function(a){return E({tag:"svg",attr:{viewBox:"0 0 496 512"},child:[{tag:"path",attr:{d:"M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z"}}]})(a)};
ba.displayName="FaGithub";var aa=function(a){return E({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"}}]})(a)};
aa.displayName="FaTwitter";var N={color:"#0076ff",textDecoration:"none",":hover":{textDecoration:"underline"}},y={paddingTop:6,paddingRight:3,paddingBottom:6,paddingLeft:3,borderTop:"1px solid #eaecef"},M=w({},y,{color:"#424242",width:17,paddingRight:2,paddingLeft:10,"@media (max-width: 700px)":{paddingLeft:20}}),O=w({},y,{textAlign:"right",paddingRight:10,"@media (max-width: 700px)":{paddingRight:20}});ca.propTypes={path:m.string.isRequired,details:m.objectOf(m.shape({path:m.string.isRequired,type:m.oneOf(["directory",
"file"]).isRequired,contentType:m.string,integrity:m.string,size:m.number})).isRequired};fa.propTypes={path:m.string.isRequired,details:m.shape({contentType:m.string.isRequired,highlights:m.arrayOf(m.string),uri:m.string,integrity:m.string.isRequired,language:m.string.isRequired,size:m.number.isRequired}).isRequired};var Ja=c.css(ia(),'\nfont-family: -apple-system,\n  BlinkMacSystemFont,\n  "Segoe UI",\n  "Roboto",\n  "Oxygen",\n  "Ubuntu",\n  "Cantarell",\n  "Fira Sans",\n  "Droid Sans",\n  "Helvetica Neue",\n  sans-serif;\n',
"\nfont-family: Menlo,\n  Monaco,\n  Lucida Console,\n  Liberation Mono,\n  DejaVu Sans Mono,\n  Bitstream Vera Sans Mono,\n  Courier New,\n  monospace;\n"),Ka=c.css(ha()),Wa=m.shape({path:m.string.isRequired,type:m.oneOf(["directory","file"]).isRequired,details:m.object.isRequired});ka.propTypes={packageName:m.string.isRequired,packageVersion:m.string.isRequired,availableVersions:m.arrayOf(m.string),filename:m.string.isRequired,target:Wa.isRequired};A.hydrate(la.createElement(ka,window.__DATA__||
{}),document.getElementById("root"))})(React,ReactDOM,emotionCore);
</script></body></html>