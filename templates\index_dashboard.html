<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Accounts Dashboard</title>
    <!-- Bootstrap 5.3 CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" xintegrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <!-- Lucide Icons CDN (for icons) -->
    <script src="https://unpkg.com/lucide@latest"></script>
    <!-- Google Fonts - Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            min-height: 100vh; /* Equivalent to min-h-screen */
            display: flex;
            flex-direction: column;
            background: linear-gradient(to bottom right, #e0f2fe, #dbeafe); /* Tailwind from-indigo-50 to-blue-100 */
        }
        /* Animations */
        .animate-fade-in {
            animation: fadeIn 0.5s ease-out forwards;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .animate-slide-down {
            animation: slideDown 0.3s ease-out forwards;
        }
        @keyframes slideDown {
            from { transform: translateY(-100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        /* Page content display for JS switching */
        .page-content {
            display: none;
        }
        .page-content.active {
            display: flex; /* Adjust to block or flex based on internal content */
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        /* Mobile menu overlay */
        .mobile-menu-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.95);
            z-index: 20;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.5rem;
            animation: slideDown 0.3s ease-out forwards;
        }
    </style>
</head>
<body class="text-secondary-emphasis"> <!-- text-gray-800 equivalent -->

    <!-- Header -->
    <header class="bg-white shadow py-3 px-4 d-flex justify-content-between align-items-center position-relative z-1"> <!-- p-6, shadow-md, flex, justify-between, items-center -->
        <div class="d-flex align-items-center">
            <i data-lucide="layout-dashboard" class="text-primary fs-3 me-3"></i> <!-- text-indigo-600, w-8 h-8, mr-3 -->
            <h1 class="fs-4 fw-bold text-primary">Project Accounts</h1> <!-- text-2xl, font-bold, text-indigo-800 -->
        </div>

        <!-- Desktop Navigation -->
        <nav id="desktop-nav" class="d-none d-md-flex space-x-4"> <!-- hidden md:flex space-x-6 -->
            <button onclick="navigateTo('home')" class="btn btn-link text-decoration-none text-secondary hover-primary fw-medium transition-colors"> <!-- text-gray-700 hover:text-indigo-600 font-medium transition duration-200 -->
                <i data-lucide="home" class="me-2 icon-sm"></i> Home <!-- mr-2 h-5 w-5 -->
            </button>
            <span id="auth-buttons-desktop">
                <!-- Login/Signup buttons will be dynamically added here -->
            </span>
            <span id="dashboard-button-desktop" class="d-none"> <!-- hidden -->
                 <button onclick="navigateTo('dashboard')" class="btn btn-link text-decoration-none text-primary fw-bold transition-colors"> <!-- text-indigo-600 font-bold transition duration-200 -->
                    <i data-lucide="layout-dashboard" class="me-2 icon-sm"></i> Dashboard <!-- mr-2 h-5 w-5 -->
                </button>
            </span>
        </nav>

        <!-- Mobile Menu Button -->
        <div class="d-md-none"> <!-- md:hidden -->
            <button id="mobile-menu-button" class="btn btn-link text-decoration-none text-secondary focus-ring"> <!-- text-gray-700 hover:text-indigo-600 focus:outline-none -->
                <i data-lucide="menu" class="icon-lg"></i> <!-- h-7 w-7 -->
            </button>
        </div>
    </header>

    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu-overlay" class="mobile-menu-overlay d-none"> <!-- hidden -->
        <button id="mobile-menu-close-button" class="btn btn-link text-decoration-none text-secondary position-absolute top-0 end-0 mt-3 me-3 focus-ring"> <!-- absolute top-6 right-6 text-gray-700 hover:text-red-500 focus:outline-none -->
            <i data-lucide="x" class="icon-xl"></i> <!-- h-9 w-9 -->
        </button>
        <button onclick="navigateTo('home')" class="btn btn-link text-decoration-none text-secondary fs-2 fw-bold py-3 transition-colors"> <!-- flex items-center text-3xl text-gray-700 hover:text-indigo-600 font-bold transition duration-200 py-3 -->
            <i data-lucide="home" class="me-4 icon-xl"></i> Home <!-- mr-4 h-8 w-8 -->
        </button>
        <span id="auth-buttons-mobile">
            <!-- Login/Signup buttons will be dynamically added here -->
        </span>
        <span id="dashboard-button-mobile" class="d-none"> <!-- hidden -->
             <button onclick="navigateTo('dashboard')" class="btn btn-link text-decoration-none text-primary fs-2 fw-bold py-3 transition-colors"> <!-- flex items-center text-3xl text-indigo-600 font-bold transition duration-200 py-3 -->
                <i data-lucide="layout-dashboard" class="me-4 icon-xl"></i> Dashboard <!-- mr-4 h-8 w-8 -->
            </button>
        </span>
    </div>

    <!-- Main Content Area -->
    <main class="flex-grow-1 d-flex justify-content-center align-items-center p-3 p-sm-4 position-relative"> <!-- flex-grow flex items-center justify-center p-6 sm:p-8 relative -->
        <!-- Home Page -->
        <div id="home-page" class="page-content bg-white rounded-3 shadow p-4 text-center max-w-xl mx-auto animate-fade-in"> <!-- p-8 bg-white rounded-xl shadow-lg animate-fade-in text-center max-w-2xl mx-auto -->
            <h2 class="fs-1 fw-bold text-primary mb-3">Welcome to Your Project Accounts Manager</h2> <!-- text-4xl font-bold text-indigo-800 mb-4 -->
            <p class="fs-5 text-secondary mb-4"> <!-- text-lg text-gray-700 mb-6 -->
                Simplify your project financials, track expenses, manage budgets, and gain clear insights into your project's economic health.
            </p>
            <div class="d-grid gap-3 d-md-flex justify-content-md-center"> <!-- space-y-4 md:space-x-4 md:space-y-0 flex flex-col md:flex-row items-center -->
                <button onclick="navigateTo('signup')" class="btn btn-primary btn-lg rounded-pill shadow-sm transition-transform"> <!-- bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105 flex items-center justify-center w-full md:w-auto -->
                    <i data-lucide="user-plus" class="me-2 icon-base"></i> Get Started - Sign Up
                </button>
                <button onclick="navigateTo('login')" class="btn btn-outline-secondary btn-lg rounded-pill shadow-sm transition-transform"> <!-- bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 px-6 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105 flex items-center justify-center w-full md:w-auto -->
                    <i data-lucide="log-in" class="me-2 icon-base"></i> Already a Member? Log In
                </button>
            </div>
        </div>

        <!-- Login Page -->
        <div id="login-page" class="page-content bg-white rounded-3 shadow p-4 max-w-sm mx-auto animate-fade-in"> <!-- p-8 bg-white rounded-xl shadow-lg animate-fade-in max-w-md mx-auto -->
            <h2 class="fs-2 fw-bold text-primary mb-4 text-center">Login to Your Account</h2> <!-- text-3xl font-bold text-indigo-700 mb-6 -->
            <form id="login-form" class="w-100 space-y-4"> <!-- w-full space-y-5 -->
                <div class="mb-3">
                    <label for="login-email" class="form-label fw-semibold">Email:</label> <!-- block text-gray-700 text-sm font-semibold mb-2 -->
                    <input type="email" id="login-email" class="form-control rounded-pill py-2 px-3" placeholder="<EMAIL>" required> <!-- shadow appearance-none border rounded-lg w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-indigo-500 transition duration-200 -->
                </div>
                <div class="mb-3">
                    <label for="login-password" class="form-label fw-semibold">Password:</label>
                    <input type="password" id="login-password" class="form-control rounded-pill py-2 px-3" placeholder="********" required>
                </div>
                <button type="submit" class="btn btn-primary btn-lg rounded-pill shadow-sm w-100 transition-transform"> <!-- bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md w-full transition duration-300 ease-in-out transform hover:scale-105 flex items-center justify-center -->
                    <i data-lucide="log-in" class="me-2 icon-base"></i> Log In
                </button>
                <p class="text-center text-secondary text-sm mt-3"> <!-- text-center text-gray-600 text-sm mt-4 -->
                    Don't have an account?
                    <button type="button" onclick="navigateTo('signup')" class="btn btn-link text-decoration-none text-primary fw-medium p-0"> <!-- text-indigo-600 hover:underline font-medium -->
                        Sign Up
                    </button>
                </p>
            </form>
        </div>

        <!-- Signup Page -->
        <div id="signup-page" class="page-content bg-white rounded-3 shadow p-4 max-w-sm mx-auto animate-fade-in"> <!-- p-8 bg-white rounded-xl shadow-lg animate-fade-in max-w-md mx-auto -->
            <h2 class="fs-2 fw-bold text-primary mb-4 text-center">Create Your Account</h2> <!-- text-3xl font-bold text-indigo-700 mb-6 -->
            <form id="signup-form" class="w-100 space-y-4"> <!-- w-full space-y-5 -->
                <div class="mb-3">
                    <label for="signup-full-name" class="form-label fw-semibold">Full Name:</label>
                    <input type="text" id="signup-full-name" class="form-control rounded-pill py-2 px-3" placeholder="John Doe" required>
                </div>
                <div class="mb-3">
                    <label for="signup-email" class="form-label fw-semibold">Email:</label>
                    <input type="email" id="signup-email" class="form-control rounded-pill py-2 px-3" placeholder="<EMAIL>" required>
                </div>
                <div class="mb-3">
                    <label for="signup-password" class="form-label fw-semibold">Password:</label>
                    <input type="password" id="signup-password" class="form-control rounded-pill py-2 px-3" placeholder="********" required>
                </div>
                <button type="submit" class="btn btn-primary btn-lg rounded-pill shadow-sm w-100 transition-transform"> <!-- bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md w-full transition duration-300 ease-in-out transform hover:scale-105 flex items-center justify-center -->
                    <i data-lucide="user-plus" class="me-2 icon-base"></i> Sign Up
                </button>
                <p class="text-center text-secondary text-sm mt-3">
                    Already have an account?
                    <button type="button" onclick="navigateTo('login')" class="btn btn-link text-decoration-none text-primary fw-medium p-0">
                        Log In
                    </button>
                </p>
            </form>
        </div>

        <!-- Dashboard Page -->
        <div id="dashboard-page" class="page-content bg-white rounded-3 shadow p-4 text-center max-w-xl mx-auto animate-fade-in"> <!-- p-8 bg-white rounded-xl shadow-lg animate-fade-in text-center max-w-2xl mx-auto -->
            <i data-lucide="layout-dashboard" class="text-primary icon-xl mb-3"></i> <!-- text-indigo-600 w-16 h-16 mb-4 -->
            <h2 class="fs-1 fw-bold text-primary mb-3">Welcome to Your Dashboard!</h2> <!-- text-4xl font-bold text-indigo-800 mb-4 -->
            <p class="fs-5 text-secondary mb-4"> <!-- text-lg text-gray-700 mb-6 -->
                This is where you'll manage your project accounts. Implement your Django backend APIs here to fetch and display actual project data.
            </p>
            <button onclick="logout()" class="btn btn-danger btn-lg rounded-pill shadow-sm transition-transform"> <!-- bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-6 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105 -->
                Log Out
            </button>
        </div>
    </main>

    <!-- Footer -->
    <footer class="p-3 text-center text-secondary bg-light shadow-top mt-auto"> <!-- p-4 text-center text-gray-600 text-sm bg-white shadow-inner mt-auto -->
        &copy; <span id="current-year"></span> Project Accounts Management System. All rights reserved.
    </footer>

    <!-- Bootstrap 5.3 JavaScript Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" xintegrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7R7p" crossorigin="anonymous"></script>

    <script>
        let isLoggedIn = false;
        let currentPage = 'home'; // Initial page

        const pageElements = {
            'home': document.getElementById('home-page'),
            'login': document.getElementById('login-page'),
            'signup': document.getElementById('signup-page'),
            'dashboard': document.getElementById('dashboard-page')
        };

        const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenuCloseButton = document.getElementById('mobile-menu-close-button');

        const authButtonsDesktop = document.getElementById('auth-buttons-desktop');
        const dashboardButtonDesktop = document.getElementById('dashboard-button-desktop');
        const authButtonsMobile = document.getElementById('auth-buttons-mobile');
        const dashboardButtonMobile = document.getElementById('dashboard-button-mobile');

        // Helper for custom icon sizing
        document.addEventListener('DOMContentLoaded', () => {
            // Define icon sizes to match Tailwind equivalents
            const iconSizes = {
                'icon-sm': '1.25rem', // h-5 w-5
                'icon-base': '1.25rem', // h-5 w-5
                'icon-lg': '1.75rem', // h-7 w-7
                'icon-xl': '2.25rem', // h-9 w-9, w-16 h-16 (for large dashboard icon)
                'fs-3': '2rem', // for logo icon
            };

            // Apply custom sizes to Lucide icons
            document.querySelectorAll('[data-lucide]').forEach(iconElement => {
                for (const className in iconSizes) {
                    if (iconElement.classList.contains(className)) {
                        iconElement.style.width = iconSizes[className];
                        iconElement.style.height = iconSizes[className];
                        break;
                    }
                }
            });
            lucide.createIcons();
        });


        // Function to render navigation buttons based on login status
        function renderNavButtons() {
            authButtonsDesktop.innerHTML = '';
            authButtonsMobile.innerHTML = '';

            if (isLoggedIn) {
                dashboardButtonDesktop.classList.remove('d-none'); // Show
                dashboardButtonMobile.classList.remove('d-none'); // Show
            } else {
                dashboardButtonDesktop.classList.add('d-none'); // Hide
                dashboardButtonMobile.classList.add('d-none'); // Hide

                authButtonsDesktop.innerHTML = `
                    <button onclick="navigateTo('login')" class="btn btn-link text-decoration-none text-secondary hover-primary fw-medium transition-colors">
                        <i data-lucide="log-in" class="me-2 icon-sm"></i> Login
                    </button>
                    <button onclick="navigateTo('signup')" class="btn btn-link text-decoration-none text-secondary hover-primary fw-medium transition-colors">
                        <i data-lucide="user-plus" class="me-2 icon-sm"></i> Sign Up
                    </button>
                `;
                 authButtonsMobile.innerHTML = `
                    <button onclick="navigateTo('login')" class="btn btn-link text-decoration-none text-secondary fs-2 fw-bold py-3 transition-colors">
                        <i data-lucide="log-in" class="me-4 icon-xl"></i> Login
                    </button>
                    <button onclick="navigateTo('signup')" class="btn btn-link text-decoration-none text-secondary fs-2 fw-bold py-3 transition-colors">
                        <i data-lucide="user-plus" class="me-4 icon-xl"></i> Sign Up
                    </button>
                `;
            }
            // Re-render lucide icons after injecting new HTML
            lucide.createIcons();
        }

        // Function to navigate between pages
        function navigateTo(pageId) {
            // Hide all pages
            for (const id in pageElements) {
                pageElements[id].classList.remove('active');
            }

            // Show the requested page
            let targetPage = pageId;
            if (pageId === 'dashboard' && !isLoggedIn) {
                targetPage = 'login'; // Redirect to login if trying to access dashboard while logged out
            }
            pageElements[targetPage].classList.add('active');
            currentPage = targetPage; // Update current page state

            // Close mobile menu if open
            mobileMenuOverlay.classList.add('d-none'); // Hide
            document.body.style.overflow = ''; // Re-enable scrolling
        }

        // --- Event Listeners for Forms and Buttons ---

        // Login Form Submission
        document.getElementById('login-form').addEventListener('submit', function(event) {
            event.preventDefault();
            // Dummy login logic:
            isLoggedIn = true;
            navigateTo('dashboard');
            renderNavButtons(); // Update nav after login
        });

        // Signup Form Submission
        document.getElementById('signup-form').addEventListener('submit', function(event) {
            event.preventDefault();
            // Dummy signup logic:
            isLoggedIn = true;
            navigateTo('dashboard');
            renderNavButtons(); // Update nav after signup
        });

        // Logout function
        function logout() {
            isLoggedIn = false;
            navigateTo('home');
            renderNavButtons(); // Update nav after logout
        }

        // Mobile menu toggle
        mobileMenuButton.addEventListener('click', () => {
            mobileMenuOverlay.classList.remove('d-none'); // Show
            document.body.style.overflow = 'hidden'; // Disable scrolling when menu is open
        });
        mobileMenuCloseButton.addEventListener('click', () => {
            mobileMenuOverlay.classList.add('d-none'); // Hide
            document.body.style.overflow = ''; // Re-enable scrolling
        });

        // Initial setup on page load
        document.addEventListener('DOMContentLoaded', () => {
            // Set current year in footer
            document.getElementById('current-year').textContent = new Date().getFullYear();
            // Render initial navigation buttons
            renderNavButtons();
            // Navigate to the default page
            navigateTo('home');
            // Initialize Lucide icons on page load - moved to a helper
            // lucide.createIcons(); // Removed here, now handled by the custom helper for sizing
        });
    </script>
</body>
</html>
