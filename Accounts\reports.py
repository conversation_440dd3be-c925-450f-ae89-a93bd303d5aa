from datetime import datetime
import io
import numpy as np
from PIL import Image, ImageFile
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from django.contrib import messages
from django.core.mail import EmailMessage
from django.conf import settings 
from django.db.models import (
    F,
    Sum,
    Count,
    Q,
    Case,
    When,
    Value,
    ExpressionWrapper,
    Func,
    DateTimeField,
    DateField,
    IntegerField,
    CharField,
    DecimalField,
    Subquery,
    OuterRef,
    DurationField,
    FloatField,
    )

from django.db.models.functions import Cast, Round, Concat, Coalesce, Extract, Trunc
from django.http import HttpResponse, HttpResponseRedirect
from django.template.response import TemplateResponse
from django.urls import reverse
from django.template.loader import render_to_string
from weasyprint import HTML, CSS, default_url_fetcher
import tempfile
import calendar
import mimetypes
from pathlib import Path
from urllib.parse import urlparse
from django.contrib.staticfiles.finders import find
from django.core.files.storage import default_storage
from django.urls import get_script_prefix
from .models import *
from .decorators import time_of_execution

from CONSTRUCTION_PROJECT.settings.context_processors import company_info_settings
from .utils import expenditure_summary_qs, expenditure_details_qs
company_info = company_info_settings()

def url_fetcher(url, *args, **kwargs):
    # load file:// paths directly from disk
    if url.startswith('file:'):
        mime_type, encoding = mimetypes.guess_type(url)
        url_path = urlparse(url).path
        data = {
            'mime_type': mime_type,
            'encoding': encoding,
            'filename': Path(url_path).name,
        }

        default_media_url = settings.MEDIA_URL in ('', get_script_prefix())
        if not default_media_url and url_path.startswith(settings.MEDIA_URL):
            media_root = settings.MEDIA_ROOT
            if isinstance(settings.MEDIA_ROOT, Path):
                media_root = f'{settings.MEDIA_ROOT}/'
            path = url_path.replace(settings.MEDIA_URL, media_root, 1)
            data['file_obj'] = default_storage.open(path)
            return data

        elif settings.STATIC_URL and url_path.startswith(settings.STATIC_URL):
            path = url_path.replace(settings.STATIC_URL, '', 1)
            data['file_obj'] = open(find(path), 'rb')
            return data
    # fall back to weasyprint default fetcher
    return default_url_fetcher(url, *args, **kwargs)

@login_required
def contractorListReport(request):
    response = HttpResponse(content_type="application/pdf")
    response["Content-Disposition"] = (
        f"filename=Contractor List_{str(datetime.now().strftime('%Y%m%d'))}.pdf"
    )
    response["Content-Transfer-Encoding"] = "binary"

    # For Data -------------
    qs = Contractor.objects.all().order_by("IsArchive","contractor")
    isarchive = request.GET.get("isarchive")
    if isarchive:
        qs = qs.filter(IsArchive=isarchive)

    # For Data End -------------
    data = {}
    data = data | {"contractor": qs}
    data["heading"] = "Contractor List"
    data = data | company_info
    # For Data End -------------

    # base_url=request.build_absolute_uri()
    html_string = render_to_string(
        "accounts/reports/contractor_list.html", {"data": data}
    )
    html = HTML(string=html_string,base_url='/', url_fetcher=url_fetcher)
    resultfile = html.write_pdf()

    with tempfile.NamedTemporaryFile(delete=True) as output:
        output.write(resultfile)
        output.flush()
        output.seek(0)
        response.write(output.read())
    return response


@login_required
def contractorDetails(request, pk):
    contractor_table = Contractor.objects.values().get(id=pk)
    contractor_table["avatar"]=Contractor.objects.get(id=pk).avatar
    response = HttpResponse(content_type="application/pdf")
    response["Content-Disposition"] = (
        f"filename={contractor_table['contractor']}_{str(datetime.now().strftime('%Y%m%d'))}.pdf"
    )
    response["Content-Transfer-Encoding"] = "binary"
    # For Data -------------
    data = {}
    data = data | contractor_table
    data = data | company_info
    # For Data End -------------
    html_string = render_to_string(
        "accounts/reports/contractor_details.html", {"data": data}
    )
    html = HTML(string=html_string, base_url='/', url_fetcher=url_fetcher)
    resultfile = html.write_pdf()

    with tempfile.NamedTemporaryFile(delete=True) as output:
        output.write(resultfile)
        output.flush()
        output.seek(0)
        response.write(output.read())
    return response

    
@login_required
def shareholderDetails(request, pk):
    shareholder_table = Shareholder.objects.values().get(id=pk)
    response = HttpResponse(content_type="application/pdf")
    response["Content-Disposition"] = (
        f"filename={shareholder_table['id']}_{str(datetime.now().strftime('%Y%m%d'))}_{shareholder_table['shareholderName'].replace('.', '')}.pdf"
        )
    response["Content-Transfer-Encoding"] = "binary"
    # For Data -------------
    data = {}
    # shareholder_table = Shareholder.objects.get(id=pk)
    
    shareholder_table["avatar"]=Shareholder.objects.get(id=pk).avatar
 
    data = data | shareholder_table
    data = data | {'data':company_info}
    # For Data End -------------
    html_string = render_to_string(
        "accounts/reports/shareholder_details.html", data
    )
    html = HTML(string=html_string, base_url='/', url_fetcher=url_fetcher)

    resultfile = html.write_pdf()

    with tempfile.NamedTemporaryFile(delete=True) as output:
        output.write(resultfile)
        output.flush()
        output.seek(0)
        response.write(output.read())
    return response


@login_required
@time_of_execution
def expenditureSummaryReport(request):
    response = HttpResponse(content_type="application/pdf")
    response["Content-Disposition"] = (
        f"filename=Expenditure Summary Report_{str(datetime.now().strftime('%Y%m%d'))}.pdf"
    )
    response["Content-Transfer-Encoding"] = "binary"

    # For Data -------------
    def get_queryset():
        qs = expenditure_summary_qs(request)
        data = {}
        data = data | {"expenditure": qs}
        data = data | company_info
        grand_total = Expenditure.objects.aggregate(Sum("amount"))
        data["grand_total"] = grand_total
        return {"data": data}

    # For Data End -------------
    html_string = render_to_string(
        "accounts/reports/expenditure_summarized_list.html", get_queryset()
    )
    html = HTML(string=html_string, base_url='/', url_fetcher=url_fetcher)

    resultfile = html.write_pdf()

    with tempfile.NamedTemporaryFile(delete=True) as output:
        output.write(resultfile)
        output.flush()
        output.seek(0)
        response.write(output.read())
    return response


@login_required
@time_of_execution
def expenditureDetailsReport(request):
    response = HttpResponse(content_type="application/pdf")
    response["Content-Disposition"] = (
        f"filename=Expenditure Details Report_{str(datetime.now().strftime('%Y%m%d'))}.pdf"
    )
    response["Content-Transfer-Encoding"] = "binary"

    # For Data -------------
    qs_data = expenditure_details_qs(request)
    qs = qs_data
    # from_date = qs_data["from_date"]
    # to_date = qs_data["to_date"]

    data = {"expenditure": qs}
    data = data | company_info

    from_date = request.GET.get("fromdate")
    to_date = request.GET.get("todate")

    if from_date:
        data["fromdate"] = datetime.strptime(from_date, "%Y-%m-%d")
    if to_date:
        data["todate"] = datetime.strptime(to_date, "%Y-%m-%d")

    grand_total = qs.aggregate(Sum("amount"))
    data["grand_total"] = grand_total
    # For Data End -------------
    if request.path == reverse("Accounts:dateRangeExpenditureReport"):
        template = "accounts/reports/date_range_expenditure_details.html"
    else:
        template = "accounts/reports/expenditure_details.html"

    html_string = render_to_string(template, {"data": data})
    html = HTML(string=html_string,base_url='/', url_fetcher=url_fetcher)

    resultfile = html.write_pdf()

    with tempfile.NamedTemporaryFile(delete=True) as output:
        output.write(resultfile)
        output.flush()
        output.seek(0)
        response.write(output.read())
    return response


@login_required
def incomeDetailsReport(request):
    response = HttpResponse(content_type="application/pdf")
    response["Content-Disposition"] = (
        f"filename=Income Details Report_{str(datetime.now().strftime('%Y%m%d'))}.pdf"
    )
    response["Content-Transfer-Encoding"] = "binary"

    # For Data -------------
    incomesector = request.GET.get("incomesector")
    if incomesector:
        qs = Income.objects.filter(incomeItem__incomeSector=incomesector)
    else:
        qs = Income.objects.all()

    # qs = Income.objects.all()
    from_date = request.GET.get("fromdate")
    to_date = request.GET.get("todate")
    if from_date:
        qs = qs.filter(dateOfTransaction__gte=from_date)

    if to_date:
        qs = qs.filter(dateOfTransaction__lte=to_date)

    subquery_sum = (
        qs.filter(
            incomeItem=OuterRef("incomeItem__id"),
            # incomeItem__incomeSector=OuterRef("incomeItem__incomeSector__id"),
        )
        .values(
            # "incomeItem__incomeSector__incomeSector",
            "incomeItem__itemName",
        )
        .annotate(
            sum_amount=Round(Sum(F("amount")), 0),
            sum_quantity=Round(Sum(F("quantity")), 0),
            units=F("incomeItem__unit"),
        )
    )
    subquery_income_sector_sum = (
        qs.filter(incomeItem__incomeSector=OuterRef("incomeItem__incomeSector__id"))
        .values("incomeItem__incomeSector__incomeSector")
        .annotate(
            incomesector_sum=Coalesce(Sum(F("amount")), 0, output_field=DecimalField())
        )
    )

    qs = qs.annotate(
        income_sector=Subquery(
            subquery_sum.values("incomeItem__incomeSector__incomeSector")
        ),
        income_item_name=Subquery(subquery_sum.values("incomeItem__itemName")),
        sum_amount=Subquery(subquery_sum.values("sum_amount")),
        sum_quantity=Subquery(subquery_sum.values("sum_quantity")),
        units=Subquery(subquery_sum.values("units")),
    )
    if from_date or to_date:
        qs = qs.annotate(
            incomesector_sum=Subquery(
                subquery_income_sector_sum.values("incomesector_sum")
            ),
        ).order_by("income_sector", "income_item_name","dateOfTransaction")
    else:
        qs = qs.annotate(
            incomesector_sum=Subquery(
                subquery_income_sector_sum.values("incomesector_sum")
            ),
        ).order_by("income_sector", "income_item_name", "-dateOfTransaction")

    #! --------------------------------------
    data = {}
    data = data | {"income": qs}
    data = data | company_info

    if from_date:
        data["fromdate"] = datetime.strptime(from_date, "%Y-%m-%d")
    if to_date:
        data["todate"] = datetime.strptime(to_date, "%Y-%m-%d")

    grand_total = qs.aggregate(Sum("amount"))
    data["grand_total"] = grand_total
    # For Data End -------------
    if request.path == reverse("Accounts:dateRangeIncomeReport"):
        template = "accounts/reports/date_range_income_details.html"
    else:
        template = "accounts/reports/income_details.html"

    html_string = render_to_string(template, {"data": data})
    html = HTML(string=html_string, base_url='/', url_fetcher=url_fetcher)

    resultfile = html.write_pdf()

    with tempfile.NamedTemporaryFile(delete=True) as output:
        output.write(resultfile)
        output.flush()
        output.seek(0)
        response.write(output.read())
    return response


@login_required
@time_of_execution
def shareholderListReport(request):
    response = HttpResponse(content_type="application/pdf")
    response["Content-Disposition"] = (
        f"filename=Shareholder List_{str(datetime.now().strftime('%Y%m%d'))}.pdf"
    )
    response["Content-Transfer-Encoding"] = "binary"

    # For Data -------------
    qs = Shareholder.objects.all()

    subquery_sum = (
        ShareholderDeposit.objects.filter(
            shareholder_id=OuterRef("id"),
        )
        .values(
            "shareholder__id",
        )
        .annotate(
            sum_amount=Round(Sum(F("amount")), 0),
        )
    )

    qs = qs.annotate(
        sum_amount=Subquery(subquery_sum.values("sum_amount")),
    ).order_by("id")

    targeted_amount = TargetedAmount.objects.values_list("amount").order_by(
        "-inputDate","-id"
    )[0]

    #! --------------------------------------
    data = {}
    data = data | {"shareholder": qs}
    data = data | company_info
    data["targeted_amount_per_flat"] = (
        targeted_amount[0] / company_info["no_of_flat_per_share"]
    )
    data["heading"] = "Shareholders List"
    grand_total = ShareholderDeposit.objects.aggregate(Sum("amount"))
    data["grand_total"] = grand_total
    # For Data End -------------

    # base_url=request.build_absolute_uri()
    html_string = render_to_string(
        "accounts/reports/shareholder_list.html", {"data": data}
    )
    html = HTML(string=html_string,base_url='/', url_fetcher=url_fetcher)
    resultfile = html.write_pdf()

    with tempfile.NamedTemporaryFile(delete=True) as output:
        output.write(resultfile)
        output.flush()
        output.seek(0)
        response.write(output.read())
    return response


@login_required
def pdfReport(request, shareholder):
    share_holder_name = Shareholder.objects.values('shareholderName').get(id=shareholder)['shareholderName']
    response = HttpResponse(content_type="application/pdf")
    response["Content-Disposition"] = (
        f"filename={shareholder}_{share_holder_name}\'s Deposit Info_{str(datetime.now().strftime('%Y%m%d'))}.pdf"
        )
    response["Content-Transfer-Encoding"] = "binary"

    # For Data -------------

    qs = ShareholderDeposit.objects.filter(shareholder_id=shareholder)
    subquery_deposit_amount_sum = (
        Shareholder.objects.filter(id=OuterRef("shareholder_id"))
        .values("id")
        .annotate(
            deposit_amount_sum=Coalesce(
                Sum(F("shareholder_deposit__amount")), 0, output_field=DecimalField()
            ),
            shareholderName=F("shareholderName"),
            numberOfFlat=F("numberOfFlat"),
        )
    )

    if qs.count()==0:
        qs = Shareholder.objects.filter(id=shareholder).annotate(
            deposit_amount_sum=Value(0, output_field=DecimalField()),
            shareholder_id = F("id"),
        )
    else:
        qs = qs.annotate(
            deposit_amount_sum=Subquery(
                subquery_deposit_amount_sum.values("deposit_amount_sum"),
            ),
            shareholderName=Subquery(
                subquery_deposit_amount_sum.values("shareholderName"),
            ),
            numberOfFlat=Subquery(
                subquery_deposit_amount_sum.values("numberOfFlat"),
            ),
        )


    targeted_amount = TargetedAmount.objects.values_list("amount").order_by(
        "-inputDate","-id"
    )[0]
    #! --------------------------------------
    data = {}
    data = data | {"shareholder_deposit": qs}
    data = data | {"heading": "Details information of Deposited amount:"}
    data = data | company_info
    data = data | {"avatar": str(Shareholder.objects.get(id=shareholder).avatar)}
    data["targeted_amount_per_flat"] = (
        targeted_amount[0] / company_info["no_of_flat_per_share"]
    )
    # For Data End -------------

    html_string = render_to_string(
        "accounts/reports/shareholder_deposit.html", {"data": data}
    )
    # base_url = request.build_absolute_uri()
    html = HTML(string=html_string, base_url='/', url_fetcher=url_fetcher)

    resultfile = html.write_pdf()
    return {"resultfile": resultfile, "response": response}


@login_required
def shareholderDepositReport(request, shareholder):
    resultfile = pdfReport(request, shareholder)

    with tempfile.NamedTemporaryFile(delete=True) as output:
        output.write(resultfile["resultfile"])
        output.flush()
        output.seek(0)
        resultfile["response"].write(output.read())
    return resultfile["response"]


email_body_text = """Date: {}
To,
Mr. {}
Shareholder, AeroSky Tower
Bownia, Dhaka.

Dear Sir,
As-Salamu Alaikum,
We have accumulated your deposited amount upto the date mentioned above. If there are any errors in the account, we request you to kindly inform the management of Aerosky Tower for necessary corrections.

Thank you.

Sincerely,
Manager
Aerosky Tower
Bawnia, Turag, Dhaka."""

html_code = """
<ol>{}</ol>
        <div class="d-flex justify-container-between">
            <form class="row g-3 p-2">
                <div class="col-auto">
                    <input type="hidden" id="status_ok" name="status_ok" value=1 />
                </div>
                <div class="col-auto">
                <button type="submit" class="btn btn-sm btn-danger px-4 mt-0">Yes</button>
                </div>
            </form>
            <form class="row g-3 p-2">
                <div class="col-auto">
                <input type="hidden" name="status_cancel" value=2>
                <button type="submit" class="btn btn-sm btn-success mt-0">Cancel</button>
                </div>
            </form>
        </div>
"""


@login_required
def sendMailshareholderDepositReport(request):
    email_address_list_qs = (
        Shareholder.objects.exclude(email__isnull=True)
        .exclude(email__exact="")
        .values_list("shareholderName", "email", "id")
    )
    receipent_name_tupple_list = [
        (name, email, id) for name, email, id in email_address_list_qs
    ]

    if (
        request.GET.get("status_ok") == None
        and request.GET.get("status_cancel") == None
    ):
        y = ""
        for x in email_address_list_qs:
            y += "<li>" + x[0] + ": <span class='text-primary'>" + x[1] + "</span></li>"
        template_name = "template_response.html"
        heading = "Are you sure you want to send eMail to:"
        context = {
            "heading": heading,
            "html_code": html_code.format(y),
            # "img_file": "matir-bank.svg",
            # "scripts": scripts,
        }
        return TemplateResponse(request, template_name, context)

    if request.GET.get("status_cancel") == "2":
        return HttpResponseRedirect("/")

    subject = "AeroSky Tower shareholder's deposit information."
    from_mail = request.user.email
    to_cc = ""
    to_bcc = ""
    for x in receipent_name_tupple_list:
        try:
            attachedFile = pdfReport(request, int(x[2]))
            mail = EmailMessage(
                subject=subject,
                body=email_body_text.format(datetime.now().strftime("%d %B %Y"), x[0]),
                from_email=from_mail,
                to=[x[0], x[1]],
                bcc=[to_bcc],
                cc=[to_cc],
            )
            mail.attach(
                f'Deposit Information of {x[0]} {datetime.now().strftime("%Y-%m-%d")}.pdf',
                attachedFile["resultfile"],
                "application/pdf",
            )
            mail.send()
            # except Exception as ex:
        except ArithmeticError as aex:
            return HttpResponse("Invalid header found")
    receipent_list = ""
    for x in receipent_name_tupple_list:
        receipent_list += f"{x[0]}, "
    messages.success(request, f"Thank you.\nEmail sent to {receipent_list}.")
    return HttpResponseRedirect("/")
