from django.contrib.auth.decorators import login_required
from django.http import HttpResponse
from django.template.loader import get_template
from django.conf import settings
import os
from xhtml2pdf import pisa
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.fonts import addMapping
from CONSTRUCTION_PROJECT.settings.context_processors import company_info_settings
from .decorators import time_execution
from django.db.models import (
    F,
    Sum,
    Count,
    Q,
    Case,
    When,
    Value,
    ExpressionWrapper,
    Func,
    DateTimeField,
    DateField,
    IntegerField,
    CharField,
    DecimalField,
    Subquery,
    OuterRef,
    DurationField,
    FloatField,
)

from django.db.models.functions import Cast, Round, Concat, Coalesce, Extract, Trunc
from .models import *

# Get company info
company_info = company_info_settings()

# Register specialized Bengali fonts
pdfmetrics.registerFont(TTFont('Kalpurush', os.path.join(settings.BASE_DIR, 'static', 'fonts', 'kalpurush.ttf')))
pdfmetrics.registerFont(TTFont('SolaimanLipi', os.path.join(settings.BASE_DIR, 'static', 'fonts', 'SolaimanLipi.ttf')))
pdfmetrics.registerFont(TTFont('BanglaFontNikosh', os.path.join(settings.BASE_DIR, 'static', 'fonts', 'Nikosh.ttf')))

# Set default font mapping
addMapping('Kalpurush', 0, 0, 'Kalpurush')
addMapping('SolaimanLipi', 0, 0, 'SolaimanLipi')
addMapping('BanglaFontNikosh', 0, 0, 'BanglaFontNikosh')

# Try to set default font if possible
try:
    from xhtml2pdf.default import DEFAULT_FONT
    DEFAULT_FONT['helvetica'] = 'Kalpurush'
except ImportError:
    # If DEFAULT_FONT is not available, we'll handle fonts through CSS
    pass

def fetch_resources(uri, rel):
    """
    Callback to allow xhtml2pdf/reportlab to retrieve images, fonts etc.
    """
    if uri.startswith(settings.STATIC_URL):
        path = os.path.join(settings.STATIC_ROOT, uri.replace(settings.STATIC_URL, ""))
    elif uri.startswith(settings.MEDIA_URL):
        path = os.path.join(settings.MEDIA_ROOT, uri.replace(settings.MEDIA_URL, ""))
    else:
        path = os.path.join(settings.STATIC_ROOT, uri)
    return path

def render_to_pdf(template_path, context_data):
    """Helper function to render HTML template to PDF with proper Bengali font support"""
    # Get template
    template = get_template(template_path)
    
    # Add font paths to context
    context_data['font_paths'] = {
        'Kalpurush': os.path.join(settings.STATIC_URL, 'fonts', 'kalpurush.ttf'),
        'SolaimanLipi': os.path.join(settings.STATIC_URL, 'fonts', 'SolaimanLipi.ttf'),
        'Nikosh': os.path.join(settings.STATIC_URL, 'fonts', 'Nikosh.ttf')
    }
    
    # Render template
    html = template.render(context_data)
    
    # Create response
    response = HttpResponse(content_type='application/pdf')
    filename = context_data.get('filename', 'report.pdf')
    response['Content-Disposition'] = f'filename="{filename}"'
    
    # Create PDF with explicit font configuration
    pdf_options = {
        'encoding': 'UTF-8',
        'link_callback': fetch_resources,
    }
    
    pisa_status = pisa.CreatePDF(
        html,
        dest=response,
        **pdf_options
    )
    
    # Return response
    if pisa_status.err:
        return HttpResponse(f'PDF generation error: {pisa_status.err}')
    
    return response

@login_required
@time_execution
def xhtml2pdf_sgareholder_list(request):
    qs = Shareholder.objects.all()
    subquery_sum = (
        ShareholderDeposit.objects.filter(
            shareholder_id=OuterRef("id"),
        )
        .values(
            "shareholder__id",
        )
        .annotate(
            sum_amount=Round(Sum(F("amount")), 0),
        )
    )

    qs = qs.annotate(
        sum_amount=Subquery(subquery_sum.values("sum_amount")),
    ).order_by("id")

    targeted_amount = TargetedAmount.objects.values_list("amount").order_by(
        "-inputDate"
    )[0]

    data = {}
    data = data | {"shareholder": qs}
    data = data | {"heading": "Shareholders List"}
    data = data | company_info
    data["targeted_amount_per_flat"] = (
        targeted_amount[0] / company_info["no_of_flat_per_share"]
    )
    data["heading"] = "Shareholders List"
    grand_total = ShareholderDeposit.objects.aggregate(Sum("amount"))
    data["grand_total"] = grand_total
    # For Data End -------------
    template_path = 'accounts/reports/shareholder_list.html'
    context = {"data": data}
    
    # Use the improved render_to_pdf function
    return render_to_pdf(
        template_path=template_path,
        context_data=context
    )


@login_required
@time_execution
def xhtml2pdf_expenditureDetailsReport(request):
    # For Data -------------
    from_date = request.GET.get("fromdate")
    to_date = request.GET.get("todate")

    qs = Expenditure.objects.select_related("item__ItemCode").all()
    if from_date:
        qs = qs.filter(dateOfTransaction__gte=from_date)

    if to_date:
        qs = qs.filter(dateOfTransaction__lte=to_date)

    subquery_sum = (
        qs.filter(
            item_id=OuterRef("item__id"),
            item__ItemCode=OuterRef("item__ItemCode__id"),
        )
        .values(
            "item__ItemCode__workSector",
            "item__itemName",
        )
        .annotate(
            sum_amount=Round(Sum(F("amount")), 0),
            sum_quantity=Round(Sum(F("quantity")), 0),
            units=F("item__unit"),
        )
    )

    qs = qs.annotate(
        work_sector=Subquery(subquery_sum.values("item__ItemCode__workSector")),
        item_name=Subquery(subquery_sum.values("item__itemName")),
        sum_amount=Subquery(subquery_sum.values("sum_amount")),
        sum_quantity=Subquery(subquery_sum.values("sum_quantity")),
        units=Subquery(subquery_sum.values("units")),
    )

    subquery_work_sector_sum = (
        qs.filter(item__ItemCode__workSector=OuterRef("item__ItemCode__workSector"))
        .values("item__ItemCode__workSector")
        .annotate(
            worksector_sum=Coalesce(Sum(F("amount")), 0, output_field=DecimalField())
        )
    )

    if from_date or to_date:
        qs = qs.annotate(
            worksector_sum=Subquery(subquery_work_sector_sum.values("worksector_sum")),
        ).order_by("work_sector", "item_name", "dateOfTransaction")
    else:
        qs = qs.annotate(
            worksector_sum=Subquery(subquery_work_sector_sum.values("worksector_sum")),
        ).order_by("work_sector", "item_name", "-dateOfTransaction")

    #! --------------------------------------
    data = {}
    data = data | {"expenditure": qs}
    data = data | company_info

    if from_date:
        data["fromdate"] = datetime.strptime(from_date, "%Y-%m-%d")
    if to_date:
        data["todate"] = datetime.strptime(to_date, "%Y-%m-%d")

    grand_total = qs.aggregate(Sum("amount"))
    data["grand_total"] = grand_total
    # For Data End -------------
    if request.path == reverse("Accounts:dateRangeExpenditureReport"):
        template_path = "accounts/reports/date_range_expenditure_details.html"
    else:
        template_path = "accounts/reports/expenditure_details.html"
        
    context = {"data":data}
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = 'filename="ExpenditureDetailsReport.pdf"'
    template = get_template(template_path)
    html = template.render(context)
    html = html.encode("UTF-8")
    pisa_status = pisa.CreatePDF(
        html, dest=response, link_callback=fetch_resources)
    # # if error then show some funy view
    if pisa_status.err:
        return HttpResponse('We had some errors <pre>' + html + '</pre>')

    return response

def render_to_pdf(template_path, context_data):
    """Helper function to render HTML template to PDF with proper Bengali font support"""
    # Get template
    template = get_template(template_path)
    
    # Add font paths to context
    context_data['font_paths'] = {
        'Kalpurush': os.path.join(settings.STATIC_URL, 'fonts', 'kalpurush.ttf'),
        'SolaimanLipi': os.path.join(settings.STATIC_URL, 'fonts', 'SolaimanLipi.ttf'),
        'Nikosh': os.path.join(settings.STATIC_URL, 'fonts', 'Nikosh.ttf')
    }
    
    # Render template
    html = template.render(context_data)
    
    # Create response
    response = HttpResponse(content_type='application/pdf')
    filename = context_data.get('filename', 'report.pdf')
    response['Content-Disposition'] = f'filename="{filename}"'
    
    # Create PDF with explicit font configuration
    pdf_options = {
        'encoding': 'UTF-8',
        'link_callback': fetch_resources,
    }
    
    pisa_status = pisa.CreatePDF(
        html,
        dest=response,
        **pdf_options
    )
    
    # Return response
    if pisa_status.err:
        return HttpResponse(f'PDF generation error: {pisa_status.err}')
    
    return response
