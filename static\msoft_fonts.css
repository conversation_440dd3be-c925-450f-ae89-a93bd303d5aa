@charset "UTF-8";

@font-face {
  font-family: BanglaFontNikosh2;
  src: url("./fonts/nikosh2.ttf");
}
@font-face {
  font-family: BanglaFontNikosh;
  src: url("./fonts/Nikosh.ttf");
}
@font-face {
  font-family: BanglaFontSejuti;
  src: url("./fonts/Fazlay Sejuti Unicode.ttf");
}
@font-face {
  font-family: BanglaFontEid;
  src: url("./fonts/Eid_Mubarak.ttf");
}
@font-face {
  font-family: BanglaFontEkushey;
  src: url("./fonts/Ekushey_Belycon_06-09-2018.ttf");
}
@font-face {
  font-family: BanglaFontKero;
  src: url("./fonts/KEERO___.ttf");
}
@font-face {
  font-family: BanglaFontPadmo;
  src: url("./fonts/Padmo UNICODE.ttf");
}
@font-face {
  font-family: BanglaFontMadon;
  src: url("./fonts/<PERSON> MadanMohan Unicode.ttf");
}

@font-face {
  font-family: PlaywriteAUSA;
  src: url("./fonts/PlaywriteAUSA-VariableFont_wght.ttf");
}

@font-face {
  font-family: 'Exo 2';
  src: url("./fonts/Exo2-VariableFont_wght.ttf");
}

@font-face {
  font-family: CompanyNameFont;
  src: url("./fonts/RussoOne-Regular.ttf");
}
@font-face {
  font-family: AeroSkyFont;
  src: url("./fonts/Audiowide-Regular.ttf");
}

  @font-face {
    font-family: "Kalpurush";
    src: url("./fonts/Kalpurush ") format('truetype');
    font-weight: normal;
    font-style: normal;
    /* unicode-range: "U+0980 - 09FF"  */
  }

.b_fontNikosh {
  font-family: BanglaFontNikosh;
}
.b_fontNikosh2 {
  font-family: BanglaFontNikosh2 "Courier New", monospace;
}
.b_fontSejuti {
  font-family: BanglaFontSejuti;
}
.b_fonteid {
  font-family: BanglaFontEid;
}
.b_fontekush {
  font-family: BanglaFontEkushey;
}
.b_fontkero {
  font-family: BanglaFontKero;
}
.b_fontpadmo {
  font-family: BanglaFontPadmo;
}
.b_fontmadon {
  font-family: BanglaFontMadon;
}
.b_fontkalpurush {
  font-family: Kalpurush;
}
.e_PlaywriteAUSA {
  font-family: PlaywriteAUSA, serif;
  font-optical-sizing: auto;
  font-weight: bold;
  font-style: normal;
}

.exo-2 {
  font-family: "Exo 2", serif;
  font-optical-sizing: auto;
  font-weight: bold;
  font-style: normal;
}

.exo-2-1 {
  font-family: "Exo 2", serif;
  font-optical-sizing: auto;
  font-weight: normal;
  font-style: normal;
}


.playwrite-au-sa {
  font-family: "Playwrite AU SA", serif;
  font-optical-sizing: auto;
  font-weight: bold;
  font-style: normal;
}

.CompanyNameFont {
  font-family: CompanyNameFont;
}
.AeroSkyFont {
  font-family: AeroSkyFont;
}