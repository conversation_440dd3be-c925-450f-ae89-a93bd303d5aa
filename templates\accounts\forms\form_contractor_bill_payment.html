{% extends "main.html" %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}{{company_name}}{%endblock %}
{% block content %}

<div class="container pt-3" style="width: 90%; margin: auto;">
    <div class="card">
        <div class="card-header ps-2 py-1">
            <h4>{{heading}}</h4>
        </div>
        <div class="card-body">
            {% crispy form %}
        </div>
    </div>
    <br>

    <div class="card">
        <div class="card-header ps-2 py-1">
            <h4>{{data_heading}}</h4>
        </div>
        <ul class="list-group list-group-flush">
            {% for dt in data %}
            {% if forloop.counter|divisibleby:2 %}
            <li class="ps-5 pe-5 list-group-item list-group-item-light">- {{dt.contractor_name}} | {{dt}}</li>
            {% else %}
            <li class="ps-5 pe-5 list-group-item list-group-item-secondary">- {{dt.contractor_name}} | {{dt}}</li>
            {% endif %}
            {% endfor %}
        </ul>
    </div>
    </div>
    <br>

    {% endblock %}
    {% block script %}
    <script>
        var account_input = document.getElementById("id_account")
        {% if update_tag is None %}
        $('#id_item').empty();
        $('#id_item').append(`<option class='text-muted' value selected>------------</option>`);
        {% else %}
        button_div = document.getElementById("button_div")
        button_div.innerHTML = `<input type="submit" name="submit" value="Update" class="btn btn-primary btn btn-success me-2 mb-0" id="submit-id-submit">
	<a href="{%url 'Accounts:expenditure_details_list'%}" class="btn btn-danger">Cancel</a>`
        // Bank Account Field Initialization start =========
        if ($('#id_modeOfTransaction').val() == "BANK") {
            $.ajax({
                url: '/get_account_no/' + {{ object.pk }} + "/Expenditure",
                    dataType: "json",
                        success: function (data) {
                            $('#id_account').val(data[0]["fields"]["account"]);
                        }
            });
        }

        if ($('#id_modeOfTransaction').val() == "CASH") {
            account_input.disabled = true;
        }
        // Bank Account Field Initialization end =========
        {% endif %}

        $('#id_ItemCode').change(function () {
            let item_code_id = $(this).val();
            $('#id_item').empty();
            $('#id_item').append(`<option class='text-muted' value selected>------------</option>`);
            $.ajax({
                url: '/get_item/' + item_code_id,
                success: function (data) {
                    $.each(data, function (index, Item) {
                        $('#id_item').append($('<option>', {
                            value: Item.pk,
                            text: Item.fields.itemName
                        }));
                    });
                }
            });
        });

        // ======= Change Item Unit =========================
        $('#id_item').change(function () {
            let item_id = parseInt($('#id_item').val() ?? 0);
            $.ajax({
                url: '/get_unit/' + item_id,
                success: function (data) {
                    $('#id_unit').val(data[0].fields.unit);
                }
            });
        });
        // ===================================================

        // ======= Auto Calculate paid amount.========
        $('#id_rate').change(function () {
            let quantity = $('#id_quantity').val() ?? 0;
            let rate = $(this).val()
            $('#id_amount').val(quantity * rate);
        })
        // ===================================================================

        // ======= Enable and Disable Bank Account Choice.========
        {% if not update_tag %}
        account_input.disabled = true;
        {% endif %}

        $('#id_modeOfTransaction').change(function () {
            if ($(this).val() == "BANK") {
                account_input.disabled = false;
                account_input.required = true;
            } else {
                account_input.disabled = true;
                account_input.required = false;
                account_input.value = "";
            }
        })
        // =======Enable and Disable Bank Account Choice end ============

        // ======= Populate Rest Amount.========
        $('#id_bill').change(function () {
            let id_bill = document.getElementById("id_bill").value;
            $.ajax({
                url: '/get_contractor_bill_rest_amount/' + id_bill,
                success: function (data) {
                    let item = JSON.parse(data);
                    $('#id_amount').val(item['rest_amount'])
                }
            });
        })
        // ===================================================================
    </script>

    {% endblock script %}