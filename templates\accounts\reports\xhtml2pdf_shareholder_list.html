{% extends "accounts/reports/xhtml2pdf_reportbase.html" %}
{% load static %}
{% load mahimsoft_tags %}
{% load mathfilters %}
{% block title %}<title>Shareholder List {% now 'd-M-Y' %}</title>{% endblock title %}
{% block style %}
<style>
@font-face {
    font-family: 'BanglaFontSejuti';
    src: url("/static/fonts/Fazlay Sejuti Unicode.ttf") format('truetype');
    font-weight: normal;
    font-style: normal;
  }
  @font-face {
    font-family: 'BanglaFontNikosh';
    src: url("/static/fonts/Nikosh.ttf") format('truetype');
    font-weight: normal;
    font-style: normal;
  }
  @font-face {
    font-family: 'BanglaFont';
    src: url('/static/fonts/SolaimanLipi.ttf') format('truetype');
  }

  @font-face {
    font-family: "Kalpurush";
    src: url("{{ data.font_paths.Kalpurush }}") format('truetype');
    font-weight: normal;
    font-style: normal;
    unicode-range: "U+0980 - 09FF" 
  }

  table, th, td {
  border: 1px solid rgb(205, 210, 212);
  border-collapse: collapse;
  padding: 4px 5px 0px 5px;
}
  tr {
  page-break-inside: avoid;
}
  th {
  background-color:#D9D9D9;
  vertical-align: middle;
  margin: 4px 0px 0px 0px;
}
 /* margin: 10px 5px 15px 20px;
            top     margin is 10px
            right   margin is 5px
            bottom  margin is 15px
            left    margin is 20px  */

td {
    font-family: 'BanglaFontSejuti'; 
    vertical-align: middle;
    margin: 5px 0px 0px 0px;
}
  /* tr:nth-child(even) {
  background-color:rgb(167, 25, 54);
} */

@page {
    size: a4 portrait;
    margin: 5mm 10mm 5mm 10mm;
    counter-increment: page;
    margin-bottom: 8mm;
    @frame footer {
        -pdf-frame-content: footerContent;
        bottom: 0mm;
        right: 10mm;
        height: 7mm;
    }
  } 
</style>
{% endblock style %}

{% block contents %}
<p style="margin:0; text-align: right; font-size: 12px;">Date: {% now 'd-M-Y' %}</p>
<h1 style="text-align: center; color: #7a7b7d; font-size: 20px; margin: 0px 0px 0px 0px;;">{{data.heading}}</h1>
<div>
  <table repeat="1">
    <thead>
    <tr style="height: 32px;">
      <th style="width: 3%; ">#</th>
      <th style="width: 7%; ">Photo</th>
      <th style="width: 20%;">Name</th>
      <th style="width: 10%;">NID</th>
      <th style="width: 22%;">Mobile & Email</th>
      <th style="width: 10%;">Nos. of Flat</th>
      <th style="width: 14%;">Deposited Amnt</th>
      <th style="width: 14%;">Rest Amnt to Pay</th>
    </tr>
    </thead>
    <tbody>
    {% for dt in data.shareholder %}
    <tr style="height: 60 px;
      {% if forloop.counter|mod:2 == 0 %}
      background-color: #F0FFF0;
      {% endif %}">
      <td style="text-align: center;">{{forloop.counter}}</td>
      <td style="text-align: center; padding: auto;"><img src="{{dt.avatar}}" style="width: 50px; height: 50px;" alt="Avatar"></td>
      <td style="font-family:Kalpurush, sans-serif; font-size:10px;">{{dt.shareholderName}}</td>
      
      {% if dt.nid == None %}
      <td style="text-align: center;">---</td>
      {% else %}
      <td style="text-align: center;">{{dt.nid|default_if_none:"---"}}</td>
      {% endif %}

      <td style="padding: 0px 0px 0px 5px;">
      <img style="width: 14px; height: 14px;"
                src="{% static 'images/ic_phone_24px.svg' %}" alt="telephone">&nbsp; {{dt.mobile|default_if_none:"---"}}
      <br>
      <img style="width: 14px; height: 14px;"
                src="{% static 'images/email.svg' %}" alt="telephone">&nbsp; &nbsp; {{dt.email|default_if_none:"---"}}
      </td>
      <td style="text-align: center;">{{dt.numberOfFlat|floatformat:"1"}}</td>
      <td style="text-align: right;">{{dt.sum_amount|default_if_none:0|intcomma_bd}}/-</td>

      {% if dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount > 0 %}
      <td style="text-align: right; color:red;">
      {{dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount|number_product:-1|intcomma_bd}}/-
      </td>
      {% elif dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount == 0 %}
      <td style="text-align: right;">0/-</td>
      {% else %}
      <td style="text-align: right; color:green;">
        {{dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount|number_product:-1|intcomma_bd}}/-
      </td>
      {% endif %}
      
    </tr>
    {% endfor %}
    </tbody>
  </table>
</div>
<p style="color: firebrick; font-size: 12px;">NB: Rest amount to pay calculated as per targeted amount per share as {{ currency }}:
  {{data.no_of_flat_per_share|number_product:data.targeted_amount_per_flat|intcomma_bd}}/-</p>
  {% endblock %}