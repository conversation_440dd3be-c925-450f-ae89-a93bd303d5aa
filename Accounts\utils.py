from django.contrib.auth.decorators import login_required
from django.db.models import (
    F,
    Sum,
    Count,
    Q,
    Case,
    When,
    Value,
    ExpressionWrapper,
    Func,
    DateTimeField,
    DateField,
    IntegerField,
    CharField,
    DecimalField,
    Subquery,
    OuterRef,
    DurationField,
    FloatField,
    )

from django.db.models.functions import Cast, Round, Concat, Coalesce, Extract, Trunc
from .models import *
from .decorators import time_of_execution

@login_required
def expenditure_summary_qs(request=None):
    qs = Expenditure.objects.all()
    
    work_phase = request.GET.get("workphase")
    if work_phase:
        qs = qs.filter(work_phase_id=work_phase)

    qs = qs.values(
            "work_phase_id",
            "item__ItemCode__id",
            "item__id",
        ).annotate(
            work_phase_description=F("work_phase__description"),
            work_sector=F("item__ItemCode__workSector"),
            item_name=F("item__itemName"),
            unit=F("item__unit"),
            sum_amount=Round(Sum(F("amount")), 0),
            sum_quantity=Round(Sum(F("quantity")), 0),
        ).order_by("work_phase_id", "item__ItemCode__id", "item__id")

    subquery_work_sector_sum = (
            Expenditure.objects.filter(
                work_phase_id       = OuterRef("work_phase_id"),
                item__ItemCode__id  = OuterRef("item__ItemCode__id")
            )
            .values("work_phase_id","item__ItemCode__id")
            .annotate(
                worksector_sum=Coalesce(
                    Sum(F("amount")), 0, output_field=DecimalField()
                )
            )
        )

    subquery_work_phase = (
            Expenditure.objects.filter(
                work_phase_id=OuterRef("work_phase_id")
            )
            .values("work_phase_id")
            .annotate(
                work_phase_sum=Coalesce(
                    Sum(F("amount")), 0, output_field=DecimalField()
                ),
                from_date=F("work_phase__fromDate"),
                to_date=F("work_phase__toDate"),
                estimated_cost=F("work_phase__estimatedCost"),
            )
        )

    qs = qs.annotate(
            work_phase_sum=Subquery(subquery_work_phase.values("work_phase_sum")),
            worksector_sum=Subquery(subquery_work_sector_sum.values("worksector_sum")),
            work_phase_from_date=Subquery(subquery_work_phase.values("from_date")),
            work_phase_to_date=Subquery(subquery_work_phase.values("to_date")),
            estimated_cost=Subquery(subquery_work_phase.values("estimated_cost")),
            work_phase=Subquery(subquery_work_phase.values("work_phase")),
        )
    return qs


@login_required
def expenditure_details_qs(request=None):
    qs = Expenditure.objects.select_related("item__ItemCode").all()
    
    from_date = request.GET.get("fromdate")
    to_date = request.GET.get("todate")
    if from_date:
        qs = qs.filter(dateOfTransaction__gte=from_date)

    if to_date:
        qs = qs.filter(dateOfTransaction__lte=to_date)

    work_phase = request.GET.get("workphase")
    if work_phase:
        qs = qs.filter(work_phase_id=work_phase)

    subquery_item_sum = (
        qs.filter(
            work_phase_id = OuterRef("work_phase__id"),
            item__ItemCode = OuterRef("item__ItemCode__id"),
            item_id = OuterRef("item__id"),
        )
        .values(
            "work_phase_id",
            "item__ItemCode__id",
            "item__id",
        )
        .annotate(
            item_sum_amount = Round(Sum(F("amount")), 0),
            item_sum_quantity = Round(Sum(F("quantity")), 0),
            units = F("item__unit"),
            item_name = F("item__itemName"),

        )
    )
    subquery_work_sector_sum = (
        qs.filter(
            work_phase_id = OuterRef("work_phase_id"),
            item__ItemCode__id = OuterRef("item__ItemCode__id")
            )
        .values("work_phase_id","item__ItemCode__id")
        .annotate(
            worksector_sum = Coalesce(Sum(F("amount")), 0, output_field=DecimalField()),
            work_sector = F("item__ItemCode__workSector"),
        )
    )

    subquery_work_phase_sum = (
        qs.filter(
            work_phase_id=OuterRef("work_phase_id"),
            )
        .values("work_phase_id")
        .annotate(
            work_phase_sum = Coalesce(Sum(F("amount")), 0, output_field=DecimalField()),
            work_phase_description = F("work_phase__description"),
            from_date = F("work_phase__fromDate"),
            to_date = F("work_phase__toDate"),
            estimated_cost = F("work_phase__estimatedCost"),
        )
    )

    qs = qs.annotate(
        item_name = Subquery(subquery_item_sum.values("item_name")),
        item_sum_amount = Subquery(subquery_item_sum.values("item_sum_amount")),
        item_sum_quantity = Subquery(subquery_item_sum.values("item_sum_quantity")),
        units = Subquery(subquery_item_sum.values("units")),
        work_sector = Subquery(subquery_work_sector_sum.values("work_sector")),
        worksector_sum = Subquery(subquery_work_sector_sum.values("worksector_sum")),
                    work_phase_from_date=Subquery(subquery_work_phase_sum.values("from_date")),
            work_phase_to_date=Subquery(subquery_work_phase_sum.values("to_date")),
            estimated_cost=Subquery(subquery_work_phase_sum.values("estimated_cost")),

        work_phase_description = Subquery(subquery_work_phase_sum.values("work_phase_description")),
        work_phase_sum = Subquery(subquery_work_phase_sum.values("work_phase_sum")),
    ).order_by("work_phase_id", "item__ItemCode__id", "item__id", "dateOfTransaction")
    if from_date:
        qs.from_date = datetime.strptime(from_date, "%Y-%m-%d")
    if to_date:
        qs.to_date = datetime.strptime(to_date, "%Y-%m-%d")
        
    # qs.from_date=datetime.strptime(from_date, "%Y-%m-%d")
    # qs.to_date=datetime.strptime(to_date, "%Y-%m-%d")
    return qs

