# How to Execute file 🏵 ⚽︋ 🏵 ⚽︋ 🏵 ⚽︋ 🏵 ⚽︋ 🏵 ⚽︋ 🏵 ⚽
# exec(open('fdr/tests.py').read()) --For python shell
# py manage.py runscript fdr_script --For Powershell

# ANSI Color Code
RESET = "\033[0m"
BLACK = "\033[30m"
RED = "\033[31m"
GREEN = "\033[32m"
YELLOW = "\033[33m"
BLUE = "\033[34m"
MAGENTA = "\033[35m"
CYAN = "\033[36m"
WHITE = "\033[37m"

# Background colors:
BG_BLACK = "\033[40m"
BG_RED = "\033[41m"
BG_GREEN = "\033[42m"
BG_YELLOW = "\033[43m"
BG_BLUE = "\033[44m"
BG_MAGENTA = "\033[45m"
BG_CYAN = "\033[46m"
BG_WHITE = "\033[47m"

# Text formatting:
TXT_BOLD = "\033[1m"
# Dim: \033[2m
# Underline: \033[4m
# Blink: \033[5m
# Reverse: \033[7m
# Hidden: \033[8m