{% extends "main.html" %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% load mahimsoft_tags %}

{% block title %}{{company_name}}{%endblock %}
{% block content %}

<div class="container pt-3">
    <div class="card">
        <div class="card-header ps-2 py-1">
            <div class="d-flex justify-content-between">
            <h4>{{heading}}</h4>
            <h6 class="text-warning pt-2">Bank Balance: {{bank_balance.bank_balance|intcomma_bd}} | Cash Balance: {{cash_balance.cash_balance|intcomma_bd}}</h6>
        </div>
        </div>
        <div class="card-body">
            {% crispy form %}
            {% comment %} <div class="d-flex justify-content-end mb-1" id="button_div">
            {% if update_tag is None %}
                <input type="submit" name="submit" value="Submit" class="btn btn-primary btn btn-success me-2 mb-0" id="submit-id-submit">
                <input type="reset" name="reset" value="Reset" class="btn btn-inverse btn btn-danger me-0 mb-0" id="reset-id-reset">
            {% else %}
            <input type="submit" name="submit" value="Update" class="btn btn-primary btn btn-success me-2 mb-0" id="submit-id-submit">
            <a href="/expenditure_details_list/" class="btn btn-danger">Cancel</a>
            {% endif %}
            </div> {% endcomment %}
            
        </div>
    </div>
    <br>
    {% if update_tag is None %}
    <div class="card">
        <div class="card-header ps-2 py-1">
            <h4>{{data_heading}}</h4>
        </div>
        <ul class="list-group list-group-flush">
            {% for dt in data %}
            {% if forloop.counter|divisibleby:2 %}
            <li class="ps-5 pe-5 list-group-item list-group-item-light"><i class="bi bi-record-fill"></i> {{dt}}</li>
            {% else %}
            <li class="ps-5 pe-5 list-group-item list-group-item-secondary"><i class="bi bi-record-fill"></i> {{dt}}</li>
            {% endif %}
            {% endfor %}
        </ul>
    </div>
    {% endif %}
</div>
    <br>

    {% endblock %}

    {% block script %} 
    <script>
    {% if update_tag is None %}
    $('#id_incomeItem').empty();
    $('#id_incomeItem').append(`<option class='text-muted' value selected>------------</option>`);  
    {% else %}
    button_div = document.getElementById("button_div")
	button_div.innerHTML = `<input type="submit" name="submit" value="Update" class="btn btn-primary btn btn-success me-2 mb-0" id="submit-id-submit">
	<a href="{%url 'Accounts:expenditure_details_list'%}" class="btn btn-danger">Cancel</a>`
    {% endif %}
    $('#id_incomeSector').change(function() {
        let id_IncomeItem_id = $(this).val();
        $('#id_incomeItem').empty(); 
        $('#id_incomeItem').append(`<option class='text-muted' value selected>------------</option>`);        
        $.ajax({
            url: '/get_income_item/' + id_IncomeItem_id,
            success: function(data) {
                $.each(data, function(index, Item) {
                    $('#id_incomeItem').append($('<option>', {
                    value : Item.pk,
                    text : Item.fields.itemName
                    }));
                });
            }
        });
    });
    
    // ======= Auto add months from opening date to Maturity date.========
    {/* $('#id_item').change(function() {
        let unit = parseInt($('#id_Duration').val()?? 0) ;
        let x = new Date($(this).val())
        let y = new Date(x.setMonth(x.getMonth() + duration));
        $('#id_MaturityDate').val(y.toISOString().split('T')[0]);
        }) */}
    // ===================================================================
    </script>
    
    {% endblock script %}