{% extends "main.html" %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}{{company_name}}{%endblock %}
{% block content %}

<div class="container pt-3" style="width: 90%; margin: auto;">
    <div class="card">
        <div class="card-header ps-2 py-1">
            {{heading|safe}}
        </div>
        <div class="card-body">
            {% crispy form %}
        </div>
    </div>
    <br>

    {% if update_tag is None %}
    <div class="card">
        <div class="card-header ps-2 py-1">
            <h5>{{data_heading}}</h5>
        </div>
        <ul class="list-group list-group-flush">
            {% for dt in data %}
            {% if forloop.counter|divisibleby:2 %}
            <li class="ps-5 pe-5 list-group-item list-group-item-light">- {{dt}}</li>
            {% else %}
            <li class="ps-5 pe-5 list-group-item list-group-item-secondary">- {{dt}}</li>
            {% endif %}
            {% endfor %}
        </ul>
    </div>
    {% endif %}
</div>
    <br>
    {% endblock %}
    {% block script %}
    <script>
        {% if update_tag %}
        button_div = document.getElementById("button_div")
        button_div.innerHTML = `<input type="submit" name="submit" value="Update" class="btn btn-primary btn btn-success me-2 mb-0" id="submit-id-submit">
	<a href="{%url 'Accounts:expenditure_details_list'%}" class="btn btn-danger">Cancel</a>`
        {% endif %}

    </script>

    {% endblock script %}