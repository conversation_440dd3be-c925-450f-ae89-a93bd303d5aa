{% extends "accounts/reports/reportbase.html" %}
{% load static %}
{% block title %}<title>{{data.contractor}}</title>{% endblock title %}
{% block link %}
  <link href="{% static 'page_css_portrait.css' %}" rel="stylesheet">
{% endblock link %}
{% block style %}
<style>
  @page {
    @bottom-right {
        content: '\00A9 {{data.company_name}}, Page No : ' counter(page) ' of ' counter(pages);
    }
}
tr {
    height: 35px;
}
th {
    font-size: 14px; 
}
td {
    font-size: 13px; 
}
</style>
{% endblock style %}

{% block contents %}
<p class="report_date" style="margin:0;">Date: {% now 'd-M-Y' %} </p>
<div class="container" style="width: 70%; margin: auto;">
  <h4 style = "padding:0px 10px; margin:0px;" id="heading">Contractor's Details Information</h4>
    <div style="text-align: center; margin-bottom: 10px;">
    <img src="{{data.avatar}}" alt="avatar" style="border-radius: 15%; width: 100px; height: 100px;">
  </div>
    <div style="display: flex; justify-content: space-between; width: 100%; margin: auto; border: 1px solid #ccc; margin-bottom: 10px; padding: 5px 0px 5px 0px; background-color: rgb(196, 206, 194); border-radius: 7px;">
    <div style="width: 50%; text-align: right; padding: 5px;"><h3 style="padding: 0px; margin: 0px;">Contractor's Name :</h3></div>
    <div style=" width: 50%; text-align: left; padding: 5px;"><h3 style="color: red; padding: 0px; margin: 0px;"> {{data.contractor}}</h3></div>
  </div>

 <table>
  <tr>
    <th style="width: 50%; text-align: right;">Date of Join :</th>
    <td style="width: 50%; text-align: left;">{{data.dateOfJoin|date:'d-b-Y'}}</td>
  </tr>
  <tr>
    <th style="width: 50%; text-align: right;">Contractor Type :</th>
    <td style="width: 50%; text-align: left;">{{contractorType__contractorType|default_if_none:"---"}}</td>
  </tr>
  <tr>
    <th style="width: 50%; text-align: right;">Address :</th>
    <td style="width: 50%; text-align: left;">{{data.address}}</td>
  </tr>
  <tr>
    <th style="width: 50%; text-align: right;">Email :</th>
    <td style="width: 50%; text-align: left;">{{data.Email|default_if_none:"---"}}</td>
  </tr>
  <tr>
    <th style="width: 50%; text-align: right;">NID :</td>
    <td style="width: 50%; text-align: left;">{{data.NID|default_if_none:"---"}}</td>
  </tr>
  <tr>
    <th style="width: 50%; text-align: right;">TIN :</th>
    <td style="width: 50%; text-align: left;">{{data.TIN|default_if_none:"---"}}</td>
  </tr>
  <tr>
    <th style="width: 50%; text-align: right;">Telephone No :</th>
    <td style="width: 50%; text-align: left;">{{data.TelephoneNo|default_if_none:"---"}}</td>
  </tr>
  <tr>
    <th style="width: 50%; text-align: right;">Mobile No :</th>
    <td style="width: 50%; text-align: left;">{{data.Mobile|default_if_none:"---"}}</td>
  </tr>
  <tr>
    <th style="width: 50%; text-align: right;">Status :</th>
    <td style="width: 50%; text-align: left; color: {{data.IsArchive|yesno:'red,green,yellow'}};">{{data.IsArchive|yesno:'Inactive,Active,maybe'}}</td>
  </tr>
</table>
</div>
{% endblock contents %}
{% block script %}
<script>
  // let elements = document.getElementsByTagName("style");
  // let pageNo=elements.
</script>
{% endblock script %}