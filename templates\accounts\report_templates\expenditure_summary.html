{% extends "main.html" %}
{% load static %} 
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% load mahimsoft_tags %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}{{company_name}}{%endblock %}
{% block style %}
<style>
    td, th {
        font-size: small;
    }
</style>
{% endblock style %}
{% block content %}

<div class="container pt-3" style="width: 80%; margin: auto;">
        <div class="d-flex justify-content-start align-items-center my-2"> 
        <h4 class="text-muted">Expenditure Summary</h4>
        <h5 class="text-danger ms-4">[Total Expenditure: {{ currency }} {{grand_total.amount__sum|intcomma_bd}}]</h5>
        </div>

    <div class="mb-2 text-end">
        <a href="/expenditureSummaryReport/" target="_blank" class="btn btn-sm btn-outline-danger"><i class="bi bi-download"></i> PDF</a>
            <a href='{{ request.META.HTTP_REFERER }}' class='btn btn-outline-primary btn-sm me-0 ms-1 mb-0' role='button'><i class="bi bi-arrow-left-square-fill"></i> Back</a>
    </div>
  {% regroup expenditure by work_phase_description as work_phase %}
      <div class="nav_tabs">
        <ul class="nav nav-tabs justify-content-center card-header-tabs" role="tablist">
            {% for phase in work_phase %}
            <li class="nav-item" role="presentation">
                <a class="nav-link x_nav-link {% if forloop.last %}active" aria-current="page{% endif %}" data-bs-toggle="pill" href="#{{ phase.grouper|remove_spaces_n_leading_digit }}">{{ phase.grouper }}</a>
                <hr class="text-white p-0 m-0 bg-white">
            </li>
            {% endfor %}
        </ul>
    </div>
<div class="tab-content">
  {% for phase in work_phase %}
  <div id="{{ phase.grouper|remove_spaces_n_leading_digit }}" class="tab-pane fade {% if forloop.last %}show active{% endif %}">
<div class="card rounded mb-2">
              <h5 class="card-header text-success" style="background-color:rgb(188, 213, 203);">
                <div class="d-flex justify-content-between p-0 m-0 align-items-center">
                <span>{{ phase.grouper }}:</span>
                <a href="{% url 'Accounts:expenditureSummaryReport' %}?workphase={{phase.list.0.work_phase_id}}" target="_blank" style="text-decoration: none; padding-bottom: 4px;">
                  <img src="{% static 'images/file-pdf.svg' %}" width="22px" heidht="22px" alt="PDF"></a>
            </div>
            </h5>
      <div class="card-body p-0 m-0">
        <table class="table table-bordered table-striped table-sm" style="width: 99%; margin: auto;">
          <tr>
            </div>
            </h5>
      <div class="card-body p-0 m-0">
        <table class="table table-bordered table-striped table-sm" style="width: 99%; margin: auto;">
          <tr>
            <th style="background-color: inherit; text-align:right; width:20%;">Start Date:</th>
            <td style="width:30%;">{{phase.list.0.work_phase_from_date}}</td>
            <th style="background-color: inherit; text-align:right; width:20%;">End Date:</th>
            <td>{{phase.list.0.work_phase_to_date}}</td>
          </tr>
          <tr>
            <th style="background-color: inherit; text-align:right; width:20%;">Estimated Cost:</th>
            <td  style="width:30%;">{{ currency }} {{phase.list.0.estimated_cost|intcomma_bd}}</td>
            <th style="background-color: inherit; text-align:right; width:20%;">Total Spent:</th>
            <td>{{ currency }} {{phase.list.0.work_phase_sum|intcomma_bd}}</td>
          </tr>
        </table>
      </div>
</div>
    
    
  {% regroup phase.list by work_sector as work_sector %}

{% for items in work_sector %}
<div class="card rounded mb-2">
<div class="card py-1 my-0" style="background-color: #DEDEDE;">
<h6 class="text-primary py-1 m-0 ps-3"><i class="bi bi-back"></i> {{ items.grouper }}</h6>
</div>
<div class="table-responsive-sm m-1" >
    <table class="table table-bordered table-striped table-sm">
        <thead>
            <tr>
                <th class="text-center" style="width: 5%;">#</th>
                <th class="text-center" style="width: 40%;">Item Name</th>
                <th class="text-center" style="width: 20%;">Quantity</th>
                <th class="text-center" style="width: 15%;">Unit</th>
                <th class="text-end  pe-5 me-5" style="width: 20%;">Amount ({{ currency }})</th>
            </tr>
        </thead>
        <tbody>

{% for item in items.list  %}
<tr>
    <td class = "text-center">{{forloop.counter}}</td>
    <td>{{item.item_name}}</td> 
    <td class="text-end pe-5 me-5">{{item.sum_quantity|intcomma_bd}}</td>       
    <td class="text-center">{{item.unit}}</td>
    <td class="text-end pe-5 me-5">{{item.sum_amount|intcomma_bd}}/=</td>
</tr>
{% endfor %}
<tr>
<th colspan="4" class="text-end">Total =</th>
<th class="text-end pe-5 me-5">{{items.list.0.worksector_sum|intcomma_bd}}/=</th>
</tr>
</tbody>
</table>
</div> 
</div> 
{% endfor %}
</div>
{% endfor %}

</div>
</div>

{% endblock %}