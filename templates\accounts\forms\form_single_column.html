{% extends "main.html" %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}{{company_name}}{%endblock %}
{% block content %}

<div class="container pt-3" style="width:70%;">
    <div class="card">
        <div class="card-header ps-2 py-1 me-0">
            <h4>{{heading}}</h4>
        </div>
        <div class="card-body">
            <form autocomplete="off" method="post" class="p-3 pb-0" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="form form-floating pe-4">
                    {{form|crispy}}
                </div>
                {% if update_tag is None %}
                <div class="d-flex justify-content-end mb-1" id="button_div">
                    <input type="submit" name="submit" value="Submit" class="btn btn-success btn-sm me-2 mb-0"
                        id="submit-id-submit">
                    <input type="reset" name="reset" value="Reset" class="btn btn-danger btn-sm me-2 mb-0"
                        id="reset-id-reset">
                    <a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 mb-0' role='button'>Go
                        Back</a>
                    {% else %}
                    <input type="submit" name="submit" value="Update" class="btn btn-primary btn btn-success me-2 mb-0"
                        id="submit-id-submit">
                    <a href="{{redirect_url}}" class="btn btn-danger">Cancel</a>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>
    <br>
    {% if update_tag is None %}
    <div class="card pb-0 mb-0">
        <div class="card-header ps-2 py-1">
            <h4>{{data_heading}}</h4>
        </div>
        <div class="card-body">
        <ul class="list-group list-group-flush">
            {% for dt in data %}

            {% if forloop.counter|divisibleby:2 %}
            <li class="ps-5 pe-5 list-group-item list-group-item-light">
                <div class="d-flex justify-content-between">- {{dt}}
                    {% if detail_tag == True %}
                    <a href="{{ dt.get_absolute_url}}"><i class="bi bi-file-earmark-text-fill"></i></a>
                    {% endif %}
                </div>
            </li>

            {% else %}
            <li class="ps-5 pe-5 list-group-item list-group-item-secondary">
                <div class="d-flex justify-content-between">- {{dt}}
                    {% if detail_tag == True %}
                    <a href="{{ dt.get_absolute_url}}"><i class="bi bi-file-earmark-text-fill"></i></a>
                    {% endif %}
                </div>
            </li>
            {% endif %}
            {% endfor %}
        </ul>
         </div>
    </div>
    {% endif %}
</div>
<br>
<br>

{% comment %} <!-- <ol>
    {% for key,val in request.META.items %}
    <li>{{key}}🌼{{val}}</li>

    {% endfor %}
</ol> --> {% endcomment %}
{% endblock %}