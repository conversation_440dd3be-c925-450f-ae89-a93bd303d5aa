{% extends "main.html" %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}{{company_name}}{%endblock %}
{% block content %}

<div class="container pt-3" style="width: 70%; margin: auto;">
    <div class="card">
        <div class="card-header ps-2 py-1">
            <h4>{{heading}}</h4>
        </div>
        <div class="card-body">
            {% crispy form %}
        </div>
    </div>
    <br>
    <div class="card">
        <div class="card-header ps-2 py-1">
            <h4>{{data_heading}}</h4>
        </div>
        {% regroup data by ItemCode as items %}
        {% for item in items %}
        <div class="d-flex justify-content-between p-2" style="background-color: #FFE4C4;">
        <h6 class="display-8">{{ item.grouper }}</h6>
        <h6 class="text-danger">[No. of Items: {{ item.list|length }}]</h6>
        </div>
        <ul class="list-group list-group-flush">
            {% for dt in item.list %}
            {% if forloop.counter|divisibleby:2 %}
            <li class="ps-5 pe-5 list-group-item list-group-item-light">
            <div class="d-flex justify-content-between">- {{dt}}
            {% if detail_tag == True %}
            <a href="{{ dt.get_absolute_url}}">Details</a>
            {% endif %}
            </div>
            </li>

            {% else %}
            <li class="ps-5 pe-5 list-group-item list-group-item-secondary">
            <div class="d-flex justify-content-between">- {{dt}} 
            {% if detail_tag == True %}
            <a href="{{ dt.get_absolute_url}}">Details</a>
            {% endif %}
            </div>
            </li>
            {% endif %}
            {% endfor %}
        </ul>
        {% endfor %}
    </div>
    </div>
    <br>
    {% endblock %}