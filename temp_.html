<!DOCTYPE html>
<html lang="en">
<head>
  <title>Bootstrap Example</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<style>
		/* Override Bootstrap's link active state styles */
	.nav-link {
		color: #fff !important;
		}	 
	.nav-link.active{
		color: rgb(42, 165, 73) !important;
		background-color: aliceblue !important;
	}
</style>
</head>
<body>
	<div class="container-fluid mt-3">
		<nav class="navbar navbar-expand-lg bg-body-tertiary ">
  <div class="container-fluid">
    <a class="navbar-brand" href="#">Navbar</a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarSupportedContent">
      <ul class="navbar-nav me-auto mb-2 mb-lg-0">
        <li class="nav-item">
          <a class="nav-link active" aria-current="page" href="#">Home</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#">Link</a>
        </li>
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
            Dropdown
          </a>
          <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#">Action</a></li>
            <li><a class="dropdown-item" href="#">Another action</a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item" href="#">Something else here</a></li>
          </ul>
        </li>
        <li class="nav-item">
          <a class="nav-link disabled" aria-disabled="true">Disabled</a>
        </li>
      </ul>
      <form class="d-flex" role="search">
        <input class="form-control me-2" type="search" placeholder="Search" aria-label="Search"/>
        <button class="btn btn-outline-success" type="submit">Search</button>
      </form>
    </div>
  </div>
</nav>
  <h3>Navbar Forms</h3>
  <p>You can also include forms inside the navigation bar.</p>

</div>

<nav class="navbar navbar-expand-sm navbar-dark bg-dark">
  <div class="container-fluid">
    <a class="navbar-brand" href="javascript:void(0)">Logo</a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mynavbar">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="mynavbar">
      <ul class="navbar-nav me-auto">
        <li class="nav-item">
          <a class="nav-link" href="javascript:void(0)">Link</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="javascript:void(0)">Link</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="javascript:void(0)">Link</a>
        </li>
      </ul>
      <form class="d-flex">
        <input class="form-control me-2" type="text" placeholder="Search">
        <button class="btn btn-primary" type="button">Search</button>
      </form>
    </div>
  </div>
</nav>

<div class="container-fluid mt-3">
  <h3>Navbar Forms</h3>
  <p>You can also include forms inside the navigation bar.</p>
</div>


	<div class="container text-center">
		<div class="mt-1"> 
			<h2 class="text-success"> 
				GeeksforGeeks 
			</h2> 
			<h3> 
				Card with Tabs 
				and content in Bootstrap 5 ?
			</h3> 
		</div> 
	<div class="row justify-content-center mt-2">
		<div class="col-md-6">
			<div class="card">
				<div class="card-header bg-success text-white">
					<ul class="nav nav-tabs card-header-tabs" id="myTab"
						role="tablist">
							<li class="nav-item">
								<a class="nav-link active" id="tab1-tab"
								data-bs-toggle="tab"
								href="#tab1" role="tab"
								aria-controls="tab1" aria-selected="true">
								Read
								</a>
							</li>
							<li class="nav-item">
								<a class="nav-link text-white" id="tab2-tab"
								data-bs-toggle="tab" href="#tab2" role="tab"
								aria-controls="tab2" aria-selected="false">
								Practice
								</a>
							</li>
							<li class="nav-item">
								<a class="nav-link text-white" id="tab3-tab"
								data-bs-toggle="tab" href="#tab3" role="tab"
								aria-controls="tab3" aria-selected="false">
								Learn
								</a>
							</li>
						</ul>
				</div>
				<div class="card-body">
					<div class="tab-content" id="myTabContent">
						<div class="tab-pane fade show active" id="tab1"
								role="tabpanel" aria-labelledby="tab1-tab">
							<p>
								<strong class="text-success">
										Company Profile and Brand
									</strong><br>
								GeeksforGeeks is a leading platform that provides 
								computer science resources and coding challenges 
								for programmers and technology enthusiasts, 
								along with interview and exam preparations for 
								upcoming aspirants. With a strong emphasis on 
								enhancing coding skills and knowledge, it has 
								become a trusted destination for over 12 million
								plus registered users worldwide. 
								<br/>
								Our exceptional mentors hailing from top colleges
								& organizations have the ability 
								<br/>
								Our brand is built on the pillars of expertise, 
								accessibility, and community. We strive to empower 
								individuals to enhance their programming skills, 
								to bridge the gap between academia and industry, 
								and provide a supportive community to the learners.								 
							</p>
								<a href=
							"https://www.geeksforgeeks.org/" class="text-success">
								Visit GeeksforGeeks
								</a> <!-- Example link -->
							</div>
							<div class="tab-pane fade" id="tab2" role="tabpanel"
								aria-labelledby="tab2-tab">
							<p>This is the content of Tab 2.</p>
							</div>
							<div class="tab-pane fade" id="tab3" role="tabpanel"
							aria-labelledby="tab3-tab">
							<p>This is the content of Tab 3.</p>
							</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div> 
<p class="muted">Placeholder text to demonstrate some 
    <a href="#" data-bs-toggle="tooltip" data-bs-title="Default tooltip">inline links</a> 
    with tooltips. This is now just filler, no killer. Content placed here just to mimic the presence of 
    <a href="#" data-bs-toggle="tooltip" data-bs-title="Another tooltip">real text</a>. 
    And all that just to give you an idea of how tooltips would look when used in real-world situations. So hopefully you’ve now seen how 
    <a href="#" data-bs-toggle="tooltip" data-bs-title="Another one here too">these tooltips on links</a> 
    can work in practice, once you use them on 
    <a href="#" data-bs-toggle="tooltip" data-bs-title="The last tip!">your own</a> site or project.</p>
</body>
<script>
const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]')
const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl))
</script>
</html>


