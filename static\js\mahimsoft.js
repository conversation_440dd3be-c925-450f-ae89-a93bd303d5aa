function textPlacing(text,element) {
    document.getElementById(element).innerHTML = text;
}

function jsColorTextRandom(text,element){
    x=[...text]; // converting word to array
    y="";
    for (let i = 0; i < x.length; i++) {
        y +="<span Style='color:rgb(" + Math.floor(Math.random() * 255) + "," + Math.floor(Math.random() * 255) + "," + Math.floor(Math.random() * 255) + ")'>" + x[i] + "</span>";
        }
    document.getElementById(element).innerHTML = y;
}

function jsColorTextGradient(text,element){
    let x=[...text]; // converting word to array
    let y="";
    let z = 255/x.length;
    for (let i = 0; i < x.length; i++) {
        y +="<span Style='color:rgb(200," + (Math.round(z)-2)*(i+1) +"," + Math.round(z)*i + ")'>" + x[i] + "</span>";
    }
    document.getElementById(element).innerHTML = y;
}

var numbers = {
    0: '০',
    1: '১',
    2: '২',
    3: '৩',
    4: '৪',
    5: '৫',
    6: '৬',
    7: '৭',
    8: '৮',
    9: '৯'
};

function replaceNumbers(input) {
    var output = [];
    for (var i = 0; i < input.length ?? 0; ++i) {
        if (numbers.hasOwnProperty(input[i])) {
            output.push(numbers[input[i]]);
        } else {
            output.push(input[i]);
        }
    }
    return output.join('');
}

function dateConversion(myDate, enMonthType, language, lineBreak) {
    var mydate = new Date(myDate);
    var convertedDate = "";
    if (language == 'En') {
        if (enMonthType == "sortMonth") {
            let month = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
                "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"][mydate.getMonth()];
            convertedDate = mydate.getDate() + ' ' + month + ' ' + mydate.getFullYear();
        } else {
            let month = ["January", "February", "March", "April", "May", "June",
                "July", "August", "September", "October", "November", "December"][mydate.getMonth()];
            convertedDate = mydate.getDate() + ' ' + month + ' ' + mydate.getFullYear();
        }
    } else {
        let month = ["জানুয়ারি", "ফেব্রুয়ারি", "মার্চ", "এপ্রিল", "মে", "জুন",
            "জুলাই", "আগস্ট", "সেপ্টেম্বর", "অক্টোবর", "নভেম্বর", "ডিসেম্বর"][mydate.getMonth()];
        let day = ["রবিবার", "সোমবার", "মঙ্গলবার", "বুধবার", "বৃহস্পতিবার", "শুক্রবার", "শনিবার"][mydate.getDay()]
        if (lineBreak == true) {
            convertedDate = mydate.getDate() + ' ' + month + ' ' + mydate.getFullYear() + ",<br>" + day;
        } else {
            convertedDate = mydate.getDate() + ' ' + month + ' ' + mydate.getFullYear() + ", " + day;
        }
        convertedDate = replaceNumbers(convertedDate);
    }
    return convertedDate
}

function confirmationAlert(formID, Message){
  document.addEventListener('DOMContentLoaded', function() {
    document.querySelector(formID).addEventListener('submit', function(event) {
      var confirmed = confirm(Message); //'Are you sure you want to submit this form?'
      if (!confirmed) {
        event.preventDefault(); // Prevent form submission
      }
    });
  });
}

function formatDateAsYYYYmmDD(date) {
    var d = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

    if (month.length < 2) 
        month = '0' + month;
    if (day.length < 2) 
        day = '0' + day;

    return [year, month, day].join('-');
}

// For Django message auto hide =======
$("#toast").delay(10000).fadeOut('slow');
// $('#toast').show(0).delay(5000).hide(0);


// Code for Testing Porpose =========================
// var ctx = document.getElementById('myChart').getContext('2d');
// var myChart = new Chart(ctx, {
//     type: 'pie',
//     data: {
//         labels: ['Red', 'Blue', 'Yellow'],
//         datasets: [{
//             data: [10, 20, 30],
//             backgroundColor: ['red', 'blue', 'yellow']
//         }]
//     },
//     options: {
//         tooltips: {
//             callbacks: {
//                 label: function (tooltipItem, data) {
//                     // Get the data label and value
//                     var label = data.labels[tooltipItem.index];
//                     var value = data.datasets[0].data[tooltipItem.index];

//                     // Format the tooltip value as a percentage
//                     var total = data.datasets[0].data.reduce(function (previousValue, currentValue) {
//                         return previousValue + currentValue;
//                     });
//                     var percentage = ((value / total) * 100).toFixed(2) + '%';

//                     // Return the formatted label and value
//                     return label + ': ' + value + ' (' + percentage + ')';
//                 }
//             }
//         }
//     }
// }
// )


// ====================================


// var ctx = document.getElementById('myChart').getContext('2d');
// var myChart = new Chart(ctx, {
//     type: 'bar',
//     data: {
//         labels: ['Label 1', 'Label 2', 'Label 3', 'Label 4', 'Label 5'],
//         datasets: [{
//             label: 'Dataset',
//             data: [12, 19, 3, 5, 2],
//             backgroundColor: 'rgba(0, 123, 255, 0.5)',
//             borderColor: 'rgba(0, 123, 255, 1)',
//             borderWidth: 1
//         }]
//     },
//     options: {
//         scales: {
//             y: {
//                 beginAtZero: true
//             }
//         },
//         plugins: {
//             title: {
//                 display: true,
//                 text: 'Customized Chart Labels',
//                 font: {
//                     size: 18,
//                     weight: 'bold'
//                 }
//             },
//             legend: {
//                 labels: {
//                     font: {
//                         size: 14,
//                         weight: 'normal'
//                     }
//                 }
//             }
//         }
//     }
// });
//==========================================

// options: {
//     plugins: {
//         datalabels: {
//             align: 'end',
//                 anchor: 'end',
//                     color: function(context) {
//                         return context.dataset.backgroundColor;
//                     },
//             font: function(context) {
//                 var w = context.chart.width;
//                 return {
//                     size: w < 512 ? 12 : 14,
//                     weight: 'bold',
//                 };
//             },
//             formatter: function(value, context) {
//                 return context.chart.data.labels[context.dataIndex];
//             }
//         }
//     },

//     // Core options
//     aspectRatio: 5 / 3,
//         layout: {
//         padding: {
//             top: 32
//         }
//     },
//     elements: {
//         line: {
//             fill: false,
//                 tension: 0.4
//         }
//     },
//     scales: {
//         x: {
//             display: false,
//                 offset: true
//         },
//         y: {
//             beginAtZero: true
//         }
//     }
// }
