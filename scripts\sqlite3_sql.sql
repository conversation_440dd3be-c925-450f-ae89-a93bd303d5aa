/* Random Number TODO: SELECT ABS(RANDOM()) % (HIGH - LOW) + LOW */
UPDATE Accounts_Shareholderdeposit
SET shareholder_id = ABS(RANDOM()) % (13 - 1) + 1;
SELECT ABS(RANDOM()) % (13 - 1) + 1;
-- Change image extension ===============
UPDATE nursery_cpvp
SET image = REPLACE(image, '.jpg', '.webp')
WHERE LOWER(image) LIKE '%.jpg';


UPDATE UPDATE nursery_cpvp
SET image = REPLACE(image, '.jpg', '.webp')
WHERE image LIKE '%.jpg';