{% extends "accounts/reports/reportbase.html" %}
{% load static %}
{% load mahimsoft_tags %}
{% load bangla_text %}
{% block title %}<title>Date Range Expenditure Details</title>{% endblock title %}

{% block link %}
  <link href="{% static 'page_css_portrait.css' %}" rel="stylesheet">
{% endblock link %}
{% block style %}
<style>
  @page {
    @bottom-right {
        content: '\00A9 {{data.company_name}}, Page No : ' counter(page) ' of ' counter(pages);
    }
}
</style>
{% endblock style %}

{% block contents %}
<div class="container" style="width: 90%; margin: auto;">
<p class="report_date">Date: {% now 'd-M-Y' %}</p>
<div class="flex_content_between" style="margin-top: 10px;">
  <h3 style="color: #7a7b7d; font-size: 20px; margin: 0px 0px 10px 0px;">Expenditure Details Report
    <span style="color: darkolivegreen; font-size: 14px;">[Date Range: {{data.fromdate|date:"d-M-Y"}}
      to {{data.todate|date:"d-M-Y"}}]</span></h3>
      <h4 style="color: #EE6A50; font-size: 16px; margin: 0px 0px 7px 0px;">Total: {{ data.currency }} {{data.grand_total.amount__sum|intcomma_bd}}</h4>
</div>
{% regroup data.expenditure by work_phase_description as work_phase %}
  {% for phase in work_phase %}
    <div class="pagebreak_after">
      <div style="border: 1px solid #ccc; border-radius: 6px; margin-bottom: 10px;">
        <h3 style="font-size: 16px; font-weight: bold; padding: 10px; border-radius-top: 7px; background-color:rgb(196, 206, 194); margin:0px 0px 0px 0px; color: #f14242;">
        {{ phase.grouper }}:
      </h3>
    <table style="width: 100%; margin: auto; margin-bottom: 0px;">
      <tr>
        <th style="background-color: inherit; text-align:right; width:20%;">Start Date:</th>
        <td style="width:30%;">{{phase.list.0.work_phase_from_date}}</td>
        <th style="background-color: inherit; text-align:right; width:20%;">End Date:</th>
        <td style="text-align: left;">{{phase.list.0.work_phase_to_date}}</td>
      </tr>
      <tr>
        <th style="background-color: inherit; text-align:right; width:20%;">Estimated Cost:</th>
        <td style="width:30%;">{{ data.currency }} {{phase.list.0.estimated_cost|intcomma_bd}}</td>
        <th style="background-color: inherit; text-align:right; width:20%;">Total Spent:</th>
        <td style="text-align: left;">{{ data.currency }} {{phase.list.0.work_phase_sum|intcomma_bd}}</td>
      </tr>
    </table>
</div>
{% regroup phase.list by work_sector as sectors %}
      {% for sector in sectors %}
          <div class="r_corners_5px" style="display: flex; justify-content: space-between; align-items: middle;">
              <h3 style="font-size: 16px;padding: 3px; font-weight: bold; margin:2px 0px 0px 0px; color: #f14242;">
                <img style="width: 14px; height: 14px;"
                src="{% static 'images/back.svg' %}" alt="telephone">&nbsp;{{ sector.grouper }}</h3>
              <p style="margin:5px 0px 0px 0px;">Total: {{ data.currency }} {{sector.list.0.worksector_sum|intcomma_bd}}</p>
          </div>

      {% regroup sector.list by item_name as item %}
      {% for row in item %}
      <div style="margin: 5px 0px 0px 0px;">
          <table style="width: 100%;">
              <thead>
            <tr>
                <td colspan="7">
                    <div style="display: flex; justify-content: space-between; padding: 5px; font-size: 14px; color: darkolivegreen; background-color: #ffffff; border: #ffffff;">
                        <h5 style="padding: 0px; margin: 0px; font-size: 16px;"><span style="font-size: 10px; margin: 0; padding: 0px 0px 0px 0px;">&#128309;</span> {{ row.grouper }}</h5>
                        <p style="padding: 0px; margin: 0px;">Total: {{ data.currency }} {{row.list.0.item_sum_amount|intcomma_bd}}</p>
                    </div>
                </td>
            </tr>
                  <tr>
                      <th style="width: 5%;">#</th>
                      <th style="width: 15%;">Date</th>
                      <th style="width: 30%;">Description</th>
                      <th style="width: 10%;">Quantity</th>
                      <th style="width: 10%;">Unit</th>
                      <th style="width: 15%;">Voucher No.</th>
                      <th style="width: 15%;">Amount ({{ data.currency }}</th>
                  </tr>
              </thead>
              <tbody>
                  
                  {% for dt in row.list %}
                                  <tr>
                                      <td style="text-align: center;">{{forloop.counter}}</td>
                                      <td>{{dt.dateOfTransaction}}</td>
                                      <td>{{dt.description|bangla_text_span|safe}}</td>
                                      <td style="text-align: center;">{{dt.quantity|intcomma_bd}}</td>
                                      <td style="text-align: center;">{{dt.units}}</td>
                                      <td style="text-align: center;">{{dt.voucherNo}}</td>
                                      <td style="text-align: right;">{{dt.amount|intcomma_bd}}/-</td>
                                  </tr>
                                  {% endfor %}
                  <tr>
                      <th colspan="3" style="text-align: right;">Total Quantity =</th>
                      <th>{{row.list.0.item_sum_quantity|intcomma_bd}}</th>
                      <th>{{row.list.0.units}}</th>
                      <th style="text-align: right;">Total Amount =</th>
                      <th style="text-align: right;">{{row.list.0.item_sum_amount|intcomma_bd}}/-</th>
                    
                  </tr>
              </tbody>
          </table>
      </div>
  
          {% endfor %}
          {% endfor %}
        </div>
          {% endfor %}
  </div>
{% endblock %}