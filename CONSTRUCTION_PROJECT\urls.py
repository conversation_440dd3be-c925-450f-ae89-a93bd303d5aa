from django.contrib import admin
from django.urls import path, include, re_path
from django.conf import settings
from CONSTRUCTION_PROJECT.settings.base import config
from django.conf.urls.static import static

if int(config["NOT_AVAILABLE"]) == 0:
    urlpatterns = [
        path("", include("Accounts.urls", namespace="Accounts")),
        path("admin/", admin.site.urls),
        path("registration/", include("registration.urls", namespace="registration")),
        # path("accounts/", include("django.contrib.auth.urls")),
        path('accounts/', include('allauth.urls')),
    ]
else:
    urlpatterns = [
    path("", include("Accounts.urls", namespace="Accounts")),
    re_path(r'.+', include("Accounts.urls", namespace="Accounts")),
    ]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

if settings.DEBUG:
    urlpatterns += static(
        settings.STATIC_URL, document_root=settings.STATICFILES_DIRS[0]
    )
