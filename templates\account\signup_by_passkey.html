{% extends "account/base_entrance.html" %}
{% load allauth i18n %}
{% block head_title %}
    {% trans "Signup" %}
{% endblock head_title %}
{% block content %}
    {% element h1 %}
        {% trans "Passkey Sign Up" %}
    {% endelement %}
    {% setvar link %}
        <a href="{{ login_url }}">
        {% endsetvar %}
        {% setvar end_link %}
        </a>
    {% endsetvar %}
    {% element p %}
        {% blocktranslate %}Already have an account? Then please {{ link }}sign in{{ end_link }}.{% endblocktranslate %}
    {% endelement %}
    {% url 'account_signup_by_passkey' as action_url %}
    {% element form form=form method="post" action=action_url tags="entrance,signup" %}
        {% slot body %}
            {% csrf_token %}
            {% element fields form=form unlabeled=True %}
            {% endelement %}
            {{ redirect_field }}
        {% endslot %}
        {% slot actions %}
            {% element button tags="prominent,signup" type="submit" %}
                {% trans "Sign Up" %}
            {% endelement %}
        {% endslot %}
    {% endelement %}
    {% element hr %}
    {% endelement %}
    {% element button href=signup_url tags="prominent,signup,outline,primary" %}
        {% trans "Other options" %}
    {% endelement %}
{% endblock content %}
