from datetime import date, datetime, time, timedelta
from django.contrib.auth.models import User
from Accounts.models import CurrentWorkPhase
from Accounts.models import UserLoggedinRecord
from django.utils import timezone
# from Accounts.models import DecimalField

from .base import config

# from fdr.models import CurrentFiscal

try:
    visitor_count= UserLoggedinRecord.objects.all().count(),
    unique_visitor_count = UserLoggedinRecord.objects.values("visitorIP").distinct().count(),
except:
    visitor_count = 0
    unique_visitor_count = 0

#! Setup ===================
 
work_phase_status = int(config["WORKPHASE"])
anonymous_company = int(config["ANONYMOUS"])
language = "en"

font_en_banner = "AeroSkyFont"
font_bn_banner = "b_fontkero"

font_en_other = "AeroSkyFont"
font_bn_other = "b_fontpadmo"

font_size_en_banner = "fs-5"
font_size_bn_banner = "fs-1"


def company_info_settings(request=None):
    if anonymous_company:
        if language == "en":
            company_name = "Name of The Institution"
            company_head_office = "Head Office"
            company_abr = "X.Tower"
            copy_right = "Mahim Soft | "
            company_address = (
                "Address (House No.###, Street, City, State, Postcode, County, Country)"
            )
            font_banner = font_en_banner
            font_other = font_en_other
            font_size_banner = font_size_en_banner
        else:
            company_name = "প্রতিষ্ঠান/কোম্পানির পূর্ন নাম"
            copy_right = "মাহিম সফ্‌ট | "
            company_abr = "এক্সটিডিসিএল"
            company_head_office = "প্রতিষ্ঠানের প্রধান কার্যালয়"
            company_address = (
                "প্রতিষ্ঠানের ঠিকানা (হাউজ নং-###, এলাকার নাম, শহরের নাম, পোস্ট কোড-####)"
            )
            font_banner = font_bn_banner
            font_other = font_bn_other
            font_size_banner = font_size_bn_banner
        # From static/icons Folder ===========
        company_logo_fevicon = "AeroSky3.png"
        company_logo_sm = "AeroSky3.png"
        company_logo_lg = "AeroSky3.png"
        company_logo_pdf = "AeroSky3.webp"
    else:
        if language == "en":
            copy_right      = config["COPY_RIGHT"]
            company_name    = config["COMPANY_NAME"]
            company_abr     = config["COMPANY_ABR"]
            company_address = config["COMPANY_ADDRESS"]
            company_head_office = ""
            #! Font asigning ================
            font_banner = font_en_banner
            font_size_banner = font_size_en_banner
            font_other = font_en_other
        else:
            copy_right      = config["COPY_RIGHT_BN"]
            company_name    = config["COMPANY_NAME_BN"]
            company_abr     = config["COMPANY_ABR_BN"]
            company_address = config["COMPANY_ADDRESS_BN"]
            company_head_office = ""
            #! Font asigning ======================
            font_banner = font_bn_banner
            font_other = font_bn_other
            font_size_banner = font_size_bn_banner
        # From static/icons Folder ===========
        company_logo_fevicon = "AeroSky3.png"
        company_logo_sm = "AeroSky3.png"
        company_logo_lg = "AeroSky3.png"
        company_logo_pdf = "AeroSky3.webp"
    time = timezone.now()
    total_number_of_flat = 45
    no_of_flat_per_share = 1
    work_phase = False
    if work_phase_status:
        work_phase =True

    return {
        "company_name": company_name,
        "company_abr": company_abr,
        "company_address": company_address,
        "company_head_office": company_head_office,
        "copy_right": copy_right,
        "work_phase":work_phase,
        #! Font assigning ======================
        "font_banner": font_banner,
        "font_other": font_other,
        "font_size_banner": font_size_banner,
        # Logo and Icons ========
        "company_logo_sm": company_logo_sm,
        "company_logo_lg": company_logo_lg,
        "company_logo_pdf": company_logo_pdf,
        "company_logo_fevicon": company_logo_fevicon,
        "time": time,
        "total_number_of_flat": total_number_of_flat,
        "no_of_flat_per_share": no_of_flat_per_share,
        "visitor_count": visitor_count,
        "unique_visitor_count": unique_visitor_count,
        "currency": "BDT",
        "currency_symbol": "Tk.",
    }
