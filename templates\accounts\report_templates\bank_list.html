{% extends "main.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% load mahimsoft_tags %}
{% block style %}
<style>
    .tooltip {
        position: relative;
        display: inline-block;
        border-bottom: 1px dotted black;
    }

    .tooltip .tooltiptext {
        visibility: hidden;
        width: 120px;
        background-color: black;
        color: #fff;
        text-align: center;
        border-radius: 6px;
        padding: 5px 0;

        /* Position the tooltip */
        position: absolute;
        z-index: 1;
    }

    .tooltip:hover .tooltiptext {
        visibility: visible;
    }
    td, th {
        font-size: small;
    }
</style>
{% endblock %}
{% block content %}
<div class="container pt-3" style="width: 90%; margin: auto;">
    <div>
        <h3 class="text-muted text-center">{{heading}}</h3>
    </div>
    <br>
<p class="text-end"><a href='{{ request.META.HTTP_REFERER }}' class='btn btn-outline-primary btn-sm me-0 ms-1 mb-0' role='button'><i class="bi bi-arrow-left-square-fill"></i> Back</a></p>
    <div class="table table-active">
        <table class="table table-sm align-middle table-bordered table-striped table-responsive">
            <tr>
                <th class="text-center">#</th>
                <th class="text-center">Account Name</th>
                <th class="text-center">Bank Name</th>
                <th class="text-center">Branch Name</th>
                <th class="text-center">Account Number</th>
                <th class="text-center">Opening Date</th>
                <th class="text-center">Remarks</th>
                <th colspan="2" class="text-center">Actions</th>
            </tr>
            {% for dt in bank %}
            <tr>
                <td class="text-center">{{forloop.counter}}</td>
                <td {% if dt.IsArchive == True %} class="text-muted text-decoration-line-through" {% endif %}>
                    {{dt.accountName}}
                </td>
                <td {% if dt.IsArchive == True %} class="text-muted text-decoration-line-through" {% endif %}>
                    {{dt.bankName}}
                </td>
                <td class="text-center">{{dt.branchName}}</td>
                <td>{{dt.accountNo}}</td>
                <td>{{dt.openingDate|date:"d b Y"|title}}</td>
                <td>{{dt.remarks}}</td>
                {% if request.user|has_group:"Admin"%}
                <td class="text-center"><a class="b_link" href="#"><i class="bi bi-trash"></i></a></td>
                <td class="text-center"><a class="c_link" href="/bank_account_update/{{dt.id}}"><i
                            class="bi bi-pencil-square"></i></a>
                    </a></td>
                {% endif %}
            </tr>
            {% endfor %}
        </table>
    </div>
</div>
<br>

{% endblock %}