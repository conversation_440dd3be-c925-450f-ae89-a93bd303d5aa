{% extends "main.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}{{company_name}}{%endblock %}
{% block content %}

<div class="container pt-3" style="width:70%;">
    <div class="card">
        <div class="card-header ps-2 py-1">
            <h4 style="align-items: center;" class="p-0 m-0"><img src="{% static 'images/raise-up.png' %}" alt="logout" style="width: 24px; height: 24px;"> {{heading}}</h4>
        </div>
        <div class="card-body">
            <form autocomplete="off" method="post" class="p-3 pb-0" enctype="multipart/form-data">
            {% crispy form %}
            </form>
        </div>
    </div>
    <br>

    <div class="card">
        <div class="card-header ps-2 py-1">
            <h4 class="p-0 m-0">{{data_heading}}</h4>
        </div>
        <ul class="list-group list-group-flush">
            {% for dt in data %}

            {% if forloop.counter|divisibleby:2 %}
            <li class="ps-5 pe-5 list-group-item list-group-item-light">
                <div class="d-flex justify-content-between">- {{dt}}
                    {% if detail_tag == True %}
                    <a href="{{ dt.get_absolute_url}}">Details</a>
                    {% endif %}
                </div>
            </li>

            {% else %}
            <li class="ps-5 pe-5 list-group-item list-group-item-secondary">
                <div class="d-flex justify-content-between">- {{dt}}
                    {% if detail_tag == True %}
                    <a href="{{ dt.get_absolute_url}}">Details</a>
                    {% endif %}
                </div>
            </li>
            {% endif %}
            {% endfor %}
        </ul>
    </div>
</div>
    <br>

{% endblock %}