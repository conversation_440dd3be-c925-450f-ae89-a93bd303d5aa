{% extends "main.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}{{company_name}}{%endblock %}
{% block content %}

<div class="container pt-3" style="width:50%;">
    <div class="d-flex justify-content-center mb-3">
        <img src="{{object.avatar}}" alt="masud"
            style="border-radius: 15%; width: 100px; height: 100px;  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);">
    </div>
    <h5 class="card-title bg-light p-2">Sub: Shareholder's Details Information</h5>
    <div class="card">
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-3 fw-bold">
                    <p class="card-text text-end">Name:</p>
                </div>
                <div class="col">
                    <h5 class="card-text text-start text-danger">{{object.shareholderName}}</h5>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-3 fw-bold">
                    <p class="card-text text-end">Date of Join:</p>
                </div>
                <div class="col">
                    <p class="card-text text-start">{{object.dateOfJoin|date:'d-b-Y'}}</p>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-3 fw-bold">
                    <p class="card-text text-end">Number{{ object.numberOfFlat|pluralize:"s" }} of Share:</p>
                </div>
                <div class="col">
                    <p class="card-text text-start">{{object.numberOfFlat}}</p>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-3 fw-bold">
                    <p class="card-text text-end">Address:</p>
                </div>
                <div class="col">
                    <p class="card-text text-start">
                        {% if object.address == None%}
                        ---
                        {% else %}
                        {{object.address}}
                        {% endif %}
                    </p>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-3 fw-bold">
                    <p class="card-text text-end">Email:</p>
                </div>
                <div class="col">
                    <p class="card-text text-start">{{object.email}}</p>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-3 fw-bold">
                    <p class="card-text text-end">NID:</p>
                </div>
                <div class="col">
                    <p class="card-text text-start">
                        {% if object.nid == None%}
                        ---
                        {% else %}
                        {{object.nid}}
                        {% endif %}
                    </p>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-3 fw-bold">
                    <p class="card-text text-end">TIN:</p>
                </div>
                <div class="col">
                    <p class="card-text text-start">

                        {% if object.TIN == None%}
                        ---
                        {% else %}
                        {{object.TIN}}
                        {% endif %}
                    </p>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-3 fw-bold">
                    <p class="card-text text-end">Mobile No:</p>
                </div>
                <div class="col">
                    <p class="card-text text-start">{{object.mobile}}</p>
                </div>

            </div>
        </div>

    </div>
    <div class="d-flex justify-content-end py-3">
        {% comment %} <!-- <a href="/shareholderDetails/{{object.id}}" target="_blank" class="btn btn-sm btn-outline-danger"><i
                class="bi bi-download"></i> PDF</a> --> {% endcomment %}
                <div class="d-flex justify-content-end">
                    <a href="{% url 'Accounts:shareholderDetails' object.id %}" target="_blank" class="btn btn-sm btn-outline-danger"><i class="bi bi-download"></i> PDF</a>
                    <a href='{{ request.META.HTTP_REFERER }}' class='btn btn-outline-primary btn-sm me-0 ms-1 mb-0' role='button'><i class="bi bi-arrow-left-square-fill"></i> Back</a>
                    </div>
    </div>
</div>

{% endblock %}