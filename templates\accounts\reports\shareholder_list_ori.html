{% extends "accounts/reports/reportbase.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% load mahimsoft_tags %}
{% block title %}<title>Shareholder List {% now 'd-M-Y' %}</title>{% endblock title %}
{% block style %}
<style>
  th,
  td {
    font-size: small;
  }

  @page {
    size: a4 landscape;
    margin: 5mm 5mm 10mm 5mm;
    counter-increment: page;

    /* @bottom-left {
      bottom:100px;
      content: attr(hr);
    } */
    @bottom-right {
      font-family: 'Times New Roman';
      content: '\00A9 {{data.copy_right}} Page No : ' counter(page) ' of ' counter(pages);
      white-space: pre;
      color: grey;
    }
  }
</style>
{% endblock style %}
{% block contents %}

<p class="text-end" style="margin:0;">Date: {% now 'd-M-Y' %}</p>
<h3 class="card card-title text-muted text-center mb-2 py-1">{{data.heading}}</h3>
<div class="table table-active my-1">
  <table class="table table-sm align-middle table-bordered table-striped table-hover table-responsive mt-1 mb-0">
    <thead>
    <tr>
      <th class="text-center"style="width: 2%;">#</th>
      <th class="text-center"style="width: 7%;">Photo</th>
      <th class="text-center"style="width: 20%;">Name</th>
      <th class="text-center"style="width: 10%;">NID</th>
      <th class="text-center"style="width: 10%;">Mobile & Email</th>
      <th class="text-center"style="width: 10%;">Nos. of Flat</th>
      <th class="text-center"style="width: 10%;">Deposited Amnt</th>
      <th class="text-center"style="width: 10%;">Rest Amnt to Pay</th>
    </tr>
    </thead>
    <tbody>
    {% for dt in data.shareholder %}
    <tr>
      <td class="text-center">{{forloop.counter}}</td>
      <td class="text-center"><img src="{{dt.avatar}}" class="card-img-top" alt="masud"
          style="width: 50px; height: 50px;">
      </td>
      <td class="ps-2" style="vertical-align: middle;">{{dt.shareholderName}}</td>

      {% if dt.nid == None %}
      <td class="ps-2 text-center" style="vertical-align: middle;">---</td>
      {% else %}
      <td class="ps-2" style="vertical-align: middle;">{{dt.nid|default_if_none:"---"}}</td>
      {% endif %}
      <td class="ps-2" style="vertical-align: middle;"><i class="bi bi-telephone"></i> {{dt.mobile|default_if_none:"---"}}
        <br><i class="bi bi-envelope-at"></i> {{dt.email|default_if_none:"---"}}
      </td>
      <td class="text-center">{{dt.numberOfFlat|floatformat:"1"}}</td>
      <td class="text-end pe-2">{{dt.sum_amount|default_if_none:0|intcomma_bd}}/-</td>
      {% if dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount > 0 %}
      <td class="text-end pe-2 text-danger">
        {{dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount|number_product:-1|intcomma_bd}}/-
      </td>
      {% elif dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount == 0 %}
      <td class="text-end pe-2">
        0/-
      </td>
      {% else %}
      <td class="text-end pe-2">
        {{dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount|number_product:-1|intcomma_bd}}/-
      </td>
      {% endif %}
    </tr>
    {% endfor %}
    </tbody>
  </table>
</div>
<p class="text-danger">NB: Rest amount to pay calculated as per targeted amount per share as {{ currency }}:
  {{data.no_of_flat_per_share|number_product:data.targeted_amount_per_flat|intcomma_bd}}/-</p>
{% endblock %}