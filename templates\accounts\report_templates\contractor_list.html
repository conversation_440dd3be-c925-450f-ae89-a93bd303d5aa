{% extends "main.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% load mahimsoft_tags %}
{% block extend_header%}{%endblock%}
{% block style %}
<style>
		/* Override Bootstrap's link active state styles */
	.nav-link_mahim {
		color: #000000 !important;
	
		}	 
	.nav-link.active{
		color: rgb(228, 6, 6) !important;
		background-color: aliceblue !important;
	}
    td, th {
        font-size: small;
    }
 /* .tab-content {
        padding: 1rem;
        border: 1px solid #ccc;
        border-radius: 0.25rem;
    }  */
</style>
{% endblock style %}
{% block content %}
<div class="container pt-3" style="width: 90%; margin: auto;">
    <div>
        <h3 class="text-muted text-center">{{heading}}</h3>
    </div>
    <br>
    <div class="d-flex justify-content-end mb-2"><a href="{% url 'Accounts:contractorListReport' %}" target="_blank" style="color: #046d3e; text-decoration: none;">
        <button class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i> PDF</button>
    </a>
    <a href='{{ request.META.HTTP_REFERER }}' class='btn btn-outline-primary btn-sm me-0 ms-1 mb-0' role='button'><i class="bi bi-arrow-left-square-fill"></i> Back</a>
</div>
<!-- TODO: Radio button 
<form action="" method="get">
<div class="d-flex justify-content-start mb-2 bg-light">
    <span>
  <input type="radio" id="all_contractors" name="options" value="1" 
  {% if options == "1" or not options %} checked {% endif %}>
  <label for="all_contractors">All Contractors</label>
    </span>
    <span class="ms-3">
  <input type="radio" id="active_contractors" name="options" value="2" {% if options == "2" %} checked {% endif %}>
  <label for="active_contractors">Active Contractors</label>
    </span>
    <span class="ms-3">
  <input type="radio" id="inactive_contractors" name="options" value="3" {% if options == "3" %} checked {% endif %}>
  <label for="inactive_contractors">Inactive Contractors</label>
    </span>
    <div class="ms-3">
        <button type="submit" class="btn btn-sm btn-outline-primary mt-0">Show</button>
    </div>
    </div>
</form>
TODO: Radio button end -->

 {% regroup contractor by IsArchive as contractors %}
     <div class="nav_tabs">
        <ul class="nav nav-tabs justify-content-strat card-header-tabs" role="tablist">
            {% for item in contractors %}
            <li class="nav-item" role="presentation">
                <a class="nav-link {% if forloop.first %}active" aria-current="page{% endif %}" data-bs-toggle="pill" href="#{{ item.grouper|yesno:'yeah,no,maybe'|remove_spaces_n_leading_digit }}" style="text-decoration: none;">{{ item.grouper|yesno:'Inactive Contractors,Active Contractors,maybe' }}</a>
            </li>
            {% endfor %}
        </ul>
    </div>
    <div class="tab-content">
    {% for contractor in contractors %}
    <div id="{{ contractor.grouper|yesno:'yeah,no,maybe'|remove_spaces_n_leading_digit }}" class="tab-pane fade {% if forloop.first %}show active{% endif %}">
    <h5 class="text-success" style="background-color:rgb(188, 213, 203);">
          <div class="d-flex justify-content-between align-items-center my-2 p-2">
          <span>{{ contractor.grouper|yesno:'Inactive Contractors,Active Contractors,maybe' }}:</span>
          <span><a href="{% url 'Accounts:contractorListReport' %}?isarchive={{contractor.list.0.IsArchive}}" target="_blank" class="text-danger" style="text-decoration: none;">
            <img src="{% static 'images/file-pdf.svg' %}" width="22px" heidht="22px" alt="PDF"></a></span>
      </div>
      </h5>
      


    <div class="table table-active">
        <table class="table table-sm align-middle table-bordered table-striped table-hover table-responsive">
            <tr>
                <th class="text-center">#</th>
                <th class="text-center">Photo</th>
                <th class="text-center">Name</th>
                <th class="text-center">Contractor Type</th>
                <th class="text-center">Mobile</th>
                <th class="text-center">Email</th>
                <th class="text-center">Date of Join</th>
                <th class="text-center">Status</th>
                <th colspan="3" class="text-center">Action</th>
            </tr>
            {% for dt in contractor.list %}
            <tr>
                <td class="text-center">{{forloop.counter}}</td>
                <td><img src="{{dt.avatar}}" class="card-img-top" alt="masud"
                        style="border-radius: 15%; width: 80px; height: 80px;  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);">
                </td>
                <td {% if dt.IsArchive == True %} class="text-muted text-decoration-line-through" {% endif %}>
                    {{dt.contractor}}
                </td>
                <td class="text-center">{{dt.contractorType|default_if_none:"---"}}</td>
                <td>{{dt.Mobile|default_if_none:"---"}}</td>
                <td>{{dt.Email|default_if_none:"---"}}</td>
                <td>{{dt.dateOfJoin|date:"d b Y"}}</td>
                <td class="{{dt.IsArchive|yesno:'text-danger,text-success'}}">{{dt.IsArchive|yesno:'Inactive,Active,maybe'}}</td>
                <td class="text-center"><a class="a_link" href="{{dt.get_absolute_url}}">
                        <i class="bi bi-file-earmark-text-fill"></a></i></td>
                {% if request.user|has_group:"Admin"%}
                <td class="text-center"><a class="b_link" href="#"><i class="bi bi-trash"></i></a></td>
                <td class="text-center"><a class="c_link" href="/contractor_update/{{dt.id}}"><i
                            class="bi bi-pencil-square"></i></a>
                    </a></td>
                {% endif %}
            </tr>
            {% endfor %}
        </table>
    </div>
    </div>
    {% endfor %}
</div>
</div>
<br>
{% endblock %}