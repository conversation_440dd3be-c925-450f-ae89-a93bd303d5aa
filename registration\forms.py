from django.forms import ModelForm
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User, Group
from django import forms


class CreateUserForm(UserCreationForm):
    group = forms.ModelChoiceField(queryset=Group.objects.all())
    class Meta:
        model = User
        fields = ["username", "email", "password1", "password2", "group"]
        # widgets = {
        #     "groups": forms.ChoiceField(),
        # }
