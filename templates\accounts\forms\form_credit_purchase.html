{% extends "main.html" %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}{{company_name}}{%endblock %}
{% block content %}

<div class="container pt-3">
    <div class="card">
        <div class="card-header ps-2 py-1">
            <h4>{{heading}}</h4>
        </div>
        <div class="card-body">
            {% crispy form %}
        </div>
    </div>
    <br>

    <div class="card">
        <div class="card-header ps-2 py-1">
            <h4>{{data_heading}}</h4>
        </div>
        <ul class="list-group list-group-flush">
            {% for dt in data %}
            {% if forloop.counter|divisibleby:2 %}
            <li class="ps-5 pe-5 list-group-item list-group-item-light">- {{dt}}</li>
            {% else %}
            <li class="ps-5 pe-5 list-group-item list-group-item-secondary">- {{dt}}</li>
            {% endif %}
            {% endfor %}
        </ul>
    </div>
    </div>
    <br>

    {% endblock %}
