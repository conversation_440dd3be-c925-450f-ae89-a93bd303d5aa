{% extends "account/base_entrance.html" %}
{% load i18n allauth account %}
{% block head_title %}
    {% trans "Password Reset" %}
{% endblock head_title %}
{% block content %}
    {% element h1 %}
        {% trans "Password Reset" %}
    {% endelement %}
    {% if user.is_authenticated %}
        {% include "account/snippets/already_logged_in.html" %}
    {% endif %}
    {% element p %}
        {% trans "Forgotten your password? Enter your email address below, <br> and we'll send you an email allowing you to reset it." %}
    {% endelement %}
    {% url 'account_reset_password' as reset_url %}
    {% element form form=form method="post" action=reset_url %}
        {% slot body %}
            {% csrf_token %}
            {% element fields form=form %}
            {% endelement %}
        {% endslot %}
        {% slot actions %}
            {% element button type="submit" %}
                {% trans 'Reset My Password' %}
            {% endelement %}
        {% endslot %}
    {% endelement %}
    {% element p %}
        {% blocktrans %}Please contact us if you have any trouble resetting your password.{% endblocktrans %}
    {% endelement %}
{% endblock content %}
