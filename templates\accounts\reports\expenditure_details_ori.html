{% extends "accounts/reports/reportbase.html" %}
{% load static %}
{% load mahimsoft_tags %}
{% block title %}<title>{{data.company_abr}}</title>{% endblock title %}

{% block style %}
<style>
  th,
  td {
    font-size: small;
  }

  @page {
    size: a4 portrait;
    margin: 5mm 5mm 10mm 5mm;
    counter-increment: page;

    @bottom-right {
      font-family: 'Times New Roman';
      content: '\00A9 {{data.copy_right}} Page No : ' counter(page);
      white-space: pre;
      color: grey;
    }
  } /* top | right | bottom | left    */
</style>
{% endblock style %}
{% block contents %}
<p class="text-end" style="margin:0;">Date: {% now 'd-M-Y' %}</p>
<div class="container pt-3">
  <div class="card p-1 mb-3 text-center">
    <h2 class="card-title text-muted p-0">Project Extenditure Details</h2>
  </div>
  {% regroup data.expenditure by work_sector as sectors %}
  {% for sector in sectors %}
    <div class="d-flex justify-content-between p-0 mb-2" style="background-color: #C4C4C4;">
      <h5 class="card-title p-0 pt-1 ps-2 m-0">{{ sector.grouper }}</h5>
      <p class="p-0 pt-1 pe-2 m-0">Total: {{ currency }} {{sector.list.0.worksector_sum|intcomma_bd}}</p>
    </div>
      {% regroup sector.list by item_name as item %}
      {% for row in item %}
          <div class="d-flex justify-content-between">
            <h5 class="p-0 ps-1 pt-1"><i class="bi bi-back"></i> {{ row.grouper }}</h5>
            <p class="pt-1 pe-2 pb-0 mb-0">Total: {{ currency }} {{row.list.0.sum_amount|intcomma_bd}}</p>
          </div>
  
          <div class="table-responsive-sm">
            <table class="table table-bordered table-striped table-sm">
              <thead>
          <tr>
            <th class="text-center" style="width: 5%;">#</th>
            <th class="text-center" style="width: 15%;">Date</th>
            <th class="text-center" style="width: 30%;">Description</th>
            <th class="text-center" style="width: 10%;">Quantity</th>
            <th class="text-center" style="width: 10%;">Unit</th>
            <th class="text-center" style="width: 15%;">Voucher No.</th>
            <th class="text-end pe-2" style="width: 15%;">Amount ({{ currency }}</th>
          </tr>
              </thead>
              <tbody>
                {% for dt in row.list %}
          <tr>
            <td class="text-center">{{forloop.counter}}</td>
            <td>{{dt.dateOfTransaction}}</td>
            <td>{{dt.description}}</td>
            <td class="text-end pe-2">{{dt.quantity|intcomma_bd}}</td>
            <td class="text-center">{{dt.units}}</td>
            <td class="text-center">{{dt.voucherNo}}</td>
            <td class="text-end pe-2">{{dt.amount|intcomma_bd}}/-</td>
          </tr>
                {% endfor %}
                <tr>
                  <th colspan="3" class="text-end">Total Quantity =</th>
                  <th class="text-end pe-2">{{row.list.0.sum_quantity|intcomma_bd}}</th>
                  <th>{{row.list.0.units}}</th>
                  <th class="text-end">Total =</th>
                  <th class="text-end pe-2">{{row.list.0.sum_amount|intcomma_bd}}/-</th>
                </tr>
              </tbody>
            </table>
          </div>
      {% endfor %}
    {% endfor %}
  
    <div class="d-flex justify-content-start">
      <h5 class="text-danger ps-1">Total Expenditure: {{ currency }} {{data.grand_total.amount__sum|intcomma_bd}}</h5>
    </div>
  </div>

  {% endblock %}