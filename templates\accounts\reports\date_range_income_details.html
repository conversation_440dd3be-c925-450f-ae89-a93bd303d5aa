{% extends "accounts/reports/reportbase.html" %}
{% load static %}
{% load mahimsoft_tags %}

{% block link %}
  <link href="{% static 'page_css_portrait.css' %}" rel="stylesheet">
{% endblock link %}

{% block title %}<title>{{data.company_abr}}</title>{% endblock title %}

{% block contents %}

<div>
        <h3>Sub: Income Details <span>[Date Range:
                {{data.fromdate|date:"d-b-Y"|title}}
                to {{data.todate|date:"d-b-Y"|title}}]</span></h3>

    <div>
        <h5>Total Income during the period: {{ data.currency }} {{data.grand_total.amount__sum|intcomma_bd}}
        </h5>
    </div>
    {% regroup data.income by income_sector as sectors %}
    {% for sector in sectors %}
        <div style="display: flex; justify-content: space-between;">
            <h5 style="font-size: 16px; margin: 0px; padding: 0px; color: #f14242;"><i class="bi bi-back"></i> {{ sector.grouper }}</h5>
            <p>Total: {{ data.currency }} {{sector.list.0.incomesector_sum|intcomma_bd}}</p>
        </div>

    {% regroup sector.list by income_item_name as item %}
    {% for row in item %}
    <div style="background-color: #FCE6C9;">
        <div>
            <h5>{{ row.grouper }}</h5>
            <p>Total: {{ data.currency }} {{row.list.0.sum_amount|intcomma_bd}}</p>
        </div>
    </div>
    <div>
        <table>
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 15%;">Date</th>
                    <th style="width: 30%;">Description</th>
                    <th style="width: 10%;">Quantity</th>
                    <th style="width: 10%;">Unit</th>
                    <th style="width: 15%;">Voucher No.</th>
                    <th style="width: 15%;">Amount ({{ data.currency }}</th>

                </tr>
            </thead>
            <tbody>

                {% for dt in row.list %}
                <tr>
                    <td>{{forloop.counter}}</td>
                    <td>{{dt.dateOfTransaction}}</td>
                    <td>{{dt.description}}</td>
                    <td>{{dt.quantity|intcomma_bd}}</td>
                    <td>{{dt.units}}</td>
                    <td>{{dt.voucherNo}}</td>
                    <td>{{dt.amount|intcomma_bd}}/-</td>
                </tr>
                {% endfor %}
                <tr>
                    <th colspan="3">Total Quantity =</th>
                    <th>{{row.list.0.sum_quantity|intcomma_bd}}</th>
                    <th>{{row.list.0.units}}</th>
                    <th>Total Amount =</th>
                    <th>{{row.list.0.sum_amount|intcomma_bd}}/-</th>
                </tr>
            </tbody>
        </table>
    </div>

    {% endfor %}
    {% endfor %}
    <p>{{data.company_name}}</p>
</div>

{% endblock %}