{% extends "accounts/reports/reportbase.html" %}
{% load static %}
{% load mahimsoft_tags %}
{% block title %}<title>{{data.company_abr}}</title>{% endblock title %}
{% block link %}
  <link href="{% static 'page_css_portrait.css' %}" rel="stylesheet">
{% endblock link %}
{% block style %}
<style>
  @page {
    @bottom-right {
        content: '\00A9 {{data.company_name}}, Page No : ' counter(page) ' of ' counter(pages);
    }
}

</style>
{% endblock style %}
{% block contents %}
<div class="container" style="width: 90%; margin: auto;">
  <p class="report_date">Date: {% now 'd-M-Y' %}</p>
  <div class="flex_content_between">
    <h2 id="heading">Extenditure Summary</h2>
      <div class="pagebreak_after:not">
      <h5 style="color: #f14242;">Total Expenditure: {{ data.currency }} {{data.grand_total.amount__sum|intcomma_bd}}</h5>
  </div>
  </div>

  {% regroup data.expenditure by work_phase_description as work_phase %}
  {% for phase in work_phase %}
    <div class="pagebreak_after">
    <div style="border: 1px solid #ccc; border-radius: 7px; margin-bottom: 5px;">
        <h3 style="font-size: 16px; font-weight: bold; padding: 10px; border-radius-top: 7px; background-color:rgb(196, 206, 194); margin:0px 0px 0px 0px; color: #f14242;">
        {{ phase.grouper }}:
      </h3>
      <div>
    <table style="width: 100%;">
      <tr>
        <th style="background-color: inherit; text-align:right; width:20%;">Start Date:</th>
        <td>{{phase.list.0.work_phase_from_date}}</td>
        <th style="background-color: inherit; text-align:right; width:20%;">End Date:</th>
        <td style="text-align: left;">{{phase.list.0.work_phase_to_date}}</td>
      </tr>
      <tr>
        <th style="background-color: inherit; text-align:right; width:20%;">Estimated Cost:</th>
        <td style="text-align: left;">{{ data.currency }} {{phase.list.0.estimated_cost|intcomma_bd}}</td>
        <th style="background-color: inherit; text-align:right; width:20%;">Total Spent:</th>
        <td style="text-align: left;">{{ data.currency }} {{phase.list.0.work_phase_sum|intcomma_bd}}</td>
      </tr>
    </table>
    </div>
    </div>
    
  {% regroup phase.list by work_sector as work_sector %}
  {% for items in work_sector %}
  <div style="border: 1px solid #ccc; border-radius: 7px; margin-bottom: 5px;">
        <div>
        <h3 style="font-size: 16px; font-weight: bold; padding: 10px; margin:0px 0px 0px 0px; background-color:rgb(236, 244, 199); color: #f14242;">
          <img style="width: 14px; height: 14px;"
          src="{% static 'images/back.svg' %}" alt="telephone">&nbsp;{{ items.grouper }}</h3>
      </div>
    <table style="width: 100%; margin: auto;">
      <thead>
        <tr>
          <th style="width: 5%;">#</th>
          <th style="width: 38%;">Item Name</th>
          <th style="width: 15%;">Quantity</th>
          <th style="width: 10%;">Unit</th>
          <th style="width: 18%;">Amount ({{ data.currency }})</th>
        </tr>
      </thead>
      <tbody>

        {% for item in items.list %}
        <tr>
          <td style="text-align: center;">{{forloop.counter}}</td>
          <td>{{item.item_name}}</td>
          <td style="text-align: center;">{{item.sum_quantity|intcomma_bd}}</td>
          <td style="text-align: center;">{{item.unit}}</td>
          <td style="text-align: right; padding-right: 15px;">{{item.sum_amount|intcomma_bd}}/=</td>
        </tr>
        {% endfor %}
        <th colspan="4" style="text-align: right; padding-right: 15px;">Total =</th>
        <th style="text-align: right; padding-right: 15px;">{{items.list.0.worksector_sum|intcomma_bd}}/=</th>

        </tr>
      </tbody>
    </table>
    </div>
  {% endfor %}
  </div>
  {% endfor %}
</div>

{% endblock contents %}