{% extends "main.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% load mahimsoft_tags %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}{{company_name}}{%endblock %}

{% block style %}
<style>
    td, th {
        font-size: small;
    }
</style>
{% endblock style %}

{% block content %}


<div class="card p-2 my-2 text-center">
    <h3 class="text-muted">Credit Purchase Payment History</h3>
</div>
<div class="d-flex justify-content-between mb-2">
    <h5 class="text-danger">Paid Amount (Grand Total): {{ currency }} {{grand_total_paid_bill.grand_total_paid_bill|intcomma_bd}}
    </h5>
    <div class="justify-content-end">
    <a href="/expenditureDetailsReport/" target="_blank" class="btn btn-sm btn-outline-danger">
        <i class="bi bi-download"></i> PDF</a>
        <a href='{{ request.META.HTTP_REFERER }}' class='btn btn-outline-primary btn-sm me-0 ms-1 mb-0' role='button'><i class="bi bi-arrow-left-square-fill"></i> Back</a>
    </div>
</div>
{% regroup credit_payment by seller as sellers %}
{% for bill in sellers %}
<div class="card mb-2">
    <div class="card-header p-1 d-flex justify-content-between">
        <h6 class="p-0 py-1 m-0"><i class="bi bi-back"></i> {{ bill.grouper }}</h6>
        <p class="text-danger pe-2 mb-0">Dues: {{ currency }}
            {{bill.list.0.bill_amount|subtract:bill.list.0.individual_credit_payment_total|intcomma_bd}}</p>
    </div>
    <div class="card-body p-0 m-0">
        {% regroup bill.list by bill as bills %}
        {% for row in bills %}
        <div class="table-responsive-sm">
            <table class="table table-bordered table-striped table-sm">
                <thead>
                    <tr>
                        <th class="text-center">#</th>
                        <th class="text-center">Payment Date</th>
                        <th class="text-center">Voucher No.</th>
                        <th class="text-end pe-4">Amount</th>
                        <th class="text-center">Remarks</th>
                        <!-- <th class="text-end pe-4">Labor Tea</th> -->
                        {% if request.user|has_group:"Admin"%}
                        <th class="text-center">Delete</th>
                        {% endif %}
                    </tr>
                </thead>
                <tbody>
                    {% if request.user|has_group:"Admin"%}
                    {% for dt in row.list %}
                    <tr class="">
                        <td class="text-center">{{forloop.counter}}</td>
                        <td>{{dt.dateOfTransaction}}</td>
                        <td class="text-center">{{dt.voucherNo}}</td>
                        <td class="text-end pe-4">{{dt.amount|intcomma_bd}}/-</td>
                        <td class="text-center">{{dt.remarks}}</td>
                        <td class="text-center text-danger"><a class="deposit ps-2"
                                href="/record_delete/{{dt.id}}/crp"><i class="bi bi-trash"></i></a>
                        </td>
                    </tr>
                    {% endfor %}
                    {% else %}

                    {% for dt in row.list %}
                    <tr>
                        <td class="text-center">{{forloop.counter}}</td>
                        <td>{{dt.dateOfTransaction}}</td>
                        <td class="text-center">{{dt.voucherNo}}</td>
                        <td class="text-end pe-4">{{dt.amount|intcomma_bd}}/-</td>
                    </tr>
                    {% endfor %}
                    {% endif %}
                    <tr>
                        <th colspan="3" class="text-end">Total Amount =</th>
                        <th class="text-end pe-4">{{row.list.0.individual_credit_payment_total|intcomma_bd}}/-</th>
                        <th colspan="2"></th>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    {% endfor %}
</div>
{% endfor %}


{% endblock %}