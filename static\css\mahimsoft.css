@charset "UTF-8";

@font-face {
  font-family: CompanyNameFont;
  src: url("./fonts/RussoOne-Regular.ttf");
}
@font-face {
  font-family: AeroSkyFont;
  src: url("./fonts/Audiowide-Regular.ttf");
}

@font-face {
  font-family: BalletRegular;
  src: url("./fonts/Ballet-Regular-VariableFont_opsz.ttf");
}

@font-face {
  font-family: GrechenFuemen;
  src: url("./fonts/GrechenFuemen-Regular.ttf");
}

@font-face {
  font-family: LoversQuarrel;
  src: url("./fonts/LoversQuarrel-Regular.ttf");
}
@font-face {
  font-family: Mrs<PERSON>heppards;
  src: url("./fonts/MrsSheppards-Regular.ttf");
}
@font-face {
  font-family: NothingYouCouldDo;
  src: url("./fonts/NothingYouCouldDo-Regular.ttf");
}
@font-face {
  font-family: Lobster;
  src: url("./fonts/Lobster-Regular.ttf");
}

.CompanyNameFont {
    font-family: CompanyNameFont;
  }
.AeroSkyFont {
    font-family: AeroSkyFont;
  }

.BalletRegular {
    font-family: BalletRegular;
  }

.GrechenFuemen {
    font-family: GrechenFuemen;
  }

.LoversQuarrel {
    font-family: LoversQuarrel;
  }

.MrsSheppards {
    font-family: MrsSheppards;
  }

.NothingYouCouldDo {
    font-family: NothingYouCouldDo;
  }
.Lobster {
    font-family: Lobster;
  }


/* Bangla Fonts */

/*=============== CSS File ================*/

@font-face {
  font-family: FontsNikosh2;
  src: url("./fonts/nikosh2.ttf");
}
@font-face {
  font-family: FontsSejuti;
  src: url("./fonts/Fazlay Sejuti Unicode.ttf");
}
@font-face {
  font-family: FontsEid;
  src: url("./fonts/Eid_Mubarak.ttf");
}
@font-face {
  font-family: FontsEkushey;
  src: url("./fonts/Ekushey_Belycon_06-09-2018.ttf");
}
@font-face {
  font-family: FontsKero;
  src: url("./fonts/KEERO___.ttf");
}
@font-face {
  font-family: FontsPadmo;
  src: url("./fonts/Padmo UNICODE.ttf");
}
@font-face {
  font-family: FontsMadon;
  src: url("./fonts/Li MadanMohan Unicode.ttf");
}
@font-face {
  font-family: digital7;
  src: url("./fonts/digital-7.ttf");
}

.b_fontNikosh2 {
  font-family: FontsNikosh2;
}
.b_fontSejuti {
  font-family: FontsSejuti;
}
.b_fonteid {
  font-family: FontsEid;
}
.b_fontekush {
  font-family: FontsEkushey;
}
.b_fontkero {
  font-family: FontsKero;
}
.b_fontpadmo {
  font-family: FontsPadmo;
}
.b_fontmadon {
  font-family: FontsMadon;
}
.digital7 {
  font-family: digital7;
}

/*! Link style */
a.a_link:link {
  color: #4169E1;
  background-color: transparent;
  text-decoration: none;
}

a.a_link:visited {
  color: #4169E1;
  background-color: transparent;
  text-decoration: none;
}

a.a_link:hover {
  color: red;
  background-color: transparent;
  text-decoration: underline;
}

a.a_link:active {
  color: yellow;
  background-color: transparent;
  text-decoration: underline;
}

a.b_link:link {
  color: red;
  background-color: transparent;
  text-decoration: none;
}

a.b_link:visited {
  color: Red;
  background-color: transparent;
  text-decoration: none;
}

a.b_link:hover {
  color: #FFA54F;
  background-color: transparent;
  text-decoration: underline;
}

a.b_link:active {
  color: yellow;
  background-color: transparent;
  text-decoration: underline;
}


a.c_link:link {
  color: green;
  background-color: transparent;
  text-decoration: none;
}

a.c_link:visited {
  color: green;
  background-color: transparent;
  text-decoration: none;
}

a.c_link:hover {
  color:yellow;
  background-color: transparent;
  text-decoration: underline;
}

a.c_link:active {
  color: yellow;
  background-color: transparent;
  text-decoration: underline;
}
/*! Link style End */
.isDisabled {
  color: currentColor;
  cursor: not-allowed;
  opacity: 0.5;
  text-decoration: none;
}

.footer1 a {
  font-size: 18px;
  display: inline-block;
  color: rgba(0, 0, 0, 0.7);
  text-decoration: none;
}

.footer2 a:hover {
  /* background: #1bbd36; */
  color: #1bbd36;
  text-decoration: none;
}

 /* 
div {
  font-family: "FontsNikosh2", Times, serif;
}

h {
  font-family: "FontsNikosh2", "Courier New", monospace;
}

p {
  font-family: "FontsNikosh2", Helvetica, sans-serif;
}

td {
  font-family: "b_fontpadmo", Helvetica, sans-serif;
}
th {
  font-family: b_fontpadmo, Helvetica, sans-serif;
}
table {
  font-family: "b_fontpadmo", Helvetica, sans-serif;
}
*/



/* Tooltip */

.tooltip_w3s {
  position: relative;
  display: inline-block;
  /* border-bottom: 1px dotted black; */
}

.tooltip_w3s .tooltiptext_w3s {
  visibility: hidden;
  width: 120px;
  background-color: #555;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px 2px 5px 2px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip_w3s .tooltiptext_w3s::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #555 transparent transparent transparent;
}

.tooltip_w3s:hover .tooltiptext_w3s {
  visibility: visible;
  opacity: 1;
}

/* Tooltip End */