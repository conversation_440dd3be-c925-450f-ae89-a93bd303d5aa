{% extends "accounts/reports/reportbase.html" %}
{% load static %}
{% block title %}<title>{{data.heading}}</title>{% endblock title %}
{% block link %}
  <link href="{% static 'page_css_portrait.css' %}" rel="stylesheet">
{% endblock link %}
{% block style %}
<style>
  @page {
    @bottom-right {
        content: '\00A9 {{data.company_name}}, Page No : ' counter(page) ' of ' counter(pages);
    }
}
tr {
    height: 35px;
}
th {
    font-size: 14px; 
}
td {
    font-size: 13px; 
}
</style>
{% endblock style %}
{% block contents %}
<p class="report_date" style="margin:0;">Date: {% now 'd-M-Y' %}</p>
<div>
    <div class="card">
        <h3 id="heading">{{data.heading}}</h3>
    </div>

 {% regroup data.contractor by IsArchive as contractors %}
    {% for contractor in contractors %}
    <div>
          <div style="background-color:rgb(188, 213, 203); border-radius: 7px; padding: 10px; margin-bottom: 10px;">
          <h4 style="margin: 0px; padding: 0px;">{{ contractor.grouper|yesno:'Inactive Contractors,Active Contractors,maybe' }}:</h4>
      </div>
        <table style="margin: 5px 0px 20px 0px; padding: 0px;">
            <tr>
                <th>#</th>
                <th>Photo</th>
                <th>Name</th>
                <th>Contractor Type</th>
                <th>Mobile</th>
                <th>Email</th>
                <th>Date of Join</th>
                <th>Status</th>
                
            </tr>
            {% for dt in contractor.list %}
            <tr>
                <td>{{forloop.counter}}</td>
                <td><img src="{{dt.avatar}}" alt="masud"
                        style="border-radius: 15%; width: 80px; height: 80px;  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);">
                </td>
                <td>
                    {{dt.contractor}}
                </td>
                <td>{{dt.contractorType|default_if_none:"---"}}</td>
                <td>{{dt.Mobile|default_if_none:"---"}}</td>
                <td>{{dt.Email|default_if_none:"---"}}</td>
                <td>{{dt.dateOfJoin|date:"d b Y"}}</td>
                <td style="color:{{dt.IsArchive|yesno:'red,green'}}">{{dt.IsArchive|yesno:'Inactive,Active,maybe'}}</td>
            </tr>
            {% endfor %}
        </table>
    </div>
    {% endfor %}
</div>
<br>
{% endblock %}