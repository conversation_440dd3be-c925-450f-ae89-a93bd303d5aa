{% extends "accounts/reports/reportbase.html" %}
{% load static %}
{% load mahimsoft_tags %}

{% block link %}
<link href="{% static 'page_css_portrait.css' %}" rel="stylesheet">
{% endblock link %}
{% block title %}<title>Project Income Details</title>{% endblock title %}
{% block style %}
<style>
  @page {
    @bottom-right {
        content: '\00A9 {{data.company_name}}, Page No : ' counter(page) ' of ' counter(pages);
    }
}
</style>
{% endblock style %}

{% block contents %}
<div class="container" style="width: 90%; margin: auto;">
    <p class="report_date">Date: {% now 'd-M-Y' %}</p>
    <div class="card">
        <h3 id="heading">Project Income Details</h3>
    </div>
    <div class="mb-2">
        <h4 style="color: #EE6A50;">Total Income: {{ data.currency }} {{data.grand_total.amount__sum|intcomma_bd}}</h4>
    </div>
    {% regroup data.income by income_sector as sectors %}
    {% for sector in sectors %}
    <div class="pagebreak_after">
    <div class="flex_content_between" style="border: 1px solid #ccc; background-color:rgb(196, 206, 194); border-radius: 6px; margin-bottom: 10px;">
        <h3 style="font-size: 16px; font-weight: bold; padding: 10px; border-radius-top: 7px; margin:0px 0px 0px 0px; color: #f14242;">
        {{forloop.counter}}. {{ sector.grouper }}:
      </h3>
      <p style="margin: 0px; padding: 5px;">Total: {{ data.currency }} {{sector.list.0.incomesector_sum|intcomma_bd}}</p>
      </div>

    {% regroup sector.list by income_item_name as item %}
    {% for row in item %}
        <table style="width: 100%; margin-top: 5px 0px 0px 0px;">
            <thead>
            <tr style="height: 24px;">
              <td colspan="7" style="padding: 0px; margin: 0px;">
                  <div style="display: flex; justify-content: space-between; align-items: middle; padding: 5px; background-color: #ffffff; border: #ffffff;">
                      <h3 style="padding: 0px; margin: 0px;"><span style="margin: 0; padding: 0px 0px 0px 0px;">&#128309;</span> {{ row.grouper }}</h3>
                      <p style="padding: 0px; margin: 0px;">Total Spent: {{ data.currency }} {{row.list.0.sum_amount|intcomma_bd}}</p>
                  </div>
              </td>
          </tr>
                <tr>
                    <th style="text-align: center; width: 5%;">#</th>
                    <th style="text-align: center; width: 15%;">Date</th>
                    <th style="text-align: center; width: 30%;">Description</th>
                    <th style="text-align: center; width: 10%;">Quantity</th>
                    <th style="text-align: center; width: 10%;">Unit</th>
                    <th style="text-align: center; width: 15%;">Voucher No.</th>
                    <th style="text-align: center; width: 15%;">Amount ({{ data.currency }})</th>

                </tr>
            </thead>
            <tbody>

                {% for dt in row.list %}
                <tr class="">
                    <td>{{forloop.counter}}</td>
                    <td>{{dt.dateOfTransaction}}</td>
                    <td>{{dt.description}}</td>
                    <td>{{dt.quantity|intcomma_bd}}</td>
                    <td>{{dt.units}}</td>
                    <td>{{dt.voucherNo}}</td>
                    <td>{{dt.amount|intcomma_bd}}/-</td>
                </tr>
                {% endfor %}
                <tr>
                    <th colspan="3">Total Quantity =</th>
                    <th>{{row.list.0.sum_quantity|intcomma_bd}}</th>
                    <th>{{row.list.0.units}}</th>
                    <th>Total Amount =</th>
                    <th>{{row.list.0.sum_amount|intcomma_bd}}/-</th>
                </tr>
            </tbody>
        </table>

    {% endfor %}
    </div>
    {% endfor %}
</div>

{% endblock %}