
{% load static %}
{% load i18n %}
{% load mahimsoft_tags %}
<style>
/* ============ desktop view ============ */
@media all and (min-width: 992px) {
	.dropdown-menu li{ position: relative; 	}
	.nav-item .submenu{ 
		display: none;
		position: absolute;
		left:100%; top:-7px;
	}
	.nav-item .submenu-left{ 
		right:100%; left:auto;
	}
	.dropdown-menu > li:hover{ background-color: #f1f1f1 }
	.dropdown-menu > li:hover > .submenu{ display: block; }
}	
/* ============ desktop view .end// ============ */

/* ============ small devices ============ */
@media (max-width: 991px) {
  .dropdown-menu .dropdown-menu{
      margin-left:0.7rem; margin-right:0.7rem; margin-bottom: .5rem;
  }
}	
/* ============ small devices .end// ============ */
  .hello-msg {
    font-size: 18px;
    color: #fff;
    margin-right: 20px;
    text-decoration: inherit;
  }

  a:hover {
    color: rgb(7, 240, 96);
  }
    </style>

<nav class="navbar sticky-top navbar-expand-lg py-0 navbar-dark bg-dark">
<div class="container-fluid" style="vertical-align: middle;">
    <a class="navbar-brand" href="{% url 'Accounts:index' %}">
      <img class="bg-secondary-subtle pt-0 text-start" style="vertical-align: middle; width: 36px; height: 36px;"
        src="{% get_static_prefix %}images/{{company_logo_lg}}">
      
      <span class="hello-msg {{font_banner}} {{font_size_banner}} p-0 m-0" style="color:#6CA6CD;">{{company_name}}</span>
      {% comment %} <img src="{% static 'images/dream_tower.svg' %}" style="vertical-align: middle; width: 210px; height: 18px;"
        alt=""> {% endcomment %}
      </a>

<button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#main_nav"  
 aria-controls="navbarText" aria-expanded="false" aria-label="Toggle navigation">
<span class="navbar-toggler-icon"></span>
</button>

<div class="collapse navbar-collapse" id="main_nav">
  <ul class="navbar-nav">
    <!-- Expenditure -->
    <li class="nav-item dropdown" id="myDropdown">
      <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" 
      aria-expanded="false">Expenditure
      </a>
      <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
        <li><a class="dropdown-item" href="{% url 'Accounts:expenditure_posting' %}">Expenditure Posting</a></li>
        <hr class="dropdown-divider my-0">
        <li><a class="dropdown-item" href="{% url 'Accounts:credit_purchase' %}">Credit Purchase</a></li>
        <li><a class="dropdown-item" href="{% url 'Accounts:credit_purchase_payment' %}">Credit Purchase Payment</a>
        </li>
        <hr class="dropdown-divider my-0">
        <li><a class="dropdown-item" style="background-color: #E0FFFF; width: 85%;" href="#">
            <div class="d-flex justify-content-between">
            <span><i class="bi bi-file-earmark-text"></i> Reports</span>
            <span><i class="bi bi-play-fill"></i></span>
            </div>
          </a>
          <ul class="submenu dropdown-menu">
<li><a class="dropdown-item" href="{% url 'Accounts:expenditure_summary' %}">Expenditure Summary</a></li>
          <li><a class="dropdown-item" href="{% url 'Accounts:expenditure_details_list' %}">Expenditure Details</a></li>
          <li><a class="dropdown-item" href="{% url 'Accounts:date_range_expenditure' %}">Date Range Expenditure</a></li>
          <hr class="dropdown-divider my-0">
          <li><a class="dropdown-item" href="{% url 'Accounts:credit_purchase_payment_history' %}">Credit Purchase Payment History</a></li>
          </ul>
        </li>
        <hr class="dropdown-divider my-0">
        <li><a class="dropdown-item" style="background-color: #E0FFFF; width: 85%;" href="#">
            <div class="d-flex justify-content-between">
            <span><i class="bi bi-plus-square"></i> ADD</span>
            <span><i class="bi bi-play-fill"></i></span>
            </div>
        </a>
          <ul class="submenu dropdown-menu">
        <li><a class="dropdown-item" href="{% url 'Accounts:item' %}"><i class="bi bi-ui-checks"></i> Add Item</a>
        <li><a class="dropdown-item" href="{% url 'Accounts:itemcode' %}"><i class="bi bi-diagram-2-fill"></i> Add Work Sector</a></li>
        </li>
          </ul>
        </li>
      </ul>
      </li>
      <!-- Expenditure end -->
      <!-- Shareholder -->
    <li class="nav-item dropdown" id="myDropdown">
      <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" 
      aria-expanded="false">Shareholder
      </a>
      <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
        <li><a class="dropdown-item" href="{% url 'Accounts:shareholder_deposit' %}"><i class="bi bi-server"></i> Shareholder Deposit</a></li>
        <hr class="dropdown-divider my-0">
        <li><a class="dropdown-item" style="background-color: #E0FFFF; width: 86%;" href="#">
            <div class="d-flex justify-content-between align-items-center">
            <span><img src="{% static '/images/email.png' %}" alt="logout" class="p-0 mb-2" style="width: 16px; height: 16px;"> Mail to Shareholders</span>
            <span><i class="bi bi-play-fill"></i></span>
            </div>
        </a>
          <ul class="submenu dropdown-menu">
            <li><a class="dropdown-item" href="{% url 'Accounts:send_mail' %}">Send Mail</a></li>
            <li>
              <a class="dropdown-item" href="{% url 'Accounts:sendMailshareholderDepositReport' %}">Send mail for
                Shareholder Deposit info</a>
            </li>
          </ul>
        </li>

            <hr class="dropdown-divider my-0">
            <li><a class="dropdown-item text-danger" href="{% url 'Accounts:targetedamount' %}">
            <img src="{% static 'images/raise-up.png' %}" alt="logout" style="width: 16px; height: 16px;"> Assign New Deposit Target</a></li>
                        <li><a class="dropdown-item" href="{% url 'Accounts:shareholder_list' %}"><i class="bi bi-file-earmark-text"></i> Shareholder List</a></li>
        <li><a class="dropdown-item" href="{% url 'Accounts:shareholder' %}"><i class="bi bi-person-plus-fill"></i>
            Add Shareholder</a></li>
      </ul>
      </li>
      <!-- Shareholder end -->

      <!-- Contractor -->
    <li class="nav-item dropdown" id="myDropdown">
      <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" 
      aria-expanded="false">Contractor
      </a>
      <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
            <li><a class="dropdown-item" href="{% url 'Accounts:contractor_bill_submission' %}">Contractor Bill
                Submission</a>
            </li>
            <li><a class="dropdown-item" href="{% url 'Accounts:contractor_bill_payment' %}">Contractor Bill Payment</a>
            </li>
        <hr class="dropdown-divider my-0">

          <li><a class="dropdown-item" style="background-color: #E0FFFF; width: 86%;" href="#">
            <div class="d-flex justify-content-between">
            <span><i class="bi bi-file-earmark-text"></i> Reports</span>
            <span><i class="bi bi-play-fill"></i></span>
            </div>
        </a>
          <ul class="submenu dropdown-menu">
          <li><a class="dropdown-item" href="{% url 'Accounts:contractor_list' %}">Contractor List</a></li>
<li><a class="dropdown-item" href="{% url 'Accounts:contractor_bill_payment_history' %}">Contractor Bill Payment History</a></li>
          </ul>
        </li>
          <hr class="dropdown-divider my-0">
          <li><a class="dropdown-item" style="background-color: #E0FFFF; width: 86%;" href="#">
            <div class="d-flex justify-content-between">
            <span><i class="bi bi-plus-square"></i> ADD</span>
            <span><i class="bi bi-play-fill"></i></span>
            </div>
        </a>
          <ul class="submenu dropdown-menu">
        <li><a class="dropdown-item" href="{% url 'Accounts:contractor' %}"><i class="bi bi-people-fill"></i> Add
            Contractor</a></li>
        <li><a class="dropdown-item" href="{% url 'Accounts:contractortype' %}"><i class="bi bi-person-lines-fill"></i>
            Add Contractor Type</a></li>
          </ul>
        </li>
      </ul>
      </li>
      <!-- Contractor end -->
      <!-- work_phase -->
    <li class="nav-item dropdown" id="myDropdown">
      <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" 
      aria-expanded="false">Work Phase
      </a>
      <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
          <li><a class="dropdown-item" href="{% url 'Accounts:work_phase_change' %}"><i class="bi bi-arrow-left-right"></i> Change Work Phase</a></li>
          <hr class="dropdown-divider my-0">
          <li><a class="dropdown-item" href="{% url 'Accounts:work_phase' %}"><i class="bi bi-diagram-3-fill"></i> Add New Work Phase</a></li>
          </ul>
      </li>
      <!-- Work Phase end -->
      <!-- Income -->
      <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown"
          aria-expanded="false">
          Income
        </a>
        <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
          <li><a class="dropdown-item" href="{% url 'Accounts:income_add' %}"><i class="bi bi-server"></i> Income Posting</a></li>
          
            <hr class="dropdown-divider my-0">
          <li><a class="dropdown-item" href="{% url 'Accounts:details_income_list' %}"><i class="bi bi-file-earmark-text"></i> Details Income Report</a></li>
          <li><a class="dropdown-item" href="{% url 'Accounts:date_range_income' %}"><i class="bi bi-file-earmark-text"></i> Date Range Income Report</a></li>
          <hr class="dropdown-divider my-0">
          <li><a class="dropdown-item" style="background-color: #E0FFFF; width: 87%;" href="#">
            <div class="d-flex justify-content-between">
            <span><i class="bi bi-plus-square"></i> ADD</span>
            <span><i class="bi bi-play-fill"></i></span>
            </div>
        </a>
          <ul class="submenu dropdown-menu">
        <li><a class="dropdown-item" href="{% url 'Accounts:income_item_add' %}">Add Income Item</a></li>
          <li><a class="dropdown-item" href="{% url 'Accounts:income_sector_add' %}">Add Income Sector</a></li>
          </ul>
        </li>
        </ul>
      </li>
      <!-- Income end -->
      <!-- Bank and Cash -->
      <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown"
          aria-expanded="false">
          Bank
        </a>
        <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
          <li><a class="dropdown-item" href="{% url 'Accounts:bank_transaction' %}">Bank Transaction</a></li>

          <li>
            <hr class="dropdown-divider my-0">
          </li>
          <li><a class="dropdown-item" href="{% url 'Accounts:add_new_bank_account' %}"><i class="bi bi-plus-square"></i> Add New Bank Account</a></li>

          <li>
            <hr class="dropdown-divider my-0">
          </li>
          <li><a class="dropdown-item" href="{% url 'Accounts:bank_list' %}"><i class="bi bi-file-earmark-text"></i> Bank List</a></li>


        </ul>
      </li>
<!-- Bank and Cash end -->
<!-- User -->
{% if user.is_authenticated %}
        <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown"
            aria-expanded="false">
            User
          </a>

          <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
            <li><a class="dropdown-item" href="{% url 'account_reset_password' %}">
            <img src="{% static '/images/lock_15394975.png' %}" alt="logout" style="width: 20px; height: 20px;"> {% trans "Reset Password" %}</a></li>
            {% if request.user|has_group:"Admin"%}
            <li>
              <a class="dropdown-item text-danger" href="{% url 'registration:registerPage' %}">
            {% trans '<i class="bi bi-person-fill-add"></i>' %} &nbsp;Add New User</a>
            </li>
            <li>
              <a class="dropdown-item" href="{% url 'account_logout' %}">
            <img src="{% static '/images/log-out.png' %}" alt="logout" style="width: 20px; height: 20px;">
             Log Out</a> 
            </li>
            {% endif %}
          </ul>
        </li>
{% endif %}
<!-- User end -->
  </ul>
  <div class="navbar-nav ms-auto me-0">
        <nobr>
        <span class="navbar-text me-0 pe-0">
        {% if user.is_authenticated %}
        <span class="hello-msg nav-item"><span class="Lobster {{font_size_banner}}" style="color:#6CA6CD;">Hello,</span>
          {{request.user|title}}</span>

        {% else %}
        <span class="hello-msg nav-item"><a class="hello-msg" href="{% url 'account_login' %}">{% trans "Login" %}</a></span>
        {% endif %}
      </span>
      </nobr>
      </div>
</div>
<!-- navbar-collapse.// -->
</div>
<!-- container-fluid.// -->
</nav>

<!-- Default dropend button -->
 <script>
document.addEve0ntListener("DOMContentLoaded", function(){
// make it as accordion for smaller screens
if (window.innerWidth < 992) {

  // close all inner dropdowns when parent is closed
  document.querySelectorAll('.navbar .dropdown').forEach(function(everydropdown){
    everydropdown.addEventListener('hidden.bs.dropdown', function () {
      // after dropdown is hidden, then find all submenus
        this.querySelectorAll('.submenu').forEach(function(everysubmenu){
          // hide every submenu as well
          everysubmenu.style.display = 'none';
        });
    })
  });

  document.querySelectorAll('.dropdown-menu a').forEach(function(element){
    element.addEventListener('click', function (e) {
        let nextEl = this.nextElementSibling;
        if(nextEl && nextEl.classList.contains('submenu')) {	
          // prevent opening link if link needs to open dropdown
          e.preventDefault();
          if(nextEl.style.display == 'block'){
            nextEl.style.display = 'none';
          } else {
            nextEl.style.display = 'block';
          }

        }
    });
  })
}
// end if innerWidth
}); 
// DOMContentLoaded  end

 </script>