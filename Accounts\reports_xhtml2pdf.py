from django.contrib.auth.decorators import login_required
from xhtml2pdf import pisa, default
from xhtml2pdf.default import DEFAULT_CSS
from xhtml2pdf.files import pisaFileObject
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
from io import BytesIO
from django.template.loader import get_template
from django.http import HttpResponse
from .models import *
from django.db.models.functions import Cast, Round, Concat, Coalesce, Extract, Trunc
from django.db.models import (
    F,
    Sum,
    Count,
    Q,
    Case,
    When,
    Value,
    ExpressionWrapper,
    Func,
    DateTimeField,
    DateField,
    IntegerField,
    CharField,
    DecimalField,
    Subquery,
    OuterRef,
    DurationField,
    FloatField,
    )
from CONSTRUCTION_PROJECT.settings.context_processors import company_info_settings
import os
from django.conf import settings
from .decorators import time_of_execution

company_info = company_info_settings()

pisaFileObject.getNamedFile = lambda self: self.uri
pdfmetrics.registerFont(TTFont('BanglaFont', 'static/fonts/SolaimanLipi.ttf'))
pdfmetrics.registerFont(TTFont('BanglaFontSejuti', 'static/fonts/Fazlay Sejuti Unicode.ttf'))
pdfmetrics.registerFont(TTFont('BanglaFontNikosh', 'static/fonts/Nikosh.ttf'))

def fetch_resources(uri, rel=None):
    if uri.startswith('/static/'):
        return os.path.join(settings.BASE_DIR, uri[1:])
    return uri

def render_to_pdf(template_src, context_dict={}):
    template = get_template(template_src)
    html = template.render(context_dict)
    result = BytesIO()

    base_url = settings.STATIC_URL # Or a full file path like 'file:///your/project/static/'

    pdf = pisa.pisaDocument(BytesIO(html.encode("UTF-8")), result,
                            encoding='UTF-8',
                            link_callback=lambda uri, rel: os.path.join(settings.STATIC_ROOT, uri.replace(settings.STATIC_URL, '')) if uri.startswith(settings.STATIC_URL) else uri, # A more robust link_callback
                            base_url=base_url)

    if not pdf.err:
        return HttpResponse(result.getvalue(), content_type='application/pdf')
    return HttpResponse('We had some errors <pre>%s</pre>' % html)


@login_required
@time_of_execution
def xhtml2pdf_sgareholder_list(request):
    qs = Shareholder.objects.all()
    subquery_sum = (
        ShareholderDeposit.objects.filter(
            shareholder_id=OuterRef("id"),
        )
        .values(
            "shareholder__id",
        )
        .annotate(
            sum_amount=Round(Sum(F("amount")), 0),
        )
    )

    qs = qs.annotate(
        sum_amount=Subquery(subquery_sum.values("sum_amount")),
    ).order_by("id")

    targeted_amount = TargetedAmount.objects.values_list("amount").order_by(
        "-inputDate"
    )[0]
    context_data = {
        'Kalpurush': os.path.join(settings.STATIC_URL, 'fonts', 'kalpurush.ttf'),
        'SolaimanLipi': os.path.join(settings.STATIC_URL, 'fonts', 'SolaimanLipi.ttf'),
        'Nikosh': os.path.join(settings.STATIC_URL, 'fonts', 'Nikosh.ttf')
    }
    data = {}
    data = data | {"shareholder": qs}
    data = data | {"heading": "Shareholders List"}
    data = data | company_info
    data["font_paths"] = context_data
    data["targeted_amount_per_flat"] = (
        targeted_amount[0] / company_info["no_of_flat_per_share"]
    )
    data["heading"] = "Shareholders List"
    grand_total = ShareholderDeposit.objects.aggregate(Sum("amount"))
    data["grand_total"] = grand_total
    # For Data End -------------
    template_path = 'accounts/reports/xhtml2pdf_shareholder_list.html'
    context = {"data":data}
    response = HttpResponse(content_type='application/pdf')

    response['Content-Disposition'] = 'filename="Shareholders_List.pdf"'

    template = get_template(template_path)
    
    html = template.render(context)
    result = BytesIO()
    # html = html.encode("UTF-8")

    # # create a pdf
    # print(fetch_resources('/static/fonts/Fazlay Sejuti Unicode.ttf', 'font'))

    """
    xhtml2pdf.pisa.CreatePDF(src, dest=None, dest_bytes=False, path='', link_callback=None, debug=0, 
    default_css=None, xhtml=False, encoding=None, xml_output=None, raise_exception=True, 
    capacity=100 * 1024, context_meta=None, encrypt=None, signature=None, **_kwargs)
    """
    pisa_status = pisa.pisaDocument(BytesIO(html.encode("UTF-8")), result,
                            encoding='UTF-8',
                            link_callback=fetch_resources, # A more robust link_callback
                            base_url = settings.STATIC_URL
                           )
    # # if error then show some funy view
    
    if not pisa_status.err:
        return HttpResponse(result.getvalue(), content_type='application/pdf')
    
    return HttpResponse('We had some errors <pre>' + html + '</pre>')

    # with open("test.pdf", "w+b") as result_file:
    # # convert HTML to PDF
    #     pisa_status = pisa.CreatePDF(
    #     html,       # page data
    #     dest=result_file,  # destination file
    # )


    # return render(request, template_path, context)
    return response


@login_required
@time_of_execution
def xhtml2pdf_expenditureDetailsReport(request):
    # For Data -------------
    from_date = request.GET.get("fromdate")
    to_date = request.GET.get("todate")

    qs = Expenditure.objects.select_related("item__ItemCode").all()
    if from_date:
        qs = qs.filter(dateOfTransaction__gte=from_date)

    if to_date:
        qs = qs.filter(dateOfTransaction__lte=to_date)

    subquery_sum = (
        qs.filter(
            item_id=OuterRef("item__id"),
            item__ItemCode=OuterRef("item__ItemCode__id"),
        )
        .values(
            "item__ItemCode__workSector",
            "item__itemName",
        )
        .annotate(
            sum_amount=Round(Sum(F("amount")), 0),
            sum_quantity=Round(Sum(F("quantity")), 0),
            units=F("item__unit"),
        )
    )

    qs = qs.annotate(
        work_sector=Subquery(subquery_sum.values("item__ItemCode__workSector")),
        item_name=Subquery(subquery_sum.values("item__itemName")),
        sum_amount=Subquery(subquery_sum.values("sum_amount")),
        sum_quantity=Subquery(subquery_sum.values("sum_quantity")),
        units=Subquery(subquery_sum.values("units")),
    )

    subquery_work_sector_sum = (
        qs.filter(item__ItemCode__workSector=OuterRef("item__ItemCode__workSector"))
        .values("item__ItemCode__workSector")
        .annotate(
            worksector_sum=Coalesce(Sum(F("amount")), 0, output_field=DecimalField())
        )
    )

    if from_date or to_date:
        qs = qs.annotate(
            worksector_sum=Subquery(subquery_work_sector_sum.values("worksector_sum")),
        ).order_by("work_sector", "item_name", "dateOfTransaction")
    else:
        qs = qs.annotate(
            worksector_sum=Subquery(subquery_work_sector_sum.values("worksector_sum")),
        ).order_by("work_sector", "item_name", "-dateOfTransaction")

    #! --------------------------------------
    data = {}
    data = data | {"expenditure": qs}
    data = data | company_info

    if from_date:
        data["fromdate"] = datetime.strptime(from_date, "%Y-%m-%d")
    if to_date:
        data["todate"] = datetime.strptime(to_date, "%Y-%m-%d")

    grand_total = qs.aggregate(Sum("amount"))
    data["grand_total"] = grand_total
    # For Data End -------------
    if request.path == reverse("Accounts:dateRangeExpenditureReport"):
        template_path = "accounts/reports/date_range_expenditure_details.html"
    else:
        template_path = "accounts/reports/expenditure_details.html"
        
    context = {"data":data}
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = 'filename="ExpenditureDetailsReport.pdf"'
    template = get_template(template_path)
    html = template.render(context)
    html = html.encode("utf-8")
    pisa_status = pisa.CreatePDF(
        html, dest=response, link_callback=fetch_resources, encoding='utf-8',
                pdf_version = 1.7)
    # # if error then show some funy view
    if pisa_status.err:
        return HttpResponse('We had some errors <pre>' + html + '</pre>')

    return response