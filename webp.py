from PIL import Image

def resize_and_convert(image_path, output_path, new_size, background_color=(255, 255, 255)):
    """
    Resizes an image and converts it to WebP with a specified background color.

    Args:
        image_path: Path to the input image (PNG or JPG).
        output_path: Path to save the converted WebP image.
        new_size: Tuple (width, height) for the new image size.
        background_color: Tuple (R, G, B) for the background color (default: white).
    """
    try:
        img = Image.open(image_path).convert("RGBA")
    except FileNotFoundError:
        print(f"Error: Image not found at {image_path}")
        return
    except Exception as e:
        print(f"Error opening image: {e}")
        return

    # Create a new image with the specified background color
    background = Image.new('RGBA', new_size, background_color + (255,))

    # Resize the input image
    resized_img = img.resize(new_size, Image.Resampling.LANCZOS)

    # Paste the resized image onto the background
    background.paste(resized_img, (0, 0), resized_img)

    # Save as WebP
    try:
        background.save(output_path, "webp", lossless=True)
        print(f"Image saved to {output_path}")
    except Exception as e:
        print(f"Error saving image: {e}")

if __name__ == '__main__':
    input_image = "input.png"  # Replace with your input image path
    output_image = "output.webp" # Replace with your desired output path
    new_size = (500, 500) # Replace with your desired size
    background_color = (200, 200, 200) # Replace with your desired background color (gray example)
    resize_and_convert(input_image, output_image, new_size, background_color)


from PIL import Image
import os

def convert_image(image_path, output_dir, size=(512, 512)):
    """
    Resizes and converts an image to WebP, preserving PNG transparency.

    Args:
        image_path (str): Path to the input image (PNG or JPG).
        output_dir (str): Directory to save the converted WebP image.
        size (tuple): Desired size for the image (width, height).
    """

    try:
        img = Image.open(image_path)

        # Resize the image
        img = img.resize(size, Image.Resampling.LANCZOS)

        # Convert to RGBA to handle transparency
        if img.mode != 'RGBA':
          img = img.convert('RGBA')

        # Create the output filename
        filename = os.path.splitext(os.path.basename(image_path))[0]
        output_path = os.path.join(output_dir, f"{filename}.webp")
        
        # Save as WebP
        img.save(output_path, "webp", lossless=True) # lossless for best quality
        print(f"Converted {image_path} to {output_path}")

    except FileNotFoundError:
        print(f"Error: Image not found at {image_path}")
    except Exception as e:
        print(f"Error processing {image_path}: {e}")


def batch_convert(input_dir, output_dir, size=(512, 512)):
    """
    Converts all PNG and JPG images in a directory.

    Args:
        input_dir (str): Directory containing input images.
        output_dir (str): Directory to save the converted WebP images.
        size (tuple): Desired size for the images (width, height).
    """
    if not os.path.exists(output_dir):
      os.makedirs(output_dir)

    for filename in os.listdir(input_dir):
        if filename.lower().endswith((".png", ".jpg", ".jpeg")):
            image_path = os.path.join(input_dir, filename)
            convert_image(image_path, output_dir, size)

if __name__ == '__main__':
    input_directory = "input_images"  # Replace with your input directory
    output_directory = "output_webp" # Replace with your output directory
    resize_size = (256, 256)        # Replace with your desired size

    batch_convert(input_directory, output_directory, resize_size)