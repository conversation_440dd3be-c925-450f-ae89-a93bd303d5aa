{% extends 'base.html' %} {% load static %} {% load humanize %}
    {% block title %}<title>কর্মকর্তা/কর্মচারীর বিস্তারিত তথ্য</title>{% endblock title %}
{% block contents %}
<script src="{% static 'js/rana.js' %}"></script>
  <div class="row pt-2">
    <div class="col-6">
      <h4 class="text-success">কর্মকর্তা/কর্মচারীর বিস্তারিত তথ্যঃ</h4>
    </div>
    <div class="col-6 text-end">Date : {{time}}
  {% csrf_token %}
  <a href="{% url 'pmis:RPTEmployeeDetails' Recrset.id %}" target="_blank">
    <button type="button" class="btn btn-outline-danger btn-sm">প্রিন্ট করুন</button>
  </a>
  <a href="{% url 'pmis:EmpolyeeDetails' urlNoPre %}">
    <button type="button" class="btn btn-outline-danger btn-sm"><i class="fas fa-angle-double-left"></i></button>
  </a>
  <a href="{% url 'pmis:EmpolyeeDetails' urlNoNext %}">
    <button type="button" class="btn btn-outline-danger btn-sm"><i class="fas fa-angle-double-right"></i></button>
  </a>
    </div>
  </div>

 <div class="row">
    <div class="col-3 border border-secondary p-2">
      <center>
        {% if Recrset.IMAGE %}
        <img
          class="img-thumbnail"
          src="{{Recrset.IMAGE.url}}"
          alt="masud"
          style="border-radius: 10%; width: 225px; height:225px;"
        />
        {% else %}
        <img
          src="{%static 'media/avatar.png'%}"
          alt="Zaman"
          style="border-radius: 10%; width: 225px; height: 225px;"
        />
        {% endif %}
</center>
      </div>
 <div class="col border border-secondary pt-1">
      <table class="table table-striped table-hover table-sm b_fontNikosh2">
        <tr class="table-active">
          <th class="align-top b_fontNikosh2" style="text-align: right"><h3>নামঃ</h3></th>
          <td><h3 class="text-danger b_fontNikosh2">{{Recrset.PersonName}}, <span class="text-dark">আইডি নংঃ</span>&nbsp; <span id="JGID"> {{ Recrset.JGID }}</span></h3></td>
        </tr>
        <tr class="table-active">
          <th class="align-top" style="text-align: right">পদবীঃ </th>
          <td>{{Recrset.Designation}}, 
            {% if Recrset.JGSubSection %}
            {{ Recrset.JGSubSection }}
            {% elif Recrset.JGSection %}
            {{ Recrset.JGSection }}
            {% elif Recrset.JGDepartment %}
            {{ Recrset.JGDepartment }}
            {% elif Recrset.JGDivision %}
            {{ Recrset.JGDivision }}
            {% else %}
            ব্যবস্থাপনা পরিচালকের দপ্তর
            {% endif %}
          </td>
        </tr>
        <tr class="table-active">
          <th class="align-top" style="text-align: right">মোবাইল ও ই-মেইলঃ</th>
          <td>মোবাইলঃ {{Recrset.Mobile}}, ই-মেইলঃ {{Recrset.Email}}
          </td>
        </tr>
        <tr class="table-active">
          <th class="align-middle" style="text-align: right">যোগদান ও পিআরএল তারিখঃ</th>
          <td>যোগদানঃ {{ Recrset.JoiningDate|date:'d M, Y' }}. &nbsp পিআরএলঃ {{ Recrset.PRLDate|date:'d M, Y'}}</td>
        </tr>
        <tr class="table-active">
          <th class="align-top" style="text-align: right">বেতন-স্কেলঃ</th>
          <td id="payscale">{{ Recrset.PayScale }}, বর্তমান বেসিকঃ {{Recrset.CurrentBasicPay|intcomma}}</td>
        </tr>
       </table>
        <script>  
          var text = document.getElementById("payscale").innerText;
          var jgid = document.getElementById("JGID").innerText;
          document.getElementById("payscale").innerHTML = replaceNumbers(text);
          document.getElementById("JGID").innerHTML = replaceNumbers(jgid);
        </script>
    </div>
    </div>
<div class="pt-2">
<ul class="nav nav-tabs" id="myTab" role="tablist">
  <li class="nav-item" role="presentation">
    <button class="nav-link active" id="basicinfo-tab" data-bs-toggle="tab" data-bs-target="#basicinfo" type="button" role="tab" aria-controls="basicinfo" aria-selected="true">সাধারণ তথ্যা</button>
  </li>
  <li class="nav-item" role="presentation">
    <button class="nav-link" id="education-tab" data-bs-toggle="tab" data-bs-target="#education" type="button" role="tab" aria-controls="education" aria-selected="false">শিক্ষাগত যোগ্যতা</button>
  </li>
  <li class="nav-item" role="presentation">
    <button class="nav-link" id="training-tab" data-bs-toggle="tab" data-bs-target="#training" type="button" role="tab" aria-controls="training" aria-selected="false">প্রশিক্ষণ</button>
  </li>
  <li class="nav-item" role="presentation">
    <button class="nav-link" id="promotion-tab" data-bs-toggle="tab" data-bs-target="#promotion" type="button" role="tab" aria-controls="promotion" aria-selected="false">পদোন্নতী বৃত্তান্ত</button>
  </li>
  <li class="nav-item" role="presentation">
    <button class="nav-link" id="servicehistory-tab" data-bs-toggle="tab" data-bs-target="#servicehistory" type="button" role="tab" aria-controls="servicehistory" aria-selected="false">চাকুরী বৃত্তান্ত</button>
  </li>
</ul>

<div class="tab-content">
    <div class="tab-pane active" id="basicinfo" role="tabpanel" aria-labelledby="basicinfo-tab">  
      <div class="row pt-2">
        <h6 class="text-danger">সাধারণ তথ্যঃ</h6>
        <div class="col-6">
<table class="table table-striped table-sm b_fontNikosh2">
    <tr>
    <th colspan="2" class="align-middle" style="text-align: center;background-color:#fd7e14;">ঠিকানা</th>  
    </tr>

    <tr>
     <td class="align-top" style="text-align: right"><b>পিতার নামঃ</b><br><b>মাতার নামঃ</b><br><b>স্থায়ী ঠিকানাঃ</b><br></td> 
     <td>{{ Recrset.FathersName }}<br>{{ Recrset.MothersName }}<br>
      {% for x in PerAdd %}
      {{x.Address1}}, {{x.Address2}} <br> 
      {{x.PostOffice}}, {{x.PoliceStation}} <br>  
      {{x.District}}। 
      {% endfor %}
     </td>
       
     </tr>
         <tr>
     <td class="align-top" style="text-align: right" ><b>বর্তমান ঠিকানাঃ</b></td> 
     {% for x in PreAdd %}
     <td>{{x.Address1}}, {{x.Address2}}<br> 
      {{x.PostOffice}}, {{x.PoliceStation}}<br>
      {{x.District}}।<br><br><br>
     </td>
     {% endfor %}
    </table>
    </div>
    <div class="col-6">
      <table class="table table-striped table-sm b_fontNikosh2">
        <tr><th colspan="2" class="align-middle" style="text-align: center;background-color:#fd7e14;">অন্যান্য তথ্য</th></tr>
          <td class="align-top" style="text-align: right"><b> লিঙ্গঃ</b><br><b>বৈবাহীক অবস্থাঃ</b><br><b>ধর্মঃ</b><br><b>রক্তের গ্রুপঃ</b><br><b>উচ্চতাঃ</b></td>
          <td>{{ Recrset.get_Gender_display }} <br>{{ Recrset.get_MartialStatus_display }} <br>{{ Recrset.get_Religion_display }}<br>
            {% for x in Health %}
            {{ x.BloodGroup }}<br>{{ x.Height }}
            {% endfor %}
          </td>
      <tr>
        <td class="align-top" style="text-align: right"><b>এনআইডিঃ</b><br><b>টিআইএনঃ</b><br><b>পাসপোর্টঃ</b><br><b>ড্রাইভিং লাইসেন্সঃ</b><br><b>মুক্তিযোদ্ধাঃ</b></td>
      <td>{{ Recrset.NID }} <br>{{ Recrset.TIN }} <br>{{ Recrset.PassportNo }}<br>
        {{ Recrset.DrivingLicenceNo }}<br>
        {{ Recrset.get_FreedomFighter_display }}</td></tr>
    </table>
    </div>
    </div>
  
    </div>
    <div class="tab-pane" id="education" role="tabpanel" aria-labelledby="education-tab">
      <div class="pt-2">
      <h6 class="text-danger">শিক্ষাগত যোগ্যতাঃ</h6>
    <table class="table table-striped table-hover b_fontNikosh2">
    <tr class="table-active" style="background-color:#fd7e14;">
    <th class="align-middle" style="text-align: center">#</th>
    <th class="align-middle" style="text-align: center">সার্টিফিকেট</th>
    <th class="align-middle" style="text-align: center">বিষয়</th>
    <th class="align-middle" style="text-align: center">প্রতিষ্ঠানের নাম</th>
    <th class="align-middle" style="text-align: center">পাশের বছর</th>
    <th class="align-middle" style="text-align: center">ফলাফল </th>
    <th class="align-middle" style="text-align: center">বোর্ড/ইউনিভার্সিটি </th>
    </tr>
    {% for e in EduQuali %}
    <tr class="table-active">
    <td class="align-middle">{{forloop.counter}}</td>
    <td class="align-middle">{{e.Certificate}}</td>
    <td class="align-middle">{{e.PrincipalSubject}}</td>
    <td class="align-middle">{{e.NameOfInstitution}}</td>
    <td class="align-middle" >{{e.PassingYear}}</td>
    <td class="align-middle">{{e.Result}}</td>
    <td class="align-middle">{{e.BoardUni}}</td>
    </tr>
    {% endfor %}
    </table>
    </div>
    </div>
    <div class="tab-pane" id="training" role="tabpanel" aria-labelledby="training-tab">
          <div class="pt-2">
            <h6 class="text-danger">স্থানীয় প্রশিক্ষণঃ</h6>
            <table class="table table-striped table-hover b_fontNikosh2">
              <tr class="table-active" style="background-color:#fd7e14;">
                <th class="align-middle" style="text-align: center">#</th>
                <th class="align-middle" style="text-align: center">কোর্সের শিরনাম</th>
                <th class="align-middle" style="text-align: center">প্রতিষ্ঠানের নাম</th>
                <th class="align-middle" style="text-align: center; width:140px;">শুরুর তারিখ</th>
                <th class="align-middle" style="text-align: center; width:140px;">শেষের তারিখ</th>
                <th class="align-middle" style="text-align: center">ফলাফল</th>
                <th class="align-middle" style="text-align: center">প্রশিক্ষণের ধরণ</th>
              </tr>
              {% for e in LocTraining %}
              <tr class="table-active">
                <td class="align-middle">{{forloop.counter}}</td>
                <td class="align-middle">{{e.CourseTitle}}</td>
                <td class="align-middle">{{e.Institution}}</td>
                <td class="align-middle">{{e.FromDate|date:"d M, Y"}}</td>
                <td class="align-middle">{{e.ToDate|date:"d M, Y"}}</td>
                <td class="align-middle">{{e.Grade}}</td>
                <td class="align-middle">{{e.Training}}</td>
              </tr>
              {% endfor %}
            </table>
            <h6 class="text-danger">বৈদেশীক প্রশিক্ষণঃ</h6>
            <table class="table table-striped table-hover b_fontNikosh2">
              <tr class="table-active" style="background-color:#fd7e14;">
                <th class="align-middle" style="text-align: center">#</th>
                <th class="align-middle" style="text-align: center">কোর্সের শিরনাম</th>
                <th class="align-middle" style="text-align: center">প্রতিষ্ঠানের নাম</th>
                <th class="align-middle" style="text-align: center; width:140px;">শুরুর তারিখ</th>
                <th class="align-middle" style="text-align: center; width:140px;">শেষের তারিখ</th>
                <th class="align-middle" style="text-align: center">ফলাফল</th>
                <th class="align-middle" style="text-align: center">প্রশিক্ষণের ধরণ</th>
              </tr>
              {% for e in ForTraining %}
              <tr class="table-active">
                <td class="align-middle">{{forloop.counter}}</td>
                <td class="align-middle">{{e.CourseTitle}}</td>
                <td class="align-middle">{{e.Institution}}</td>
                <td class="align-middle">{{e.FromDate|date:"d M, Y"}}</td>
                <td class="align-middle">{{e.ToDate|date:"d M, Y"}}</td>
                <td class="align-middle">{{e.Grade}}</td>
                <td class="align-middle">{{e.Training}}</td>
              </tr>
              {% endfor %}
            </table>
          </div>
    </div>
    <div class="tab-pane" id="promotion" role="tabpanel" aria-labelledby="promotion-tab">
     <div class="pt-2"> <h6 class="text-danger">পদোন্নতী বৃত্তান্তঃ</h6>
      <table class="table table-striped table-hover b_fontNikosh2">
    <tr class="table-active" style="background-color:#fd7e14;">
    <th class="align-middle" style="text-align: center">#</th>
    <th class="align-middle" style="text-align: center">পদের নাম</th>
    <th class="align-middle" style="text-align: center">পদোন্নতীর তারিখ</th>
    <th class="align-middle" style="text-align: center">যোগদানের তারিখ</th>
    <th class="align-middle" style="text-align: center">পদোন্নতীর ধরণ</th>
    <th class="align-middle" style="text-align: center">বেতন গ্রেড </th>
    </tr>
    {% for e in Promotion %}
    <tr class="table-active">
    <td class="align-middle">{{forloop.counter}}</td>
    <td class="align-middle">{{e.PostRank}}</td>
    <td class="align-middle">{{e.PromotionDate|date:"d M, Y"}}</td>
    <td class="align-middle">{{e.GODate|date:"d M, Y"}}</td>
    <td class="align-middle" >{{e.NatureOfPromotion}}</td>
    <td class="align-middle">{{e.PayScale}}</td>
    </tr>
    {% endfor %}
    </table>

    </div>
    </div>

    <div class="tab-pane" id="servicehistory" role="tabpanel" aria-labelledby="servicehistory-tab">
      <div class="pt-2">
      <h6 class="text-danger">চাকুরী বৃত্তান্তঃ</h6>
      <table class="table table-striped table-hover b_fontNikosh2">
              <tr class="table-active" style="background-color:#fd7e14;">
                <th class="align-middle" style="text-align: center">#</th>
                <th class="align-middle" style="text-align: center">পদবী</th>
                <th class="align-middle" style="text-align: center">দপ্তর/লোকেশন</th>
                <th class="align-middle" style="text-align: center">ডিভিশন/ডিপার্টমেন্ট</th>
                <th class="align-middle" style="text-align: center">কোম্পানী/প্রতিষ্ঠান</th>
                <th class="align-middle" style="text-align: center">ক্যাডার</th>
                <th class="align-middle" style="text-align: center; width:140px;">শুরুর তারিখ</th>
                <th class="align-middle" style="text-align: center; width:140px;">শেষের তারিখ</th>
                <th class="align-middle" style="text-align: center">বেতন স্কেল</th>
              </tr>
              {% for e in SerHistory %}
              <tr class="table-active">
                <td class="align-middle">{{forloop.counter}}</td>
                <td class="align-middle">{{e.Post}}</td>
                <td class="align-middle">{{e.Location}}</td>
                <td class="align-middle">{{e.DivisionDepartment}}</td>
                <td class="align-middle">{{e.OrganizationCompany}}</td>
                <td class="align-middle">{{e.get_Cadre_display}}</td>
                <td class="align-middle">{{e.FromDate|date:"d M, Y"}}</td>
                <td class="align-middle">{{e.ToDate|date:"d M, Y"}}</td>
                <td class="align-middle">{{e.PayScale}}</td>
              </tr>
              {% endfor %}
            </table>
    </div>
    </div>
    
</div>
</div>

<script>
  var firstTabEl = document.querySelector('#myTab li:last-child button')
  var firstTab = new bootstrap.Tab(firstTabEl)
  firstTab.show()
</script>

{% endblock %}