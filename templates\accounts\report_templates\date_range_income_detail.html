{% extends "main.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% load mahimsoft_tags %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}{{company_name}}{%endblock %}
{% block content %}

<div class="container pt-3">
    {% if fromdate %}
    <div class="p-2 my-2 text-start">
        <h4 class="text-muted">Income Details <span class="text-success fs-5">[Date Range:
                {{fromdate|date:"d-b-Y"|title}}
                to {{todate|date:"d-b-Y"|title}}]</span></h4>
    </div>
    {% else %}
    <div class="p-2 my-2 text-center">
        <h3 class="text-muted">Date Range Income Details</h3>
    </div>
    {% endif %}
    <div class="card text-dark border-warning bg-light">

        <div class="card-header text-muted">
            <h5>Select date range:</h5>
        </div>
        <div class="card-body col-auto p-0 m-0 ps-2">
            <form class="row g-3 p-2">
                <div class="col-auto">
                    From Date: <input type="date" id="fromdate" name="fromdate" />
                </div>
                <div class="col-auto">
                    To Date: <input type="date" id="todate" name="todate" />
                </div>
                <div class="col-auto">
                    <button type="submit" class="btn btn-sm btn-success mt-0">Show</button>
                </div>
            </form>
        </div>
    </div>
    {% if fromdate %}
    <div class="d-flex justify-content-between mt-2 mb-0">
        <h5 class="text-danger ps-2">Total Income during the period: {{ currency }} {{grand_total.amount__sum|intcomma_bd}}
        </h5>
        <span>
        <a href="/dateRangeIncomeReport/?fromdate={{fromdate|date:'Y-m-d'}}&todate={{todate|date:'Y-m-d'}}"
            target="_blank" class="btn btn-sm btn-outline-danger">
            <i class="bi bi-download"></i> PDF</a>
            <a href='{{ request.META.HTTP_REFERER }}' class='btn btn-outline-primary btn-sm me-0 ms-1 mb-0' role='button'><i class="bi bi-arrow-left-square-fill"></i> Back</a>
        </span>
        </div>
    {% regroup income by income_sector as sectors %}
    {% for sector in sectors %}
    <div class="card bg-secondary">
        <div class="d-flex justify-content-between">
            <h5 class="text-white p-0 pt-1 ps-2"><i class="bi bi-back"></i> {{ sector.grouper }}</h5>
            <p class="text-white pt-1 pe-2 mb-0">Total: {{ currency }} {{sector.list.0.incomesector_sum|intcomma_bd}}</p>
        </div>
    </div>
    {% regroup sector.list by income_item_name as item %}
    {% for row in item %}
    <div class="card mt-2" style="background-color: #FCE6C9;">
        <div class="d-flex justify-content-between">
            <h5 class="p-0 ps-2 pt-1">{{ row.grouper }}</h5>
            <p class="pt-1 pe-2 pb-0 mb-0">Total: {{ currency }} {{row.list.0.sum_amount|intcomma_bd}}</p>
        </div>
    </div>
    <div class="table-responsive-sm">
        <table class="table table-bordered table-striped table-sm">
            <thead>
                <tr>
                    <th class="text-center" style="width: 5%;">#</th>
                    <th class="text-center" style="width: 15%;">Date</th>
                    <th class="text-center" style="width: 30%;">Description</th>
                    <th class="text-center" style="width: 10%;">Quantity</th>
                    <th class="text-center" style="width: 10%;">Unit</th>
                    <th class="text-center" style="width: 15%;">Voucher No.</th>
                    <th class="text-end pe-4" style="width: 15%;">Amount ({{ currency }})</th>
                    {% if request.user|has_group:"Admin"%}
                    <th colspan="2" class="text-center" style="width: 15%;">Edit</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% if request.user|has_group:"Admin"%}
                {% for dt in row.list %}
                <tr class="">
                    <td class="text-center">{{forloop.counter}}</td>
                    <td>{{dt.dateOfTransaction}}</td>
                    <td>{{dt.description}}</td>
                    <td class="text-end pe-4">{{dt.quantity|intcomma_bd}}</td>
                    <td>{{dt.units}}</td>
                    <td class="text-center">{{dt.voucherNo}}</td>
                    <td class="text-end pe-4">{{dt.amount|intcomma_bd}}/-</td>
                    {% if dt.contractor_bill_payment or dt.credit_purchase_payment %}
                    <td class="text-center isDisabled"><i class="bi bi-trash"></i></td>
                    <td class="text-center isDisabled"><i class="bi bi-pencil-square"></i></td>
                    {% else %}
                    <td class="text-center"><a class="a_link" href="#"><i class="bi bi-trash"></i></a></td>
                    <td class="text-center"><a class="b_link" href="/expenditure_update/{{dt.id}}"><i
                                class="bi bi-pencil-square"></i></a></td>
                    {% endif %}
                </tr>
                {% endfor %}
                {% else %}

                {% for dt in row.list %}
                <tr class="">
                    <td class="text-center">{{forloop.counter}}</td>
                    <td>{{dt.dateOfTransaction}}</td>
                    <td>{{dt.description}}</td>
                    <td class="text-end pe-4">{{dt.quantity|intcomma_bd}}</td>
                    <td>{{dt.units}}</td>
                    <td class="text-center">{{dt.voucherNo}}</td>
                    <td class="text-end pe-4">{{dt.amount|intcomma_bd}}/-</td>
                </tr>
                {% endfor %}
                {% endif %}
                <tr>
                    <th colspan="3" class="text-end">Total Quantity =</th>
                    <th class="text-end pe-4">{{row.list.0.sum_quantity|intcomma_bd}}</th>
                    <th>{{row.list.0.units}}</th>
                    <th class="text-end">Total Amount =</th>
                    <th class="text-end pe-4">{{row.list.0.sum_amount|intcomma_bd}}/-</th>
                    <th colspan="2"></th>
                </tr>
            </tbody>
        </table>
    </div>

    {% endfor %}
    {% endfor %}
    {% endif %}
</div>

{% endblock %}

{% block script %}
<script>
    const to_date = new Date();
    to_date.setDate(1);
    document.getElementById('fromdate').valueAsDate = to_date;
    // from_date.setMonth(from_date.getMonth()-1);
    document.getElementById('todate').valueAsDate = new Date();
</script>
{% endblock script %}