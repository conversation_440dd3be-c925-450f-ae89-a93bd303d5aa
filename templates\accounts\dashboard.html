{% extends "main.html" %}
{% load static %}
{% load mahimsoft_tags %}
{% comment %} {% load i18n %} {% endcomment %}
{% block extend_header%}
<style>
    a.shareholder:link {
        color: chocolate;
    }

    a.shareholder:visited {
        color: chocolate;
    }

    a.shareholder:active {
        color: blueviolet;
    }

    a.shareholder:hover {
        color: darkturquoise;
    }
    .pagebreak {
        page-break-after: always;
    }
    td, th {
        font-size: small;
    }

</style>
{%endblock%}
{% block content %}

<div class="row row-cols-1 row-cols-lg-2 p-0 m-0 mt-1">
    <div class="col-lg-6 p-0 m-0 pagebreak" style="border: thin; border-color: brown;">
        <div class="card ms-1 pb-0 mb-0">
            <div class="card-header ps-2 py-0 me-0">
                <h5 class="p-0 ps-1 pt-1">Project Location</h5>
            </div>
            <div class="card-body text-center p-0 m-0">
                <iframe
                    src="https://www.google.com/maps/embed/v1/place?key=AIzaSyBtsUPyvqs1mggY53hvaDiGrT1C8Siecr8&q=20.629110,92.327648&maptype=satellite"
                    width="570" height="400" style="border:1;" allowfullscreen="" loading="lazy"
                    referrerpolicy="no-referrer-when-downgrade"></iframe>
            </div>
        </div>
    </div>
    <div class="col p-0 m-0 pagebreak" style="border: thin; border-color: brown;">
        {{ fig_pie_chart|safe }}
    </div>
</div>

<br>
<!--Deposit Information-->
<div class="row row-cols-1 row-cols-lg-3 p-0 m-0 mt-0">
    <div class="col p-0 m-0">
        <div class="card text-start m-1" style="height: 9rem;">
            <div class="card-header p-0 m-0">
                <h5 class="p-0 ps-2 pt-1">Amount Deposited:</h5>
            </div>
            <div class="card-body p-0 m-0">
                <table class="table table-bordered table-sm p-0 m-0">
                    <tr class="p-0 m-0 text-center">
                        <th>Shareholders Deposit</th>
                        <th>Income From Project</th>
                    </tr>
                    <tr class="p-0 m-0 text-center">
                        <td>{{ currency }} {{total_shareholder_deposited.amount__sum|intcomma_bd}}</td>
                        <td>{{ currency }} {{project_income.amount__sum|intcomma_bd}}</td>
                    </tr>
                </table>
                <h6 class="text-center pt-2">Total = {{ currency }} {{total_deposited_amount|intcomma_bd}}</h6>

            </div>
        </div>
    </div>

    <div class="col p-0 m-0">
        <div class="card text-start m-1" style="height: 9rem;">
            <div class="card-header p-0 me-0">
                <h5 class="p-0 ps-2 pt-1">Total Expenditure:</h5>
            </div>
            <div class="card-body text-center">
                <h6 class="pt-4">{{ currency }}: {{total_Expenditure.amount__sum|intcomma_bd|default_if_none:"0"}}</h6>
            </div>
        </div>
    </div>
    <div class="col p-0 m-0">
        <div class="card text-start m-1" style="height: 9rem;">
            <div class="card-header p-0 m-0">
                <h5 class="p-0 ps-2 pt-1">Amount Available:</h5>
            </div>
            <div class="card-body text-center">
                <h6 class="pt-4">{{ currency }}: {{total_deposited_amount|subtract:total_Expenditure.amount__sum|intcomma_bd}}
                </h6>
            </div>
        </div>
    </div>
</div>
<!--Deposit Information end-->
<br>

{% if qs_data|length > 1 %}
<div class="p-0 m-0" style="border: thin; border-color: brown;">
    <div class="card m-1 mb-0">
        {{ chart_deposit_target|safe }}
    </div>
</div>

<div class="card mx-1 my-2">
    <div class="card-header py-0 me-0">
        <h5 class="p-0 ps-1 pt-1">Shareholders Deposit Information</h5>
    </div>
    {% with first_half_length=qs_data|length|devided:2|floatformat:0 second_half_length=qs_data|length|devided:2|floatformat:0|add:":" %}
    <div class="card-body m-0 pt-0 px-2">
        <div class="row row-cols-1 row-cols-lg-2 px-1">
            <div class="col col-lg-6 p-0 m-0" style="border: thin; border-color: brown;">
                <div class="table table-active mb-0 pb-0">
                    <table class="table table-sm align-middle table-responsive mb-0">
                        <tr class="table-success">
                            <th>#</th>
                            <th  style="width:37%">Shareholder</th>
                            <th class="text-center">Share</th>
                            <th class="text-center">Deposited Amount</th>
                            <th class="text-center">to Deposit</th>
                        </tr>
                        {% for x in qs_data|slice:first_half_length %}
                        <tr>
                            <td class="table-success text-center">{{forloop.counter}}</td>
                            <td>
                                <div class="d-flex justify-content-between p-0">
                                    {{x.shareholderName}}
                                    <a class="shareholder" href="{{x.get_absolute_url}}" target="_blank">
                                        <span class="tooltip_w3s">
                                        <i class="bi bi-file-earmark-text-fill"></i>
                                        <span class="tooltiptext_w3s">{{x.shareholderName}}'s Details Info.</span>
                                        </span>
                                        </a>
                                </div>
                            </td>
                            <td class="text-center">{{x.numberOfFlat|floatformat:"2"}}</td>
                            <td class="text-end">
                                <div class="d-flex justify-content-end">
                                    {{x.deposited_sum|intcomma_bd|default_if_none:"---"}}
                                    <a class="deposit ps-2" href="/shareholder_deposit_list/{{x.id}}" target="_blank">
                                        <span class="tooltip_w3s">
                                        <i class="bi bi-server"></i>
                                        <span class="tooltiptext_w3s">{{x.shareholderName}}'s Deposit History</span>
                                        </span>
                                        </a>
                                </div>
                            </td>
                            {% if x.deposited_sum != None %}
                            <td class="text-end pe-2
                            {% if x.amount_to_deposit|subtract:x.deposited_sum > 0 %}
                            text-danger
                        {% endif %}">{{x.deposited_sum|subtract:x.amount_to_deposit|intcomma_bd}}</td>
                            {% else %}
                            <td class="text-end pe-2">{{x.amount_to_deposit|intcomma_bd}}</td>
                            {% endif %}
                        </tr>
                        {% endfor %}

                    </table>
                </div>

            </div>

            <div class="col p-0 m-0" style="border: thin; border-color: brown;">
                <div class="table table-active mb-0 pb-0">
                    <table class="table table-sm align-middle table-responsive mb-0">
                        <tr class="table-success">
                            <th>#</th>
                            <th style="width:37%">Shareholder</th>
                            <th class="text-center">Share</th>
                            <th class="text-center">Deposited Amount</th>
                            <th class="text-center">to Deposit</th>
                        </tr>
                        {% for x in qs_data|slice:second_half_length %}
                        <tr>
                            <td class="table-success text-center">{{forloop.counter|add:first_half_length}}</td>
                            <td>
                                <div class="d-flex justify-content-between p-0">
                                    {{x.shareholderName}}
                                    <a class="shareholder" href="{{x.get_absolute_url}}" target="_blank">
                                        <span class="tooltip_w3s">
                                        <i class="bi bi-file-earmark-text-fill"></i>
                                        <span class="tooltiptext_w3s">{{x.shareholderName}}'s Details Info.</span>
                                        </span>
                                </div>
                            </td>
                            <td class="text-center">{{x.numberOfFlat|floatformat:"2"}}</td>
                            <td class="text-end">
                                <div class="d-flex justify-content-end">
                                    {{x.deposited_sum|intcomma_bd|default_if_none:"---"}}
                                    <a class="deposit ps-2" href="/shareholder_deposit_list/{{x.id}}" target="_blank">
                                        <span class="tooltip_w3s">
                                        <i class="bi bi-server"></i>
                                        <span class="tooltiptext_w3s">{{x.shareholderName}}'s Deposit History</span>
                                        </span>
                                    </a>
                                </div>
                            </td>
                            {% if x.deposited_sum != None %}

                            <td class="text-end pe-2 
                            {% if x.amount_to_deposit|subtract:x.deposited_sum > 0 %}
                                text-danger
                            {% endif %}
                                 ">{{x.deposited_sum|subtract:x.amount_to_deposit|intcomma_bd}}</td>
                            {% else %}
                            <td class="text-end pe-2">{{x.amount_to_deposit|intcomma_bd}}</td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                    </table>
                </div>

            </div>
        </div>
    </div>
    <div class="text-center p-0 m-0 text-success">
        <h5 class="p-0 ps-1 py-1 align-middle">Targeted Amount Per Share: {{ currency }} {{targeted_amount|intcomma_bd}} 
            <span class="text-muted fs-6"> [Start Date: {{start_date|date:"d-M-Y"}}, Deadline: {{deadline|date:"d-M-Y"}}]</span>
        </h5>
    </div>
</div>
<br>
{% endwith %}
{% endif %}
<!-- Bank and Cash -->
<div class="card text-start m-1">
    <div class="card-header p-0 m-0">
        <h5 class="p-0 ps-2 pt-1">Bank and Cash:</h5>
    </div>
    <div class="row row-cols-1 row-cols-lg-3 p-0 m-0 mt-0">
        <div class="col-lg-4 p-0 m-0 py-3 text-center">
            <h6> Bank Balance: <span class="text-danger">{{ currency }} {{bank_and_cash.bank_balance|intcomma_bd}}</span></h6>
        </div>
        <div class="col-lg-4 p-0 m-0 py-3 text-center">
            <h6>Cash in Hand: <span class="text-danger">{{ currency }} {{bank_and_cash.cash_balance|intcomma_bd}}</span></h6>
        </div>
        <div class="col-lg-4 p-0 m-0 py-3 text-center">
            <h6>Total Amount Available: <span class="text-danger">{{ currency }} {{bank_and_cash.total_balance|intcomma_bd}}</span>
            </h6>
        </div>
    </div>
</div>
<!-- Bank and Cash end -->
<br>
<!--#Done -->
<div class="row row-cols-1 row-cols-lg-2 ps-2 pe-2">
    <div class="col-lg-6 m-0 p-0">
        <div class="card text-start m-1 ms-2">
            <div class="card-header p-0 m-0 ps-2">
                <h5 class="p-0 ps-1 pt-1">Contractor Bill Payment Status:</h5>
            </div>
            <div class="table table-active mb-0 pb-0">
                <table class="table table-sm align-middle table-responsive mb-0">
                    <tr>
                        <th>#</th>
                        <th>Contractor</th>
                        <th>Bill Date</th>
                        <th>Amount</th>
                        <th>Paid Amnt</th>
                        <th>Amnt to Pay</th>
                    </tr>
                    {% for x in qs_contractor_bill_status %}
                    <tr>
                        <td>{{forloop.counter}}</td>
                        <td>{{x.contractor}}</td>
                        <td class="text-center">{{x.submission_date}}</td>
                        <td class="text-end">{{ currency }} {{x.amount|intcomma_bd}}</td>
                        <td class="text-end pe-2">{{ currency }} {{x.sum_amount|intcomma_bd|default_if_none:"0"}}</td>
                        <td class="text-end pe-2"> {{ currency }} {{x.rest_amount|intcomma_bd}}</td>
                    </tr>
                    {% endfor %}
                    <tr>
                        <th colspan="5" class="text-end pe-2">Total Amount to Pay =</th>
                        <th class="text-end pe-2">
                            {{ currency }} {{qs_contractor_bill_status_sum|intcomma_bd|default_if_none:"0"}}
                        </th>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    <div class="col m-0 p-0">
        <div class="card text-start m-1 me-2">
            <div class="card-header p-0 m-0 ps-2">
                <h5 class="p-0 ps-1 pt-1">Credit Purchase Payment Status:</h5>
            </div>
            <div class="table table-active mb-0 pb-0">
                <table class="table table-sm align-middle table-responsive mb-0">
                    <tr>
                        <th>#</th>
                        <th>Seller</th>
                        <th>Purchase Date</th>
                        <th>Amount</th>
                        <th>Paid Amnt</th>
                        <th>Amnt to Pay</th>
                    </tr>
                    {% for x in qs_credit_purchase_payment_status %}
                    <tr>
                        <td>{{forloop.counter}}</td>
                        <td>{{x.seller}}</td>
                        <td class="text-center">{{x.purchase_date}}</td>
                        <td class="text-end">{{ currency }} {{x.amount|intcomma_bd|intcomma_bd}}</td>
                        <td class="text-end pe-2">{{ currency }} {{x.sum_amount|intcomma_bd|default_if_none:"0"}}</td>
                        <td class="text-end pe-2">{{ currency }} {{x.rest_amount|intcomma_bd}}</td>
                    </tr>
                    {% endfor %}
                    <tr>
                        <th colspan="5" class="text-end pe-2">Total Amount to Pay =</th>
                        <th class="text-end pe-2">
                            {{ currency }} {{qs_credit_purchase_payment_status_sum|intcomma_bd}}
                        </th>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<br>
<!--Account Receivable and Payable-->
<div class="row row-cols-1 p-0 m-0 mt-0">
    <div class="col p-0 m-0">
        <div class="card text-start m-1">
            <div class="card-header p-0 m-0">
                <h5 class="p-0 ps-2 pt-1">Accounts Payable & Receivable:</h5>
            </div>
            <div class="card-body p-0 m-0">
                <table class="table table-bordered table-sm p-0 m-0">
                    <tr class="p-0 m-0 text-center">
                        <th colspan="2">Payable</th>
                        <th>Receivable</th>
                    </tr>
                    <tr class="p-0 m-0 text-center fs-6">
                        <td>Advance Deposit from <span class="text-danger">{{nos_of_advance_payer}}</span> Shareholders
                        </td>
                        <td>
                            {{ currency }} {{amount_payable_for_shareholder_advance_deposit|intcomma_bd}}
                        </td>
                        <td rowspan="4">
                            <h6 class="text-danger pt-lg-3">
                                {{ currency }} {{total_amount_receivable_from_shareholder|intcomma_bd}}</h6>
                            (Total amount receivable from <span class="text-danger">{{nos_of_defaulter}}</span>
                            Shareholders<br>as per targeted amount <span class="text-danger">{{ currency }}
                                {{targeted_amount|intcomma_bd}}</span> per share)
                        </td>
                    </tr>

                    <tr class="p-0 m-0 text-center fs-6">
                        <td>Contractor Bill</td>
                        <td>
                            {{ currency }} {{qs_contractor_bill_status_sum|intcomma_bd|default_if_none:"---"}}
                        </td>
                    </tr>
                    <tr class="p-0 m-0 text-center">
                        <td>Credit Purchase</td>
                        <td>
                            {{ currency }} {{qs_credit_purchase_payment_status_sum|intcomma_bd|default_if_none:"---"}}
                        </td>
                    </tr>
                    <tr class="p-0 m-0 text-center">
                        <th>Total =</th>
                        <th>
                            {{ currency }} {{amount_payable_for_shareholder_advance_deposit|add:qs_credit_purchase_payment_status_sum|add:qs_contractor_bill_status_sum|intcomma_bd|default_if_none:"---"}}
                        </th>
                    </tr>

                    <!-- <tr class="p-0 m-0 text-center">
                    <th colspan="3">
                        Total =
                        {{total_deposited_amount|add:total_amount_receivable_from_shareholder|subtract:total_Expenditure.amount__sum|intcomma_bd}}
                    </th>

                </tr> -->
                </table>
            </div>
        </div>
    </div>
</div>
<!--Account Receivable and Payable end-->
<!-- <div class="row row-cols-1 row-cols-lg-2 p-0 m-0">
        <div class="col p-0 m-0" style="border: thin; border-color: brown;">
            <div class="card p-0 m-0">
                {{ chart_shareholder|safe }}
            </div>
        </div>
        <div class="col p-0 m-0" style="border: thin; border-color: brown;">
            <div class="card p-0 m-0">
                {{ fig_bar_chart|safe }}
            </div>
        </div>
    </div> -->
<br>
{% comment %} <!-- {{qs_data|length|devided:2|floatformat:"0"}} --> {% endcomment %}    
{% endblock %}