{% extends "main.html" %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% load mahimsoft_tags %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}{{company_name}}{%endblock %}
{% block content %}
<div class="card mb-2">
    <h4 class="card-header text-muted ps-2">Shareholder Name: <span class=text-danger>{{shareholder_deposit.0.shareholderName}}</span></h4>
    <div class="row">
        <div class="col">
            <div class="card-body p-0">
                <div class="table table-active p-0 m-0">
                    <table class="table table-sm align-middle table-bordered table-striped table-responsive p-0 m-0">
                        <tbody class="p-0 m-0">
                            <tr>
                                <td class="text-start px-2">Number{{ shareholder_deposit.0.numberOfFlat|pluralize:"s" }} of Share</td>
                                <td class="text-start">: {{shareholder_deposit.0.numberOfFlat|floatformat:"1"}}</td>
                            </tr>
                            <tr>
                                <td class="text-start px-2">Targeted Amount</td>
                                <td class="text-start">: {{ currency }}
                                    {{shareholder_deposit.0.numberOfFlat|number_product:targeted_amount_per_flat|intcomma_bd}}
                                </td>
                            </tr>
                            <tr>
                                <td class="text-start px-2">Amount Deposited</td>
                                <td class="text-start">: {{ currency }} {{shareholder_deposit.0.deposit_amount_sum|intcomma_bd}}
                                </td>
                            </tr>
                            <tr>
                                {% if shareholder_deposit.0.numberOfFlat|number_product:targeted_amount_per_flat|subtract:shareholder_deposit.0.deposit_amount_sum > 0 %}
                                <td class="text-start px-2">Amount to Deposit</td>
                                <td class="text-start text-danger">: {{ currency }}
                                    {{shareholder_deposit.0.numberOfFlat|number_product:targeted_amount_per_flat|subtract:shareholder_deposit.0.deposit_amount_sum|number_product:-1|intcomma_bd}}
                                </td>
                                {% elif shareholder_deposit.0.numberOfFlat|number_product:targeted_amount_per_flat|subtract:shareholder_deposit.0.deposit_amount_sum == 0 %}
                                <td class="text-start px-2">Amount to Deposit</td>
                                <td class="text-start">
                                : ---
                            </td>
                                {% else %}
                                <td class="text-start px-2">Advance Deposited</td>
                                <td class="text-start text-success">: {{ currency }}
                                    
                                    {{shareholder_deposit.0.numberOfFlat|number_product:targeted_amount_per_flat|subtract:shareholder_deposit.0.deposit_amount_sum|number_product:-1|intcomma_bd|default_if_none:"---"}}
                                </td>
                                {% endif %}
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col text-end">
            <img src="{{avatar}}" class="card-img-top" alt="shareholder"
                style="border-radius: 15%; width: 100px; height: 100px;  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);">
        </div>
    </div>
</div>

<div class="d-flex justify-content-between mb-2">
    <h5>{{heading}}</h5> 
    <span><a href="/shareholderDepositReport/{{shareholder_deposit.0.shareholder_id}}" target="_blank"
        class="btn btn-sm btn-outline-danger"><i class="bi bi-download"></i> PDF</a>
        <a href='{{ request.META.HTTP_REFERER }}' class='btn btn-outline-primary btn-sm me-0 ms-1 mb-0' role='button'><i class="bi bi-arrow-left-square-fill"></i> Back</a></span>
</div>
<!-- shareholderDepositReport -->
<div class="table table-active">
    <table class="table table-sm align-middle table-bordered table-striped table-hover table-responsive">
        <tr>
            <th class="text-center">#</th>
            <th class="text-center">Date</th>
            <th class="text-center">Mode of Deposit</th>
            <th class="text-center">Amount ({{ currency }})</th>
            <th class="text-center">Remarks</th>
            {% if request.user|has_group:"Admin"%}
            <th class="text-center">Edit</th>
            {% endif %}
        </tr>
        {% for item in shareholder_deposit %}
        <tr>
            <td class="text-center">{{forloop.counter}}</td>
            <td class="text-start ps-2">{{item.dateOfTransaction|date:"d b Y"|title}}</td>
            <td class="text-start ps-2">{{item.modeOfTransaction}}</td>
            <td class="text-end pe-4">{{item.amount|intcomma_bd}}</td>
            {% if item.remarks == None %}
            <td class="text-center">
                ----
            </td>
            {% else %}
            <td>
                {{item.remarks}}
            </td>
            {% endif %}
            {% if request.user|has_group:"Admin"%}
            <td class="text-center">
                {% if item.amount > 0 or item.amount != None %}
                <a class="c_link" href="/shareholder_deposit_update/{{item.id}}"><i
                        class="bi bi-pencil-square"></i></a>
                {% endif %}
            </td>
            {% endif %}

        </tr>
        {% endfor %}
    </table>
</div>
<br>

{% endblock %}