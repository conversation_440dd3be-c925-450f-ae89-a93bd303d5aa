import pandas as pd
import plotly.express as px
import plotly.graph_objs as go
from .models import (
    Shareholder,
    ShareholderDeposit,
    Expenditure,
    ShareholderDeposit,
    Shareholder,
    TargetedAmount,
)

from django.db.models import (
    Case,
    Cha<PERSON><PERSON><PERSON>,
    Count,
    DecimalField,
    DurationField,
    ExpressionWrapper,
    F,
    FloatField,
    IntegerField,
    OuterRef,
    Q,
    Subquery,
    Sum,
    Value,
    When,
)
from django.db.models.functions import Cast, Coalesce, Concat, Extract, Round, Trunc
from CONSTRUCTION_PROJECT.settings.context_processors import company_info_settings

company_info = company_info_settings()

def pie_chart(query_set, label_field, data_field):
    labels = []
    data = []
    queryset = query_set  # gov_status_wise_total
    for item in queryset:
        labels.append(item[label_field])  # 'gov_status'
        data.append(int(item[data_field]))  # 'tota_fdr_amount'
    return {"labels": labels, "data": data}

def chart():
    work_phase_count = Expenditure.objects.aggregate(
    work_phase_count=Count('work_phase_id', distinct=True)
    )
    # work_phase_count["work_phase_count"] = 1
    if company_info["work_phase"] and work_phase_count["work_phase_count"] > 1:
        qs = Expenditure.objects.values("work_phase").annotate(
            labels=F("work_phase__description"),
            Amount=Sum(Coalesce(F("amount"), 0, output_field=FloatField())),
        )
        chart_title = "Work Phase Wise Spent Amount"
    else:
        qs = Expenditure.objects.values("item__ItemCode__workSector").annotate(
            labels=F("item__ItemCode__workSector"),
            Amount=Sum(Coalesce(F("amount"), 0, output_field=FloatField())),
        )
        chart_title = "Work Sector Wise Spent Amount"

    fig_pie = px.pie(
        qs,
        values="Amount",
        names="labels",
        title=chart_title,
        #  color_discrete_sequence=px.colors.sequential.RdBu
    )
    # update_traces(title_text="Work Sector", selector=dict(type='pie'))
    fig_pie.update_traces(textinfo="label+percent", textposition="outside")
    fig_pie.update_layout(
        showlegend=True,
        title={"font_size": 20, "xanchor": "auto", "x": 0.5},
        legend=dict(title_font_family="Times New Roman",  font=dict(size=9)),
    )
    fig_pie_chart = fig_pie.to_html()

    #! Bar Chart for shareholder deposit and to pay ================
    qs_shareholder = Shareholder.objects.all()

    qs_deposit_subquery = (
        ShareholderDeposit.objects.filter(shareholder_id=OuterRef("id"))
        .values("shareholder_id")
        .annotate(
            deposited_amount_sum=Coalesce(
                Sum(F("amount")), 0, output_field=DecimalField()
            )
        )
    )
    targeted_amount = TargetedAmount.objects.values_list("amount").order_by(
        "-inputDate","-id"
    )[0]
    targeted_amount_per_flat = targeted_amount[0] / company_info["no_of_flat_per_share"]
    qs_data = qs_shareholder.annotate(
        deposited_sum=Subquery(qs_deposit_subquery.values("deposited_amount_sum")),
        amount_to_deposit=(F("numberOfFlat") * targeted_amount_per_flat),
    )

    data_deposit_target = [
        [
            x.shareholderName,
            0 if x.deposited_sum == None else int(x.deposited_sum) if int(x.deposited_sum) <= int(x.amount_to_deposit) else int(x.amount_to_deposit),
            int(
                x.amount_to_deposit
                - (0 if x.deposited_sum == None else int(x.deposited_sum))
            ) if int(
                x.amount_to_deposit
                - (0 if x.deposited_sum == None else int(x.deposited_sum))
            ) >= 0 else 0,
            int(
                x.amount_to_deposit
                - (0 if x.deposited_sum == None else int(x.deposited_sum))
            )*-1 if int(
                x.amount_to_deposit
                - (0 if x.deposited_sum == None else int(x.deposited_sum))
            ) < 0 else 0,
        ]
        for x in qs_data
    ]

    df_deposit_target = pd.DataFrame(
        data_deposit_target,
        columns=["Shareholders", "Amount Deposited", "Amount to Deposit", "Advance Deposited"],
    )

    # df_deposit_target["Amount to Deposit"] = df_deposit_target["Amount to Deposit"] if df_deposit_target["Amount to Deposit"] > 0 else 0
    # df_deposit_target["Advance Deposited"] = df_deposit_target["Amount to Deposit"] if df_deposit_target["Amount to Deposit"] < 0 else 0

    # Create a stacked bar chart using Plotly
    fig_deposit_target = px.bar(
        df_deposit_target,
        barmode="stack",
        # barmode="group",
        # color=["red", "goldenrod", "#00D"], #color_discrete_map="identity",
        # color_discrete_sequence=px.colors.qualitative.T10,
        text_auto=".3s",
        x="Shareholders",
        y=["Amount Deposited", "Amount to Deposit","Advance Deposited"],
        title="Shareholders Amount Deposit Info:",
        labels={"value": "Amount (Taka)", "variable": "Deposit Type"},
    )
    fig_deposit_target.update_layout(  # customize font and legend orientation & position
        # font_family="Rockwell",
        legend=dict(
            title=None, orientation="h", y=1, yanchor="bottom", x=0.5, xanchor="center"
        ),
        title={"font_size": 20, "xanchor": "center", "x": 0.5},
    )

    chart_deposit_target = fig_deposit_target.to_html()
    #! Bar Chart for shareholder deposit and to pay end ================

    context = {"fig_pie_chart":fig_pie_chart, 
                "chart_deposit_target":chart_deposit_target}

    return context

def dummy_chart():
    labels = ['Dummy: Phase01', 'Dummy: Phase02', 'Dummy: Phase03', 'Dummy: Phase04', 'Dummy: Phase05']
    values = [4000000,1000000,6000000,400000,800000]

    fig_pie_chart = go.Figure(data=[go.Pie(labels=labels, values=values, textinfo='label+value+percent')])
    fig_pie_chart.update_layout(
    title_text="Work Phase Wise Spent Amount",
    # title_font=dict(size=20),
    # legend=dict(x=1, y=0.5),
    # margin=dict(l=20, r=20, t=50, b=20),
    # paper_bgcolor='lightgray',
    # plot_bgcolor='white',
    
    title={"font_size": 20, "xanchor": "auto", "x": 0.5},
    annotations=[dict(text=f'Total: {sum(values)/10**5:,.2f} Lac', x=1.25, y=-.10,font=dict(color="#E04A1C", size=18), showarrow=False)],
    )

    fig_pie_chart=fig_pie_chart.to_html()


    # Shareholders = ['CSE', 'Mech', 'Electronics']
    Shareholders = [
        "Chocolate Platypus",
        "Peach Peacock",
        "Red Bee",
        "Gold Badger",
        "Copper Roadrunner",
        "Tan Emu",
        "Scarlet Heron",
        "Indigo Aardwolf",
        "Ivory Rabbit",
        "Gold Junglefowl",
        "Pink Ocelot",
        "Emerald Guppy",
        "Turquoise Rooster",
        "Cyan Partridge",
        "Blue Skink",
        "Lime Ape",
        "Violet Mule",
        "Lavender Swan",
        "Sapphire Kangaroo",
        "Blush Deer",
    ]
    deposited =  [
        200000,
        250000,
        500000,
        400000,
        350000,
        350000,
        300000,
        300000,
        400000,
        300000,
        250000,
        250000,
        300000,
        500000,
        500000,
        450000,
        450000,
        350000,
        350000,
        250000,
    ]
    to_deposit = [
        300000,
        250000,
        0,
        100000,
        150000,
        150000,
        200000,
        200000,
        100000,
        200000,
        250000,
        250000,
        200000,
        0,
        0,
        50000,
        50000,
        150000,
        150000,
        250000,
    ]
    print(len(Shareholders), len(deposited), len(to_deposit))
    trace1 = go.Bar(
    x = Shareholders,
    y = deposited,
    text=[f"{x/1000:.0f} k" for x in deposited],
    textfont=dict(size=9, color='white'),
    textposition='auto',
    name = 'Amount Deposited',
    )

    trace2 = go.Bar(
    x = Shareholders,
    y = to_deposit,
    text=[f"{x/1000:.0f} k" for x in to_deposit],
    textfont=dict(size=9, color='white'),
    textposition='auto',
    name = 'Amount to Deposited'
    )

    layout = go.Layout(
    title={
            'text': "Shareholder Deposit Information",
            'x': 0.5,
            'xanchor': 'center',
            'y': .85,
            'yanchor': 'top',
            'font':dict(color="#CD7054", size=20),
        },
    xaxis={
        'title': {
            'text': 'Shareholder',
            # 'font':dict(color="#CD7054", size=10),
        }
    },
    yaxis={
        'title': {
            'text': 'Amount (Taka)'
        }
    },
    barmode='stack',
    legend=dict(
            title=None, orientation="h", y=1.08, yanchor="top", x=0.5, xanchor="center"
        ),
        
    )

    data = [trace1, trace2]
    chart_deposit_target = go.Figure(data = data, layout = layout)
    # chart_deposit_target.update_layout(
    # font_family="Courier New",
    # font_color="blue",
    # title_font_family="Times New Roman",
    # title_font_color="#CD7054",
    # title_font_size="20",
    # legend_title_font_color="green"
    # )   
    chart_deposit_target = chart_deposit_target.to_html()

    return {"fig_pie_chart":fig_pie_chart, 
                "chart_deposit_target":chart_deposit_target}