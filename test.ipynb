{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6cddbdd1", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "Mime type rendering requires nbformat>=4.2.0 but it is not installed", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 14\u001b[39m\n\u001b[32m      3\u001b[39m fig = go.Figure(data=[go.Scatter(\n\u001b[32m      4\u001b[39m     x=[\u001b[32m1\u001b[39m, \u001b[32m2\u001b[39m, \u001b[32m3\u001b[39m, \u001b[32m4\u001b[39m, \u001b[32m5\u001b[39m],\n\u001b[32m      5\u001b[39m     y=[\u001b[32m100\u001b[39m, \u001b[32m2000\u001b[39m, \u001b[32m30000\u001b[39m, \u001b[32m400000\u001b[39m, \u001b[32m5000000\u001b[39m],\n\u001b[32m   (...)\u001b[39m\u001b[32m     11\u001b[39m     hovertemplate=\u001b[33m'\u001b[39m\u001b[33mValue: \u001b[39m\u001b[33m%\u001b[39m\u001b[38;5;132;01m{y:.2s}\u001b[39;00m\u001b[33m'\u001b[39m,\n\u001b[32m     12\u001b[39m )])\n\u001b[32m     13\u001b[39m fig.update_layout(title_text=\u001b[33m'\u001b[39m\u001b[33mScatter Plot with .2s Text Format\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m14\u001b[39m \u001b[43mfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mshow\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\Dream Tower\\CONSTRUCTION_PROJECT\\env\\Lib\\site-packages\\plotly\\basedatatypes.py:3410\u001b[39m, in \u001b[36mBaseFigure.show\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   3377\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m   3378\u001b[39m \u001b[33;03mShow a figure using either the default renderer(s) or the renderer(s)\u001b[39;00m\n\u001b[32m   3379\u001b[39m \u001b[33;03mspecified by the renderer argument\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m   3406\u001b[39m \u001b[33;03mNone\u001b[39;00m\n\u001b[32m   3407\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m   3408\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[34;01mplotly\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[34;01mp<PERSON>\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m3410\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mpio\u001b[49m\u001b[43m.\u001b[49m\u001b[43mshow\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\Dream Tower\\CONSTRUCTION_PROJECT\\env\\Lib\\site-packages\\plotly\\io\\_renderers.py:394\u001b[39m, in \u001b[36mshow\u001b[39m\u001b[34m(fig, renderer, validate, **kwargs)\u001b[39m\n\u001b[32m    389\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    390\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mMime type rendering requires ipython but it is not installed\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    391\u001b[39m         )\n\u001b[32m    393\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m nbformat \u001b[38;5;129;01mor\u001b[39;00m Version(nbformat.__version__) < Version(\u001b[33m\"\u001b[39m\u001b[33m4.2.0\u001b[39m\u001b[33m\"\u001b[39m):\n\u001b[32m--> \u001b[39m\u001b[32m394\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    395\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mMime type rendering requires nbformat>=4.2.0 but it is not installed\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    396\u001b[39m         )\n\u001b[32m    398\u001b[39m     ipython_display.display(bundle, raw=\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[32m    400\u001b[39m \u001b[38;5;66;03m# external renderers\u001b[39;00m\n", "\u001b[31mValueError\u001b[39m: Mime type rendering requires nbformat>=4.2.0 but it is not installed"]}], "source": ["import plotly.graph_objects as go\n", "\n", "fig = go.Figure(data=[go.<PERSON><PERSON>(\n", "    x=[1, 2, 3, 4, 5],\n", "    y=[100, 2000, 30000, 400000, 5000000],\n", "    mode='markers+text',\n", "    text=[100, 2000, 30000, 400000, 5000000],\n", "    textposition='top center',\n", "    textfont=dict(size=12),\n", "    marker=dict(size=10),\n", "    hovertemplate='Value: %{y:.2s}',\n", ")])\n", "fig.update_layout(title_text='Scatter Plot with .2s Text Format')\n", "fig.show()"]}], "metadata": {"kernelspec": {"display_name": "env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}