{% extends "main.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load mahimsoft_tags %}
{% load crispy_forms_filters %}

{% block title %}{{company_name}}{%endblock %}
{% block content %}

<div class="container pt-3">
    <div class="card">
        <div class="card-header ps-2 py-1">
            <h4><img src="{% static 'images/expenses-icon copy.svg' %}" width="24px" height="24px" alt=""> {{heading}}</h4>
        </div>
        <div class="card-body">
            {% crispy form %}
        </div>
    </div>
    <br>
    {% if update_tag is None %}
    <div class="card">
        <div class="card-header ps-2 py-1 me-0">
            <h5>{{data_heading}}</h5>
        </div>
        <div class="card-body">
        <table class="table table-bordered table-striped table-hover table-responsive align-middle table-sm">
            <tr class="text-center">
                <th>SL</th>
                <th>Date</th>
                <th>Description</th>
                <th>Quantity</th>
                <th>Unit</th>
                <th>Voucher No.</th>
                <th>Amount</th>
            </tr>
            {% for dt in data %}
            <tr>
                <td class="text-center">{{forloop.counter}}</td>
                <td>{{dt.dateOfTransaction}}</td>
                <td>{{dt.description}}</td>
                <td class="text-end pe-4">{{dt.quantity|intcomma_bd}}</td>
                <td>{{dt.unit}}</td>
                <td class="text-center">{{dt.voucherNo}}</td>
                <td class="text-end pe-4">{{dt.amount|intcomma_bd}}/-</td>
            </tr>
            {% endfor %}
        </table>
    </div>
  
    </div>
    {% endif %}
</div>
    <br>

    {% endblock content %}

    {% block script %}
    <script>
        var account_input = document.getElementById("id_account")
        {% if update_tag is None %}
        $('#id_item').empty();
        $('#id_item').append(`<option class='text-muted' value selected>------------</option>`);
        {% else %}
        button_div = document.getElementById("button_div")
        button_div.innerHTML = `<input type="submit" name="submit" value="Update" class="btn btn-primary btn btn-success me-2 mb-0" id="submit-id-submit">
	<a href="{%url 'Accounts:expenditure_details_list'%}" class="btn btn-danger">Cancel</a>`
        // Bank Account Field Initialization start =========
        if ($('#id_modeOfTransaction').val() == "BANK") {
            $.ajax({
                url: '/get_account_no/' + {{ object.pk }} + "/Expenditure",
        dataType: "json",
            success: function (data) {
                $('#id_account').val(data[0]["fields"]["account"]);
            }
            });
        }

        if ($('#id_modeOfTransaction').val() == "CASH") {
            account_input.disabled = true;
        }
        // Bank Account Field Initialization end =========
        {% endif %}

        $('#id_ItemCode').change(function () {
            let item_code_id = $(this).val();
            $('#id_item').empty();
            $('#id_item').append(`<option class='text-muted' value selected>------------</option>`);
            $.ajax({
                url: '/get_item/' + item_code_id,
                success: function (data) {
                    $.each(data, function (index, Item) {
                        $('#id_item').append($('<option>', {
                            value: Item.pk,
                            text: Item.fields.itemName
                        }));
                    });
                }
            });
        });

        // ======= Change Item Unit =========================
        $('#id_item').change(function () {
            let item_id = parseInt($('#id_item').val() ?? 0);
            $.ajax({
                url: '/get_unit/' + item_id,
                success: function (data) {
                    $('#id_unit').val(data[0].fields.unit);
                }
            });
        });
        // ===================================================

        // ======= Auto Calculate paid amount.========
        $('#id_rate').change(function () {
            let quantity = $('#id_quantity').val() ?? 0;
            let rate = $(this).val() ?? 0;
            $('#id_amount').val(Math.round((quantity * rate)*100)/100);
        })
        $('#id_quantity').change(function () {
            let quantity = $(this).val() ?? 0;
            let rate = $('#id_rate').val() ?? 0;
            $('#id_amount').val(Math.round((quantity * rate)*100)/100) ;
        })
        // ===================================================================

        // ======= Enable and Disable Bank Account Choice.========

        {% if not update_tag %}
        account_input.disabled = true;
        {% endif %}

        $('#id_modeOfTransaction').change(function () {
            if ($(this).val() == "BANK") {
                account_input.disabled = false;
                account_input.required = true;
                $('#bank_balance').addClass(`d-none`);
            } else if ($(this).val() == "CASH") {
                account_input.disabled = true;
                account_input.required = false;
                account_input.value = "";
                let payment_amount = $('#id_amount').val();
                $.ajax({
                url: '/get_balance/',
                success: function (data) {
                    if (data - payment_amount < 0) {
                        $('#bank_balance').removeClass(`d-none`);
                            $('#bank_balance').removeClass(`text-success bg-light`);
                            $('#bank_balance').addClass("text-danger bg-warning");
                        $('#bank_balance').text(`Cash Balance: {{currency}} ${Number(data).toFixed(0)}, is not enough to make payment: {{currency}} ${payment_amount}`);
                    } else {
                        $('#bank_balance').removeClass(`d-none text-danger bg-warning`);
                        $('#bank_balance').addClass("text-success bg-light");
                        $('#bank_balance').text(`After payment, Cash Balance: {{currency}} ${Number(data - payment_amount).toFixed(0)}`);
                    }
                }
            });
            }
        })
        // =======Enable and Disable Bank Account Choice end ============
{% if not update_tag %}
        $('#id_account').change(function () {
            let account_id = $(this).val();
            let payment_amount = $('#id_amount').val();
            $.ajax({
                url: '/get_balance/?transaction_type=BANK&account_id=' + account_id,
                success: function (data) {
                    if (data - payment_amount < 0) {
                        $('#bank_balance').removeClass(`d-none`);
                            $('#bank_balance').removeClass(`text-success bg-light`);
                            $('#bank_balance').addClass("text-danger bg-warning");

                        $('#bank_balance').text(`Bank Balance: {{currency}} ${Number(data).toFixed(0)}, is not enough to make payment: {{currency}} ${payment_amount}`);
                    } else {
                        $('#bank_balance').removeClass(`d-none text-danger bg-warning`);
                        $('#bank_balance').addClass("text-success bg-light");
                        $('#bank_balance').text(`After payment, Bank Balance: {{currency}} ${Number(data - payment_amount).toFixed(0)}`);
                    }
                    
                    
                }
            });
        })

        $('#id_amount').change(function () {
            if ($('#id_modeOfTransaction').val() == "BANK") {
                let account_id = $('#id_account').val();
                let payment_amount = $(this).val();
                $.ajax({
                    url: '/get_balance/?transaction_type=BANK&account_id=' + account_id,
                    success: function (data) {
                        if (data - payment_amount < 0) {
                            $('#bank_balance').removeClass(`d-none text-success bg-light`);
                            $('#bank_balance').addClass("text-danger bg-warning");
                            $('#bank_balance').text(`Bank Balance: {{currency}} ${Number(data).toFixed(0)}, is not enough to make payment: {{currency}} ${payment_amount}`);
                        } else {
                        $('#bank_balance').removeClass(`d-none text-danger bg-warning`);
                        $('#bank_balance').addClass("text-success bg-light");
                        $('#bank_balance').text(`After payment, Bank Balance: {{currency}} ${Number(data - payment_amount).toFixed(0)}`);
                        }
                    }
                });
            } else if ($('#id_modeOfTransaction').val() == "CASH") {
                let payment_amount = $('#id_amount').val();
                $.ajax({
                url: '/get_balance/',
                success: function (data) {
                    if (data - payment_amount < 0) {
                        $('#bank_balance').removeClass(`d-none`);
                            $('#bank_balance').removeClass(`text-success bg-light`);
                            $('#bank_balance').addClass("text-danger bg-warning");
                        $('#bank_balance').text(`Cash Balance: {{currency}} ${Number(data).toFixed(0)}, is not enough to make payment: {{currency}} ${payment_amount}`);
                    } else {
                        $('#bank_balance').removeClass(`d-none text-danger bg-warning`);
                        $('#bank_balance').addClass("text-success bg-light");
                        $('#bank_balance').text(`After payment, Cash Balance: {{currency}} ${Number(data - payment_amount).toFixed(0)}`);
                    }
                }
            });
            }
        })

        $('#id_rate').change(function () {
            if ($('#id_modeOfTransaction').val() == "BANK") {
                let account_id = $('#id_account').val();
                let payment_amount = $('#id_amount').val();
                $.ajax({
                    url: '/get_balance/?transaction_type=BANK&account_id=' + account_id,
                    success: function (data) {
                        if (data - payment_amount < 0) {
                            $('#bank_balance').removeClass(`d-none text-success bg-light`);
                            $('#bank_balance').addClass("text-danger bg-warning");
                            $('#bank_balance').text(`Bank Balance: {{currency}} ${Number(data).toFixed(0)}, is not enough to make payment: {{currency}} ${payment_amount}`);
                        } else {
                        $('#bank_balance').removeClass(`d-none text-danger bg-warning`);
                        $('#bank_balance').addClass("text-success bg-light");
                        $('#bank_balance').text(`After payment, Bank Balance: {{currency}} ${Number(data - payment_amount).toFixed(0)}`);
                        }
                    }
                });
            } else if ($('#id_modeOfTransaction').val() == "CASH"){
                let payment_amount = $('#id_amount').val();
                $.ajax({
                url: '/get_balance/',
                success: function (data) {
                    if (data - payment_amount < 0) {
                        $('#bank_balance').removeClass(`d-none`);
                            $('#bank_balance').removeClass(`text-success bg-light`);
                            $('#bank_balance').addClass("text-danger bg-warning");
                        $('#bank_balance').text(`Cash Balance: {{currency}} ${Number(data).toFixed(0)}, is not enough to make payment: {{currency}} ${payment_amount}`);
                    } else {
                        $('#bank_balance').removeClass(`d-none text-danger bg-warning`);
                        $('#bank_balance').addClass("text-success bg-light");
                        $('#bank_balance').text(`After payment, Cash Balance: {{currency}} ${Number(data - payment_amount).toFixed(0)}`);
                    }
                }
            });
            }
        })

        $('#id_quantity').change(function () {
            if ($('#id_modeOfTransaction').val() == "BANK") {
                let account_id = $('#id_account').val();
                let payment_amount = $('#id_amount').val();
                $.ajax({
                    url: '/get_balance/?transaction_type=BANK&account_id=' + account_id,
                    success: function (data) {
                        if (data - payment_amount < 0) {
                            $('#bank_balance').removeClass(`d-none text-success bg-light`);
                            $('#bank_balance').addClass("text-danger bg-warning");
                            $('#bank_balance').text(`Bank Balance: {{currency}} ${Number(data).toFixed(0)}, is not enough to make payment: {{currency}} ${payment_amount}`);
                        } else {
                        $('#bank_balance').removeClass(`d-none text-danger bg-warning`);
                        $('#bank_balance').addClass("text-success bg-light");
                        $('#bank_balance').text(`After payment, Bank Balance: {{currency}} ${Number(data - payment_amount).toFixed(0)}`);
                        }
                    }
                });
            } else if ($('#id_modeOfTransaction').val() == "CASH")  {
                let payment_amount = $('#id_amount').val();
                $.ajax({
                url: '/get_balance/',
                success: function (data) {
                    if (data - payment_amount < 0) {
                        $('#bank_balance').removeClass(`d-none`);
                            $('#bank_balance').removeClass(`text-success bg-light`);
                            $('#bank_balance').addClass("text-danger bg-warning");
                        $('#bank_balance').text(`Cash Balance: {{currency}} ${Number(data).toFixed(0)}, is not enough to make payment: {{currency}} ${payment_amount}`);
                    } else {
                        $('#bank_balance').removeClass(`d-none text-danger bg-warning`);
                        $('#bank_balance').addClass("text-success bg-light");
                        $('#bank_balance').text(`After payment, Cash Balance: {{currency}} ${Number(data - payment_amount).toFixed(0)}`);
                    }
                }
            });
            }
        })

        $('#reset-id-reset').click(function () {
            $('#bank_balance').addClass(`d-none`);
        })
        {% endif %}
    </script>

    {% endblock script %}