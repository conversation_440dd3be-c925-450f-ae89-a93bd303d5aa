{% extends "main.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% load mahimsoft_tags %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}{{company_name}}{%endblock %}
{% block extend_header %}
  <link href="{% static 'form_css.css' %}" rel="stylesheet">
{% endblock extend_header %}

{% block style %}
<style>
    th {
    background-color: #D9D9D9;
    border: 1px solid rgb(186, 191, 192);
    /* vertical-align: middle; */
    text-align: center;
    margin: 0px 0px 0px 0px;
    padding: 0px 5px 0px 5px;
    font-size: 13px;
}

td {
    font-family: 'BanglaFontSejuti';
    /* vertical-align: middle; */
    margin: 0px 0px 0px 0px;
    padding: 0px 5px 0px 5px;
    font-size: 12px;
}
</style>
{% endblock style %}
    

{% block content %}

<div class="container pt-3" style="width: 90%; margin: auto;">
    <div class="card">
        <div class="card-header ps-2 py-1">
            <h4>{{heading}}</h4>
        </div>
        <div class="card-body">
            {% crispy form %}
        </div>
    </div>
    <br>
    {% if update_tag is None %}
    <div class="card">
        <div class="card-header ps-2 py-1">
            <h4>{{data_heading}}</h4>
        </div>
        <div class="card-body mb-0 pb-0">
            <div class="table table-active">
            <table class="table table-sm align-middle table-bordered table-striped table-hover table-responsive">
                <thead>
                <tr class="text-center">
                <th style="width: 3%;">SL</th>
                <th style="width: 80px;">Photo</th>
                <th>Name</th>
                <th>Contractor Type</th>
                <th>Mobile</th>
                <th>Email</th>
                <th>Date of Join</th>
                <th>Status</th>
                <th>&#128270;</th>
                </tr>
                </thead>
                <tbody>
                {% for dt in data %}
                <tr>
                <td class="text-center">{{forloop.counter}}</td>
                <td class="text-center"><img src="{{dt.avatar}}" class="card-img-top" alt="masud"
                        style="border-radius: 15%; width: 80px; height: 80px;  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);">
                </td>
                <td {% if dt.IsArchive == True %} class="text-muted text-decoration-line-through" {% endif %}>
                    {{dt.contractor}}
                </td>
                <td class="text-center">{{dt.contractorType|default_if_none:"---"}}</td>
                <td>{{dt.Mobile|default_if_none:"---"}}</td>
                <td>{{dt.Email|default_if_none:"---"}}</td>
                <td class="text-center">{{dt.dateOfJoin|date:"d M Y"}}</td>
                <td class="text-center {{dt.IsArchive|yesno:'text-danger,text-success'}}">{{dt.IsArchive|yesno:'Inactive,Active,maybe'}}</td>
                <td class="text-center"><a class="a_link" href="{{dt.get_absolute_url}}">
                        <i class="bi bi-file-earmark-text-fill"></a></i></td>
                </tr>
                {% endfor %}
            </tbody>
            </table>
            </div>
            </div>
            {% endif %}
        </div>

        <br>
        {% comment %} {{request.META}} {% endcomment %}
        {% endblock %}
        {% block script %}

        <script>
            {% if update_tag %}
            button_div = document.getElementById("button_div")
            button_div.innerHTML = `<input type="submit" name="submit" value="Update" class="btn btn-primary btn btn-success me-2 mb-0" id="submit-id-submit">
	<a href="{%url 'Accounts:contractor_list'%}" class="btn btn-danger">Cancel</a>`
            {% endif %}
            
            
            // TODO: Chosen Image Show ===========
            
            function readURL(input) {
                if (input.files && input.files[0]) {
                    var reader = new FileReader();
                    reader.onload = function (e) {
                        $('#blah').attr('src', e.target.result);
                        
                    };
                    
                    reader.readAsDataURL(input.files[0]);
                }
            }
            
            $("#id_image").change(function () {
                $('#blah').attr('src', "");
                readURL(this);
            });
        </script>
        {% endblock script %}