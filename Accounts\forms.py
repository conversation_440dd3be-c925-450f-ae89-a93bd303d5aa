from django import forms
from PIL import Image
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import calendar
from django_svg_image_form_field import SvgAndImageFormField
from django.db.models import (
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Count,
    DecimalField,
    DurationField,
    ExpressionWrapper,
    F,
    FloatField,
    IntegerField,
    OuterRef,
    Q,
    Subquery,
    Sum,
    Value,
    When,
)
from django.db.models.functions import Cast, Coalesce, Concat, Extract, Round, Trunc
from .models import (
    Shareholder,
    Expenditure,
    ContractorType,
    Contractor,
    ItemCode,
    Item,
    ContractorBillSubmission,
    ContractorBillPayment,
    ShareholderDeposit,
    TargetedAmount,
    CreditPurchase,
    CreditPurchasePayment,
    IncomeSector,
    IncomeItem,
    Income,
    BankAccount,
    BankLedger,
    WorkPhase,
    CurrentWorkPhase,
)
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Submit, Row, Column, Reset, HTML


class TargetedAmountForm(forms.ModelForm):
    class Meta:
        model = TargetedAmount
        fields = "__all__"
        labels = {
            "amount": "Targeted Amount:",
            "inputDate": "Start Date:",
            "deadline": "Deadline:",
        }

        widgets = {
            "inputDate": forms.DateInput(
                format=("%Y-%m-%d"),
                attrs={"class": "", "placeholder": "YYYY-MM-DD", "type": "date"},
            ),
            "deadline": forms.DateInput(
                format=("%Y-%m-%d"),
                attrs={"class": "", "placeholder": "YYYY-MM-DD", "type": "date"},
            ),
        }

    def __init__(self, *args, **kwargs):
        super(TargetedAmountForm, self).__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.fields['deadline'].initial = datetime.now() + relativedelta(months=1)
        self.helper.attrs["autocomplete"] = "off"
        self.helper.layout = Layout(
            Row(
                Column("amount", css_class="form-group col me-2 mb-0"),
                Column("inputDate", css_class="form-group col-4 me-2 mb-0"),
                Column("deadline", css_class="form-group col-4 me-4 mb-0"),
                css_class="form-row",
            ),

            HTML("<div class='d-flex justify-content-end mb-1' id ='button_div'>"),
            Submit("submit", "Submit", css_class="btn btn-success btn-sm me-2 mb-0"),
            Reset("reset", "Reset", css_class="btn btn-danger btn-sm me-2 mb-0"),
            HTML("<a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 mb-0' role='button'><i class='bi bi-arrow-left-square'></i> Back</a>"),
            HTML("</div>"),
        )


class ContractorTypeForm(forms.ModelForm):
    class Meta:
        model = ContractorType
        fields = "__all__"
        labels = {
            "contractorType": "Contractor Type:",
        }


class ItemCodeForm(forms.ModelForm):
    class Meta:
        model = ItemCode
        fields = "__all__"
        labels = {
            "workSector": "Work Sector:",
        }


class ItemForm(forms.ModelForm):
    class Meta:
        model = Item
        fields = "__all__"
        labels = {
            "ItemCode": "Work Sector:",
            "itemName": "Item Name:",
            "unit": "Unit:",
        }

    def __init__(self, *args, **kwargs):
        super(ItemForm, self).__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.helper.attrs["autocomplete"] = "off"
        self.helper.layout = Layout(
            Row(
                Column("ItemCode", css_class="form-group col-3 me-4 mb-0"),
                Column("itemName", css_class="form-group col-5 ms-2 me-2 mb-0"),
                Column("unit", css_class="form-group col me-4  mb-0"),
                css_class="form-row",
            ),
            HTML("<div class='d-flex justify-content-end mb-1' id ='button_div'>"),
            Submit("submit", "Submit", css_class="btn btn-success btn-sm me-2 mb-0"),
            Reset("reset", "Reset", css_class="btn btn-danger btn-sm me-2 mb-0"),
            HTML("<a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 mb-0' role='button'><i class='bi bi-arrow-left-square'></i> Back</a>"),
            HTML("</div>"),
        )


class ShareholderForm(forms.ModelForm):
    class Meta:
        model = Shareholder
        fields = "__all__"
        labels = {
            "dateOfJoin": "Date of Join:",
            "shareholderName": "Name:",
            "address": "Address:",
            "mobile": "Mobile:",
            "email": "Email:",
            "nid": "NID:",
            "numberOfFlat": "Nos. of Share:",
            "image": "Shareholder's Photo:",
            "gender": "Gender:",
        }

        widgets = {
            "dateOfJoin": forms.DateInput(
                format=("%Y-%m-%d"),
                attrs={"class": "", "placeholder": "Select Date", "type": "date"},
            ),
            'image': forms.FileInput(),
            # "image": forms.ImageField(), 
            # ImageField(widget=FileInput) class Meta: model = Profile fields = [“avatar”, “is_dark_theme”,“is_logicmorph_staff”]
        }

    def __init__(self, *args, **kwargs):
        super(ShareholderForm, self).__init__(*args, **kwargs)
        self.fields["gender"].choices = [("", "select gender"),] + \
        list(self.fields["gender"].choices)[0:] 
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.helper.attrs["autocomplete"] = "off"
        self.helper.layout = Layout(
            Row(
                Column("dateOfJoin", css_class="form-group col-2 me-2 mb-0"),
                Column("shareholderName", css_class="form-group col-2 mb-0"),
                Column("address", css_class="form-group col ms-2 me-2 mb-0"),
                Column("mobile", css_class="form-group col-2 me-4 mb-0"),
                css_class="form-row",
            ),
            Row(
                Column("email", css_class="form-group col-2 me-2 mb-0"),
                Column("nid", css_class="form-group col-2 me-2 mb-0"),
                Column("numberOfFlat", css_class="form-group col-2 me-2 mb-0"),
                Column("gender", css_class="form-group col-1 me-4 mb-0"),
                Column("image", css_class="form-group col ms-2 me-4 mb-0"),
                css_class="form-row",
            ),
            HTML("<div class='d-flex justify-content-end mb-1' id ='button_div'>"),
            Submit("submit", "Submit", css_class="btn btn-success btn-sm me-2 mb-0"),
            Reset("reset", "Reset", css_class="btn btn-danger btn-sm me-2 mb-0"),
            HTML("<a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 mb-0' role='button'><i class='bi bi-arrow-left-square'></i> Back</a>"),
            HTML("</div>"),
        )


class ContractorForm(forms.ModelForm):
    class Meta:
        model = Contractor
        fields = "__all__"
        labels = {
            "dateOfJoin": "Date of Join:",
            "contractor": "Name of The Firm:",
            "contractorType": "Contractor Type:",
            "address": "Address:",
            "NID": "NID:",
            "TIN": "TIN:",
            "TelephoneNo": "Telephone No:",
            "Mobile": "Mobile:",
            "Email": "Email:",
            "image": "Photo:",
            "IsArchive": "Archive:",
        }
        field_classes = {
            'image': SvgAndImageFormField,
        }
        widgets = {
            "dateOfJoin": forms.DateInput(
                format=("%Y-%m-%d"),
                attrs={"class": "", "placeholder": "Select Date", "type": "date"},
            ),
            "address": forms.Textarea(attrs={"rows": 1, "cols": 15}),
            'image': forms.FileInput(),
        }
    def __init__(self, *args, **kwargs):
        super(ContractorForm, self).__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.helper.attrs["autocomplete"] = "off"
        self.helper.layout = Layout(
            Row(
                Column("dateOfJoin", css_class="form-group col-2 me-2 mb-0"),
                Column("contractor", css_class="form-group col-2 mb-0"),
                Column("contractorType", css_class="form-group col-2 ms-2 me-2 mb-0"),
                Column("address", css_class="form-group col ms-4 me-4 mb-0"),
                css_class="form-row",
            ),
            Row(
                Column("NID", css_class="form-group col-2 me-2 mb-0"),
                Column("TIN", css_class="form-group col-2 me-2 mb-0"),
                Column("Email", css_class="form-group col-2 me-2 mb-0"),
                Column("TelephoneNo", css_class="form-group col-2 me-2 mb-0"),
                Column("Mobile", css_class="form-group col me-4 mb-0"),
                css_class="form-row",
            ),
            Row(
            HTML("<div class='d-flex justify-content-start align-items-center p-2'>"),
                Column("image", css_class="form-group col-3 mb-0 me-2"),
                HTML("""<img class='img-thumbnail ms-4' id='blah' src={% if object.image %} '{{object.image.url}}' {% else %} '/static/images/avatar_default.svg' {% endif %} onerror="this.src='/static/images/avatar_default.svg'" alt='' width='100' height='100'/></div>"""),
                css_class="form-row",
            ),
            Row(
                Column("IsArchive", css_class="form-group col-4 mb-0"),
                css_class="form-row",
            ),
            HTML("<div class='d-flex justify-content-end mb-1' id ='button_div'>"),
            Submit("submit", "Submit", css_class="btn btn-success btn-sm me-2 mb-0"),
            Reset("reset", "Reset", css_class="btn btn-danger btn-sm me-2 mb-0"),
            HTML("<a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 mb-0' role='button'><i class='bi bi-arrow-left-square'></i> Back</a>"),
            HTML("</div>"),
        )


class ContractorBillSubmissionForm(forms.ModelForm):
    class Meta:
        model = ContractorBillSubmission
        fields = "__all__"
        labels = {
            "contractor": "Contractor:",
            "dateOfBillSubmission": "Date:",
            "description": "Description:",
            "amount": "Amount:",
            "remarks": "Remarks:",
        }
        widgets = {
            "dateOfBillSubmission": forms.DateInput(
                format=("%Y-%m-%d"),
                attrs={"class": "", "placeholder": "Select Date", "type": "date"},
            ),
            # "remarks": forms.Textarea(attrs={"rows": 2, "cols": 15}),
        }

    def __init__(self, *args, **kwargs):
        super(ContractorBillSubmissionForm, self).__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.fields["contractor"].queryset = Contractor.objects.filter(IsArchive=False)
        self.helper.form_method = "post"
        self.helper.attrs["autocomplete"] = "off"
        self.helper.layout = Layout(
            Row(
                Column("contractor", css_class="form-group col-3 me-4 mb-0"),
                Column(
                    "dateOfBillSubmission", css_class="form-group col-2 ms-3 me-2 mb-0"
                ),
                Column("description", css_class="form-group col ms-2 me-2 mb-0"),
                Column("amount", css_class="form-group col-1 ms-2 me-4 mb-0"),
                css_class="form-row",
            ),
            Row(
                Column("remarks", css_class="form-group col me-4 mb-0"),
                css_class="form-row",
            ),
            HTML("<div class='d-flex justify-content-end mb-1' id ='button_div'>"),
            Submit("submit", "Submit", css_class="btn btn-success btn-sm me-2 mb-0"),
            Reset("reset", "Reset", css_class="btn btn-danger btn-sm me-2 mb-0"),
            HTML("<a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 mb-0' role='button'><i class='bi bi-arrow-left-square'></i> Back</a>"),
            HTML("</div>"),
        )


class ContractorBillPaymentModelForm(forms.ModelForm):
    class Meta:
        model = ContractorBillPayment
        fields = ["dateOfTransaction",
                "item",
                "unit",
                "rate",
                "quantity",
                "amount",
                # "labor_fooding",
                "modeOfTransaction",
                "voucherNo",
                "remarks",
                "bill",
                ]
        labels = {
            "bill": "Bill:",
            "dateOfTransaction": "Transaction Date:",
            "item": "Item",
            "modeOfTransaction":"Mode:",
            # "labor_fooding": "Labor Tea:",
            "voucherNo": "Voucher No:",
            "remarks": "Remarks:",
            "quantity": "quantity:",
            "unit": "Unit:",
            "rate": "Rate:",
            "amount": "Amount:",
        }
        widgets = {
            "dateOfTransaction": forms.DateInput(
                format=("%Y-%m-%d"),
                attrs={"class": "", "placeholder": "Select Date", "type": "date"},
            ),
        }


class ContractorBillPaymentForm(ContractorBillPaymentModelForm):
    account_queryset = BankAccount.objects.filter(isArchive=False)
    account = forms.ModelChoiceField(
        queryset=account_queryset,
        label="Select Account:",
        required=False,
    )

    QrySetItemCode = ItemCode.objects.only("workSector").distinct()
    ItemCode = forms.ModelChoiceField(
        queryset=QrySetItemCode, label="Work Sector:", required=False
    )

    def __init__(self, *args, **kwargs):
        super(ContractorBillPaymentForm, self).__init__(*args, **kwargs)
        qs = ContractorBillSubmission.objects.values("id").annotate(
            sum_amount=Sum(F("bill_submission__amount")),
            amount=F("amount"),
        )
        query_set = ContractorBillSubmission.objects.filter(
            id__in=(
                qs.filter(amount__gt=Coalesce(F("sum_amount"), 0)).values_list("id")
            )
        )

        self.fields["bill"].queryset = query_set
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.helper.attrs["autocomplete"] = "off"
        self.helper.layout = Layout(
            Row(
                Column("bill", css_class="form-group col-3 me-4 mb-0"),
                Column(
                    "dateOfTransaction", css_class="form-group ms-2 col-2 me-2 mb-0"
                ),
                Column("ItemCode", css_class="form-group col me-5 mb-0"),
                css_class="form-row",
            ),
            Row(
                Column("item", css_class="form-group col-3 me-4 mb-0"),
                Column("unit", css_class="form-group col-1 ms-2 me-2 ms-0  mb-0"),
                Column("quantity", css_class="form-group col-1 me-2 mb-0"),
                Column("rate", css_class="form-group col-1 me-2 mb-0"),
                Column("amount", css_class="form-group col-1 me-2 mb-0"),
                # Column("labor_fooding", css_class="form-group col-1  me-2 mb-0"),
                Column("modeOfTransaction", css_class="form-group col me-5 ms-0  mb-0"),
                css_class="form-row",
            ),
            Row(
                Column("account", css_class="form-group col-3 ms-0 me-4 mb-0"),
                Column("voucherNo", css_class="form-group col-2 me-2 ms-2  mb-0"),
                Column("remarks", css_class="form-group col me-4 mb-0"),
                css_class="form-row",
            ),
            HTML("<div class='d-flex justify-content-end mb-1' id ='button_div'>"),
            Submit("submit", "Submit", css_class="btn btn-success btn-sm me-2 mb-0"),
            Reset("reset", "Reset", css_class="btn btn-danger btn-sm me-2 mb-0"),
            HTML("<a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 mb-0' role='button'><i class='bi bi-arrow-left-square'></i> Back</a>"),
            HTML("</div>"),
        )


class CreditPurchaseForm(forms.ModelForm):
    class Meta:
        model = CreditPurchase
        fields = "__all__"
        labels = {
            "dateOfPurchase": "Date",
            "seller ": "Seller:",
            "address ": "Address:",
            "description ": "Description:",
            "mobile ": "Mobile:",
            "email ": "Email:",
            "amount ": "Amount:",
            "remarks ": "Remarks:",
        }
        widgets = {
            "dateOfPurchase": forms.DateInput(
                format=("%Y-%m-%d"),
                attrs={"class": "", "placeholder": "Select Date", "type": "date"},
            ),
        }

    def __init__(self, *args, **kwargs):
        super(CreditPurchaseForm, self).__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.helper.attrs["autocomplete"] = "off"
        self.helper.layout = Layout(
            Row(
                Column("seller", css_class="form-group col-2 me-2 mb-0"),
                Column("address", css_class="form-group col-4 me-2 mb-0"),
                Column("mobile", css_class="form-group col-2 me-2 mb-0"),
                Column("email", css_class="form-group col me-4 mb-0"),
                css_class="form-row",
            ),
            Row(
                Column("dateOfPurchase", css_class="form-group col-2 me-2 mb-0"),
                Column("description", css_class="form-group col-3 me-2 mb-0"),
                Column("amount", css_class="form-group col-1 me-2 mb-0"),
                Column("remarks", css_class="form-group col me-4 mb-0"),
                css_class="form-row",
            ),
            HTML("<div class='d-flex justify-content-end mb-1' id ='button_div'>"),
            Submit("submit", "Submit", css_class="btn btn-success btn-sm me-2 mb-0"),
            Reset("reset", "Reset", css_class="btn btn-danger btn-sm me-2 mb-0"),
            HTML("<a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 mb-0' role='button'><i class='bi bi-arrow-left-square'></i> Back</a>"),
            HTML("</div>"),
        )


class CreditPurchasePaymentModelForm(forms.ModelForm):
    QrySetItemCode = ItemCode.objects.only("workSector").distinct()
    ItemCode = forms.ModelChoiceField(
        queryset=QrySetItemCode, label="Work Sector:", required=False
    )

    class Meta:
        model = CreditPurchasePayment
        fields = ["dateOfTransaction",
                    "seller",
                    "item",
                    "description",
                    "unit",
                    "rate",
                    "quantity",
                    "voucherNo",
                    "modeOfTransaction",
                    "amount",
                    "remarks",
                    ]
        labels = {
            "seller": "Seller:",
            "dateOfTransaction": "Date:",
            "description": "Description:",
            "unit": "Unit:",
            "quantity": "quantity:",
            "rate": "Rate:",
            "amount": "Amount:",
            "modeOfTransaction":"Mode",
            "voucherNo": "Voucher:",
            "remarks": "Remarks:",
            "item": "Item",
        }
        widgets = {
            "dateOfTransaction": forms.DateInput(
                format=("%Y-%m-%d"),
                attrs={"class": "", "placeholder": "Select Date", "type": "date"},
            ),
        }


class CreditPurchasePaymentForm(CreditPurchasePaymentModelForm):
    account_queryset = BankAccount.objects.filter(isArchive=False)
    account = forms.ModelChoiceField(
        queryset=account_queryset,
        label="Select Account:",
        required=False,
    )
    def __init__(self, *args, **kwargs):
        super(CreditPurchasePaymentForm, self).__init__(*args, **kwargs)
        qs = CreditPurchase.objects.values("id").annotate(
            sum_amount=Sum(F("Seller__amount")),
            amount=F("amount"),
        )
        query_set = CreditPurchase.objects.filter(
            id__in=(
                qs.filter(amount__gt=Coalesce(F("sum_amount"), 0)).values_list("id")
            )
        )

        self.fields["seller"].queryset = query_set
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.helper.attrs["autocomplete"] = "off"
        self.helper.layout = Layout(
            Row(
                Column("seller", css_class="form-group col-3 me-4 mb-0"),
                Column(
                    "dateOfTransaction", css_class="form-group ms-2 col-2 me-2 mb-0"
                ),
                Column("ItemCode", css_class="form-group col-2 me-4 mb-0"),
                Column("item", css_class="form-group col ms-2 me-5 mb-0"),
                css_class="form-row",
            ),
            Row(
                Column("description", css_class="form-group col me-2 mb-0"),
                Column("unit", css_class="form-group col-1 me-2 ms-0  mb-0"),
                Column("quantity", css_class="form-group col-1 me-2 mb-0"),
                Column("rate", css_class="form-group col-1 me-2 mb-0"),
                Column("amount", css_class="form-group col-1 me-2 mb-0"),
                Column("modeOfTransaction", css_class="form-group col-1 me-5 ms-0  mb-0"),
                css_class="form-row",
            ),
            Row(
                Column("account", css_class="form-group col-3 ms-0 me-4 mb-0"),
                Column("voucherNo", css_class="form-group col-1 me-2 ms-2  mb-0"),
                Column("remarks", css_class="form-group col me-4 mb-0"),
                css_class="form-row",
            ),
            HTML("<div class='d-flex justify-content-end mb-1' id ='button_div'>"),
            Submit("submit", "Submit", css_class="btn btn-success btn-sm me-2 mb-0"),
            Reset("reset", "Reset", css_class="btn btn-danger btn-sm me-2 mb-0"),
            HTML("<a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 mb-0' role='button'><i class='bi bi-arrow-left-square'></i> Back</a>"),
            HTML("</div>"),
        )


class ExpenditureModelForm(forms.ModelForm):
    class Meta:
        model = Expenditure
        fields = ["dateOfTransaction",
                    "item",
                    "description",
                    "unit",
                    "rate",
                    "quantity",
                    "voucherNo",
                    "modeOfTransaction",
                    "amount",
                    "remarks",
                    ]

        widgets = {
            "dateOfTransaction": forms.DateInput(
                format=("%Y-%m-%d"),
                attrs={"class": "", "placeholder": "Select Date", "type": "date"},
            ),
        }
        labels = {
            "dateOfTransaction": "Date:",
            "item": "Item:",
            "description": "Description:",
            "unit": "Unit:",
            "quantity": "quantity:",
            "rate": "Rate:",
            "amount": "Amount:",
            "modeOfTransaction": "Mode:",
            "voucherNo": "Voucher No:",
            "remarks": "Remarks:",
        }


class ExpenditureForm(ExpenditureModelForm):
    QrySetItemCode = ItemCode.objects.only("workSector").distinct()
    ItemCode = forms.ModelChoiceField(
        queryset=QrySetItemCode, label="Work Sector:", required=False
    )

    account_queryset = BankAccount.objects.filter(isArchive=False)
    account = forms.ModelChoiceField(
        queryset=account_queryset,
        label="Select Account:",
        required=False,
    )

    def __init__(self, *args, **kwargs):
        super(ExpenditureForm, self).__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.helper.attrs["autocomplete"] = "off"
        self.helper.layout = Layout(
            # First Row: Date, Item Code, Item Name
            # Columns configured to stack on extra-small (col-12),
            # then arrange on medium (col-md-*) and large (col-lg-*) screens.
            # Sums for MD: 4+3+5 = 12 (OK)
            # Sums for LG: 3+2+7 = 12 (OK)
            Row(
                Column("dateOfTransaction", css_class="col-12 col-md-4 col-lg-2"),
                Column("ItemCode", css_class="col-12 col-md-3 col-lg-2 me-3"),
                Column("item", css_class="col-12 col-md-5 col-lg mb-3"),
                css_class="row row-cols-1 row-cols-md-2 row-cols-lg-3 gx-5 gy-1", # Bootstrap 5 gutter for consistent spacing between columns
            ),

            # Second Row: Description (given its potential length, it gets its own row or half-width)
            Row(
                Column("description", css_class="col-12 col-md-11 col-lg-11"), # Full width across all devices
                css_class="row row-cols-1 row-cols-md-2 row-cols-lg-3 gx-5 gy-1",
            ),

            # Third Row: Unit, Quantity, Rate, Amount (small fields, grouped together)
            # Stacks on extra-small (col-6 for two per row), then arranges on MD/LG
            # Sums for MD: 3+3+3+3 = 12 (OK)
            # Sums for LG: 3+3+3+3 = 12 (OK)
            Row(
                Column("unit", css_class="col-6 col-md-3 col-lg-1"),
                Column("quantity", css_class="col-6 col-md-3 col-lg-1"),
                Column("rate", css_class="col-6 col-md-3 col-lg-1"),
                Column("amount", css_class="col-6 col-md-3 col-lg"),
                css_class="row row-cols-1 row-cols-md-2 row-cols-lg gx-5 gy-1",
            ),

            # Fourth Row: Mode of Transaction (gets its own line as it's separate from calculations)
            # Row(
            #     css_class="row row-cols-1 row-cols-md-2 row-cols-lg-3 gx-5 gy-1",
            # ),

            # Fifth Row: Account, Voucher No, Remarks
            # Sums for MD: 6+2+4 = 12 (OK)
            # Sums for LG: 4+2+6 = 12 (OK)
            Row(
                Column("modeOfTransaction", css_class="col-12 col-md-6 col-lg-1 me-3"), # Adapts nicely
                Column("account", css_class="col-12 col-md-6 col-lg-4 me-3"),
                Column("voucherNo", css_class="col-6 col-md-2 col-lg-1"), # col-6 on mobile to prevent squishing
                Column("remarks", css_class="col-12 col-md-4 col-lg-3"),
                css_class="row row-cols-1 row-cols-md-2 row-cols-lg-4 gx-5 gy-1",
            ),

            # Informational text (already good with Bootstrap classes)
            HTML("<p><samp class='text-danger d-none blockquote-footer p-0 m-0' id='bank_balance' style='background-color: #FFEFD5;'>Masud Zaman</samp></p>"),

            # Buttons (already good with Bootstrap classes)
            HTML("<div class='d-flex justify-content-end mt-4 mb-1' id='button_div'>"), # Added mt-4 for top margin
            Submit("submit", "Submit", css_class="btn btn-success btn-sm me-2 mb-0"),
            Reset("reset", "Reset", css_class="btn btn-danger btn-sm me-2 mb-0"),
            HTML("<a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 mb-0' role='button'><i class='bi bi-arrow-left-square'></i> Back</a>"),
            HTML("</div>"),
        )


class SendMailForm(forms.Form):
    email_id = forms.CharField(required=False)
    email_cc = forms.EmailField(required=False)
    email_bcc = forms.EmailField(required=False)
    subject = forms.CharField(
        max_length=200,
        help_text="<code class='text-muted'>You Must have a subject.</code>",
    )
    msg = forms.CharField(widget=forms.Textarea, required=False)
    attachment = forms.FileField(required=False)


class ChartForm(forms.Form):
    start = forms.DateField(widget=forms.DateInput(attrs={"type": "date"}))
    end = forms.DateField(widget=forms.DateInput(attrs={"type": "date"}))


class ShareholderDepositModelForm(forms.ModelForm):
    class Meta:
        model = ShareholderDeposit
        fields = [
            "dateOfTransaction",
            "modeOfTransaction",
            "amount",
            "remarks",
            "shareholder",
        ]
        labels = {
            "shareholder": "Shareholder:",
            "dateOfTransaction": "Date:",
            "modeOfTransaction": "Mode:",
            "amount": "Amount:",
            "remarks": "Remarks:",
        }
        widgets = {
            "dateOfTransaction": forms.DateInput(
                format=("%Y-%m-%d"),
                attrs={"class": "", "placeholder": "Select Date", "type": "date"},
            ),
            # "remarks": forms.Textarea(attrs={"rows": 2, "cols": 15}),
        }


class ShareholderDepositForm(ShareholderDepositModelForm):
    account_queryset = BankAccount.objects.filter(isArchive=False)
    account = forms.ModelChoiceField(
        queryset=account_queryset,
        label="Select Account:",
        required=False,
    )

    def __init__(self, *args, **kwargs):
        super(ShareholderDepositForm, self).__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.helper.attrs["autocomplete"] = "off"
        self.helper.layout = Layout(
            Row(
                Column("shareholder", css_class="form-group col-3 me-4 mb-0"),
                Column(
                    "dateOfTransaction", css_class="form-group col-2 ms-2 me-2 mb-0"
                ),
                Column("modeOfTransaction", css_class="form-group col-1 me-4 mb-0"),
                Column("amount", css_class="form-group col ms-2 me-4 mb-0"),
                css_class="form-row",
            ),
            Row(
                Column("account", css_class="form-group col-5 me-4 mb-0"),
                Column("remarks", css_class="form-group col ms-2 me-4 mb-0"),
                css_class="form-row",
            ),
            HTML("<div class='d-flex justify-content-end mb-1' id='button_div'>"),
            Submit("submit", "Submit", css_class="btn btn-success btn-sm me-2 mb-0"),
            Reset("reset", "Reset", css_class="btn btn-danger btn-sm me-2 mb-0"),
            HTML("<a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 mb-0' role='button'><i class='bi bi-arrow-left-square'></i> Back</a>"),
            HTML("</div>"),
        )


class IncomeSectorForm(forms.ModelForm):
    class Meta:
        model = IncomeSector
        fields = "__all__"
        labels = {
            "incomeSector": "Income Sector:",
        }


class IncomeItemForm(forms.ModelForm):
    class Meta:
        model = IncomeItem
        fields = "__all__"
        labels = {
            "incomeSector": "Income Sector:",
            "itemName": "Item Name:",
            "unit": "Unit:",
        }

    def __init__(self, *args, **kwargs):
        super(IncomeItemForm, self).__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.helper.attrs["autocomplete"] = "off"
        self.helper.layout = Layout(
            Row(
                Column("incomeSector", css_class="form-group col-3 me-5 mb-0"),
                Column("itemName", css_class="form-group col-5 me-3 mb-0"),
                Column("unit", css_class="form-group col me-4  mb-0"),
                css_class="form-row",
            ),
            HTML("<div class='d-flex justify-content-end mb-1' id ='button_div'>"),
            Submit("submit", "Submit", css_class="btn btn-success btn-sm me-2 mb-0"),
            Reset("reset", "Reset", css_class="btn btn-danger btn-sm me-2 mb-0"),
            HTML("<a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 mb-0' role='button'><i class='bi bi-arrow-left-square'></i> Back</a>"),
            HTML("</div>"),
        )


# TODO: ============================
class IncomeModelForm(forms.ModelForm):
    class Meta:
        model = Income
        fields = ["dateOfTransaction",
                    "incomeItem",
                    "description",
                    "unit",
                    "quantity",
                    "rate",
                    "voucherNo",
                    "modeOfTransaction",
                    "amount",
                    "remarks"
                    ]

        widgets = {
            "dateOfTransaction": forms.DateInput(
                format=("%Y-%m-%d"),
                attrs={"class": "", "placeholder": "Select Date", "type": "date"},
            ),
        }
        labels = {
            "dateOfTransaction": "Date:",
            "incomeItem": "Income Item:",
            "description": "Description:",
            "unit": "Unit:",
            "quantity": "quantity:",
            "rate": "Rate:",
            "modeOfTransaction": "Mode:",
            "amount": "Amount:",
            "voucherNo": "Voucher No:",
            "remarks": "Remarks:",
        }


class IncomeForm(IncomeModelForm):
    QrySetIncomeSector = IncomeSector.objects.only("incomeSector").distinct()
    incomeSector = forms.ModelChoiceField(
        queryset=QrySetIncomeSector, label="Income Sector:", required=False
    )
    account_queryset = BankAccount.objects.filter(isArchive=False)
    account = forms.ModelChoiceField(
        queryset=account_queryset,
        label="Select Account:",
        required=False,
    )

    def __init__(self, *args, **kwargs):
        super(IncomeForm, self).__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.helper.attrs["autocomplete"] = "off"
        self.helper.layout = Layout(
            Row(
                Column("dateOfTransaction", css_class="form-group col-2 me-2 mb-0"),
                Column("incomeSector", css_class="form-group col-2 me-4 mb-0"),
                Column("incomeItem", css_class="form-group col ms-2 me-5 mb-0"),
                css_class="form-row",
            ),
            Row(
                Column("description", css_class="form-group col me-2  mb-0"),
                Column("unit", css_class="form-group col-1 me-2 ms-0  mb-0"),
                Column("quantity", css_class="form-group col-1 me-2 mb-0"),
                Column("rate", css_class="form-group col-1 me-2 mb-0"),
                Column("amount", css_class="form-group col-1 ms-0 me-2 mb-0"),
                Column("modeOfTransaction", css_class="form-group col-1 me-5 mb-0"),
                css_class="form-row",
            ),
            Row(
                Column("account", css_class="form-group col-5 me-4 mb-0"),
                Column("voucherNo", css_class="form-group col-1 me-2 ms-2  mb-0"),
                Column("remarks", css_class="form-group col me-4 mb-0"),
                css_class="form-row",
            ),
            HTML("<div class='d-flex justify-content-end mb-1' id='button_div'>"),
            Submit("submit", "Submit", css_class="btn btn-success btn-sm me-2 mb-0"),
            Reset("reset", "Reset", css_class="btn btn-danger btn-sm me-2 mb-0"),
            HTML("<a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 mb-0' role='button'><i class='bi bi-arrow-left-square'></i> Back</a>"),
            HTML("</div>"),
        )


#! Bank and Cash =======================
class BankAccountForm(forms.ModelForm):
    class Meta:
        model = BankAccount
        fields = "__all__"
        labels = {
            "openingDate": "Opening Date",
            "accountName": "Account Name:",
            "bankName": "Bank Name:",
            "branchName": "Branch Name:",
            "accountNo": "Account Number:",
            "remarks": "Remarks:",
        }
        widgets = {
            "openingDate": forms.DateInput(
                format=("%Y-%m-%d"),
                attrs={"class": "", "placeholder": "Select Date", "type": "date"},
            ),
        }

    def __init__(self, *args, **kwargs):
        super(BankAccountForm, self).__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.helper.attrs["autocomplete"] = "off"
        self.helper.layout = Layout(
            Row(
                Column("openingDate", css_class="form-group col-2 mb-0 me-2"),
                Column("accountName", css_class="form-group col-4 ms-2 me-2"),
                Column("bankName", css_class="form-group ms-2 me-4 mb-0"),
                css_class="form-row",
            ),
            Row(
                Column("branchName", css_class="form-group col ms-2 me-3 mb-0"),
                Column("accountNo", css_class="form-group col-3 me-2 mb-0"),
                Column("remarks", css_class="form-group ms-2 me-4 mb-0"),
                css_class="form-row",
            ),
            Row(
                Column("isArchive", css_class="form-group col-2 me-2 mb-0"),
                css_class="form-row",
            ),
            HTML("<div class='d-flex justify-content-end mb-1' id ='button_div'>"),
            Submit("submit", "Submit", css_class="btn btn-success btn-sm me-2 mb-0"),
            Reset("reset", "Reset", css_class="btn btn-danger btn-sm me-2 mb-0"),
            HTML("<a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 mb-0' role='button'><i class='bi bi-arrow-left-square'></i> Back</a>"),
            HTML("</div>"),
        )


class BankTransactionModelForm(forms.ModelForm):
    class Meta:
        model = BankLedger
        fields = ["dateOfTransaction", "account", "description", "remarks"]
        labels = {
            "dateOfTransaction": "Date:",
            "account": "Account:",
            "description": "Description:",
            "remarks": "Remarks:",
        }
        widgets = {
            "dateOfTransaction": forms.DateInput(
                format=("%Y-%m-%d"),
                attrs={"class": "", "placeholder": "Select Date", "type": "date"},
            ),
        }

    def __init__(self, *args, **kwargs):
        super(BankTransactionModelForm, self).__init__(*args, **kwargs)
        self.fields["account"].queryset = BankAccount.objects.filter(isArchive=False)


class BankTransactionForm(BankTransactionModelForm):
    transactionType = forms.ChoiceField(
        choices=(("", "----"), ("D", "Deposit to Bank"), ("W", "Cash Withdrawal")),
        required=True,
        label="Select Transaction Type:",
    )

    amount = forms.IntegerField(required=True, label="Amount")

    def __init__(self, *args, **kwargs):
        super(BankTransactionForm, self).__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.helper.attrs["autocomplete"] = "off"
        self.helper.layout = Layout(
            Row(
                Column("transactionType", css_class="form-group col-3 me-4 mb-0"),
                css_class="form-row",
            ),
            Row(
                Column("dateOfTransaction", css_class="form-group col-2 me-2 mb-0"),
                Column("account", css_class="form-group col-3 ms-2 me-4 mb-0"),
                Column("description", css_class="form-group col ms-3 me-2 mb-0"),
                Column("amount", css_class="form-group col-1 ms-2 me-4 mb-0"),
                css_class="form-row",
            ),
            Row(
                Column("remarks", css_class="form-group col me-4 mb-0"),
                css_class="form-row",
            ),
            HTML("<div class='d-flex justify-content-end mb-1' id ='button_div'>"),
            Submit("submit", "Submit", css_class="btn btn-success btn-sm me-2 mb-0"),
            Reset("reset", "Reset", css_class="btn btn-danger btn-sm me-2 mb-0"),
            HTML("<a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 mb-0' role='button'><i class='bi bi-arrow-left-square'></i> Back</a>"),
            HTML("</div>"),
        )


#! Work Phase =======================
class WorkPhaseForm(forms.ModelForm):
    class Meta:
        model = WorkPhase
        fields = ["description",
                    "fromDate",
                    "toDate",
                    "estimatedCost"]
        labels = {
            "description": "Description:",
            "fromDate": "From Date:",
            "toDate": "To Date:",
            "estimatedCost": "EstimatedCost:",
        }
        widgets = {
            "fromDate": forms.DateInput(
                format=("%Y-%m-%d"),
                attrs={"class": "", "placeholder": "Select Date", "type": "date"},
            ),
            "toDate": forms.DateInput(
                format=("%Y-%m-%d"),
                attrs={"class": "", "placeholder": "Select Date", "type": "date"},
            ),
        }

    def __init__(self, *args, **kwargs):
        super(WorkPhaseForm, self).__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.helper.attrs["autocomplete"] = "off"
        self.helper.layout = Layout(
            Row(
                Column("description", css_class="form-group col me-4 mb-0"),
                css_class="form-row",
            ),
            Row(
                Column("fromDate", css_class="form-group col me-2 mb-0"),
                Column("toDate", css_class="form-group col ms-2 me-2 mb-0"),
                Column("estimatedCost", css_class="form-group col-2 ms-2 me-4 mb-0"),
                css_class="form-row",
            ),
            HTML("<div class='d-flex justify-content-end mb-1' id ='button_div'>"),
            Submit("submit", "Submit", css_class="btn btn-success btn-sm me-2 mb-0"),
            Reset("reset", "Reset", css_class="btn btn-danger btn-sm me-2 mb-0"),
            HTML("<a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 mb-0' role='button'><i class='bi bi-arrow-left-square'></i> Back</a>"),
            HTML("</div>"),
        )

class CurrentWorkPhaseForm(forms.ModelForm):
    class Meta:
        model = CurrentWorkPhase
        fields = ["workPhase","dateOfChange",]
        labels = {
            "workPhase": "Work Phase:",
            "dateOfChange": "Date:",
        }
        widgets = {
            "dateOfChange": forms.DateInput(
                format=("%Y-%m-%d"),
                attrs={"class": "", "placeholder": "Select Date", "type": "date"},
            ),
        }

    def __init__(self, *args, **kwargs):
        super(CurrentWorkPhaseForm, self).__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.helper.attrs["autocomplete"] = "off"
        self.helper.layout = Layout(
            Row(
                Column("workPhase", css_class="form-group col me-4 mb-0"),
                Column("dateOfChange", css_class="form-group col ms-2 me-4 mb-0"),
                css_class="form-row",
            ),
            HTML("<div class='d-flex justify-content-end mb-1' id ='button_div'>"),
            Submit("submit", "Submit", css_class="btn btn-success btn-sm me-2 mb-0"),
            Reset("reset", "Reset", css_class="btn btn-danger btn-sm me-2 mb-0"),
            HTML("<a href='{{ request.META.HTTP_REFERER }}' class='btn btn-primary btn-sm me-0 mb-0' role='button'><i class='bi bi-arrow-left-square'></i> Back</a>"),
            HTML("</div>"),
        )

