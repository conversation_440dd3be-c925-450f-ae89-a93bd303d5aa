{% extends "main.html" %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters %}
{% comment %} {% load i18n %} {% endcomment %}
{% block title %}{{company_name}}{%endblock %}
{% block content %}

<div class="container pt-3" style="width: 70%; margin: auto;">
    <div class="card">
        <div class="card-header ps-2 py-1">
            <h4><i class="bi bi-server"></i> {{heading}}</h4>
        </div>
        <div class="card-body">
            {% crispy form %}
        </div>
    </div>
    <br>
    {% if update_tag is None %}
    <div class="card" id="deposit_details">
        <div class="card-header ps-2 py-1">
            <h4>{{data_heading}}</h4>
        </div>
        <ul class="list-group list-group-flush">
            {% for dt in data %}
            {% if forloop.counter|divisibleby:2 %}
            <li class="ps-5 pe-5 list-group-item list-group-item-light">- {{dt}}</li>
            {% else %}
            <li class="ps-5 pe-5 list-group-item list-group-item-secondary">- {{dt}}</li>
            {% endif %}
            {% endfor %}
        </ul>
    </div>
    {% endif %}
    </div>
    <br>

    {% endblock %}

    {% block script %}
    <script>
        var account_input = document.getElementById("id_account")
        {% if update_tag is True %}
        button_div = document.getElementById("button_div")
        button_div.innerHTML = `<input type="submit" name="submit" value="Update" class="btn btn-primary btn btn-success me-2 mb-0" id="submit-id-submit">
	<a href="{%url 'Accounts:shareholder_list'%}" class="btn btn-danger">Cancel</a>`
        // Bank Account Field Initialization start =========
        if ($('#id_modeOfTransaction').val() == "BANK") {
            $.ajax({
                url: '/get_account_no/' + {{ object.pk }} + "/ShareholderDeposit",
        dataType: "json",
            success: function (data) {
                $('#id_account').val(data[0]["fields"]["account"]);
            }
            });
        }

        if ($('#id_modeOfTransaction').val() == "CASH") {
            account_input.disabled = true;
        }
        // Bank Account Field Initialization end =========
        {% else %}
        $('#id_shareholder').change(function () {
            let shareholder_id = $(this).val();
            let htm = '<div class="card-header p-2 me-0 d-flex justify-content-between">'
            $.ajax({
                url: '/get_shareholder_deposit_info/' + shareholder_id,
                dataType: "json",
                success: function (data) {
                    htm += `<h3 class='pb-0 mb-0'><img src="${data[0]['avatar']}" class="card-img-top" alt="masud" style="border-radius: 15%; width: 50px; height: 50px;">
                    ${data[0]["shareholderName"]}</h3><h5 class="text-warning pt-2 pe-3">Total Deposited Amount: Tk ${data[data.length - 1]["total_deposit"]}/-</h5>`
                    htm += `</div><div class="d-flex justify-content-center"><table style="width: 90%;" class="table table-sm align-middle table-striped table-hover table-responsive">
                        <tr><th>Date</th><th>Deposit Mode</th><th class="text-end pe-5">Amount</th><th>Remarks</th></tr>`
                    $.each(data, function (index, Item) {
                        if (index != data.length - 1) {
                            htm += `<tr>
                        <td>${Item["date_of_transaction"]}</td>
                        <td>${Item["modeOfTransaction"]}</td>
                        <td class="text-end pe-5">${Item["amount"]}</td>
                        <td>${Item["remarks"]}</td>
                        </tr>`

                        }
                    });
                    htm += `</table></div>`
                    document.getElementById('deposit_details').innerHTML = htm;
                }
            });
        });

        {% endif %}
        // ======= Enable and Disable Bank Account Choice.========

        {% if not update_tag %}
        account_input.disabled = true;
        {% endif %}

        $('#id_modeOfTransaction').change(function () {
            if ($(this).val() == "BANK") {
                account_input.disabled = false;
                account_input.required = true;
            } else {
                account_input.disabled = true;
                account_input.required = false;
                account_input.value = "";
            }
        })
        // =======Enable and Disable Bank Account Choice end ============

    </script>
    {% endblock script %}