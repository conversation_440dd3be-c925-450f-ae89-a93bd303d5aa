{% extends "accounts/reports/reportbase.html" %}
{% load static %}
{% load mahimsoft_tags %}
{% load bangla_text %}
{% load mathfilters %}
{% block title %}<title>Shareholder List {% now 'd-M-Y' %}</title>{% endblock title %}
{% block link %}
  <link href="{% static 'page_css_portrait.css' %}" rel="stylesheet">
{% endblock link %}
{% block style %}
<style>
  @page {
    @bottom-right {
        content: '\00A9 {{data.company_name}}, Page No : ' counter(page) ' of ' counter(pages);
    }
}
</style>
{% endblock style %}

{% block contents %}
<p class="report_date">Date: {% now 'd-M-Y' %}</p>
<h1 id = "heading">{{data.heading}}</h1>
<div>
  <table>
    <thead>
    <tr style="height: 32px;">
      <th style="width: 3%; ">#</th>
      <th style="width: 6%; ">Photo</th>
      <th style="width: 18%;">Name</th>
      <th style="width: 10%;">NID</th>
      <th style="width: 24%;">Mobile & Email</th>
      <th style="width: 10%;">Nos. of Share</th>
      <th style="width: 14%;">Deposited Amnt</th>
      <th style="width: 15%;">Rest Amnt to Pay</th>
    </tr>
    </thead>
    <tbody>
    {% for dt in data.shareholder %}
    <tr style="height: 60 px;">
      <td style="text-align: center;">{{forloop.counter}}</td>
      <td style="text-align: center; padding: auto;">
        <img src="{{dt.avatar}}" 
        style="width: 50px; height: 50px;" alt="Avatar">
      </td>
      <td style="font-family:BanglaFontNikosh, sans-serif; font-size:10px;">{{dt.shareholderName|bangla_text_span|safe}}</td>
      
      {% if dt.nid == None %}
      <td style="text-align: center;">---</td>
      {% else %}
      <td style="text-align: center;">{{dt.nid|default_if_none:"---"}}</td>
      {% endif %}
      
      <td style="padding: 0px 0px 0px 5px; text-align: left;">&#128222; {{dt.mobile|default_if_none:"---"}}
      <br>&#128231; {{dt.email|default_if_none:"---"}}</td>
      <td style="text-align: center;">{{dt.numberOfFlat|floatformat:"1"}}</td>
      <td style="text-align: right;">{{dt.sum_amount|default_if_none:0|intcomma_bd}}/-</td>
      {% if dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount > 0 %}
      <td style="text-align: right; color:red;">
        {{dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount|number_product:-1|intcomma_bd}}/-
      </td>
      {% elif dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount == 0 %}
      <td style="text-align: right;">0/-</td>
      {% else %}
      <td style="text-align: right; color:green;">
        {{dt.numberOfFlat|number_product:data.targeted_amount_per_flat|subtract:dt.sum_amount|number_product:-1|intcomma_bd}}/-
      </td>
      {% endif %}
    </tr>
    {% endfor %}
    </tbody>
  </table>
</div>
<p style="color: firebrick; font-size: 12px;">NB: Rest amount to pay calculated as per targeted amount per share as {{ data.currency }}:
  {{data.no_of_flat_per_share|number_product:data.targeted_amount_per_flat|intcomma_bd}}/-</p>
  {% endblock %}

